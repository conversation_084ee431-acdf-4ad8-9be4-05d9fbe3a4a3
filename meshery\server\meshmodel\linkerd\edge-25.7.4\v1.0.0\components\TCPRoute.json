{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "TCP Route", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "linkerd", "displayName": "<PERSON><PERSON>", "description": "git://github.com/meshery/meshery-linkerd/master/templates/meshmodel/components/stable-2.9.5", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Cloud Native Network"}, "subCategory": "Service Mesh", "metadata": {"isAnnotation": false, "primaryColor": "#0DA6E5", "secondaryColor": "#0DA6E5", "shape": "circle", "source_uri": "git://github.com/linkerd/linkerd2/main/charts/linkerd-crds/", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\" height=\"20\" width=\"20\"><style xmlns=\"http://www.w3.org/2000/svg\">svg {enable-background:new 0 0 500 500}</style><style xmlns=\"http://www.w3.org/2000/svg\">.st2{fill:#2beda7}</style><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_1_\" x1=\"477.221\" x2=\"477.221\" y1=\"106.515\" y2=\"308.8\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_1_)\" d=\"M460.4 106.5v182.8l33.7 19.5V126z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_2_\" x1=\"25.459\" x2=\"25.459\" y1=\"106.52\" y2=\"308.812\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_2_)\" d=\"M8.6 308.8l33.7-19.5V106.5L8.6 126z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M173.8 307l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st2\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_3_\" x1=\"477.221\" x2=\"477.221\" y1=\"196.062\" y2=\"382.938\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_3_)\" d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_4_\" x1=\"403.048\" x2=\"403.048\" y1=\"238.884\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_4_)\" d=\"M394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_5_\" x1=\"328.877\" x2=\"328.877\" y1=\"281.704\" y2=\"472.469\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_5_)\" d=\"M312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_6_\" x1=\"173.82\" x2=\"173.82\" y1=\"281.704\" y2=\"472.466\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_6_)\" d=\"M159.6 454.5l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_7_\" x1=\"99.649\" x2=\"99.649\" y1=\"238.883\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_7_)\" d=\"M86.8 412.5l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_8_\" x1=\"25.478\" x2=\"25.478\" y1=\"196.059\" y2=\"382.936\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_8_)\" d=\"M12.6 369.7l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st2\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\" height=\"20\" width=\"20\"><style xmlns=\"http://www.w3.org/2000/svg\">svg {enable-background:new 0 0 500 500}</style><style xmlns=\"http://www.w3.org/2000/svg\">.st0{fill:#fff}</style><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M460.4 106.5v182.8l33.7 19.5V126zM8.6 308.8l33.7-19.5V106.5L8.6 126zm165.2-1.8l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st0\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4zM394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5zM312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3zm-152.4-18l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6zm-72.8-42l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7zm-74.2-42.8l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\" class=\"st0\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st0\"></path></svg>"}, "model": {"version": "edge-25.7.4"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#0DA6E5", "secondaryColor": "#0DA6E5", "shape": "circle", "svgColor": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\"><style>svg {enable-background:new 0 0 500 500}</style><style>.st2{fill:#2beda7}</style><linearGradient id=\"SVGID_1_\" x1=\"477.221\" x2=\"477.221\" y1=\"106.515\" y2=\"308.8\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_1_)\" d=\"M460.4 106.5v182.8l33.7 19.5V126z\"/><linearGradient id=\"SVGID_2_\" x1=\"25.459\" x2=\"25.459\" y1=\"106.52\" y2=\"308.812\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_2_)\" d=\"M8.6 308.8l33.7-19.5V106.5L8.6 126z\"/><path d=\"M173.8 307l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st2\"/><linearGradient id=\"SVGID_3_\" x1=\"477.221\" x2=\"477.221\" y1=\"196.062\" y2=\"382.938\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_3_)\" d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4z\"/><linearGradient id=\"SVGID_4_\" x1=\"403.048\" x2=\"403.048\" y1=\"238.884\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_4_)\" d=\"M394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5z\"/><linearGradient id=\"SVGID_5_\" x1=\"328.877\" x2=\"328.877\" y1=\"281.704\" y2=\"472.469\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_5_)\" d=\"M312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3z\"/><linearGradient id=\"SVGID_6_\" x1=\"173.82\" x2=\"173.82\" y1=\"281.704\" y2=\"472.466\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_6_)\" d=\"M159.6 454.5l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6z\"/><linearGradient id=\"SVGID_7_\" x1=\"99.649\" x2=\"99.649\" y1=\"238.883\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_7_)\" d=\"M86.8 412.5l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7z\"/><linearGradient id=\"SVGID_8_\" x1=\"25.478\" x2=\"25.478\" y1=\"196.059\" y2=\"382.936\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_8_)\" d=\"M12.6 369.7l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\"/><path d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st2\"/></svg>", "svgComplete": "", "svgWhite": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\"><style>svg {enable-background:new 0 0 500 500}</style><style>.st0{fill:#fff}</style><path d=\"M460.4 106.5v182.8l33.7 19.5V126zM8.6 308.8l33.7-19.5V106.5L8.6 126zm165.2-1.8l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st0\"/><path d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4zM394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5zM312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3zm-152.4-18l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6zm-72.8-42l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7zm-74.2-42.8l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\" class=\"st0\"/><path d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st0\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/linkerd/linkerd2/main/charts/linkerd-crds/"}, "configuration": null, "component": {"version": "gateway.networking.k8s.io/v1alpha2", "kind": "TCPRoute", "schema": "{\n \"description\": \"TCPRoute provides a way to route TCP requests. When combined with a Gateway\\nlistener, it can be used to forward connections on the port specified by the\\nlistener to a set of backends specified by the TCPRoute.\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Spec defines the desired state of TCPRoute.\",\n   \"properties\": {\n    \"parentRefs\": {\n     \"description\": \"ParentRefs references the resources (usually Gateways) that a Route wants\\nto be attached to. Note that the referenced parent resource needs to\\nallow this for the attachment to be complete. For Gateways, that means\\nthe Gateway needs to allow attachment from Routes of this kind and\\nnamespace. For Services, that means the Service must either be in the same\\nnamespace for a \\\"producer\\\" route, or the mesh implementation must support\\nand allow \\\"consumer\\\" routes for the referenced Service. ReferenceGrant is\\nnot applicable for governing ParentRefs to Services - it is not possible to\\ncreate a \\\"producer\\\" route for a Service in a different namespace from the\\nRoute.\\n\\n\\nThere are two kinds of parent resources with \\\"Core\\\" support:\\n\\n\\n* Gateway (Gateway conformance profile)\\n* Service (Mesh conformance profile, ClusterIP Services only)\\n\\n\\nThis API may be extended in the future to support additional kinds of parent\\nresources.\\n\\n\\nParentRefs must be _distinct_. This means either that:\\n\\n\\n* They select different objects.  If this is the case, then parentRef\\n  entries are distinct. In terms of fields, this means that the\\n  multi-part key defined by `group`, `kind`, `namespace`, and `name` must\\n  be unique across all parentRef entries in the Route.\\n* They do not select different objects, but for each optional field used,\\n  each ParentRef that selects the same object must set the same set of\\n  optional fields to different values. If one ParentRef sets a\\n  combination of optional fields, all must set the same combination.\\n\\n\\nSome examples:\\n\\n\\n* If one ParentRef sets `sectionName`, all ParentRefs referencing the\\n  same object must also set `sectionName`.\\n* If one ParentRef sets `port`, all ParentRefs referencing the same\\n  object must also set `port`.\\n* If one ParentRef sets `sectionName` and `port`, all ParentRefs\\n  referencing the same object must also set `sectionName` and `port`.\\n\\n\\nIt is possible to separately reference multiple distinct objects that may\\nbe collapsed by an implementation. For example, some implementations may\\nchoose to merge compatible Gateway Listeners together. If that is the\\ncase, the list of routes attached to those resources should also be\\nmerged.\\n\\n\\nNote that for ParentRefs that cross namespace boundaries, there are specific\\nrules. Cross-namespace references are only valid if they are explicitly\\nallowed by something in the namespace they are referring to. For example,\\nGateway has the AllowedRoutes field, and ReferenceGrant provides a\\ngeneric way to enable other kinds of cross-namespace reference.\\n\\n\\n\\nParentRefs from a Route to a Service in the same namespace are \\\"producer\\\"\\nroutes, which apply default routing rules to inbound connections from\\nany namespace to the Service.\\n\\n\\nParentRefs from a Route to a Service in a different namespace are\\n\\\"consumer\\\" routes, and these routing rules are only applied to outbound\\nconnections originating from the same namespace as the Route, for which\\nthe intended destination of the connections are a Service targeted as a\\nParentRef of the Route.\\n\\n\\n\\n\\n\\n\\n\",\n     \"items\": {\n      \"description\": \"ParentReference identifies an API object (usually a Gateway) that can be considered\\na parent of this resource (usually a route). There are two kinds of parent resources\\nwith \\\"Core\\\" support:\\n\\n\\n* Gateway (Gateway conformance profile)\\n* Service (Mesh conformance profile, ClusterIP Services only)\\n\\n\\nThis API may be extended in the future to support additional kinds of parent\\nresources.\\n\\n\\nThe API object must be valid in the cluster; the Group and Kind must\\nbe registered in the cluster for this reference to be valid.\",\n      \"properties\": {\n       \"group\": {\n        \"default\": \"gateway.networking.k8s.io\",\n        \"description\": \"Group is the group of the referent.\\nWhen unspecified, \\\"gateway.networking.k8s.io\\\" is inferred.\\nTo set the core API group (such as for a \\\"Service\\\" kind referent),\\nGroup must be explicitly set to \\\"\\\" (empty string).\\n\\n\\nSupport: Core\",\n        \"maxLength\": 253,\n        \"pattern\": \"^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n        \"type\": \"string\"\n       },\n       \"kind\": {\n        \"default\": \"Gateway\",\n        \"description\": \"Kind is kind of the referent.\\n\\n\\nThere are two kinds of parent resources with \\\"Core\\\" support:\\n\\n\\n* Gateway (Gateway conformance profile)\\n* Service (Mesh conformance profile, ClusterIP Services only)\\n\\n\\nSupport for other resources is Implementation-Specific.\",\n        \"maxLength\": 63,\n        \"minLength\": 1,\n        \"pattern\": \"^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$\",\n        \"type\": \"string\"\n       },\n       \"name\": {\n        \"description\": \"Name is the name of the referent.\\n\\n\\nSupport: Core\",\n        \"maxLength\": 253,\n        \"minLength\": 1,\n        \"type\": \"string\"\n       },\n       \"namespace\": {\n        \"description\": \"Namespace is the namespace of the referent. When unspecified, this refers\\nto the local namespace of the Route.\\n\\n\\nNote that there are specific rules for ParentRefs which cross namespace\\nboundaries. Cross-namespace references are only valid if they are explicitly\\nallowed by something in the namespace they are referring to. For example:\\nGateway has the AllowedRoutes field, and ReferenceGrant provides a\\ngeneric way to enable any other kind of cross-namespace reference.\\n\\n\\n\\nParentRefs from a Route to a Service in the same namespace are \\\"producer\\\"\\nroutes, which apply default routing rules to inbound connections from\\nany namespace to the Service.\\n\\n\\nParentRefs from a Route to a Service in a different namespace are\\n\\\"consumer\\\" routes, and these routing rules are only applied to outbound\\nconnections originating from the same namespace as the Route, for which\\nthe intended destination of the connections are a Service targeted as a\\nParentRef of the Route.\\n\\n\\n\\nSupport: Core\",\n        \"maxLength\": 63,\n        \"minLength\": 1,\n        \"pattern\": \"^[a-z0-9]([-a-z0-9]*[a-z0-9])?$\",\n        \"type\": \"string\"\n       },\n       \"port\": {\n        \"description\": \"Port is the network port this Route targets. It can be interpreted\\ndifferently based on the type of parent resource.\\n\\n\\nWhen the parent resource is a Gateway, this targets all listeners\\nlistening on the specified port that also support this kind of Route(and\\nselect this Route). It's not recommended to set `Port` unless the\\nnetworking behaviors specified in a Route must apply to a specific port\\nas opposed to a listener(s) whose port(s) may be changed. When both Port\\nand SectionName are specified, the name and port of the selected listener\\nmust match both specified values.\\n\\n\\n\\nWhen the parent resource is a Service, this targets a specific port in the\\nService spec. When both Port (experimental) and SectionName are specified,\\nthe name and port of the selected port must match both specified values.\\n\\n\\n\\nImplementations MAY choose to support other parent resources.\\nImplementations supporting other types of parent resources MUST clearly\\ndocument how/if Port is interpreted.\\n\\n\\nFor the purpose of status, an attachment is considered successful as\\nlong as the parent resource accepts it partially. For example, Gateway\\nlisteners can restrict which Routes can attach to them by Route kind,\\nnamespace, or hostname. If 1 of 2 Gateway listeners accept attachment\\nfrom the referencing Route, the Route MUST be considered successfully\\nattached. If no Gateway listeners accept attachment from this Route,\\nthe Route MUST be considered detached from the Gateway.\\n\\n\\nSupport: Extended\",\n        \"format\": \"int32\",\n        \"maximum\": 65535,\n        \"minimum\": 1,\n        \"type\": \"integer\"\n       },\n       \"sectionName\": {\n        \"description\": \"SectionName is the name of a section within the target resource. In the\\nfollowing resources, SectionName is interpreted as the following:\\n\\n\\n* Gateway: Listener name. When both Port (experimental) and SectionName\\nare specified, the name and port of the selected listener must match\\nboth specified values.\\n* Service: Port name. When both Port (experimental) and SectionName\\nare specified, the name and port of the selected listener must match\\nboth specified values.\\n\\n\\nImplementations MAY choose to support attaching Routes to other resources.\\nIf that is the case, they MUST clearly document how SectionName is\\ninterpreted.\\n\\n\\nWhen unspecified (empty string), this will reference the entire resource.\\nFor the purpose of status, an attachment is considered successful if at\\nleast one section in the parent resource accepts it. For example, Gateway\\nlisteners can restrict which Routes can attach to them by Route kind,\\nnamespace, or hostname. If 1 of 2 Gateway listeners accept attachment from\\nthe referencing Route, the Route MUST be considered successfully\\nattached. If no Gateway listeners accept attachment from this Route, the\\nRoute MUST be considered detached from the Gateway.\\n\\n\\nSupport: Core\",\n        \"maxLength\": 253,\n        \"minLength\": 1,\n        \"pattern\": \"^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"maxItems\": 32,\n     \"type\": \"array\",\n     \"x-kubernetes-validations\": [\n      {\n       \"message\": \"sectionName or port must be specified when parentRefs includes 2 or more references to the same parent\",\n       \"rule\": \"self.all(p1, self.all(p2, p1.group == p2.group \\u0026\\u0026 p1.kind == p2.kind \\u0026\\u0026 p1.name == p2.name \\u0026\\u0026 (((!has(p1.__namespace__) || p1.__namespace__ == '') \\u0026\\u0026 (!has(p2.__namespace__) || p2.__namespace__ == '')) || (has(p1.__namespace__) \\u0026\\u0026 has(p2.__namespace__) \\u0026\\u0026 p1.__namespace__ == p2.__namespace__)) ? ((!has(p1.sectionName) || p1.sectionName == '') == (!has(p2.sectionName) || p2.sectionName == '') \\u0026\\u0026 (!has(p1.port) || p1.port == 0) == (!has(p2.port) || p2.port == 0)): true))\"\n      },\n      {\n       \"message\": \"sectionName or port must be unique when parentRefs includes 2 or more references to the same parent\",\n       \"rule\": \"self.all(p1, self.exists_one(p2, p1.group == p2.group \\u0026\\u0026 p1.kind == p2.kind \\u0026\\u0026 p1.name == p2.name \\u0026\\u0026 (((!has(p1.__namespace__) || p1.__namespace__ == '') \\u0026\\u0026 (!has(p2.__namespace__) || p2.__namespace__ == '')) || (has(p1.__namespace__) \\u0026\\u0026 has(p2.__namespace__) \\u0026\\u0026 p1.__namespace__ == p2.__namespace__ )) \\u0026\\u0026 (((!has(p1.sectionName) || p1.sectionName == '') \\u0026\\u0026 (!has(p2.sectionName) || p2.sectionName == '')) || ( has(p1.sectionName) \\u0026\\u0026 has(p2.sectionName) \\u0026\\u0026 p1.sectionName == p2.sectionName)) \\u0026\\u0026 (((!has(p1.port) || p1.port == 0) \\u0026\\u0026 (!has(p2.port) || p2.port == 0)) || (has(p1.port) \\u0026\\u0026 has(p2.port) \\u0026\\u0026 p1.port == p2.port))))\"\n      }\n     ]\n    },\n    \"rules\": {\n     \"description\": \"Rules are a list of TCP matchers and actions.\",\n     \"items\": {\n      \"description\": \"TCPRouteRule is the configuration for a given rule.\",\n      \"properties\": {\n       \"backendRefs\": {\n        \"description\": \"BackendRefs defines the backend(s) where matching requests should be\\nsent. If unspecified or invalid (refers to a non-existent resource or a\\nService with no endpoints), the underlying implementation MUST actively\\nreject connection attempts to this backend. Connection rejections must\\nrespect weight; if an invalid backend is requested to have 80% of\\nconnections, then 80% of connections must be rejected instead.\\n\\n\\nSupport: Core for Kubernetes Service\\n\\n\\nSupport: Extended for Kubernetes ServiceImport\\n\\n\\nSupport: Implementation-specific for any other resource\\n\\n\\nSupport for weight: Extended\",\n        \"items\": {\n         \"description\": \"BackendRef defines how a Route should forward a request to a Kubernetes\\nresource.\\n\\n\\nNote that when a namespace different than the local namespace is specified, a\\nReferenceGrant object is required in the referent namespace to allow that\\nnamespace's owner to accept the reference. See the ReferenceGrant\\ndocumentation for details.\\n\\n\\n\\u003cgateway:experimental:description\\u003e\\n\\n\\nWhen the BackendRef points to a Kubernetes Service, implementations SHOULD\\nhonor the appProtocol field if it is set for the target Service Port.\\n\\n\\nImplementations supporting appProtocol SHOULD recognize the Kubernetes\\nStandard Application Protocols defined in KEP-3726.\\n\\n\\nIf a Service appProtocol isn't specified, an implementation MAY infer the\\nbackend protocol through its own means. Implementations MAY infer the\\nprotocol from the Route type referring to the backend Service.\\n\\n\\nIf a Route is not able to send traffic to the backend using the specified\\nprotocol then the backend is considered invalid. Implementations MUST set the\\n\\\"ResolvedRefs\\\" condition to \\\"False\\\" with the \\\"UnsupportedProtocol\\\" reason.\\n\\n\\n\\u003c/gateway:experimental:description\\u003e\\n\\n\\nNote that when the BackendTLSPolicy object is enabled by the implementation,\\nthere are some extra rules about validity to consider here. See the fields\\nwhere this struct is used for more information about the exact behavior.\",\n         \"properties\": {\n          \"group\": {\n           \"default\": \"\",\n           \"description\": \"Group is the group of the referent. For example, \\\"gateway.networking.k8s.io\\\".\\nWhen unspecified or empty string, core API group is inferred.\",\n           \"maxLength\": 253,\n           \"pattern\": \"^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n           \"type\": \"string\"\n          },\n          \"kind\": {\n           \"default\": \"Service\",\n           \"description\": \"Kind is the Kubernetes resource kind of the referent. For example\\n\\\"Service\\\".\\n\\n\\nDefaults to \\\"Service\\\" when not specified.\\n\\n\\nExternalName services can refer to CNAME DNS records that may live\\noutside of the cluster and as such are difficult to reason about in\\nterms of conformance. They also may not be safe to forward to (see\\nCVE-2021-25740 for more information). Implementations SHOULD NOT\\nsupport ExternalName Services.\\n\\n\\nSupport: Core (Services with a type other than ExternalName)\\n\\n\\nSupport: Implementation-specific (Services with type ExternalName)\",\n           \"maxLength\": 63,\n           \"minLength\": 1,\n           \"pattern\": \"^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"Name is the name of the referent.\",\n           \"maxLength\": 253,\n           \"minLength\": 1,\n           \"type\": \"string\"\n          },\n          \"namespace\": {\n           \"description\": \"Namespace is the namespace of the backend. When unspecified, the local\\nnamespace is inferred.\\n\\n\\nNote that when a namespace different than the local namespace is specified,\\na ReferenceGrant object is required in the referent namespace to allow that\\nnamespace's owner to accept the reference. See the ReferenceGrant\\ndocumentation for details.\\n\\n\\nSupport: Core\",\n           \"maxLength\": 63,\n           \"minLength\": 1,\n           \"pattern\": \"^[a-z0-9]([-a-z0-9]*[a-z0-9])?$\",\n           \"type\": \"string\"\n          },\n          \"port\": {\n           \"description\": \"Port specifies the destination port number to use for this resource.\\nPort is required when the referent is a Kubernetes Service. In this\\ncase, the port number is the service port number, not the target port.\\nFor other resources, destination port might be derived from the referent\\nresource or this field.\",\n           \"format\": \"int32\",\n           \"maximum\": 65535,\n           \"minimum\": 1,\n           \"type\": \"integer\"\n          },\n          \"weight\": {\n           \"default\": 1,\n           \"description\": \"Weight specifies the proportion of requests forwarded to the referenced\\nbackend. This is computed as weight/(sum of all weights in this\\nBackendRefs list). For non-zero values, there may be some epsilon from\\nthe exact proportion defined here depending on the precision an\\nimplementation supports. Weight is not a percentage and the sum of\\nweights does not need to equal 100.\\n\\n\\nIf only one backend is specified and it has a weight greater than 0, 100%\\nof the traffic is forwarded to that backend. If weight is set to 0, no\\ntraffic should be forwarded for this entry. If unspecified, weight\\ndefaults to 1.\\n\\n\\nSupport for this field varies based on the context where used.\",\n           \"format\": \"int32\",\n           \"maximum\": 1000000,\n           \"minimum\": 0,\n           \"type\": \"integer\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-validations\": [\n          {\n           \"message\": \"Must have port for Service reference\",\n           \"rule\": \"(size(self.group) == 0 \\u0026\\u0026 self.kind == 'Service') ? has(self.port) : true\"\n          }\n         ]\n        },\n        \"maxItems\": 16,\n        \"minItems\": 1,\n        \"type\": \"array\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"maxItems\": 16,\n     \"minItems\": 1,\n     \"type\": \"array\"\n    }\n   },\n   \"required\": [\n    \"rules\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"required\": [\n  \"spec\"\n ],\n \"title\": \"TCP Route\",\n \"type\": \"object\"\n}"}}