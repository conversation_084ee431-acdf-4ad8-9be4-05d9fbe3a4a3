{"id": "00000000-0000-0000-0000-000000000000", "evaluationQuery": "", "kind": "hierarchical", "metadata": {"description": "A relationship between two objects that are siblings", "styles": {"primaryColor": "", "svgColor": "", "svgWhite": ""}, "isAnnotation": false}, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "schemaVersion": "relationships.meshery.io/v1alpha3", "selectors": [{"allow": {"from": [{"id": null, "kind": "*", "match": {"refs": [["configuration", "metadata", "labels"]]}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "*", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "", "sub_type": "", "kind": "github", "status": "", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}], "to": [{"id": null, "kind": "*", "match": {"refs": [["configuration", "metadata", "labels"]]}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "*", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "", "sub_type": "", "kind": "github", "status": "", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}]}}], "subType": "matchlabels", "status": "enabled", "type": "sibling", "version": "v1.0.0"}