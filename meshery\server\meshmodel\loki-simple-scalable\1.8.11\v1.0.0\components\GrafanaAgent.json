{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Grafana Agent", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "loki-simple-scalable", "displayName": "Loki Simple Scalable", "description": "Loki is a horizontally scalable, highly available, multi-tenant log aggregation system inspired by Prometheus. It is designed to be very cost effective and easy to operate. It does not index the contents of the logs, but rather a set of labels for each log stream.", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Observability and Analysis"}, "subCategory": "Logging", "metadata": {"isAnnotation": false, "primaryColor": "#F15B2B", "secondaryColor": "#FAED1E", "shape": "circle", "source_uri": "https://github.com/grafana/helm-charts/releases/download/loki-simple-scalable-1.8.11/loki-simple-scalable-1.8.11.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 48 56\" fill=\"none\">\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.0478 54.9248L11.3838 50.4663L6.92529 51.1304L7.68418 55.5889L12.0478 54.9248Z\" fill=\"url(#paint0_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M46.957 42.4032L46.1981 38.0396L26.7515 41.0751L27.3206 45.4388L46.957 42.4032Z\" fill=\"url(#paint1_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M20.395 46.5772L24.8535 45.8183L24.1895 41.4546L19.731 42.1186L20.395 46.5772Z\" fill=\"url(#paint2_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M19.0674 53.7865L18.3085 49.4229L13.9448 50.0869L14.514 54.5454L19.0674 53.7865Z\" fill=\"url(#paint3_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M5.88135 44.2055L6.54539 48.6641L11.0039 48L10.3399 43.5415L5.88135 44.2055Z\" fill=\"url(#paint4_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M27.6997 47.9051L28.4586 52.4585L48.0001 49.4229L47.3361 44.9644L27.6997 47.9051Z\" fill=\"url(#paint5_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.5333 53.407L25.8969 52.8378L25.2329 48.2844L20.7744 49.0433L21.5333 53.407Z\" fill=\"url(#paint6_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.8062 43.1621L13.565 47.6205L17.9287 46.9566L17.2646 42.498L12.8062 43.1621Z\" fill=\"url(#paint7_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.39921 41.4546L1.99207 5.97632L0 6.26089L5.50197 41.7392L7.39921 41.4546Z\" fill=\"url(#paint8_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.96032 41.0751L4.07888 2.94067L2.18164 3.32014L8.06308 41.3597L9.96032 41.0751Z\" fill=\"url(#paint9_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M14.3245 40.4111L8.15847 0L6.26123 0.379412L12.4272 40.6008L14.3245 40.4111Z\" fill=\"url(#paint10_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.8852 40.0315L11.1935 3.2251L9.39111 3.50967L15.0828 40.2212L16.8852 40.0315Z\" fill=\"url(#paint11_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.2491 39.2728L16.2215 6.64038L14.3242 6.92495L19.3519 39.6523L21.2491 39.2728Z\" fill=\"url(#paint12_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.8104 38.8935L18.593 5.02783L16.6958 5.31241L22.0081 39.1781L23.8104 38.8935Z\" fill=\"url(#paint13_linear_17931_893)\"></path>\n<defs xmlns=\"http://www.w3.org/2000/svg\">\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint0_linear_17931_893\" x1=\"11.6469\" y1=\"66.8772\" x2=\"1.23198\" y2=\"-0.802501\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint1_linear_17931_893\" x1=\"39.9916\" y1=\"62.5154\" x2=\"29.5768\" y2=\"-5.1639\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint2_linear_17931_893\" x1=\"25.5063\" y1=\"64.7445\" x2=\"15.0913\" y2=\"-2.93516\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint3_linear_17931_893\" x1=\"18.5788\" y1=\"65.8105\" x2=\"8.1638\" y2=\"-1.86922\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint4_linear_17931_893\" x1=\"11.6394\" y1=\"66.8784\" x2=\"1.22448\" y2=\"-0.80128\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint5_linear_17931_893\" x1=\"39.9982\" y1=\"62.5143\" x2=\"29.5833\" y2=\"-5.16528\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint6_linear_17931_893\" x1=\"25.506\" y1=\"64.7443\" x2=\"15.091\" y2=\"-2.93537\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint7_linear_17931_893\" x1=\"18.5788\" y1=\"65.8103\" x2=\"8.16407\" y2=\"-1.86867\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint8_linear_17931_893\" x1=\"10.1623\" y1=\"65.7597\" x2=\"0.284696\" y2=\"1.57166\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint9_linear_17931_893\" x1=\"13.0129\" y1=\"67.1431\" x2=\"2.40785\" y2=\"-1.77243\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint10_linear_17931_893\" x1=\"17.6338\" y1=\"68.0331\" x2=\"6.38943\" y2=\"-5.0367\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint11_linear_17931_893\" x1=\"19.8305\" y1=\"65.208\" x2=\"9.57925\" y2=\"-1.40832\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint12_linear_17931_893\" x1=\"23.7353\" y1=\"61.7393\" x2=\"14.6289\" y2=\"2.56246\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint13_linear_17931_893\" x1=\"26.4465\" y1=\"62.1967\" x2=\"16.9911\" y2=\"0.751851\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n</defs>\n</svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 48 56\" fill=\"none\">\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.0478 54.9248L11.3838 50.4663L6.92529 51.1304L7.68418 55.5889L12.0478 54.9248Z\" fill=\"url(#paint0_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M46.957 42.4032L46.1981 38.0396L26.7515 41.0751L27.3206 45.4388L46.957 42.4032Z\" fill=\"url(#paint1_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M20.395 46.5772L24.8535 45.8183L24.1895 41.4546L19.731 42.1186L20.395 46.5772Z\" fill=\"url(#paint2_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M19.0674 53.7865L18.3085 49.4229L13.9448 50.0869L14.514 54.5454L19.0674 53.7865Z\" fill=\"url(#paint3_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M5.88135 44.2055L6.54539 48.6641L11.0039 48L10.3399 43.5415L5.88135 44.2055Z\" fill=\"url(#paint4_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M27.6997 47.9051L28.4586 52.4585L48.0001 49.4229L47.3361 44.9644L27.6997 47.9051Z\" fill=\"url(#paint5_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.5333 53.407L25.8969 52.8378L25.2329 48.2844L20.7744 49.0433L21.5333 53.407Z\" fill=\"url(#paint6_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.8062 43.1621L13.565 47.6205L17.9287 46.9566L17.2646 42.498L12.8062 43.1621Z\" fill=\"url(#paint7_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.39921 41.4546L1.99207 5.97632L0 6.26089L5.50197 41.7392L7.39921 41.4546Z\" fill=\"url(#paint8_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.96032 41.0751L4.07888 2.94067L2.18164 3.32014L8.06308 41.3597L9.96032 41.0751Z\" fill=\"url(#paint9_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M14.3245 40.4111L8.15847 0L6.26123 0.379412L12.4272 40.6008L14.3245 40.4111Z\" fill=\"url(#paint10_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.8852 40.0315L11.1935 3.2251L9.39111 3.50967L15.0828 40.2212L16.8852 40.0315Z\" fill=\"url(#paint11_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.2491 39.2728L16.2215 6.64038L14.3242 6.92495L19.3519 39.6523L21.2491 39.2728Z\" fill=\"url(#paint12_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.8104 38.8935L18.593 5.02783L16.6958 5.31241L22.0081 39.1781L23.8104 38.8935Z\" fill=\"url(#paint13_linear_17931_893)\"></path>\n<defs xmlns=\"http://www.w3.org/2000/svg\">\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint0_linear_17931_893\" x1=\"11.6469\" y1=\"66.8772\" x2=\"1.23198\" y2=\"-0.802501\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint1_linear_17931_893\" x1=\"39.9916\" y1=\"62.5154\" x2=\"29.5768\" y2=\"-5.1639\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint2_linear_17931_893\" x1=\"25.5063\" y1=\"64.7445\" x2=\"15.0913\" y2=\"-2.93516\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint3_linear_17931_893\" x1=\"18.5788\" y1=\"65.8105\" x2=\"8.1638\" y2=\"-1.86922\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint4_linear_17931_893\" x1=\"11.6394\" y1=\"66.8784\" x2=\"1.22448\" y2=\"-0.80128\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint5_linear_17931_893\" x1=\"39.9982\" y1=\"62.5143\" x2=\"29.5833\" y2=\"-5.16528\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint6_linear_17931_893\" x1=\"25.506\" y1=\"64.7443\" x2=\"15.091\" y2=\"-2.93537\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint7_linear_17931_893\" x1=\"18.5788\" y1=\"65.8103\" x2=\"8.16407\" y2=\"-1.86867\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint8_linear_17931_893\" x1=\"10.1623\" y1=\"65.7597\" x2=\"0.284696\" y2=\"1.57166\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint9_linear_17931_893\" x1=\"13.0129\" y1=\"67.1431\" x2=\"2.40785\" y2=\"-1.77243\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint10_linear_17931_893\" x1=\"17.6338\" y1=\"68.0331\" x2=\"6.38943\" y2=\"-5.0367\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint11_linear_17931_893\" x1=\"19.8305\" y1=\"65.208\" x2=\"9.57925\" y2=\"-1.40832\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint12_linear_17931_893\" x1=\"23.7353\" y1=\"61.7393\" x2=\"14.6289\" y2=\"2.56246\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint13_linear_17931_893\" x1=\"26.4465\" y1=\"62.1967\" x2=\"16.9911\" y2=\"0.751851\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n</defs>\n</svg>"}, "model": {"version": "1.8.11"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#F15B2A", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\"\n         viewBox=\"0 0 103.7 112.7\" style=\"enable-background:new 0 0 103.7 112.7;\" xml:space=\"preserve\">\n<style type=\"text/css\">\n        .st0{fill:url(#SVGID_1_);}\n</style>\n<linearGradient id=\"SVGID_1_\" gradientUnits=\"userSpaceOnUse\" x1=\"51.85\" y1=\"1069.5107\" x2=\"51.85\" y2=\"966.6585\" gradientTransform=\"matrix(1 0 0 1 0 -931.4)\">\n        <stop  offset=\"0\" style=\"stop-color:#FCEE1F\"/>\n        <stop  offset=\"1\" style=\"stop-color:#F15B2A\"/>\n</linearGradient>\n<path class=\"st0\" d=\"M103.5,49.9c-0.2-1.9-0.5-4.1-1.1-6.5c-0.6-2.4-1.6-5-2.9-7.8c-1.4-2.7-3.1-5.6-5.4-8.3\n        c-0.9-1.1-1.9-2.1-2.9-3.2c1.6-6.3-1.9-11.8-1.9-11.8c-6.1-0.4-9.9,1.9-11.3,2.9c-0.2-0.1-0.5-0.2-0.7-0.3c-1-0.4-2.1-0.8-3.2-1.2\n        c-1.1-0.3-2.2-0.7-3.3-0.9c-1.1-0.3-2.3-0.5-3.5-0.7c-0.2,0-0.4-0.1-0.6-0.1C64.1,3.6,56.5,0,56.5,0c-8.7,5.6-10.4,13.1-10.4,13.1\n        s0,0.2-0.1,0.4c-0.5,0.1-0.9,0.3-1.4,0.4c-0.6,0.2-1.3,0.4-1.9,0.7c-0.6,0.3-1.3,0.5-1.9,0.8c-1.3,0.6-2.5,1.2-3.8,1.9\n        c-1.2,0.7-2.4,1.4-3.5,2.2c-0.2-0.1-0.3-0.2-0.3-0.2c-11.7-4.5-22.1,0.9-22.1,0.9c-0.9,12.5,4.7,20.3,5.8,21.7\n        c-0.3,0.8-0.5,1.5-0.8,2.3c-0.9,2.8-1.5,5.7-1.9,8.7c-0.1,0.4-0.1,0.9-0.2,1.3C3.2,59.5,0,70.5,0,70.5c9,10.4,19.6,11,19.6,11l0,0\n        c1.3,2.4,2.9,4.7,4.6,6.8c0.7,0.9,1.5,1.7,2.3,2.6c-3.3,9.4,0.5,17.3,0.5,17.3c10.1,0.4,16.7-4.4,18.1-5.5c1,0.3,2,0.6,3,0.9\n        c3.1,0.8,6.3,1.3,9.4,1.4c0.8,0,1.6,0,2.4,0h0.4h0.3h0.5h0.5l0,0c4.7,6.8,13.1,7.7,13.1,7.7c5.9-6.3,6.3-12.4,6.3-13.8l0,0\n        c0,0,0,0,0-0.1s0-0.2,0-0.2l0,0c0-0.1,0-0.2,0-0.3c1.2-0.9,2.4-1.8,3.6-2.8c2.4-2.1,4.4-4.6,6.2-7.2c0.2-0.2,0.3-0.5,0.5-0.7\n        c6.7,0.4,11.4-4.2,11.4-4.2c-1.1-7-5.1-10.4-5.9-11l0,0c0,0,0,0-0.1-0.1l-0.1-0.1l0,0l-0.1-0.1c0-0.4,0.1-0.8,0.1-1.3\n        c0.1-0.8,0.1-1.5,0.1-2.3v-0.6v-0.3v-0.1c0-0.2,0-0.1,0-0.2v-0.5v-0.6c0-0.2,0-0.4,0-0.6s0-0.4-0.1-0.6l-0.1-0.6l-0.1-0.6\n        c-0.1-0.8-0.3-1.5-0.4-2.3c-0.7-3-1.9-5.9-3.4-8.4c-1.6-2.6-3.5-4.8-5.7-6.8c-2.2-1.9-4.6-3.5-7.2-4.6c-2.6-1.2-5.2-1.9-7.9-2.2\n        c-1.3-0.2-2.7-0.2-4-0.2h-0.5h-0.1H67h-0.2h-0.5c-0.2,0-0.4,0-0.5,0c-0.7,0.1-1.4,0.2-2,0.3c-2.7,0.5-5.2,1.5-7.4,2.8\n        c-2.2,1.3-4.1,3-5.7,4.9s-2.8,3.9-3.6,6.1c-0.8,2.1-1.3,4.4-1.4,6.5c0,0.5,0,1.1,0,1.6c0,0.1,0,0.3,0,0.4v0.4c0,0.3,0,0.5,0.1,0.8\n        c0.1,1.1,0.3,2.1,0.6,3.1c0.6,2,1.5,3.8,2.7,5.4s2.5,2.8,4,3.8s3,1.7,4.6,2.2s3.1,0.7,4.5,0.6c0.2,0,0.4,0,0.5,0s0.2,0,0.3,0\n        s0.2,0,0.3,0c0.2,0,0.3,0,0.5,0h0.1H64c0.1,0,0.2,0,0.3,0c0.2,0,0.4-0.1,0.5-0.1c0.2,0,0.3-0.1,0.5-0.1c0.3-0.1,0.7-0.2,1-0.3\n        c0.6-0.2,1.2-0.5,1.8-0.7c0.6-0.3,1.1-0.6,1.5-0.9c0.1-0.1,0.3-0.2,0.4-0.3c0.5-0.4,0.6-1.1,0.2-1.6c-0.4-0.4-1-0.5-1.5-0.3\n        c-0.1,0.1-0.2,0.1-0.4,0.2c-0.4,0.2-0.9,0.4-1.3,0.5c-0.5,0.1-1,0.3-1.5,0.4c-0.3,0-0.5,0.1-0.8,0.1c-0.1,0-0.3,0-0.4,0\n        c-0.1,0-0.3,0-0.4,0s-0.3,0-0.4,0c-0.2,0-0.3,0-0.5,0c0,0-0.1,0,0,0h-0.1h-0.1c-0.1,0-0.1,0-0.2,0s-0.3,0-0.4-0.1\n        c-1.1-0.2-2.3-0.5-3.4-1s-2.2-1.2-3.1-2.1c-1-0.9-1.8-1.9-2.5-3.1s-1.1-2.5-1.3-3.8c-0.1-0.7-0.2-1.4-0.1-2.1c0-0.2,0-0.4,0-0.6\n        c0,0.1,0,0,0,0v-0.1v-0.1c0-0.1,0-0.2,0-0.3c0-0.4,0.1-0.7,0.2-1.1c0.5-3,2-5.9,4.3-8.1c0.6-0.6,1.2-1.1,1.9-1.5\n        c0.7-0.5,1.4-0.9,2.1-1.2s1.5-0.6,2.3-0.8s1.6-0.4,2.4-0.4c0.4,0,0.8-0.1,1.2-0.1c0.1,0,0.2,0,0.3,0h0.3H67c0.1,0,0,0,0,0h0.1h0.3\n        c0.9,0.1,1.8,0.2,2.6,0.4c1.7,0.4,3.4,1,5,1.9c3.2,1.8,5.9,4.5,7.5,7.8c0.8,1.6,1.4,3.4,1.7,5.3c0.1,0.5,0.1,0.9,0.2,1.4v0.3V66\n        c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3v0.3v0.3c0,0.2,0,0.6,0,0.8c0,0.5-0.1,1-0.1,1.5c-0.1,0.5-0.1,1-0.2,1.5\n        c-0.1,0.5-0.2,1-0.3,1.5c-0.2,1-0.6,1.9-0.9,2.9c-0.7,1.9-1.7,3.7-2.9,5.3c-2.4,3.3-5.7,6-9.4,7.7c-1.9,0.8-3.8,1.5-5.8,1.8\n        c-1,0.2-2,0.3-3,0.3h-0.2h-0.2h-0.3h-0.5h-0.3c0.1,0,0,0,0,0h-0.1c-0.5,0-1.1,0-1.6-0.1c-2.2-0.2-4.3-0.6-6.4-1.2s-4.1-1.4-6-2.4\n        c-3.8-2-7.2-4.9-9.9-8.2c-1.3-1.7-2.5-3.5-3.5-5.4s-1.7-3.9-2.3-5.9s-0.9-4.1-1-6.2v-0.4v-0.1v-0.1v-0.2V60v-0.1v-0.1v-0.2v-0.5V59\n        l0,0v-0.2c0-0.3,0-0.5,0-0.8c0-1,0.1-2.1,0.3-3.2c0.1-1.1,0.3-2.1,0.5-3.2c0.2-1.1,0.5-2.1,0.8-3.2c0.6-2.1,1.3-4.1,2.2-6\n        c1.8-3.8,4.1-7.2,6.8-9.9c0.7-0.7,1.4-1.3,2.2-1.9c0.3-0.3,1-0.9,1.8-1.4s1.6-1,2.5-1.4c0.4-0.2,0.8-0.4,1.3-0.6\n        c0.2-0.1,0.4-0.2,0.7-0.3c0.2-0.1,0.4-0.2,0.7-0.3c0.9-0.4,1.8-0.7,2.7-1c0.2-0.1,0.5-0.1,0.7-0.2s0.5-0.1,0.7-0.2\n        c0.5-0.1,0.9-0.2,1.4-0.4c0.2-0.1,0.5-0.1,0.7-0.2c0.2,0,0.5-0.1,0.7-0.1s0.5-0.1,0.7-0.1l0.4-0.1l0.4-0.1c0.2,0,0.5-0.1,0.7-0.1\n        c0.3,0,0.5-0.1,0.8-0.1c0.2,0,0.6-0.1,0.8-0.1c0.2,0,0.3,0,0.5-0.1h0.3H61h0.2c0.3,0,0.5,0,0.8-0.1h0.4c0,0,0.1,0,0,0h0.1h0.2\n        c0.2,0,0.5,0,0.7,0c0.9,0,1.8,0,2.7,0c1.8,0.1,3.6,0.3,5.3,0.6c3.4,0.6,6.7,1.7,9.6,3.2c2.9,1.4,5.6,3.2,7.8,5.1\n        c0.1,0.1,0.3,0.2,0.4,0.4c0.1,0.1,0.3,0.2,0.4,0.4c0.3,0.2,0.5,0.5,0.8,0.7s0.5,0.5,0.8,0.7c0.2,0.3,0.5,0.5,0.7,0.8\n        c1,1,1.9,2.1,2.7,3.1c1.6,2.1,2.9,4.2,3.9,6.2c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.2,0.2,0.5,0.4,0.7\n        c0.1,0.2,0.2,0.5,0.3,0.7c0.1,0.2,0.2,0.5,0.3,0.7c0.4,0.9,0.7,1.8,1,2.7c0.5,1.4,0.8,2.6,1.1,3.6c0.1,0.4,0.5,0.7,0.9,0.7\n        c0.5,0,0.8-0.4,0.8-0.9C103.6,52.7,103.6,51.4,103.5,49.9z\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\"\n         viewBox=\"0 0 103.7 112.7\" style=\"enable-background:new 0 0 103.7 112.7;\" xml:space=\"preserve\" width='103.7' height='112.7'>\n<style type=\"text/css\">\n        .st0{fill:#FFFFFF;}\n</style>\n<path class=\"st0\" d=\"M103.5,49.9c-0.2-1.9-0.5-4.1-1.1-6.5c-0.6-2.4-1.6-5-2.9-7.8c-1.4-2.7-3.1-5.6-5.4-8.3\n        c-0.9-1.1-1.9-2.1-2.9-3.2c1.6-6.3-1.9-11.8-1.9-11.8c-6.1-0.4-9.9,1.9-11.3,2.9c-0.2-0.1-0.5-0.2-0.7-0.3c-1-0.4-2.1-0.8-3.2-1.2\n        c-1.1-0.3-2.2-0.7-3.3-0.9c-1.1-0.3-2.3-0.5-3.5-0.7c-0.2,0-0.4-0.1-0.6-0.1C64.1,3.6,56.5,0,56.5,0c-8.7,5.6-10.4,13.1-10.4,13.1\n        s0,0.2-0.1,0.4c-0.5,0.1-0.9,0.3-1.4,0.4c-0.6,0.2-1.3,0.4-1.9,0.7c-0.6,0.3-1.3,0.5-1.9,0.8c-1.3,0.6-2.5,1.2-3.8,1.9\n        c-1.2,0.7-2.4,1.4-3.5,2.2c-0.2-0.1-0.3-0.2-0.3-0.2c-11.7-4.5-22.1,0.9-22.1,0.9c-0.9,12.5,4.7,20.3,5.8,21.7\n        c-0.3,0.8-0.5,1.5-0.8,2.3c-0.9,2.8-1.5,5.7-1.9,8.7c-0.1,0.4-0.1,0.9-0.2,1.3C3.2,59.5,0,70.5,0,70.5c9,10.4,19.6,11,19.6,11l0,0\n        c1.3,2.4,2.9,4.7,4.6,6.8c0.7,0.9,1.5,1.7,2.3,2.6c-3.3,9.4,0.5,17.3,0.5,17.3c10.1,0.4,16.7-4.4,18.1-5.5c1,0.3,2,0.6,3,0.9\n        c3.1,0.8,6.3,1.3,9.4,1.4c0.8,0,1.6,0,2.4,0h0.4h0.3h0.5h0.5l0,0c4.7,6.8,13.1,7.7,13.1,7.7c5.9-6.3,6.3-12.4,6.3-13.8l0,0\n        c0,0,0,0,0-0.1s0-0.2,0-0.2l0,0c0-0.1,0-0.2,0-0.3c1.2-0.9,2.4-1.8,3.6-2.8c2.4-2.1,4.4-4.6,6.2-7.2c0.2-0.2,0.3-0.5,0.5-0.7\n        c6.7,0.4,11.4-4.2,11.4-4.2c-1.1-7-5.1-10.4-5.9-11l0,0c0,0,0,0-0.1-0.1l-0.1-0.1l0,0l-0.1-0.1c0-0.4,0.1-0.8,0.1-1.3\n        c0.1-0.8,0.1-1.5,0.1-2.3v-0.6v-0.3v-0.1c0-0.2,0-0.1,0-0.2v-0.5v-0.6c0-0.2,0-0.4,0-0.6s0-0.4-0.1-0.6l-0.1-0.6l-0.1-0.6\n        c-0.1-0.8-0.3-1.5-0.4-2.3c-0.7-3-1.9-5.9-3.4-8.4c-1.6-2.6-3.5-4.8-5.7-6.8c-2.2-1.9-4.6-3.5-7.2-4.6c-2.6-1.2-5.2-1.9-7.9-2.2\n        c-1.3-0.2-2.7-0.2-4-0.2h-0.5h-0.1H67h-0.2h-0.5c-0.2,0-0.4,0-0.5,0c-0.7,0.1-1.4,0.2-2,0.3c-2.7,0.5-5.2,1.5-7.4,2.8\n        c-2.2,1.3-4.1,3-5.7,4.9s-2.8,3.9-3.6,6.1c-0.8,2.1-1.3,4.4-1.4,6.5c0,0.5,0,1.1,0,1.6c0,0.1,0,0.3,0,0.4v0.4c0,0.3,0,0.5,0.1,0.8\n        c0.1,1.1,0.3,2.1,0.6,3.1c0.6,2,1.5,3.8,2.7,5.4s2.5,2.8,4,3.8s3,1.7,4.6,2.2s3.1,0.7,4.5,0.6c0.2,0,0.4,0,0.5,0s0.2,0,0.3,0\n        s0.2,0,0.3,0c0.2,0,0.3,0,0.5,0h0.1H64c0.1,0,0.2,0,0.3,0c0.2,0,0.4-0.1,0.5-0.1c0.2,0,0.3-0.1,0.5-0.1c0.3-0.1,0.7-0.2,1-0.3\n        c0.6-0.2,1.2-0.5,1.8-0.7c0.6-0.3,1.1-0.6,1.5-0.9c0.1-0.1,0.3-0.2,0.4-0.3c0.5-0.4,0.6-1.1,0.2-1.6c-0.4-0.4-1-0.5-1.5-0.3\n        c-0.1,0.1-0.2,0.1-0.4,0.2c-0.4,0.2-0.9,0.4-1.3,0.5c-0.5,0.1-1,0.3-1.5,0.4c-0.3,0-0.5,0.1-0.8,0.1c-0.1,0-0.3,0-0.4,0\n        c-0.1,0-0.3,0-0.4,0s-0.3,0-0.4,0c-0.2,0-0.3,0-0.5,0c0,0-0.1,0,0,0h-0.1h-0.1c-0.1,0-0.1,0-0.2,0s-0.3,0-0.4-0.1\n        c-1.1-0.2-2.3-0.5-3.4-1s-2.2-1.2-3.1-2.1c-1-0.9-1.8-1.9-2.5-3.1s-1.1-2.5-1.3-3.8c-0.1-0.7-0.2-1.4-0.1-2.1c0-0.2,0-0.4,0-0.6\n        c0,0.1,0,0,0,0v-0.1v-0.1c0-0.1,0-0.2,0-0.3c0-0.4,0.1-0.7,0.2-1.1c0.5-3,2-5.9,4.3-8.1c0.6-0.6,1.2-1.1,1.9-1.5\n        c0.7-0.5,1.4-0.9,2.1-1.2s1.5-0.6,2.3-0.8s1.6-0.4,2.4-0.4c0.4,0,0.8-0.1,1.2-0.1c0.1,0,0.2,0,0.3,0h0.3H67c0.1,0,0,0,0,0h0.1h0.3\n        c0.9,0.1,1.8,0.2,2.6,0.4c1.7,0.4,3.4,1,5,1.9c3.2,1.8,5.9,4.5,7.5,7.8c0.8,1.6,1.4,3.4,1.7,5.3c0.1,0.5,0.1,0.9,0.2,1.4v0.3V66\n        c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3v0.3v0.3c0,0.2,0,0.6,0,0.8c0,0.5-0.1,1-0.1,1.5c-0.1,0.5-0.1,1-0.2,1.5\n        c-0.1,0.5-0.2,1-0.3,1.5c-0.2,1-0.6,1.9-0.9,2.9c-0.7,1.9-1.7,3.7-2.9,5.3c-2.4,3.3-5.7,6-9.4,7.7c-1.9,0.8-3.8,1.5-5.8,1.8\n        c-1,0.2-2,0.3-3,0.3h-0.2h-0.2h-0.3h-0.5h-0.3c0.1,0,0,0,0,0h-0.1c-0.5,0-1.1,0-1.6-0.1c-2.2-0.2-4.3-0.6-6.4-1.2s-4.1-1.4-6-2.4\n        c-3.8-2-7.2-4.9-9.9-8.2c-1.3-1.7-2.5-3.5-3.5-5.4s-1.7-3.9-2.3-5.9s-0.9-4.1-1-6.2v-0.4v-0.1v-0.1v-0.2V60v-0.1v-0.1v-0.2v-0.5V59\n        l0,0v-0.2c0-0.3,0-0.5,0-0.8c0-1,0.1-2.1,0.3-3.2c0.1-1.1,0.3-2.1,0.5-3.2c0.2-1.1,0.5-2.1,0.8-3.2c0.6-2.1,1.3-4.1,2.2-6\n        c1.8-3.8,4.1-7.2,6.8-9.9c0.7-0.7,1.4-1.3,2.2-1.9c0.3-0.3,1-0.9,1.8-1.4s1.6-1,2.5-1.4c0.4-0.2,0.8-0.4,1.3-0.6\n        c0.2-0.1,0.4-0.2,0.7-0.3c0.2-0.1,0.4-0.2,0.7-0.3c0.9-0.4,1.8-0.7,2.7-1c0.2-0.1,0.5-0.1,0.7-0.2s0.5-0.1,0.7-0.2\n        c0.5-0.1,0.9-0.2,1.4-0.4c0.2-0.1,0.5-0.1,0.7-0.2c0.2,0,0.5-0.1,0.7-0.1s0.5-0.1,0.7-0.1l0.4-0.1l0.4-0.1c0.2,0,0.5-0.1,0.7-0.1\n        c0.3,0,0.5-0.1,0.8-0.1c0.2,0,0.6-0.1,0.8-0.1c0.2,0,0.3,0,0.5-0.1h0.3H61h0.2c0.3,0,0.5,0,0.8-0.1h0.4c0,0,0.1,0,0,0h0.1h0.2\n        c0.2,0,0.5,0,0.7,0c0.9,0,1.8,0,2.7,0c1.8,0.1,3.6,0.3,5.3,0.6c3.4,0.6,6.7,1.7,9.6,3.2c2.9,1.4,5.6,3.2,7.8,5.1\n        c0.1,0.1,0.3,0.2,0.4,0.4c0.1,0.1,0.3,0.2,0.4,0.4c0.3,0.2,0.5,0.5,0.8,0.7s0.5,0.5,0.8,0.7c0.2,0.3,0.5,0.5,0.7,0.8\n        c1,1,1.9,2.1,2.7,3.1c1.6,2.1,2.9,4.2,3.9,6.2c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.2,0.2,0.5,0.4,0.7\n        c0.1,0.2,0.2,0.5,0.3,0.7c0.1,0.2,0.2,0.5,0.3,0.7c0.4,0.9,0.7,1.8,1,2.7c0.5,1.4,0.8,2.6,1.1,3.6c0.1,0.4,0.5,0.7,0.9,0.7\n        c0.5,0,0.8-0.4,0.8-0.9C103.6,52.7,103.6,51.4,103.5,49.9z\"/>\n</svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/grafana/helm-charts/releases/download/loki-simple-scalable-1.8.11/loki-simple-scalable-1.8.11.tgz"}, "configuration": null, "component": {"version": "monitoring.grafana.com/v1alpha1", "kind": "GrafanaAgent", "schema": "{\n \"description\": \"GrafanaAgent defines a Grafana Agent deployment.\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Spec holds the specification of the desired behavior for the Grafana Agent cluster.\",\n   \"properties\": {\n    \"affinity\": {\n     \"description\": \"Affinity, if specified, controls pod scheduling constraints.\",\n     \"properties\": {\n      \"nodeAffinity\": {\n       \"description\": \"Describes node affinity scheduling rules for the pod.\",\n       \"properties\": {\n        \"preferredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \\\"weight\\\" to the sum if the node matches the corresponding matchExpressions; the node(s) with the highest sum are the most preferred.\",\n         \"items\": {\n          \"description\": \"An empty preferred scheduling term matches all objects with implicit weight 0 (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).\",\n          \"properties\": {\n           \"preference\": {\n            \"description\": \"A node selector term, associated with the corresponding weight.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"A list of node selector requirements by node's labels.\",\n              \"items\": {\n               \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"Represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"matchFields\": {\n              \"description\": \"A list of node selector requirements by node's fields.\",\n              \"items\": {\n               \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"Represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"weight\": {\n            \"description\": \"Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"required\": [\n           \"preference\",\n           \"weight\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"requiredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to an update), the system may or may not try to eventually evict the pod from its node.\",\n         \"properties\": {\n          \"nodeSelectorTerms\": {\n           \"description\": \"Required. A list of node selector terms. The terms are ORed.\",\n           \"items\": {\n            \"description\": \"A null or empty node selector term matches no objects. The requirements of them are ANDed. The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"A list of node selector requirements by node's labels.\",\n              \"items\": {\n               \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"Represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"matchFields\": {\n              \"description\": \"A list of node selector requirements by node's fields.\",\n              \"items\": {\n               \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"Represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"type\": \"array\"\n          }\n         },\n         \"required\": [\n          \"nodeSelectorTerms\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"podAffinity\": {\n       \"description\": \"Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).\",\n       \"properties\": {\n        \"preferredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \\\"weight\\\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.\",\n         \"items\": {\n          \"description\": \"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\n          \"properties\": {\n           \"podAffinityTerm\": {\n            \"description\": \"Required. A pod affinity term, associated with the corresponding weight.\",\n            \"properties\": {\n             \"labelSelector\": {\n              \"description\": \"A label query over a set of resources, in this case pods.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"namespaceSelector\": {\n              \"description\": \"A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \\\"this pod's namespace\\\". An empty selector ({}) matches all namespaces. This field is beta-level and is only honored when PodAffinityNamespaceSelector feature is enabled.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"namespaces\": {\n              \"description\": \"namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\"\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"topologyKey\": {\n              \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"topologyKey\"\n            ],\n            \"type\": \"object\"\n           },\n           \"weight\": {\n            \"description\": \"weight associated with matching the corresponding podAffinityTerm, in the range 1-100.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"required\": [\n           \"podAffinityTerm\",\n           \"weight\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"requiredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.\",\n         \"items\": {\n          \"description\": \"Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key \\u003ctopologyKey\\u003e matches that of any node on which a pod of the set of pods is running\",\n          \"properties\": {\n           \"labelSelector\": {\n            \"description\": \"A label query over a set of resources, in this case pods.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"namespaceSelector\": {\n            \"description\": \"A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \\\"this pod's namespace\\\". An empty selector ({}) matches all namespaces. This field is beta-level and is only honored when PodAffinityNamespaceSelector feature is enabled.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"namespaces\": {\n            \"description\": \"namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\"\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"topologyKey\": {\n            \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"topologyKey\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"podAntiAffinity\": {\n       \"description\": \"Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).\",\n       \"properties\": {\n        \"preferredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy the anti-affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling anti-affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \\\"weight\\\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.\",\n         \"items\": {\n          \"description\": \"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\n          \"properties\": {\n           \"podAffinityTerm\": {\n            \"description\": \"Required. A pod affinity term, associated with the corresponding weight.\",\n            \"properties\": {\n             \"labelSelector\": {\n              \"description\": \"A label query over a set of resources, in this case pods.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"namespaceSelector\": {\n              \"description\": \"A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \\\"this pod's namespace\\\". An empty selector ({}) matches all namespaces. This field is beta-level and is only honored when PodAffinityNamespaceSelector feature is enabled.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"namespaces\": {\n              \"description\": \"namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\"\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"topologyKey\": {\n              \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"topologyKey\"\n            ],\n            \"type\": \"object\"\n           },\n           \"weight\": {\n            \"description\": \"weight associated with matching the corresponding podAffinityTerm, in the range 1-100.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"required\": [\n           \"podAffinityTerm\",\n           \"weight\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"requiredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"If the anti-affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the anti-affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.\",\n         \"items\": {\n          \"description\": \"Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key \\u003ctopologyKey\\u003e matches that of any node on which a pod of the set of pods is running\",\n          \"properties\": {\n           \"labelSelector\": {\n            \"description\": \"A label query over a set of resources, in this case pods.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"namespaceSelector\": {\n            \"description\": \"A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \\\"this pod's namespace\\\". An empty selector ({}) matches all namespaces. This field is beta-level and is only honored when PodAffinityNamespaceSelector feature is enabled.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"namespaces\": {\n            \"description\": \"namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\"\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"topologyKey\": {\n            \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"topologyKey\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"apiServer\": {\n     \"description\": \"APIServerConfig allows specifying a host and auth methods to access the Kubernetes API server. If left empty, the Agent will assume that it is running inside of the cluster and will discover API servers automatically and use the pod's CA certificate and bearer token file at /var/run/secrets/kubernetes.io/serviceaccount.\",\n     \"properties\": {\n      \"authorization\": {\n       \"description\": \"Authorization section for accessing apiserver\",\n       \"properties\": {\n        \"credentials\": {\n         \"description\": \"The secret's key that contains the credentials of the request\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\"\n        },\n        \"credentialsFile\": {\n         \"description\": \"File to read a secret from, mutually exclusive with Credentials (from SafeAuthorization)\",\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"description\": \"Set the authentication type. Defaults to Bearer, Basic will cause an error\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"basicAuth\": {\n       \"description\": \"BasicAuth allow an endpoint to authenticate over basic authentication\",\n       \"properties\": {\n        \"password\": {\n         \"description\": \"The secret in the service monitor namespace that contains the password for authentication.\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\"\n        },\n        \"username\": {\n         \"description\": \"The secret in the service monitor namespace that contains the username for authentication.\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"bearerToken\": {\n       \"description\": \"Bearer token for accessing apiserver.\",\n       \"type\": \"string\"\n      },\n      \"bearerTokenFile\": {\n       \"description\": \"File to read bearer token for accessing apiserver.\",\n       \"type\": \"string\"\n      },\n      \"host\": {\n       \"description\": \"Host of apiserver. A valid string consisting of a hostname or IP followed by an optional port number\",\n       \"type\": \"string\"\n      },\n      \"tlsConfig\": {\n       \"description\": \"TLS Config to use for accessing apiserver.\",\n       \"properties\": {\n        \"ca\": {\n         \"description\": \"Struct containing the CA cert to use for the targets.\",\n         \"properties\": {\n          \"configMap\": {\n           \"description\": \"ConfigMap containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to select.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\"\n          },\n          \"secret\": {\n           \"description\": \"Secret containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"caFile\": {\n         \"description\": \"Path to the CA cert in the Prometheus container to use for the targets.\",\n         \"type\": \"string\"\n        },\n        \"cert\": {\n         \"description\": \"Struct containing the client cert file for the targets.\",\n         \"properties\": {\n          \"configMap\": {\n           \"description\": \"ConfigMap containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to select.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\"\n          },\n          \"secret\": {\n           \"description\": \"Secret containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"certFile\": {\n         \"description\": \"Path to the client cert file in the Prometheus container for the targets.\",\n         \"type\": \"string\"\n        },\n        \"insecureSkipVerify\": {\n         \"description\": \"Disable target certificate validation.\",\n         \"type\": \"boolean\"\n        },\n        \"keyFile\": {\n         \"description\": \"Path to the client key file in the Prometheus container for the targets.\",\n         \"type\": \"string\"\n        },\n        \"keySecret\": {\n         \"description\": \"Secret containing the client key file for the targets.\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\"\n        },\n        \"serverName\": {\n         \"description\": \"Used to verify the hostname for the targets.\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"required\": [\n      \"host\"\n     ],\n     \"type\": \"object\"\n    },\n    \"configMaps\": {\n     \"description\": \"ConfigMaps is a liset of config maps in the same namespace as the GrafanaAgent object which will be mounted into each running Grafana Agent pod. The ConfigMaps are mounted into /etc/grafana-agent/extra-configmaps/\\u003cconfigmap-name\\u003e.\",\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"containers\": {\n     \"description\": \"Containers allows injecting additional containers or modifying operator generated containers. This can be used to allow adding an authentication proxy to a Grafana Agent pod or to change the behavior of an operator-generated container. Containers described here modify an operator generated container if they share the same name and modifications are done via a strategic merge patch. The current container names are: `grafana-agent` and `config-reloader`. Overriding containers is entirely outside the scope of what the Grafana Agent team will support and by doing so, you accept that this behavior may break at any time without notice.\",\n     \"items\": {\n      \"description\": \"A single application container that you want to run within a pod.\",\n      \"properties\": {\n       \"args\": {\n        \"description\": \"Arguments to the entrypoint. The docker image's CMD is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\"\n       },\n       \"command\": {\n        \"description\": \"Entrypoint array. Not executed within a shell. The docker image's ENTRYPOINT is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\"\n       },\n       \"env\": {\n        \"description\": \"List of environment variables to set in the container. Cannot be updated.\",\n        \"items\": {\n         \"description\": \"EnvVar represents an environment variable present in a Container.\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n           \"type\": \"string\"\n          },\n          \"value\": {\n           \"description\": \"Variable references $(VAR_NAME) are expanded using the previously defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to \\\"\\\".\",\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n           \"properties\": {\n            \"configMapKeyRef\": {\n             \"description\": \"Selects a key of a ConfigMap.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key to select.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\"\n            },\n            \"fieldRef\": {\n             \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\",\n             \"properties\": {\n              \"apiVersion\": {\n               \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n               \"type\": \"string\"\n              },\n              \"fieldPath\": {\n               \"description\": \"Path of the field to select in the specified API version.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"fieldPath\"\n             ],\n             \"type\": \"object\"\n            },\n            \"resourceFieldRef\": {\n             \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n             \"properties\": {\n              \"containerName\": {\n               \"description\": \"Container name: required for volumes, optional for env vars\",\n               \"type\": \"string\"\n              },\n              \"divisor\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"resource\": {\n               \"description\": \"Required: resource to select\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"resource\"\n             ],\n             \"type\": \"object\"\n            },\n            \"secretKeyRef\": {\n             \"description\": \"Selects a key of a secret in the pod's namespace\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"envFrom\": {\n        \"description\": \"List of sources to populate environment variables in the container. The keys defined within a source must be a C_IDENTIFIER. All invalid keys will be reported as an event when the container is starting. When a key exists in multiple sources, the value associated with the last source will take precedence. Values defined by an Env with a duplicate key will take precedence. Cannot be updated.\",\n        \"items\": {\n         \"description\": \"EnvFromSource represents the source of a set of ConfigMaps\",\n         \"properties\": {\n          \"configMapRef\": {\n           \"description\": \"The ConfigMap to select from\",\n           \"properties\": {\n            \"name\": {\n             \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"prefix\": {\n           \"description\": \"An optional identifier to prepend to each key in the ConfigMap. Must be a C_IDENTIFIER.\",\n           \"type\": \"string\"\n          },\n          \"secretRef\": {\n           \"description\": \"The Secret to select from\",\n           \"properties\": {\n            \"name\": {\n             \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"image\": {\n        \"description\": \"Docker image name. More info: https://kubernetes.io/docs/concepts/containers/images This field is optional to allow higher level config management to default or override container images in workload controllers like Deployments and StatefulSets.\",\n        \"type\": \"string\"\n       },\n       \"imagePullPolicy\": {\n        \"description\": \"Image pull policy. One of Always, Never, IfNotPresent. Defaults to Always if :latest tag is specified, or IfNotPresent otherwise. Cannot be updated. More info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\n        \"type\": \"string\"\n       },\n       \"lifecycle\": {\n        \"description\": \"Actions that the management system should take in response to container lifecycle events. Cannot be updated.\",\n        \"properties\": {\n         \"postStart\": {\n          \"description\": \"PostStart is called immediately after a container is created. If the handler fails, the container is terminated and restarted according to its restart policy. Other management of the container blocks until the hook completes. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n          \"properties\": {\n           \"exec\": {\n            \"description\": \"Exec specifies the action to take.\",\n            \"properties\": {\n             \"command\": {\n              \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"description\": \"HTTPGet specifies the http request to perform.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n              \"items\": {\n               \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"The header field name\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"description\": \"The header field value\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"path\": {\n              \"description\": \"Path to access on the HTTP server.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"tcpSocket\": {\n            \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for the backward compatibility. There are no validation of this field and lifecycle hooks will fail in runtime when tcp handler is specified.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"preStop\": {\n          \"description\": \"PreStop is called immediately before a container is terminated due to an API request or management event such as liveness/startup probe failure, preemption, resource contention, etc. The handler is not called if the container crashes or exits. The Pod's termination grace period countdown begins before the PreStop hook is executed. Regardless of the outcome of the handler, the container will eventually terminate within the Pod's termination grace period (unless delayed by finalizers). Other management of the container blocks until the hook completes or until the termination grace period is reached. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n          \"properties\": {\n           \"exec\": {\n            \"description\": \"Exec specifies the action to take.\",\n            \"properties\": {\n             \"command\": {\n              \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"description\": \"HTTPGet specifies the http request to perform.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n              \"items\": {\n               \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"The header field name\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"description\": \"The header field value\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"path\": {\n              \"description\": \"Path to access on the HTTP server.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"tcpSocket\": {\n            \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for the backward compatibility. There are no validation of this field and lifecycle hooks will fail in runtime when tcp handler is specified.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"livenessProbe\": {\n        \"description\": \"Periodic probe of container liveness. Container will be restarted if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies the action to take.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies an action involving a GRPC port. This is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md). \\n If this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies the http request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"name\": {\n        \"description\": \"Name of the container specified as a DNS_LABEL. Each container in a pod must have a unique name (DNS_LABEL). Cannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"ports\": {\n        \"description\": \"List of ports to expose from the container. Exposing a port here gives the system additional information about the network connections a container uses, but is primarily informational. Not specifying a port here DOES NOT prevent that port from being exposed. Any port which is listening on the default \\\"0.0.0.0\\\" address inside a container will be accessible from the network. Cannot be updated.\",\n        \"items\": {\n         \"description\": \"ContainerPort represents a network port in a single container.\",\n         \"properties\": {\n          \"containerPort\": {\n           \"description\": \"Number of port to expose on the pod's IP address. This must be a valid port number, 0 \\u003c x \\u003c 65536.\",\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"hostIP\": {\n           \"description\": \"What host IP to bind the external port to.\",\n           \"type\": \"string\"\n          },\n          \"hostPort\": {\n           \"description\": \"Number of port to expose on the host. If specified, this must be a valid port number, 0 \\u003c x \\u003c 65536. If HostNetwork is specified, this must match ContainerPort. Most containers do not need this.\",\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"name\": {\n           \"description\": \"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each named port in a pod must have a unique name. Name for the port that can be referred to by services.\",\n           \"type\": \"string\"\n          },\n          \"protocol\": {\n           \"default\": \"TCP\",\n           \"description\": \"Protocol for port. Must be UDP, TCP, or SCTP. Defaults to \\\"TCP\\\".\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"containerPort\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"containerPort\",\n         \"protocol\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"readinessProbe\": {\n        \"description\": \"Periodic probe of container service readiness. Container will be removed from service endpoints if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies the action to take.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies an action involving a GRPC port. This is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md). \\n If this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies the http request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"resources\": {\n        \"description\": \"Compute Resources required by this container. Cannot be updated. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n        \"properties\": {\n         \"limits\": {\n          \"additionalProperties\": {\n           \"anyOf\": [\n            {\n             \"type\": \"integer\"\n            },\n            {\n             \"type\": \"string\"\n            }\n           ],\n           \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n           \"x-kubernetes-int-or-string\": true\n          },\n          \"description\": \"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n          \"type\": \"object\"\n         },\n         \"requests\": {\n          \"additionalProperties\": {\n           \"anyOf\": [\n            {\n             \"type\": \"integer\"\n            },\n            {\n             \"type\": \"string\"\n            }\n           ],\n           \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n           \"x-kubernetes-int-or-string\": true\n          },\n          \"description\": \"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"securityContext\": {\n        \"description\": \"SecurityContext defines the security options the container should be run with. If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext. More info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/\",\n        \"properties\": {\n         \"allowPrivilegeEscalation\": {\n          \"description\": \"AllowPrivilegeEscalation controls whether a process can gain more privileges than its parent process. This bool directly controls if the no_new_privs flag will be set on the container process. AllowPrivilegeEscalation is true always when the container is: 1) run as Privileged 2) has CAP_SYS_ADMIN Note that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"capabilities\": {\n          \"description\": \"The capabilities to add/drop when running containers. Defaults to the default set of capabilities granted by the container runtime. Note that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"add\": {\n            \"description\": \"Added capabilities\",\n            \"items\": {\n             \"description\": \"Capability represent POSIX capabilities type\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"drop\": {\n            \"description\": \"Removed capabilities\",\n            \"items\": {\n             \"description\": \"Capability represent POSIX capabilities type\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"privileged\": {\n          \"description\": \"Run container in privileged mode. Processes in privileged containers are essentially equivalent to root on the host. Defaults to false. Note that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"procMount\": {\n          \"description\": \"procMount denotes the type of proc mount to use for the containers. The default is DefaultProcMount which uses the container runtime defaults for readonly paths and masked paths. This requires the ProcMountType feature flag to be enabled. Note that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"string\"\n         },\n         \"readOnlyRootFilesystem\": {\n          \"description\": \"Whether this container has a read-only root filesystem. Default is false. Note that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"runAsGroup\": {\n          \"description\": \"The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"runAsNonRoot\": {\n          \"description\": \"Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\n          \"type\": \"boolean\"\n         },\n         \"runAsUser\": {\n          \"description\": \"The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"seLinuxOptions\": {\n          \"description\": \"The SELinux context to be applied to the container. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"level\": {\n            \"description\": \"Level is SELinux level label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"role\": {\n            \"description\": \"Role is a SELinux role label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type is a SELinux type label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"user\": {\n            \"description\": \"User is a SELinux user label that applies to the container.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"seccompProfile\": {\n          \"description\": \"The seccomp options to use by this container. If seccomp options are provided at both the pod \\u0026 container level, the container options override the pod options. Note that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"localhostProfile\": {\n            \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used. The profile must be preconfigured on the node to work. Must be a descending path, relative to the kubelet's configured seccomp profile location. Must only be set if type is \\\"Localhost\\\".\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"type indicates which kind of seccomp profile will be applied. Valid options are: \\n Localhost - a profile defined in a file on the node should be used. RuntimeDefault - the container runtime default profile should be used. Unconfined - no profile should be applied.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"windowsOptions\": {\n          \"description\": \"The Windows specific settings applied to all containers. If unspecified, the options from the PodSecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.\",\n          \"properties\": {\n           \"gmsaCredentialSpec\": {\n            \"description\": \"GMSACredentialSpec is where the GMSA admission webhook (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the GMSA credential spec named by the GMSACredentialSpecName field.\",\n            \"type\": \"string\"\n           },\n           \"gmsaCredentialSpecName\": {\n            \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n            \"type\": \"string\"\n           },\n           \"hostProcess\": {\n            \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container. This field is alpha-level and will only be honored by components that enable the WindowsHostProcessContainers feature flag. Setting this field without the feature flag will result in errors when validating the Pod. All of a Pod's containers must have the same effective HostProcess value (it is not allowed to have a mix of HostProcess containers and non-HostProcess containers).  In addition, if HostProcess is true then HostNetwork must also be set to true.\",\n            \"type\": \"boolean\"\n           },\n           \"runAsUserName\": {\n            \"description\": \"The UserName in Windows to run the entrypoint of the container process. Defaults to the user specified in image metadata if unspecified. May also be set in PodSecurityContext. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"startupProbe\": {\n        \"description\": \"StartupProbe indicates that the Pod has successfully initialized. If specified, no other probes are executed until this completes successfully. If this probe fails, the Pod will be restarted, just as if the livenessProbe failed. This can be used to provide different probe parameters at the beginning of a Pod's lifecycle, when it might take a long time to load data or warm a cache, than during steady-state operation. This cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies the action to take.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies an action involving a GRPC port. This is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md). \\n If this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies the http request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"stdin\": {\n        \"description\": \"Whether this container should allocate a buffer for stdin in the container runtime. If this is not set, reads from stdin in the container will always result in EOF. Default is false.\",\n        \"type\": \"boolean\"\n       },\n       \"stdinOnce\": {\n        \"description\": \"Whether the container runtime should close the stdin channel after it has been opened by a single attach. When stdin is true the stdin stream will remain open across multiple attach sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the first client attaches to stdin, and then remains open and accepts data until the client disconnects, at which time stdin is closed and remains closed until the container is restarted. If this flag is false, a container processes that reads from stdin will never receive an EOF. Default is false\",\n        \"type\": \"boolean\"\n       },\n       \"terminationMessagePath\": {\n        \"description\": \"Optional: Path at which the file to which the container's termination message will be written is mounted into the container's filesystem. Message written is intended to be brief final status, such as an assertion failure message. Will be truncated by the node if greater than 4096 bytes. The total message length across all containers will be limited to 12kb. Defaults to /dev/termination-log. Cannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"terminationMessagePolicy\": {\n        \"description\": \"Indicate how the termination message should be populated. File will use the contents of terminationMessagePath to populate the container status message on both success and failure. FallbackToLogsOnError will use the last chunk of container log output if the termination message file is empty and the container exited with an error. The log output is limited to 2048 bytes or 80 lines, whichever is smaller. Defaults to File. Cannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"tty\": {\n        \"description\": \"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true. Default is false.\",\n        \"type\": \"boolean\"\n       },\n       \"volumeDevices\": {\n        \"description\": \"volumeDevices is the list of block devices to be used by the container.\",\n        \"items\": {\n         \"description\": \"volumeDevice describes a mapping of a raw block device within a container.\",\n         \"properties\": {\n          \"devicePath\": {\n           \"description\": \"devicePath is the path inside of the container that the device will be mapped to.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"name must match the name of a persistentVolumeClaim in the pod\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"devicePath\",\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"volumeMounts\": {\n        \"description\": \"Pod volumes to mount into the container's filesystem. Cannot be updated.\",\n        \"items\": {\n         \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n         \"properties\": {\n          \"mountPath\": {\n           \"description\": \"Path within the container at which the volume should be mounted.  Must not contain ':'.\",\n           \"type\": \"string\"\n          },\n          \"mountPropagation\": {\n           \"description\": \"mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"This must match the Name of a Volume.\",\n           \"type\": \"string\"\n          },\n          \"readOnly\": {\n           \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.\",\n           \"type\": \"boolean\"\n          },\n          \"subPath\": {\n           \"description\": \"Path within the volume from which the container's volume should be mounted. Defaults to \\\"\\\" (volume's root).\",\n           \"type\": \"string\"\n          },\n          \"subPathExpr\": {\n           \"description\": \"Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \\\"\\\" (volume's root). SubPathExpr and SubPath are mutually exclusive.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"mountPath\",\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"workingDir\": {\n        \"description\": \"Container's working directory. If not specified, the container runtime's default will be used, which might be configured in the container image. Cannot be updated.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"enableConfigReadAPI\": {\n     \"default\": false,\n     \"description\": \"enableConfigReadAPI enables the read API for viewing currently running config port 8080 on the agent.\",\n     \"type\": \"boolean\"\n    },\n    \"image\": {\n     \"description\": \"Image, when specified, overrides the image used to run the Agent. It should be specified along with a tag. Version must still be set to ensure the Grafana Agent Operator knows which version of Grafana Agent is being configured.\",\n     \"type\": \"string\"\n    },\n    \"imagePullSecrets\": {\n     \"description\": \"ImagePullSecrets holds an optional list of references to secrets within the same namespace to use for pulling the Grafana Agent image from registries. More info: https://kubernetes.io/docs/user-guide/images#specifying-imagepullsecrets-on-a-pod\",\n     \"items\": {\n      \"description\": \"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\n      \"properties\": {\n       \"name\": {\n        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n        \"type\": \"string\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"initContainers\": {\n     \"description\": \"InitContainers allows adding initContainers to the pod definition. These can be used to, for example, fetch secrets for injection into the Grafana Agent configuration from external sources. Any errors during the execution of an initContainer will lead to a restart of the pod. More info: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/ Using initContainers for any use case other than secret fetching is entirely outside the scope of what the Grafana Agent maintainers will support and by doing so, you accept that this behavior may break at any time without notice.\",\n     \"items\": {\n      \"description\": \"A single application container that you want to run within a pod.\",\n      \"properties\": {\n       \"args\": {\n        \"description\": \"Arguments to the entrypoint. The docker image's CMD is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\"\n       },\n       \"command\": {\n        \"description\": \"Entrypoint array. Not executed within a shell. The docker image's ENTRYPOINT is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\"\n       },\n       \"env\": {\n        \"description\": \"List of environment variables to set in the container. Cannot be updated.\",\n        \"items\": {\n         \"description\": \"EnvVar represents an environment variable present in a Container.\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n           \"type\": \"string\"\n          },\n          \"value\": {\n           \"description\": \"Variable references $(VAR_NAME) are expanded using the previously defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to \\\"\\\".\",\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n           \"properties\": {\n            \"configMapKeyRef\": {\n             \"description\": \"Selects a key of a ConfigMap.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key to select.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\"\n            },\n            \"fieldRef\": {\n             \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\",\n             \"properties\": {\n              \"apiVersion\": {\n               \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n               \"type\": \"string\"\n              },\n              \"fieldPath\": {\n               \"description\": \"Path of the field to select in the specified API version.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"fieldPath\"\n             ],\n             \"type\": \"object\"\n            },\n            \"resourceFieldRef\": {\n             \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n             \"properties\": {\n              \"containerName\": {\n               \"description\": \"Container name: required for volumes, optional for env vars\",\n               \"type\": \"string\"\n              },\n              \"divisor\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"resource\": {\n               \"description\": \"Required: resource to select\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"resource\"\n             ],\n             \"type\": \"object\"\n            },\n            \"secretKeyRef\": {\n             \"description\": \"Selects a key of a secret in the pod's namespace\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"envFrom\": {\n        \"description\": \"List of sources to populate environment variables in the container. The keys defined within a source must be a C_IDENTIFIER. All invalid keys will be reported as an event when the container is starting. When a key exists in multiple sources, the value associated with the last source will take precedence. Values defined by an Env with a duplicate key will take precedence. Cannot be updated.\",\n        \"items\": {\n         \"description\": \"EnvFromSource represents the source of a set of ConfigMaps\",\n         \"properties\": {\n          \"configMapRef\": {\n           \"description\": \"The ConfigMap to select from\",\n           \"properties\": {\n            \"name\": {\n             \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"prefix\": {\n           \"description\": \"An optional identifier to prepend to each key in the ConfigMap. Must be a C_IDENTIFIER.\",\n           \"type\": \"string\"\n          },\n          \"secretRef\": {\n           \"description\": \"The Secret to select from\",\n           \"properties\": {\n            \"name\": {\n             \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"image\": {\n        \"description\": \"Docker image name. More info: https://kubernetes.io/docs/concepts/containers/images This field is optional to allow higher level config management to default or override container images in workload controllers like Deployments and StatefulSets.\",\n        \"type\": \"string\"\n       },\n       \"imagePullPolicy\": {\n        \"description\": \"Image pull policy. One of Always, Never, IfNotPresent. Defaults to Always if :latest tag is specified, or IfNotPresent otherwise. Cannot be updated. More info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\n        \"type\": \"string\"\n       },\n       \"lifecycle\": {\n        \"description\": \"Actions that the management system should take in response to container lifecycle events. Cannot be updated.\",\n        \"properties\": {\n         \"postStart\": {\n          \"description\": \"PostStart is called immediately after a container is created. If the handler fails, the container is terminated and restarted according to its restart policy. Other management of the container blocks until the hook completes. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n          \"properties\": {\n           \"exec\": {\n            \"description\": \"Exec specifies the action to take.\",\n            \"properties\": {\n             \"command\": {\n              \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"description\": \"HTTPGet specifies the http request to perform.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n              \"items\": {\n               \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"The header field name\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"description\": \"The header field value\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"path\": {\n              \"description\": \"Path to access on the HTTP server.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"tcpSocket\": {\n            \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for the backward compatibility. There are no validation of this field and lifecycle hooks will fail in runtime when tcp handler is specified.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"preStop\": {\n          \"description\": \"PreStop is called immediately before a container is terminated due to an API request or management event such as liveness/startup probe failure, preemption, resource contention, etc. The handler is not called if the container crashes or exits. The Pod's termination grace period countdown begins before the PreStop hook is executed. Regardless of the outcome of the handler, the container will eventually terminate within the Pod's termination grace period (unless delayed by finalizers). Other management of the container blocks until the hook completes or until the termination grace period is reached. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n          \"properties\": {\n           \"exec\": {\n            \"description\": \"Exec specifies the action to take.\",\n            \"properties\": {\n             \"command\": {\n              \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"description\": \"HTTPGet specifies the http request to perform.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n              \"items\": {\n               \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"The header field name\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"description\": \"The header field value\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"path\": {\n              \"description\": \"Path to access on the HTTP server.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"tcpSocket\": {\n            \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for the backward compatibility. There are no validation of this field and lifecycle hooks will fail in runtime when tcp handler is specified.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"livenessProbe\": {\n        \"description\": \"Periodic probe of container liveness. Container will be restarted if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies the action to take.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies an action involving a GRPC port. This is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md). \\n If this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies the http request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"name\": {\n        \"description\": \"Name of the container specified as a DNS_LABEL. Each container in a pod must have a unique name (DNS_LABEL). Cannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"ports\": {\n        \"description\": \"List of ports to expose from the container. Exposing a port here gives the system additional information about the network connections a container uses, but is primarily informational. Not specifying a port here DOES NOT prevent that port from being exposed. Any port which is listening on the default \\\"0.0.0.0\\\" address inside a container will be accessible from the network. Cannot be updated.\",\n        \"items\": {\n         \"description\": \"ContainerPort represents a network port in a single container.\",\n         \"properties\": {\n          \"containerPort\": {\n           \"description\": \"Number of port to expose on the pod's IP address. This must be a valid port number, 0 \\u003c x \\u003c 65536.\",\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"hostIP\": {\n           \"description\": \"What host IP to bind the external port to.\",\n           \"type\": \"string\"\n          },\n          \"hostPort\": {\n           \"description\": \"Number of port to expose on the host. If specified, this must be a valid port number, 0 \\u003c x \\u003c 65536. If HostNetwork is specified, this must match ContainerPort. Most containers do not need this.\",\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"name\": {\n           \"description\": \"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each named port in a pod must have a unique name. Name for the port that can be referred to by services.\",\n           \"type\": \"string\"\n          },\n          \"protocol\": {\n           \"default\": \"TCP\",\n           \"description\": \"Protocol for port. Must be UDP, TCP, or SCTP. Defaults to \\\"TCP\\\".\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"containerPort\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"containerPort\",\n         \"protocol\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"readinessProbe\": {\n        \"description\": \"Periodic probe of container service readiness. Container will be removed from service endpoints if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies the action to take.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies an action involving a GRPC port. This is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md). \\n If this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies the http request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"resources\": {\n        \"description\": \"Compute Resources required by this container. Cannot be updated. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n        \"properties\": {\n         \"limits\": {\n          \"additionalProperties\": {\n           \"anyOf\": [\n            {\n             \"type\": \"integer\"\n            },\n            {\n             \"type\": \"string\"\n            }\n           ],\n           \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n           \"x-kubernetes-int-or-string\": true\n          },\n          \"description\": \"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n          \"type\": \"object\"\n         },\n         \"requests\": {\n          \"additionalProperties\": {\n           \"anyOf\": [\n            {\n             \"type\": \"integer\"\n            },\n            {\n             \"type\": \"string\"\n            }\n           ],\n           \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n           \"x-kubernetes-int-or-string\": true\n          },\n          \"description\": \"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"securityContext\": {\n        \"description\": \"SecurityContext defines the security options the container should be run with. If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext. More info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/\",\n        \"properties\": {\n         \"allowPrivilegeEscalation\": {\n          \"description\": \"AllowPrivilegeEscalation controls whether a process can gain more privileges than its parent process. This bool directly controls if the no_new_privs flag will be set on the container process. AllowPrivilegeEscalation is true always when the container is: 1) run as Privileged 2) has CAP_SYS_ADMIN Note that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"capabilities\": {\n          \"description\": \"The capabilities to add/drop when running containers. Defaults to the default set of capabilities granted by the container runtime. Note that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"add\": {\n            \"description\": \"Added capabilities\",\n            \"items\": {\n             \"description\": \"Capability represent POSIX capabilities type\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"drop\": {\n            \"description\": \"Removed capabilities\",\n            \"items\": {\n             \"description\": \"Capability represent POSIX capabilities type\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"privileged\": {\n          \"description\": \"Run container in privileged mode. Processes in privileged containers are essentially equivalent to root on the host. Defaults to false. Note that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"procMount\": {\n          \"description\": \"procMount denotes the type of proc mount to use for the containers. The default is DefaultProcMount which uses the container runtime defaults for readonly paths and masked paths. This requires the ProcMountType feature flag to be enabled. Note that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"string\"\n         },\n         \"readOnlyRootFilesystem\": {\n          \"description\": \"Whether this container has a read-only root filesystem. Default is false. Note that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"runAsGroup\": {\n          \"description\": \"The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"runAsNonRoot\": {\n          \"description\": \"Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\n          \"type\": \"boolean\"\n         },\n         \"runAsUser\": {\n          \"description\": \"The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"seLinuxOptions\": {\n          \"description\": \"The SELinux context to be applied to the container. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"level\": {\n            \"description\": \"Level is SELinux level label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"role\": {\n            \"description\": \"Role is a SELinux role label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type is a SELinux type label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"user\": {\n            \"description\": \"User is a SELinux user label that applies to the container.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"seccompProfile\": {\n          \"description\": \"The seccomp options to use by this container. If seccomp options are provided at both the pod \\u0026 container level, the container options override the pod options. Note that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"localhostProfile\": {\n            \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used. The profile must be preconfigured on the node to work. Must be a descending path, relative to the kubelet's configured seccomp profile location. Must only be set if type is \\\"Localhost\\\".\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"type indicates which kind of seccomp profile will be applied. Valid options are: \\n Localhost - a profile defined in a file on the node should be used. RuntimeDefault - the container runtime default profile should be used. Unconfined - no profile should be applied.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"windowsOptions\": {\n          \"description\": \"The Windows specific settings applied to all containers. If unspecified, the options from the PodSecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.\",\n          \"properties\": {\n           \"gmsaCredentialSpec\": {\n            \"description\": \"GMSACredentialSpec is where the GMSA admission webhook (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the GMSA credential spec named by the GMSACredentialSpecName field.\",\n            \"type\": \"string\"\n           },\n           \"gmsaCredentialSpecName\": {\n            \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n            \"type\": \"string\"\n           },\n           \"hostProcess\": {\n            \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container. This field is alpha-level and will only be honored by components that enable the WindowsHostProcessContainers feature flag. Setting this field without the feature flag will result in errors when validating the Pod. All of a Pod's containers must have the same effective HostProcess value (it is not allowed to have a mix of HostProcess containers and non-HostProcess containers).  In addition, if HostProcess is true then HostNetwork must also be set to true.\",\n            \"type\": \"boolean\"\n           },\n           \"runAsUserName\": {\n            \"description\": \"The UserName in Windows to run the entrypoint of the container process. Defaults to the user specified in image metadata if unspecified. May also be set in PodSecurityContext. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"startupProbe\": {\n        \"description\": \"StartupProbe indicates that the Pod has successfully initialized. If specified, no other probes are executed until this completes successfully. If this probe fails, the Pod will be restarted, just as if the livenessProbe failed. This can be used to provide different probe parameters at the beginning of a Pod's lifecycle, when it might take a long time to load data or warm a cache, than during steady-state operation. This cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies the action to take.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies an action involving a GRPC port. This is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md). \\n If this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies the http request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host. Defaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"stdin\": {\n        \"description\": \"Whether this container should allocate a buffer for stdin in the container runtime. If this is not set, reads from stdin in the container will always result in EOF. Default is false.\",\n        \"type\": \"boolean\"\n       },\n       \"stdinOnce\": {\n        \"description\": \"Whether the container runtime should close the stdin channel after it has been opened by a single attach. When stdin is true the stdin stream will remain open across multiple attach sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the first client attaches to stdin, and then remains open and accepts data until the client disconnects, at which time stdin is closed and remains closed until the container is restarted. If this flag is false, a container processes that reads from stdin will never receive an EOF. Default is false\",\n        \"type\": \"boolean\"\n       },\n       \"terminationMessagePath\": {\n        \"description\": \"Optional: Path at which the file to which the container's termination message will be written is mounted into the container's filesystem. Message written is intended to be brief final status, such as an assertion failure message. Will be truncated by the node if greater than 4096 bytes. The total message length across all containers will be limited to 12kb. Defaults to /dev/termination-log. Cannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"terminationMessagePolicy\": {\n        \"description\": \"Indicate how the termination message should be populated. File will use the contents of terminationMessagePath to populate the container status message on both success and failure. FallbackToLogsOnError will use the last chunk of container log output if the termination message file is empty and the container exited with an error. The log output is limited to 2048 bytes or 80 lines, whichever is smaller. Defaults to File. Cannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"tty\": {\n        \"description\": \"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true. Default is false.\",\n        \"type\": \"boolean\"\n       },\n       \"volumeDevices\": {\n        \"description\": \"volumeDevices is the list of block devices to be used by the container.\",\n        \"items\": {\n         \"description\": \"volumeDevice describes a mapping of a raw block device within a container.\",\n         \"properties\": {\n          \"devicePath\": {\n           \"description\": \"devicePath is the path inside of the container that the device will be mapped to.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"name must match the name of a persistentVolumeClaim in the pod\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"devicePath\",\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"volumeMounts\": {\n        \"description\": \"Pod volumes to mount into the container's filesystem. Cannot be updated.\",\n        \"items\": {\n         \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n         \"properties\": {\n          \"mountPath\": {\n           \"description\": \"Path within the container at which the volume should be mounted.  Must not contain ':'.\",\n           \"type\": \"string\"\n          },\n          \"mountPropagation\": {\n           \"description\": \"mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"This must match the Name of a Volume.\",\n           \"type\": \"string\"\n          },\n          \"readOnly\": {\n           \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.\",\n           \"type\": \"boolean\"\n          },\n          \"subPath\": {\n           \"description\": \"Path within the volume from which the container's volume should be mounted. Defaults to \\\"\\\" (volume's root).\",\n           \"type\": \"string\"\n          },\n          \"subPathExpr\": {\n           \"description\": \"Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \\\"\\\" (volume's root). SubPathExpr and SubPath are mutually exclusive.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"mountPath\",\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"workingDir\": {\n        \"description\": \"Container's working directory. If not specified, the container runtime's default will be used, which might be configured in the container image. Cannot be updated.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"integrations\": {\n     \"description\": \"Integrations controls the integration subsystem of the Agent and settings unique to integration-specific pods that are deployed.\",\n     \"properties\": {\n      \"namespaceSelector\": {\n       \"description\": \"Label selector for namespaces to search when discovering integration resources. If nil, integration resources are only discovered in the namespace of the GrafanaAgent resource. \\n Set to `{}` to search all namespaces.\",\n       \"properties\": {\n        \"matchExpressions\": {\n         \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n         \"items\": {\n          \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"key is the label key that the selector applies to.\",\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n            \"type\": \"string\"\n           },\n           \"values\": {\n            \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"required\": [\n           \"key\",\n           \"operator\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"matchLabels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"selector\": {\n       \"description\": \"Label selector to find Integration resources to run. When nil, no integration resources will be defined.\",\n       \"properties\": {\n        \"matchExpressions\": {\n         \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n         \"items\": {\n          \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"key is the label key that the selector applies to.\",\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n            \"type\": \"string\"\n           },\n           \"values\": {\n            \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"required\": [\n           \"key\",\n           \"operator\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"matchLabels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"logFormat\": {\n     \"description\": \"LogFormat controls the logging format of the generated pods. Defaults to \\\"logfmt\\\" if not set.\",\n     \"type\": \"string\"\n    },\n    \"logLevel\": {\n     \"description\": \"LogLevel controls the log level of the generated pods. Defaults to \\\"info\\\" if not set.\",\n     \"type\": \"string\"\n    },\n    \"logs\": {\n     \"description\": \"Logs controls the logging subsystem of the Agent and settings unique to logging-specific pods that are deployed.\",\n     \"properties\": {\n      \"clients\": {\n       \"description\": \"Global set of clients to use when a discovered LogsInstance does not have any clients defined.\",\n       \"items\": {\n        \"description\": \"LogsClientSpec defines the client integration for logs, indicating which Loki server to send logs to.\",\n        \"properties\": {\n         \"backoffConfig\": {\n          \"description\": \"Configures how to retry requests to Loki when a request fails. Defaults to a minPeriod of 500ms, maxPeriod of 5m, and maxRetries of 10.\",\n          \"properties\": {\n           \"maxPeriod\": {\n            \"description\": \"Maximum backoff time between retries.\",\n            \"type\": \"string\"\n           },\n           \"maxRetries\": {\n            \"description\": \"Maximum number of retries to perform before giving up a request.\",\n            \"type\": \"integer\"\n           },\n           \"minPeriod\": {\n            \"description\": \"Initial backoff time between retries. Time between retries is increased exponentially.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"basicAuth\": {\n          \"description\": \"BasicAuth for the Loki server.\",\n          \"properties\": {\n           \"password\": {\n            \"description\": \"The secret in the service monitor namespace that contains the password for authentication.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           },\n           \"username\": {\n            \"description\": \"The secret in the service monitor namespace that contains the username for authentication.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"batchSize\": {\n          \"description\": \"Maximum batch size (in bytes) of logs to accumulate before sending the batch to Loki.\",\n          \"type\": \"integer\"\n         },\n         \"batchWait\": {\n          \"description\": \"Maximum amount of time to wait before sending a batch, even if that batch isn't full.\",\n          \"type\": \"string\"\n         },\n         \"bearerToken\": {\n          \"description\": \"BearerToken used for remote_write.\",\n          \"type\": \"string\"\n         },\n         \"bearerTokenFile\": {\n          \"description\": \"BearerTokenFile used to read bearer token.\",\n          \"type\": \"string\"\n         },\n         \"externalLabels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"ExternalLabels are labels to add to any time series when sending data to Loki.\",\n          \"type\": \"object\"\n         },\n         \"proxyUrl\": {\n          \"description\": \"ProxyURL to proxy requests through. Optional.\",\n          \"type\": \"string\"\n         },\n         \"tenantId\": {\n          \"description\": \"Tenant ID used by default to push logs to Loki. If omitted assumes remote Loki is running in single-tenant mode or an authentication layer is used to inject an X-Scope-OrgID header.\",\n          \"type\": \"string\"\n         },\n         \"timeout\": {\n          \"description\": \"Maximum time to wait for a server to respond to a request.\",\n          \"type\": \"string\"\n         },\n         \"tlsConfig\": {\n          \"description\": \"TLSConfig to use for the client. Only used when the protocol of the URL is https.\",\n          \"properties\": {\n           \"ca\": {\n            \"description\": \"Struct containing the CA cert to use for the targets.\",\n            \"properties\": {\n             \"configMap\": {\n              \"description\": \"ConfigMap containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key to select.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\"\n             },\n             \"secret\": {\n              \"description\": \"Secret containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the Secret or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"caFile\": {\n            \"description\": \"Path to the CA cert in the Prometheus container to use for the targets.\",\n            \"type\": \"string\"\n           },\n           \"cert\": {\n            \"description\": \"Struct containing the client cert file for the targets.\",\n            \"properties\": {\n             \"configMap\": {\n              \"description\": \"ConfigMap containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key to select.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\"\n             },\n             \"secret\": {\n              \"description\": \"Secret containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the Secret or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"certFile\": {\n            \"description\": \"Path to the client cert file in the Prometheus container for the targets.\",\n            \"type\": \"string\"\n           },\n           \"insecureSkipVerify\": {\n            \"description\": \"Disable target certificate validation.\",\n            \"type\": \"boolean\"\n           },\n           \"keyFile\": {\n            \"description\": \"Path to the client key file in the Prometheus container for the targets.\",\n            \"type\": \"string\"\n           },\n           \"keySecret\": {\n            \"description\": \"Secret containing the client key file for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           },\n           \"serverName\": {\n            \"description\": \"Used to verify the hostname for the targets.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"url\": {\n          \"description\": \"URL is the URL where Loki is listening. Must be a full HTTP URL, including protocol. Required. Example: https://logs-prod-us-central1.grafana.net/loki/api/v1/push.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"url\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"enforcedNamespaceLabel\": {\n       \"description\": \"EnforcedNamespaceLabel enforces adding a namespace label of origin for each metric that is user-created. The label value will always be the namespace of the object that is being created.\",\n       \"type\": \"string\"\n      },\n      \"ignoreNamespaceSelectors\": {\n       \"description\": \"IgnoreNamespaceSelectors, if true, will ignore NamespaceSelector settings from the PodLogs configs, and they will only discover endpoints within their current namespace.\",\n       \"type\": \"boolean\"\n      },\n      \"instanceNamespaceSelector\": {\n       \"description\": \"InstanceNamespaceSelector are the set of labels to determine which namespaces to watch for LogInstances. If not provided, only checks own namespace.\",\n       \"properties\": {\n        \"matchExpressions\": {\n         \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n         \"items\": {\n          \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"key is the label key that the selector applies to.\",\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n            \"type\": \"string\"\n           },\n           \"values\": {\n            \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"required\": [\n           \"key\",\n           \"operator\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"matchLabels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"instanceSelector\": {\n       \"description\": \"InstanceSelector determines which LogInstances should be selected for running. Each instance runs its own set of Prometheus components, including service discovery, scraping, and remote_write.\",\n       \"properties\": {\n        \"matchExpressions\": {\n         \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n         \"items\": {\n          \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"key is the label key that the selector applies to.\",\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n            \"type\": \"string\"\n           },\n           \"values\": {\n            \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"required\": [\n           \"key\",\n           \"operator\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"matchLabels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"logsExternalLabelName\": {\n       \"description\": \"LogsExternalLabelName is the name of the external label used to denote Grafana Agent cluster. Defaults to \\\"cluster.\\\" External label will _not_ be added when value is set to the empty string.\",\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"metrics\": {\n     \"description\": \"Metrics controls the metrics subsystem of the Agent and settings unique to metrics-specific pods that are deployed.\",\n     \"properties\": {\n      \"arbitraryFSAccessThroughSMs\": {\n       \"description\": \"ArbitraryFSAccessThroughSMs configures whether configuration based on a ServiceMonitor can access arbitrary files on the file system of the Grafana Agent container e.g. bearer token files.\",\n       \"properties\": {\n        \"deny\": {\n         \"type\": \"boolean\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"enforcedNamespaceLabel\": {\n       \"description\": \"EnforcedNamespaceLabel enforces adding a namespace label of origin for each metric that is user-created. The label value will always be the namespace of the object that is being created.\",\n       \"type\": \"string\"\n      },\n      \"enforcedSampleLimit\": {\n       \"description\": \"EnforcedSampleLimit defines global limit on the number of scraped samples that will be accepted. This overrides any SampleLimit set per ServiceMonitor and/or PodMonitor. It is meant to be used by admins to enforce the SampleLimit to keep the overall number of samples and series under the desired limit. Note that if a SampleLimit from a ServiceMonitor or PodMonitor is lower, that value will be used instead.\",\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"enforcedTargetLimit\": {\n       \"description\": \"EnforcedTargetLimit defines a global limit on the number of scraped targets. This overrides any TargetLimit set per ServiceMonitor and/or PodMonitor. It is meant to be used by admins to enforce the TargetLimit to keep the overall number of targets under the desired limit. Note that if a TargetLimit from a ServiceMonitor or PodMonitor is higher, that value will be used instead.\",\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"externalLabels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"ExternalLabels are labels to add to any time series when sending data over remote_write.\",\n       \"type\": \"object\"\n      },\n      \"ignoreNamespaceSelectors\": {\n       \"description\": \"IgnoreNamespaceSelectors, if true, will ignore NamespaceSelector settings from the PodMonitor and ServiceMonitor configs, and they will only discover endpoints within their current namespace.\",\n       \"type\": \"boolean\"\n      },\n      \"instanceNamespaceSelector\": {\n       \"description\": \"InstanceNamespaceSelector are the set of labels to determine which namespaces to watch for MetricsInstances. If not provided, only checks own namespace.\",\n       \"properties\": {\n        \"matchExpressions\": {\n         \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n         \"items\": {\n          \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"key is the label key that the selector applies to.\",\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n            \"type\": \"string\"\n           },\n           \"values\": {\n            \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"required\": [\n           \"key\",\n           \"operator\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"matchLabels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"instanceSelector\": {\n       \"description\": \"InstanceSelector determines which MetricsInstances should be selected for running. Each instance runs its own set of Metrics components, including service discovery, scraping, and remote_write.\",\n       \"properties\": {\n        \"matchExpressions\": {\n         \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n         \"items\": {\n          \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"key is the label key that the selector applies to.\",\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n            \"type\": \"string\"\n           },\n           \"values\": {\n            \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"required\": [\n           \"key\",\n           \"operator\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"matchLabels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"metricsExternalLabelName\": {\n       \"description\": \"MetricsExternalLabelName is the name of the external label used to denote Grafana Agent cluster. Defaults to \\\"cluster.\\\" External label will _not_ be added when value is set to the empty string.\",\n       \"type\": \"string\"\n      },\n      \"overrideHonorLabels\": {\n       \"description\": \"OverrideHonorLabels, if true, overrides all configured honor_labels read from ServiceMonitor or PodMonitor to false.\",\n       \"type\": \"boolean\"\n      },\n      \"overrideHonorTimestamps\": {\n       \"description\": \"OverrideHonorTimestamps allows to globally enforce honoring timestamps in all scrape configs.\",\n       \"type\": \"boolean\"\n      },\n      \"remoteWrite\": {\n       \"description\": \"RemoteWrite controls default remote_write settings for all instances. If an instance does not provide its own remoteWrite settings, these will be used instead.\",\n       \"items\": {\n        \"description\": \"RemoteWriteSpec defines the remote_write configuration for Prometheus.\",\n        \"properties\": {\n         \"basicAuth\": {\n          \"description\": \"BasicAuth for the URL.\",\n          \"properties\": {\n           \"password\": {\n            \"description\": \"The secret in the service monitor namespace that contains the password for authentication.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           },\n           \"username\": {\n            \"description\": \"The secret in the service monitor namespace that contains the username for authentication.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"bearerToken\": {\n          \"description\": \"BearerToken used for remote_write.\",\n          \"type\": \"string\"\n         },\n         \"bearerTokenFile\": {\n          \"description\": \"BearerTokenFile used to read bearer token.\",\n          \"type\": \"string\"\n         },\n         \"headers\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Headers is a set of custom HTTP headers to be sent along with each remote_write request. Be aware that any headers set by Grafana Agent itself can't be overwritten.\",\n          \"type\": \"object\"\n         },\n         \"metadataConfig\": {\n          \"description\": \"MetadataConfig configures the sending of series metadata to remote storage.\",\n          \"properties\": {\n           \"send\": {\n            \"description\": \"Send enables metric metadata to be sent to remote storage.\",\n            \"type\": \"boolean\"\n           },\n           \"sendInterval\": {\n            \"description\": \"SendInterval controls how frequently metric metadata is sent to remote storage.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"description\": \"Name of the remote_write queue. Must be unique if specified. The name is used in metrics and logging in order to differentiate queues.\",\n          \"type\": \"string\"\n         },\n         \"proxyUrl\": {\n          \"description\": \"ProxyURL to proxy requests through. Optional.\",\n          \"type\": \"string\"\n         },\n         \"queueConfig\": {\n          \"description\": \"QueueConfig allows tuning of the remote_write queue parameters.\",\n          \"properties\": {\n           \"batchSendDeadline\": {\n            \"description\": \"BatchSendDeadline is the maximum time a sample will wait in buffer.\",\n            \"type\": \"string\"\n           },\n           \"capacity\": {\n            \"description\": \"Capacity is the number of samples to buffer per shard before we start dropping them.\",\n            \"type\": \"integer\"\n           },\n           \"maxBackoff\": {\n            \"description\": \"MaxBackoff is the maximum retry delay.\",\n            \"type\": \"string\"\n           },\n           \"maxRetries\": {\n            \"description\": \"MaxRetries is the maximum number of times to retry a batch on recoverable errors.\",\n            \"type\": \"integer\"\n           },\n           \"maxSamplesPerSend\": {\n            \"description\": \"MaxSamplesPerSend is the maximum number of samples per send.\",\n            \"type\": \"integer\"\n           },\n           \"maxShards\": {\n            \"description\": \"MaxShards is the maximum number of shards, i.e. amount of concurrency.\",\n            \"type\": \"integer\"\n           },\n           \"minBackoff\": {\n            \"description\": \"MinBackoff is the initial retry delay. Gets doubled for every retry.\",\n            \"type\": \"string\"\n           },\n           \"minShards\": {\n            \"description\": \"MinShards is the minimum number of shards, i.e. amount of concurrency.\",\n            \"type\": \"integer\"\n           },\n           \"retryOnRateLimit\": {\n            \"description\": \"RetryOnRateLimit retries requests when encountering rate limits.\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"remoteTimeout\": {\n          \"description\": \"RemoteTimeout is the timeout for requests to the remote_write endpoint.\",\n          \"type\": \"string\"\n         },\n         \"sigv4\": {\n          \"description\": \"SigV4 configures SigV4-based authentication to the remote_write endpoint. Will be used if SigV4 is defined, even with an empty object.\",\n          \"properties\": {\n           \"accessKey\": {\n            \"description\": \"AccessKey holds the secret of the AWS API access key to use for signing. If not provided, The environment variable AWS_ACCESS_KEY_ID is used.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           },\n           \"profile\": {\n            \"description\": \"Profile is the named AWS profile to use for authentication.\",\n            \"type\": \"string\"\n           },\n           \"region\": {\n            \"description\": \"Region of the AWS endpoint. If blank, the region from the default credentials chain is used.\",\n            \"type\": \"string\"\n           },\n           \"roleARN\": {\n            \"description\": \"RoleARN is the AWS Role ARN to use for authentication, as an alternative for using the AWS API keys.\",\n            \"type\": \"string\"\n           },\n           \"secretKey\": {\n            \"description\": \"SecretKey of the AWS API to use for signing. If blank, the environment variable AWS_SECRET_ACCESS_KEY is used.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"tlsConfig\": {\n          \"description\": \"TLSConfig to use for remote_write.\",\n          \"properties\": {\n           \"ca\": {\n            \"description\": \"Struct containing the CA cert to use for the targets.\",\n            \"properties\": {\n             \"configMap\": {\n              \"description\": \"ConfigMap containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key to select.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\"\n             },\n             \"secret\": {\n              \"description\": \"Secret containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the Secret or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"caFile\": {\n            \"description\": \"Path to the CA cert in the Prometheus container to use for the targets.\",\n            \"type\": \"string\"\n           },\n           \"cert\": {\n            \"description\": \"Struct containing the client cert file for the targets.\",\n            \"properties\": {\n             \"configMap\": {\n              \"description\": \"ConfigMap containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key to select.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\"\n             },\n             \"secret\": {\n              \"description\": \"Secret containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the Secret or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"certFile\": {\n            \"description\": \"Path to the client cert file in the Prometheus container for the targets.\",\n            \"type\": \"string\"\n           },\n           \"insecureSkipVerify\": {\n            \"description\": \"Disable target certificate validation.\",\n            \"type\": \"boolean\"\n           },\n           \"keyFile\": {\n            \"description\": \"Path to the client key file in the Prometheus container for the targets.\",\n            \"type\": \"string\"\n           },\n           \"keySecret\": {\n            \"description\": \"Secret containing the client key file for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           },\n           \"serverName\": {\n            \"description\": \"Used to verify the hostname for the targets.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"url\": {\n          \"description\": \"URL of the endpoint to send samples to.\",\n          \"type\": \"string\"\n         },\n         \"writeRelabelConfigs\": {\n          \"description\": \"WriteRelabelConfigs holds relabel_configs to relabel samples before they are sent to the remote_write endpoint.\",\n          \"items\": {\n           \"description\": \"RelabelConfig allows dynamic rewriting of the label set, being applied to samples before ingestion. It defines `\\u003cmetric_relabel_configs\\u003e`-section of Prometheus configuration. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#metric_relabel_configs\",\n           \"properties\": {\n            \"action\": {\n             \"default\": \"replace\",\n             \"description\": \"Action to perform based on regex matching. Default is 'replace'\",\n             \"enum\": [\n              \"replace\",\n              \"keep\",\n              \"drop\",\n              \"hashmod\",\n              \"labelmap\",\n              \"labeldrop\",\n              \"labelkeep\"\n             ],\n             \"type\": \"string\"\n            },\n            \"modulus\": {\n             \"description\": \"Modulus to take of the hash of the source label values.\",\n             \"format\": \"int64\",\n             \"type\": \"integer\"\n            },\n            \"regex\": {\n             \"description\": \"Regular expression against which the extracted value is matched. Default is '(.*)'\",\n             \"type\": \"string\"\n            },\n            \"replacement\": {\n             \"description\": \"Replacement value against which a regex replace is performed if the regular expression matches. Regex capture groups are available. Default is '$1'\",\n             \"type\": \"string\"\n            },\n            \"separator\": {\n             \"description\": \"Separator placed between concatenated source label values. default is ';'.\",\n             \"type\": \"string\"\n            },\n            \"sourceLabels\": {\n             \"description\": \"The source labels select values from existing labels. Their content is concatenated using the configured separator and matched against the configured regular expression for the replace, keep, and drop actions.\",\n             \"items\": {\n              \"description\": \"LabelName is a valid Prometheus label name which may only contain ASCII letters, numbers, as well as underscores.\",\n              \"pattern\": \"^[a-zA-Z_][a-zA-Z0-9_]*$\",\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"targetLabel\": {\n             \"description\": \"Label to which the resulting value is written in a replace action. It is mandatory for replace actions. Regex capture groups are available.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"required\": [\n         \"url\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"replicaExternalLabelName\": {\n       \"description\": \"ReplicaExternalLabelName is the name of the metrics external label used to denote replica name. Defaults to __replica__. External label will _not_ be added when value is set to the empty string.\",\n       \"type\": \"string\"\n      },\n      \"replicas\": {\n       \"description\": \"Replicas of each shard to deploy for metrics pods. Number of replicas multiplied by the number of shards is the total number of pods created.\",\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"scrapeInterval\": {\n       \"description\": \"ScrapeInterval is the time between consecutive scrapes.\",\n       \"type\": \"string\"\n      },\n      \"scrapeTimeout\": {\n       \"description\": \"ScrapeTimeout is the time to wait for a target to respond before marking a scrape as failed.\",\n       \"type\": \"string\"\n      },\n      \"shards\": {\n       \"description\": \"Shards to distribute targets onto. Number of replicas multiplied by the number of shards is the total number of pods created. Note that scaling down shards will not reshard data onto remaining instances, it must be manually moved. Increasing shards will not reshard data either but it will continue to be available from the same instances. Sharding is performed on the content of the __address__ target meta-label.\",\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"nodeSelector\": {\n     \"additionalProperties\": {\n      \"type\": \"string\"\n     },\n     \"description\": \"NodeSelector defines which nodes pods should be scheduling on.\",\n     \"type\": \"object\"\n    },\n    \"paused\": {\n     \"description\": \"Paused prevents actions except for deletion to be performed on the underlying managed objects.\",\n     \"type\": \"boolean\"\n    },\n    \"podMetadata\": {\n     \"description\": \"PodMetadata configures Labels and Annotations which are propagated to created Grafana Agent pods.\",\n     \"properties\": {\n      \"annotations\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: http://kubernetes.io/docs/user-guide/annotations\",\n       \"type\": \"object\"\n      },\n      \"labels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: http://kubernetes.io/docs/user-guide/labels\",\n       \"type\": \"object\"\n      },\n      \"name\": {\n       \"description\": \"Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: http://kubernetes.io/docs/user-guide/identifiers#names\",\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"portName\": {\n     \"description\": \"Port name used for the pods and governing service. This defaults to agent-metrics.\",\n     \"type\": \"string\"\n    },\n    \"priorityClassName\": {\n     \"description\": \"PriorityClassName is the priority class assigned to pods.\",\n     \"type\": \"string\"\n    },\n    \"resources\": {\n     \"description\": \"Resources holds requests and limits for individual pods.\",\n     \"properties\": {\n      \"limits\": {\n       \"additionalProperties\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n        \"x-kubernetes-int-or-string\": true\n       },\n       \"description\": \"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n       \"type\": \"object\"\n      },\n      \"requests\": {\n       \"additionalProperties\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n        \"x-kubernetes-int-or-string\": true\n       },\n       \"description\": \"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"secrets\": {\n     \"description\": \"Secrets is a list of secrets in the same namespace as the GrafanaAgent object which will be mounted into each running Grafana Agent pod. The secrets are mounted into /etc/grafana-agent/extra-secrets/\\u003csecret-name\\u003e.\",\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"securityContext\": {\n     \"description\": \"SecurityContext holds pod-level security attributes and common container settings. When unspecified, defaults to the default PodSecurityContext.\",\n     \"properties\": {\n      \"fsGroup\": {\n       \"description\": \"A special supplemental group that applies to all containers in a pod. Some volume types allow the Kubelet to change the ownership of that volume to be owned by the pod: \\n 1. The owning GID will be the FSGroup 2. The setgid bit is set (new files created in the volume will be owned by FSGroup) 3. The permission bits are OR'd with rw-rw---- \\n If unset, the Kubelet will not modify the ownership and permissions of any volume. Note that this field cannot be set when spec.os.name is windows.\",\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"fsGroupChangePolicy\": {\n       \"description\": \"fsGroupChangePolicy defines behavior of changing ownership and permission of the volume before being exposed inside Pod. This field will only apply to volume types which support fsGroup based ownership(and permissions). It will have no effect on ephemeral volume types such as: secret, configmaps and emptydir. Valid values are \\\"OnRootMismatch\\\" and \\\"Always\\\". If not specified, \\\"Always\\\" is used. Note that this field cannot be set when spec.os.name is windows.\",\n       \"type\": \"string\"\n      },\n      \"runAsGroup\": {\n       \"description\": \"The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.\",\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"runAsNonRoot\": {\n       \"description\": \"Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\n       \"type\": \"boolean\"\n      },\n      \"runAsUser\": {\n       \"description\": \"The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.\",\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"seLinuxOptions\": {\n       \"description\": \"The SELinux context to be applied to all containers. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.\",\n       \"properties\": {\n        \"level\": {\n         \"description\": \"Level is SELinux level label that applies to the container.\",\n         \"type\": \"string\"\n        },\n        \"role\": {\n         \"description\": \"Role is a SELinux role label that applies to the container.\",\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"description\": \"Type is a SELinux type label that applies to the container.\",\n         \"type\": \"string\"\n        },\n        \"user\": {\n         \"description\": \"User is a SELinux user label that applies to the container.\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"seccompProfile\": {\n       \"description\": \"The seccomp options to use by the containers in this pod. Note that this field cannot be set when spec.os.name is windows.\",\n       \"properties\": {\n        \"localhostProfile\": {\n         \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used. The profile must be preconfigured on the node to work. Must be a descending path, relative to the kubelet's configured seccomp profile location. Must only be set if type is \\\"Localhost\\\".\",\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"description\": \"type indicates which kind of seccomp profile will be applied. Valid options are: \\n Localhost - a profile defined in a file on the node should be used. RuntimeDefault - the container runtime default profile should be used. Unconfined - no profile should be applied.\",\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"type\"\n       ],\n       \"type\": \"object\"\n      },\n      \"supplementalGroups\": {\n       \"description\": \"A list of groups applied to the first process run in each container, in addition to the container's primary GID.  If unspecified, no groups will be added to any container. Note that this field cannot be set when spec.os.name is windows.\",\n       \"items\": {\n        \"format\": \"int64\",\n        \"type\": \"integer\"\n       },\n       \"type\": \"array\"\n      },\n      \"sysctls\": {\n       \"description\": \"Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported sysctls (by the container runtime) might fail to launch. Note that this field cannot be set when spec.os.name is windows.\",\n       \"items\": {\n        \"description\": \"Sysctl defines a kernel parameter to be set\",\n        \"properties\": {\n         \"name\": {\n          \"description\": \"Name of a property to set\",\n          \"type\": \"string\"\n         },\n         \"value\": {\n          \"description\": \"Value of a property to set\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"name\",\n         \"value\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"windowsOptions\": {\n       \"description\": \"The Windows specific settings applied to all containers. If unspecified, the options within a container's SecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.\",\n       \"properties\": {\n        \"gmsaCredentialSpec\": {\n         \"description\": \"GMSACredentialSpec is where the GMSA admission webhook (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the GMSA credential spec named by the GMSACredentialSpecName field.\",\n         \"type\": \"string\"\n        },\n        \"gmsaCredentialSpecName\": {\n         \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n         \"type\": \"string\"\n        },\n        \"hostProcess\": {\n         \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container. This field is alpha-level and will only be honored by components that enable the WindowsHostProcessContainers feature flag. Setting this field without the feature flag will result in errors when validating the Pod. All of a Pod's containers must have the same effective HostProcess value (it is not allowed to have a mix of HostProcess containers and non-HostProcess containers).  In addition, if HostProcess is true then HostNetwork must also be set to true.\",\n         \"type\": \"boolean\"\n        },\n        \"runAsUserName\": {\n         \"description\": \"The UserName in Windows to run the entrypoint of the container process. Defaults to the user specified in image metadata if unspecified. May also be set in PodSecurityContext. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"serviceAccountName\": {\n     \"description\": \"ServiceAccountName is the name of the ServiceAccount to use for running Grafana Agent pods.\",\n     \"type\": \"string\"\n    },\n    \"storage\": {\n     \"description\": \"Storage spec to specify how storage will be used.\",\n     \"properties\": {\n      \"disableMountSubPath\": {\n       \"description\": \"Deprecated: subPath usage will be disabled by default in a future release, this option will become unnecessary. DisableMountSubPath allows to remove any subPath usage in volume mounts.\",\n       \"type\": \"boolean\"\n      },\n      \"emptyDir\": {\n       \"description\": \"EmptyDirVolumeSource to be used by the Prometheus StatefulSets. If specified, used in place of any volumeClaimTemplate. More info: https://kubernetes.io/docs/concepts/storage/volumes/#emptydir\",\n       \"properties\": {\n        \"medium\": {\n         \"description\": \"What type of storage medium should back this directory. The default is \\\"\\\" which means to use the node's default medium. Must be an empty string (default) or Memory. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n         \"type\": \"string\"\n        },\n        \"sizeLimit\": {\n         \"anyOf\": [\n          {\n           \"type\": \"integer\"\n          },\n          {\n           \"type\": \"string\"\n          }\n         ],\n         \"description\": \"Total amount of local storage required for this EmptyDir volume. The size limit is also applicable for memory medium. The maximum usage on memory medium EmptyDir would be the minimum value between the SizeLimit specified here and the sum of memory limits of all containers in a pod. The default is nil which means that the limit is undefined. More info: http://kubernetes.io/docs/user-guide/volumes#emptydir\",\n         \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n         \"x-kubernetes-int-or-string\": true\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"ephemeral\": {\n       \"description\": \"EphemeralVolumeSource to be used by the Prometheus StatefulSets. This is a beta field in k8s 1.21, for lower versions, starting with k8s 1.19, it requires enabling the GenericEphemeralVolume feature gate. More info: https://kubernetes.io/docs/concepts/storage/ephemeral-volumes/#generic-ephemeral-volumes\",\n       \"properties\": {\n        \"volumeClaimTemplate\": {\n         \"description\": \"Will be used to create a stand-alone PVC to provision the volume. The pod in which this EphemeralVolumeSource is embedded will be the owner of the PVC, i.e. the PVC will be deleted together with the pod.  The name of the PVC will be `\\u003cpod name\\u003e-\\u003cvolume name\\u003e` where `\\u003cvolume name\\u003e` is the name from the `PodSpec.Volumes` array entry. Pod validation will reject the pod if the concatenated name is not valid for a PVC (for example, too long). \\n An existing PVC with that name that is not owned by the pod will *not* be used for the pod to avoid using an unrelated volume by mistake. Starting the pod is then blocked until the unrelated PVC is removed. If such a pre-created PVC is meant to be used by the pod, the PVC has to updated with an owner reference to the pod once the pod exists. Normally this should not be necessary, but it may be useful when manually reconstructing a broken cluster. \\n This field is read-only and no changes will be made by Kubernetes to the PVC after it has been created. \\n Required, must not be nil.\",\n         \"properties\": {\n          \"metadata\": {\n           \"description\": \"May contain labels and annotations that will be copied into the PVC when creating it. No other fields are allowed and will be rejected during validation.\",\n           \"type\": \"object\"\n          },\n          \"spec\": {\n           \"description\": \"The specification for the PersistentVolumeClaim. The entire content is copied unchanged into the PVC that gets created from this template. The same fields as in a PersistentVolumeClaim are also valid here.\",\n           \"properties\": {\n            \"accessModes\": {\n             \"description\": \"AccessModes contains the desired access modes the volume should have. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"dataSource\": {\n             \"description\": \"This field can be used to specify either: * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot) * An existing PVC (PersistentVolumeClaim) If the provisioner or an external controller can support the specified data source, it will create a new volume based on the contents of the specified data source. If the AnyVolumeDataSource feature gate is enabled, this field will always have the same contents as the DataSourceRef field.\",\n             \"properties\": {\n              \"apiGroup\": {\n               \"description\": \"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\n               \"type\": \"string\"\n              },\n              \"kind\": {\n               \"description\": \"Kind is the type of resource being referenced\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of resource being referenced\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"kind\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"dataSourceRef\": {\n             \"description\": \"Specifies the object from which to populate the volume with data, if a non-empty volume is desired. This may be any local object from a non-empty API group (non core object) or a PersistentVolumeClaim object. When this field is specified, volume binding will only succeed if the type of the specified object matches some installed volume populator or dynamic provisioner. This field will replace the functionality of the DataSource field and as such if both fields are non-empty, they must have the same value. For backwards compatibility, both fields (DataSource and DataSourceRef) will be set to the same value automatically if one of them is empty and the other is non-empty. There are two important differences between DataSource and DataSourceRef: * While DataSource only allows two specific types of objects, DataSourceRef allows any non-core object, as well as PersistentVolumeClaim objects. * While DataSource ignores disallowed values (dropping them), DataSourceRef preserves all values, and generates an error if a disallowed value is specified. (Alpha) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\",\n             \"properties\": {\n              \"apiGroup\": {\n               \"description\": \"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\n               \"type\": \"string\"\n              },\n              \"kind\": {\n               \"description\": \"Kind is the type of resource being referenced\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of resource being referenced\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"kind\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"resources\": {\n             \"description\": \"Resources represents the minimum resources the volume should have. If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements that are lower than previous value but must still be higher than capacity recorded in the status field of the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n             \"properties\": {\n              \"limits\": {\n               \"additionalProperties\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"description\": \"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n               \"type\": \"object\"\n              },\n              \"requests\": {\n               \"additionalProperties\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"description\": \"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"selector\": {\n             \"description\": \"A label query over volumes to consider for binding.\",\n             \"properties\": {\n              \"matchExpressions\": {\n               \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n               \"items\": {\n                \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"key is the label key that the selector applies to.\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                  \"type\": \"string\"\n                 },\n                 \"values\": {\n                  \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"operator\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"matchLabels\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"storageClassName\": {\n             \"description\": \"Name of the StorageClass required by the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n             \"type\": \"string\"\n            },\n            \"volumeMode\": {\n             \"description\": \"volumeMode defines what type of volume is required by the claim. Value of Filesystem is implied when not included in claim spec.\",\n             \"type\": \"string\"\n            },\n            \"volumeName\": {\n             \"description\": \"VolumeName is the binding reference to the PersistentVolume backing this claim.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"required\": [\n          \"spec\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"volumeClaimTemplate\": {\n       \"description\": \"A PVC spec to be used by the Prometheus StatefulSets.\",\n       \"properties\": {\n        \"apiVersion\": {\n         \"description\": \"APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n         \"type\": \"string\"\n        },\n        \"kind\": {\n         \"description\": \"Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n         \"type\": \"string\"\n        },\n        \"metadata\": {\n         \"description\": \"EmbeddedMetadata contains metadata relevant to an EmbeddedResource.\",\n         \"properties\": {\n          \"annotations\": {\n           \"additionalProperties\": {\n            \"type\": \"string\"\n           },\n           \"description\": \"Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: http://kubernetes.io/docs/user-guide/annotations\",\n           \"type\": \"object\"\n          },\n          \"labels\": {\n           \"additionalProperties\": {\n            \"type\": \"string\"\n           },\n           \"description\": \"Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: http://kubernetes.io/docs/user-guide/labels\",\n           \"type\": \"object\"\n          },\n          \"name\": {\n           \"description\": \"Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: http://kubernetes.io/docs/user-guide/identifiers#names\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"spec\": {\n         \"description\": \"Spec defines the desired characteristics of a volume requested by a pod author. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n         \"properties\": {\n          \"accessModes\": {\n           \"description\": \"AccessModes contains the desired access modes the volume should have. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"dataSource\": {\n           \"description\": \"This field can be used to specify either: * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot) * An existing PVC (PersistentVolumeClaim) If the provisioner or an external controller can support the specified data source, it will create a new volume based on the contents of the specified data source. If the AnyVolumeDataSource feature gate is enabled, this field will always have the same contents as the DataSourceRef field.\",\n           \"properties\": {\n            \"apiGroup\": {\n             \"description\": \"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind is the type of resource being referenced\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name is the name of resource being referenced\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"kind\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"dataSourceRef\": {\n           \"description\": \"Specifies the object from which to populate the volume with data, if a non-empty volume is desired. This may be any local object from a non-empty API group (non core object) or a PersistentVolumeClaim object. When this field is specified, volume binding will only succeed if the type of the specified object matches some installed volume populator or dynamic provisioner. This field will replace the functionality of the DataSource field and as such if both fields are non-empty, they must have the same value. For backwards compatibility, both fields (DataSource and DataSourceRef) will be set to the same value automatically if one of them is empty and the other is non-empty. There are two important differences between DataSource and DataSourceRef: * While DataSource only allows two specific types of objects, DataSourceRef allows any non-core object, as well as PersistentVolumeClaim objects. * While DataSource ignores disallowed values (dropping them), DataSourceRef preserves all values, and generates an error if a disallowed value is specified. (Alpha) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\",\n           \"properties\": {\n            \"apiGroup\": {\n             \"description\": \"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind is the type of resource being referenced\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name is the name of resource being referenced\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"kind\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"resources\": {\n           \"description\": \"Resources represents the minimum resources the volume should have. If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements that are lower than previous value but must still be higher than capacity recorded in the status field of the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n           \"properties\": {\n            \"limits\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"description\": \"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n             \"type\": \"object\"\n            },\n            \"requests\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"description\": \"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"selector\": {\n           \"description\": \"A label query over volumes to consider for binding.\",\n           \"properties\": {\n            \"matchExpressions\": {\n             \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n             \"items\": {\n              \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"key is the label key that the selector applies to.\",\n                \"type\": \"string\"\n               },\n               \"operator\": {\n                \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                \"type\": \"string\"\n               },\n               \"values\": {\n                \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"required\": [\n               \"key\",\n               \"operator\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"matchLabels\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"storageClassName\": {\n           \"description\": \"Name of the StorageClass required by the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n           \"type\": \"string\"\n          },\n          \"volumeMode\": {\n           \"description\": \"volumeMode defines what type of volume is required by the claim. Value of Filesystem is implied when not included in claim spec.\",\n           \"type\": \"string\"\n          },\n          \"volumeName\": {\n           \"description\": \"VolumeName is the binding reference to the PersistentVolume backing this claim.\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"status\": {\n         \"description\": \"Status represents the current information/status of a persistent volume claim. Read-only. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n         \"properties\": {\n          \"accessModes\": {\n           \"description\": \"AccessModes contains the actual access modes the volume backing the PVC has. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"allocatedResources\": {\n           \"additionalProperties\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"description\": \"The storage resource within AllocatedResources tracks the capacity allocated to a PVC. It may be larger than the actual capacity when a volume expansion operation is requested. For storage quota, the larger value from allocatedResources and PVC.spec.resources is used. If allocatedResources is not set, PVC.spec.resources alone is used for quota calculation. If a volume expansion capacity request is lowered, allocatedResources is only lowered if there are no expansion operations in progress and if the actual volume capacity is equal or lower than the requested capacity. This is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.\",\n           \"type\": \"object\"\n          },\n          \"capacity\": {\n           \"additionalProperties\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"description\": \"Represents the actual resources of the underlying volume.\",\n           \"type\": \"object\"\n          },\n          \"conditions\": {\n           \"description\": \"Current Condition of persistent volume claim. If underlying persistent volume is being resized then the Condition will be set to 'ResizeStarted'.\",\n           \"items\": {\n            \"description\": \"PersistentVolumeClaimCondition contails details about state of pvc\",\n            \"properties\": {\n             \"lastProbeTime\": {\n              \"description\": \"Last time we probed the condition.\",\n              \"format\": \"date-time\",\n              \"type\": \"string\"\n             },\n             \"lastTransitionTime\": {\n              \"description\": \"Last time the condition transitioned from one status to another.\",\n              \"format\": \"date-time\",\n              \"type\": \"string\"\n             },\n             \"message\": {\n              \"description\": \"Human-readable message indicating details about last transition.\",\n              \"type\": \"string\"\n             },\n             \"reason\": {\n              \"description\": \"Unique, this should be a short, machine understandable string that gives the reason for condition's last transition. If it reports \\\"ResizeStarted\\\" that means the underlying persistent volume is being resized.\",\n              \"type\": \"string\"\n             },\n             \"status\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"description\": \"PersistentVolumeClaimConditionType is a valid value of PersistentVolumeClaimCondition.Type\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"status\",\n             \"type\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\"\n          },\n          \"phase\": {\n           \"description\": \"Phase represents the current phase of PersistentVolumeClaim.\",\n           \"type\": \"string\"\n          },\n          \"resizeStatus\": {\n           \"description\": \"ResizeStatus stores status of resize operation. ResizeStatus is not set by default but when expansion is complete resizeStatus is set to empty string by resize controller or kubelet. This is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"tolerations\": {\n     \"description\": \"Tolerations, if specified, controls the pod's tolerations.\",\n     \"items\": {\n      \"description\": \"The pod this Toleration is attached to tolerates any taint that matches the triple \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\n      \"properties\": {\n       \"effect\": {\n        \"description\": \"Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.\",\n        \"type\": \"string\"\n       },\n       \"key\": {\n        \"description\": \"Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys.\",\n        \"type\": \"string\"\n       },\n       \"operator\": {\n        \"description\": \"Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a pod can tolerate all taints of a particular category.\",\n        \"type\": \"string\"\n       },\n       \"tolerationSeconds\": {\n        \"description\": \"TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system.\",\n        \"format\": \"int64\",\n        \"type\": \"integer\"\n       },\n       \"value\": {\n        \"description\": \"Value is the taint value the toleration matches to. If the operator is Exists, the value should be empty, otherwise just a regular string.\",\n        \"type\": \"string\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"topologySpreadConstraints\": {\n     \"description\": \"TopologySpreadConstraints, if specified, controls the pod's topology spread constraints.\",\n     \"items\": {\n      \"description\": \"TopologySpreadConstraint specifies how to spread matching pods among the given topology.\",\n      \"properties\": {\n       \"labelSelector\": {\n        \"description\": \"LabelSelector is used to find matching pods. Pods that match this label selector are counted to determine the number of pods in their corresponding topology domain.\",\n        \"properties\": {\n         \"matchExpressions\": {\n          \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n          \"items\": {\n           \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"key is the label key that the selector applies to.\",\n             \"type\": \"string\"\n            },\n            \"operator\": {\n             \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n             \"type\": \"string\"\n            },\n            \"values\": {\n             \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"operator\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"matchLabels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"maxSkew\": {\n        \"description\": \"MaxSkew describes the degree to which pods may be unevenly distributed. When `whenUnsatisfiable=DoNotSchedule`, it is the maximum permitted difference between the number of matching pods in the target topology and the global minimum. For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same labelSelector spread as 1/1/0: | zone1 | zone2 | zone3 | |   P   |   P   |       | - if MaxSkew is 1, incoming pod can only be scheduled to zone3 to become 1/1/1; scheduling it onto zone1(zone2) would make the ActualSkew(2-0) on zone1(zone2) violate MaxSkew(1). - if MaxSkew is 2, incoming pod can be scheduled onto any zone. When `whenUnsatisfiable=ScheduleAnyway`, it is used to give higher precedence to topologies that satisfy it. It's a required field. Default value is 1 and 0 is not allowed.\",\n        \"format\": \"int32\",\n        \"type\": \"integer\"\n       },\n       \"topologyKey\": {\n        \"description\": \"TopologyKey is the key of node labels. Nodes that have a label with this key and identical values are considered to be in the same topology. We consider each \\u003ckey, value\\u003e as a \\\"bucket\\\", and try to put balanced number of pods into each bucket. It's a required field.\",\n        \"type\": \"string\"\n       },\n       \"whenUnsatisfiable\": {\n        \"description\": \"WhenUnsatisfiable indicates how to deal with a pod if it doesn't satisfy the spread constraint. - DoNotSchedule (default) tells the scheduler not to schedule it. - ScheduleAnyway tells the scheduler to schedule the pod in any location, but giving higher precedence to topologies that would help reduce the skew. A constraint is considered \\\"Unsatisfiable\\\" for an incoming pod if and only if every possible node assignment for that pod would violate \\\"MaxSkew\\\" on some topology. For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same labelSelector spread as 3/1/1: | zone1 | zone2 | zone3 | | P P P |   P   |   P   | If WhenUnsatisfiable is set to DoNotSchedule, incoming pod can only be scheduled to zone2(zone3) to become 3/2/1(3/1/2) as ActualSkew(2-1) on zone2(zone3) satisfies MaxSkew(1). In other words, the cluster can still be imbalanced, but scheduler won't make it *more* imbalanced. It's a required field.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"maxSkew\",\n       \"topologyKey\",\n       \"whenUnsatisfiable\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"version\": {\n     \"description\": \"Version of Grafana Agent to be deployed.\",\n     \"type\": \"string\"\n    },\n    \"volumeMounts\": {\n     \"description\": \"VolumeMounts allows configuration of additional VolumeMounts on the output StatefulSet definition. VolumEMounts specified will be appended to other VolumeMounts in the Grafana Agent container that are generated as a result of StorageSpec objects.\",\n     \"items\": {\n      \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n      \"properties\": {\n       \"mountPath\": {\n        \"description\": \"Path within the container at which the volume should be mounted.  Must not contain ':'.\",\n        \"type\": \"string\"\n       },\n       \"mountPropagation\": {\n        \"description\": \"mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10.\",\n        \"type\": \"string\"\n       },\n       \"name\": {\n        \"description\": \"This must match the Name of a Volume.\",\n        \"type\": \"string\"\n       },\n       \"readOnly\": {\n        \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.\",\n        \"type\": \"boolean\"\n       },\n       \"subPath\": {\n        \"description\": \"Path within the volume from which the container's volume should be mounted. Defaults to \\\"\\\" (volume's root).\",\n        \"type\": \"string\"\n       },\n       \"subPathExpr\": {\n        \"description\": \"Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \\\"\\\" (volume's root). SubPathExpr and SubPath are mutually exclusive.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"mountPath\",\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"volumes\": {\n     \"description\": \"Volumes allows configuration of additional volumes on the output StatefulSet definition. Volumes specified will be appended to other volumes that are generated as a result of StorageSpec objects.\",\n     \"items\": {\n      \"description\": \"Volume represents a named volume in a pod that may be accessed by any container in the pod.\",\n      \"properties\": {\n       \"awsElasticBlockStore\": {\n        \"description\": \"AWSElasticBlockStore represents an AWS Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"partition\": {\n          \"description\": \"The partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \\\"1\\\". Similarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty).\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"readOnly\": {\n          \"description\": \"Specify \\\"true\\\" to force and set the ReadOnly property in VolumeMounts to \\\"true\\\". If omitted, the default is \\\"false\\\". More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n          \"type\": \"boolean\"\n         },\n         \"volumeID\": {\n          \"description\": \"Unique ID of the persistent disk resource in AWS (Amazon EBS volume). More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"azureDisk\": {\n        \"description\": \"AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.\",\n        \"properties\": {\n         \"cachingMode\": {\n          \"description\": \"Host Caching mode: None, Read Only, Read Write.\",\n          \"type\": \"string\"\n         },\n         \"diskName\": {\n          \"description\": \"The Name of the data disk in the blob storage\",\n          \"type\": \"string\"\n         },\n         \"diskURI\": {\n          \"description\": \"The URI the data disk in the blob storage\",\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"kind\": {\n          \"description\": \"Expected values Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"diskName\",\n         \"diskURI\"\n        ],\n        \"type\": \"object\"\n       },\n       \"azureFile\": {\n        \"description\": \"AzureFile represents an Azure File Service mount on the host and bind mount to the pod.\",\n        \"properties\": {\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretName\": {\n          \"description\": \"the name of secret that contains Azure Storage Account Name and Key\",\n          \"type\": \"string\"\n         },\n         \"shareName\": {\n          \"description\": \"Share Name\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"secretName\",\n         \"shareName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"cephfs\": {\n        \"description\": \"CephFS represents a Ceph FS mount on the host that shares a pod's lifetime\",\n        \"properties\": {\n         \"monitors\": {\n          \"description\": \"Required: Monitors is a collection of Ceph monitors More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"path\": {\n          \"description\": \"Optional: Used as the mounted root, rather than the full Ceph tree, default is /\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"type\": \"boolean\"\n         },\n         \"secretFile\": {\n          \"description\": \"Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"secretRef\": {\n          \"description\": \"Optional: SecretRef is reference to the authentication secret for User, default is empty. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"user\": {\n          \"description\": \"Optional: User is the rados user name, default is admin More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"monitors\"\n        ],\n        \"type\": \"object\"\n       },\n       \"cinder\": {\n        \"description\": \"Cinder represents a cinder volume attached and mounted on kubelets host machine. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"Optional: points to a secret object containing parameters used to connect to OpenStack.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"volumeID\": {\n          \"description\": \"volume id used to identify the volume in cinder. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"configMap\": {\n        \"description\": \"ConfigMap represents a configMap that should populate this volume\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"description\": \"If unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n          \"items\": {\n           \"description\": \"Maps a string key to a path within a volume.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to project.\",\n             \"type\": \"string\"\n            },\n            \"mode\": {\n             \"description\": \"Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"name\": {\n          \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n          \"type\": \"string\"\n         },\n         \"optional\": {\n          \"description\": \"Specify whether the ConfigMap or its keys must be defined\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"csi\": {\n        \"description\": \"CSI (Container Storage Interface) represents ephemeral storage that is handled by certain external CSI drivers (Beta feature).\",\n        \"properties\": {\n         \"driver\": {\n          \"description\": \"Driver is the name of the CSI driver that handles this volume. Consult with your admin for the correct name as registered in the cluster.\",\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". If not provided, the empty value is passed to the associated CSI driver which will determine the default filesystem to apply.\",\n          \"type\": \"string\"\n         },\n         \"nodePublishSecretRef\": {\n          \"description\": \"NodePublishSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI NodePublishVolume and NodeUnpublishVolume calls. This field is optional, and  may be empty if no secret is required. If the secret object contains more than one secret, all secret references are passed.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"readOnly\": {\n          \"description\": \"Specifies a read-only configuration for the volume. Defaults to false (read/write).\",\n          \"type\": \"boolean\"\n         },\n         \"volumeAttributes\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"VolumeAttributes stores driver-specific properties that are passed to the CSI driver. Consult your driver's documentation for supported values.\",\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"driver\"\n        ],\n        \"type\": \"object\"\n       },\n       \"downwardAPI\": {\n        \"description\": \"DownwardAPI represents downward API about the pod that should populate this volume\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"Optional: mode bits to use on created files by default. Must be a Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"description\": \"Items is a list of downward API volume file\",\n          \"items\": {\n           \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n           \"properties\": {\n            \"fieldRef\": {\n             \"description\": \"Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.\",\n             \"properties\": {\n              \"apiVersion\": {\n               \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n               \"type\": \"string\"\n              },\n              \"fieldPath\": {\n               \"description\": \"Path of the field to select in the specified API version.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"fieldPath\"\n             ],\n             \"type\": \"object\"\n            },\n            \"mode\": {\n             \"description\": \"Optional: mode bits used to set permissions on this file, must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n             \"type\": \"string\"\n            },\n            \"resourceFieldRef\": {\n             \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n             \"properties\": {\n              \"containerName\": {\n               \"description\": \"Container name: required for volumes, optional for env vars\",\n               \"type\": \"string\"\n              },\n              \"divisor\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"resource\": {\n               \"description\": \"Required: resource to select\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"resource\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"emptyDir\": {\n        \"description\": \"EmptyDir represents a temporary directory that shares a pod's lifetime. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n        \"properties\": {\n         \"medium\": {\n          \"description\": \"What type of storage medium should back this directory. The default is \\\"\\\" which means to use the node's default medium. Must be an empty string (default) or Memory. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n          \"type\": \"string\"\n         },\n         \"sizeLimit\": {\n          \"anyOf\": [\n           {\n            \"type\": \"integer\"\n           },\n           {\n            \"type\": \"string\"\n           }\n          ],\n          \"description\": \"Total amount of local storage required for this EmptyDir volume. The size limit is also applicable for memory medium. The maximum usage on memory medium EmptyDir would be the minimum value between the SizeLimit specified here and the sum of memory limits of all containers in a pod. The default is nil which means that the limit is undefined. More info: http://kubernetes.io/docs/user-guide/volumes#emptydir\",\n          \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n          \"x-kubernetes-int-or-string\": true\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"ephemeral\": {\n        \"description\": \"Ephemeral represents a volume that is handled by a cluster storage driver. The volume's lifecycle is tied to the pod that defines it - it will be created before the pod starts, and deleted when the pod is removed. \\n Use this if: a) the volume is only needed while the pod runs, b) features of normal volumes like restoring from snapshot or capacity tracking are needed, c) the storage driver is specified through a storage class, and d) the storage driver supports dynamic volume provisioning through a PersistentVolumeClaim (see EphemeralVolumeSource for more information on the connection between this volume type and PersistentVolumeClaim). \\n Use PersistentVolumeClaim or one of the vendor-specific APIs for volumes that persist for longer than the lifecycle of an individual pod. \\n Use CSI for light-weight local ephemeral volumes if the CSI driver is meant to be used that way - see the documentation of the driver for more information. \\n A pod can use both types of ephemeral volumes and persistent volumes at the same time.\",\n        \"properties\": {\n         \"volumeClaimTemplate\": {\n          \"description\": \"Will be used to create a stand-alone PVC to provision the volume. The pod in which this EphemeralVolumeSource is embedded will be the owner of the PVC, i.e. the PVC will be deleted together with the pod.  The name of the PVC will be `\\u003cpod name\\u003e-\\u003cvolume name\\u003e` where `\\u003cvolume name\\u003e` is the name from the `PodSpec.Volumes` array entry. Pod validation will reject the pod if the concatenated name is not valid for a PVC (for example, too long). \\n An existing PVC with that name that is not owned by the pod will *not* be used for the pod to avoid using an unrelated volume by mistake. Starting the pod is then blocked until the unrelated PVC is removed. If such a pre-created PVC is meant to be used by the pod, the PVC has to updated with an owner reference to the pod once the pod exists. Normally this should not be necessary, but it may be useful when manually reconstructing a broken cluster. \\n This field is read-only and no changes will be made by Kubernetes to the PVC after it has been created. \\n Required, must not be nil.\",\n          \"properties\": {\n           \"metadata\": {\n            \"description\": \"May contain labels and annotations that will be copied into the PVC when creating it. No other fields are allowed and will be rejected during validation.\",\n            \"type\": \"object\"\n           },\n           \"spec\": {\n            \"description\": \"The specification for the PersistentVolumeClaim. The entire content is copied unchanged into the PVC that gets created from this template. The same fields as in a PersistentVolumeClaim are also valid here.\",\n            \"properties\": {\n             \"accessModes\": {\n              \"description\": \"AccessModes contains the desired access modes the volume should have. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"dataSource\": {\n              \"description\": \"This field can be used to specify either: * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot) * An existing PVC (PersistentVolumeClaim) If the provisioner or an external controller can support the specified data source, it will create a new volume based on the contents of the specified data source. If the AnyVolumeDataSource feature gate is enabled, this field will always have the same contents as the DataSourceRef field.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind is the type of resource being referenced\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name is the name of resource being referenced\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"dataSourceRef\": {\n              \"description\": \"Specifies the object from which to populate the volume with data, if a non-empty volume is desired. This may be any local object from a non-empty API group (non core object) or a PersistentVolumeClaim object. When this field is specified, volume binding will only succeed if the type of the specified object matches some installed volume populator or dynamic provisioner. This field will replace the functionality of the DataSource field and as such if both fields are non-empty, they must have the same value. For backwards compatibility, both fields (DataSource and DataSourceRef) will be set to the same value automatically if one of them is empty and the other is non-empty. There are two important differences between DataSource and DataSourceRef: * While DataSource only allows two specific types of objects, DataSourceRef allows any non-core object, as well as PersistentVolumeClaim objects. * While DataSource ignores disallowed values (dropping them), DataSourceRef preserves all values, and generates an error if a disallowed value is specified. (Alpha) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind is the type of resource being referenced\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name is the name of resource being referenced\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"resources\": {\n              \"description\": \"Resources represents the minimum resources the volume should have. If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements that are lower than previous value but must still be higher than capacity recorded in the status field of the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n              \"properties\": {\n               \"limits\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"description\": \"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                \"type\": \"object\"\n               },\n               \"requests\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"description\": \"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"selector\": {\n              \"description\": \"A label query over volumes to consider for binding.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"storageClassName\": {\n              \"description\": \"Name of the StorageClass required by the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n              \"type\": \"string\"\n             },\n             \"volumeMode\": {\n              \"description\": \"volumeMode defines what type of volume is required by the claim. Value of Filesystem is implied when not included in claim spec.\",\n              \"type\": \"string\"\n             },\n             \"volumeName\": {\n              \"description\": \"VolumeName is the binding reference to the PersistentVolume backing this claim.\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"spec\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"fc\": {\n        \"description\": \"FC represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"lun\": {\n          \"description\": \"Optional: FC target lun number\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"readOnly\": {\n          \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"targetWWNs\": {\n          \"description\": \"Optional: FC target worldwide names (WWNs)\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"wwids\": {\n          \"description\": \"Optional: FC volume world wide identifiers (wwids) Either wwids or combination of targetWWNs and lun must be set, but not both simultaneously.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"flexVolume\": {\n        \"description\": \"FlexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin.\",\n        \"properties\": {\n         \"driver\": {\n          \"description\": \"Driver is the name of the driver to use for this volume.\",\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". The default filesystem depends on FlexVolume script.\",\n          \"type\": \"string\"\n         },\n         \"options\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Optional: Extra command options if any.\",\n          \"type\": \"object\"\n         },\n         \"readOnly\": {\n          \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"Optional: SecretRef is reference to the secret object containing sensitive information to pass to the plugin scripts. This may be empty if no secret object is specified. If the secret object contains more than one secret, all secrets are passed to the plugin scripts.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"driver\"\n        ],\n        \"type\": \"object\"\n       },\n       \"flocker\": {\n        \"description\": \"Flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running\",\n        \"properties\": {\n         \"datasetName\": {\n          \"description\": \"Name of the dataset stored as metadata -\\u003e name on the dataset for Flocker should be considered as deprecated\",\n          \"type\": \"string\"\n         },\n         \"datasetUUID\": {\n          \"description\": \"UUID of the dataset. This is unique identifier of a Flocker dataset\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"gcePersistentDisk\": {\n        \"description\": \"GCEPersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"partition\": {\n          \"description\": \"The partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \\\"1\\\". Similarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty). More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"pdName\": {\n          \"description\": \"Unique name of the PD resource in GCE. Used to identify the disk in GCE. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"pdName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"gitRepo\": {\n        \"description\": \"GitRepo represents a git repository at a particular revision. DEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir into the Pod's container.\",\n        \"properties\": {\n         \"directory\": {\n          \"description\": \"Target directory name. Must not contain or start with '..'.  If '.' is supplied, the volume directory will be the git repository.  Otherwise, if specified, the volume will contain the git repository in the subdirectory with the given name.\",\n          \"type\": \"string\"\n         },\n         \"repository\": {\n          \"description\": \"Repository URL\",\n          \"type\": \"string\"\n         },\n         \"revision\": {\n          \"description\": \"Commit hash for the specified revision.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"repository\"\n        ],\n        \"type\": \"object\"\n       },\n       \"glusterfs\": {\n        \"description\": \"Glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/glusterfs/README.md\",\n        \"properties\": {\n         \"endpoints\": {\n          \"description\": \"EndpointsName is the endpoint name that details Glusterfs topology. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n          \"type\": \"string\"\n         },\n         \"path\": {\n          \"description\": \"Path is the Glusterfs volume path. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the Glusterfs volume to be mounted with read-only permissions. Defaults to false. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"endpoints\",\n         \"path\"\n        ],\n        \"type\": \"object\"\n       },\n       \"hostPath\": {\n        \"description\": \"HostPath represents a pre-existing file or directory on the host machine that is directly exposed to the container. This is generally used for system agents or other privileged things that are allowed to see the host machine. Most containers will NOT need this. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath --- TODO(jonesdl) We need to restrict who can use host directory mounts and who can/can not mount host directories as read/write.\",\n        \"properties\": {\n         \"path\": {\n          \"description\": \"Path of the directory on the host. If the path is a symlink, it will follow the link to the real path. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n          \"type\": \"string\"\n         },\n         \"type\": {\n          \"description\": \"Type for HostPath Volume Defaults to \\\"\\\" More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"path\"\n        ],\n        \"type\": \"object\"\n       },\n       \"iscsi\": {\n        \"description\": \"ISCSI represents an ISCSI Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://examples.k8s.io/volumes/iscsi/README.md\",\n        \"properties\": {\n         \"chapAuthDiscovery\": {\n          \"description\": \"whether support iSCSI Discovery CHAP authentication\",\n          \"type\": \"boolean\"\n         },\n         \"chapAuthSession\": {\n          \"description\": \"whether support iSCSI Session CHAP authentication\",\n          \"type\": \"boolean\"\n         },\n         \"fsType\": {\n          \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"initiatorName\": {\n          \"description\": \"Custom iSCSI Initiator Name. If initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface \\u003ctarget portal\\u003e:\\u003cvolume name\\u003e will be created for the connection.\",\n          \"type\": \"string\"\n         },\n         \"iqn\": {\n          \"description\": \"Target iSCSI Qualified Name.\",\n          \"type\": \"string\"\n         },\n         \"iscsiInterface\": {\n          \"description\": \"iSCSI Interface Name that uses an iSCSI transport. Defaults to 'default' (tcp).\",\n          \"type\": \"string\"\n         },\n         \"lun\": {\n          \"description\": \"iSCSI Target Lun number.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"portals\": {\n          \"description\": \"iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"CHAP Secret for iSCSI target and initiator authentication\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"targetPortal\": {\n          \"description\": \"iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"iqn\",\n         \"lun\",\n         \"targetPortal\"\n        ],\n        \"type\": \"object\"\n       },\n       \"name\": {\n        \"description\": \"Volume's name. Must be a DNS_LABEL and unique within the pod. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n        \"type\": \"string\"\n       },\n       \"nfs\": {\n        \"description\": \"NFS represents an NFS mount on the host that shares a pod's lifetime More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n        \"properties\": {\n         \"path\": {\n          \"description\": \"Path that is exported by the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the NFS export to be mounted with read-only permissions. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n          \"type\": \"boolean\"\n         },\n         \"server\": {\n          \"description\": \"Server is the hostname or IP address of the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"path\",\n         \"server\"\n        ],\n        \"type\": \"object\"\n       },\n       \"persistentVolumeClaim\": {\n        \"description\": \"PersistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n        \"properties\": {\n         \"claimName\": {\n          \"description\": \"ClaimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Will force the ReadOnly setting in VolumeMounts. Default false.\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"claimName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"photonPersistentDisk\": {\n        \"description\": \"PhotonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"pdID\": {\n          \"description\": \"ID that identifies Photon Controller persistent disk\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"pdID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"portworxVolume\": {\n        \"description\": \"PortworxVolume represents a portworx volume attached and mounted on kubelets host machine\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"FSType represents the filesystem type to mount Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"volumeID\": {\n          \"description\": \"VolumeID uniquely identifies a Portworx volume\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"projected\": {\n        \"description\": \"Items for all in one resources secrets, configmaps, and downward API\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"Mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"sources\": {\n          \"description\": \"list of volume projections\",\n          \"items\": {\n           \"description\": \"Projection that may be projected along with other supported volume types\",\n           \"properties\": {\n            \"configMap\": {\n             \"description\": \"information about the configMap data to project\",\n             \"properties\": {\n              \"items\": {\n               \"description\": \"If unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n               \"items\": {\n                \"description\": \"Maps a string key to a path within a volume.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"The key to project.\",\n                  \"type\": \"string\"\n                 },\n                 \"mode\": {\n                  \"description\": \"Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the ConfigMap or its keys must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"downwardAPI\": {\n             \"description\": \"information about the downwardAPI data to project\",\n             \"properties\": {\n              \"items\": {\n               \"description\": \"Items is a list of DownwardAPIVolume file\",\n               \"items\": {\n                \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n                \"properties\": {\n                 \"fieldRef\": {\n                  \"description\": \"Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.\",\n                  \"properties\": {\n                   \"apiVersion\": {\n                    \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                    \"type\": \"string\"\n                   },\n                   \"fieldPath\": {\n                    \"description\": \"Path of the field to select in the specified API version.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"fieldPath\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"mode\": {\n                  \"description\": \"Optional: mode bits used to set permissions on this file, must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n                  \"type\": \"string\"\n                 },\n                 \"resourceFieldRef\": {\n                  \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n                  \"properties\": {\n                   \"containerName\": {\n                    \"description\": \"Container name: required for volumes, optional for env vars\",\n                    \"type\": \"string\"\n                   },\n                   \"divisor\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                    \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"resource\": {\n                    \"description\": \"Required: resource to select\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"resource\"\n                  ],\n                  \"type\": \"object\"\n                 }\n                },\n                \"required\": [\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"secret\": {\n             \"description\": \"information about the secret data to project\",\n             \"properties\": {\n              \"items\": {\n               \"description\": \"If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n               \"items\": {\n                \"description\": \"Maps a string key to a path within a volume.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"The key to project.\",\n                  \"type\": \"string\"\n                 },\n                 \"mode\": {\n                  \"description\": \"Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"serviceAccountToken\": {\n             \"description\": \"information about the serviceAccountToken data to project\",\n             \"properties\": {\n              \"audience\": {\n               \"description\": \"Audience is the intended audience of the token. A recipient of a token must identify itself with an identifier specified in the audience of the token, and otherwise should reject the token. The audience defaults to the identifier of the apiserver.\",\n               \"type\": \"string\"\n              },\n              \"expirationSeconds\": {\n               \"description\": \"ExpirationSeconds is the requested duration of validity of the service account token. As the token approaches expiration, the kubelet volume plugin will proactively rotate the service account token. The kubelet will start trying to rotate the token if the token is older than 80 percent of its time to live or if the token is older than 24 hours.Defaults to 1 hour and must be at least 10 minutes.\",\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              },\n              \"path\": {\n               \"description\": \"Path is the path relative to the mount point of the file to project the token into.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"quobyte\": {\n        \"description\": \"Quobyte represents a Quobyte mount on the host that shares a pod's lifetime\",\n        \"properties\": {\n         \"group\": {\n          \"description\": \"Group to map volume access to Default is no group\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the Quobyte volume to be mounted with read-only permissions. Defaults to false.\",\n          \"type\": \"boolean\"\n         },\n         \"registry\": {\n          \"description\": \"Registry represents a single or multiple Quobyte Registry services specified as a string as host:port pair (multiple entries are separated with commas) which acts as the central registry for volumes\",\n          \"type\": \"string\"\n         },\n         \"tenant\": {\n          \"description\": \"Tenant owning the given Quobyte volume in the Backend Used with dynamically provisioned Quobyte volumes, value is set by the plugin\",\n          \"type\": \"string\"\n         },\n         \"user\": {\n          \"description\": \"User to map volume access to Defaults to serivceaccount user\",\n          \"type\": \"string\"\n         },\n         \"volume\": {\n          \"description\": \"Volume is a string that references an already created Quobyte volume by name.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"registry\",\n         \"volume\"\n        ],\n        \"type\": \"object\"\n       },\n       \"rbd\": {\n        \"description\": \"RBD represents a Rados Block Device mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/rbd/README.md\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#rbd TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"image\": {\n          \"description\": \"The rados image name. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"keyring\": {\n          \"description\": \"Keyring is the path to key ring for RBDUser. Default is /etc/ceph/keyring. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"monitors\": {\n          \"description\": \"A collection of Ceph monitors. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"pool\": {\n          \"description\": \"The rados pool name. Default is rbd. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"SecretRef is name of the authentication secret for RBDUser. If provided overrides keyring. Default is nil. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"user\": {\n          \"description\": \"The rados user name. Default is admin. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"image\",\n         \"monitors\"\n        ],\n        \"type\": \"object\"\n       },\n       \"scaleIO\": {\n        \"description\": \"ScaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Default is \\\"xfs\\\".\",\n          \"type\": \"string\"\n         },\n         \"gateway\": {\n          \"description\": \"The host address of the ScaleIO API Gateway.\",\n          \"type\": \"string\"\n         },\n         \"protectionDomain\": {\n          \"description\": \"The name of the ScaleIO Protection Domain for the configured storage.\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"SecretRef references to the secret for ScaleIO user and other sensitive information. If this is not provided, Login operation will fail.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"sslEnabled\": {\n          \"description\": \"Flag to enable/disable SSL communication with Gateway, default false\",\n          \"type\": \"boolean\"\n         },\n         \"storageMode\": {\n          \"description\": \"Indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned. Default is ThinProvisioned.\",\n          \"type\": \"string\"\n         },\n         \"storagePool\": {\n          \"description\": \"The ScaleIO Storage Pool associated with the protection domain.\",\n          \"type\": \"string\"\n         },\n         \"system\": {\n          \"description\": \"The name of the storage system as configured in ScaleIO.\",\n          \"type\": \"string\"\n         },\n         \"volumeName\": {\n          \"description\": \"The name of a volume already created in the ScaleIO system that is associated with this volume source.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"gateway\",\n         \"secretRef\",\n         \"system\"\n        ],\n        \"type\": \"object\"\n       },\n       \"secret\": {\n        \"description\": \"Secret represents a secret that should populate this volume. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"description\": \"If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n          \"items\": {\n           \"description\": \"Maps a string key to a path within a volume.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to project.\",\n             \"type\": \"string\"\n            },\n            \"mode\": {\n             \"description\": \"Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"optional\": {\n          \"description\": \"Specify whether the Secret or its keys must be defined\",\n          \"type\": \"boolean\"\n         },\n         \"secretName\": {\n          \"description\": \"Name of the secret in the pod's namespace to use. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"storageos\": {\n        \"description\": \"StorageOS represents a StorageOS volume attached and mounted on Kubernetes nodes.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"SecretRef specifies the secret to use for obtaining the StorageOS API credentials.  If not specified, default values will be attempted.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"volumeName\": {\n          \"description\": \"VolumeName is the human-readable name of the StorageOS volume.  Volume names are only unique within a namespace.\",\n          \"type\": \"string\"\n         },\n         \"volumeNamespace\": {\n          \"description\": \"VolumeNamespace specifies the scope of the volume within StorageOS.  If no namespace is specified then the Pod's namespace will be used.  This allows the Kubernetes name scoping to be mirrored within StorageOS for tighter integration. Set VolumeName to any name to override the default behaviour. Set to \\\"default\\\" if you are not using namespaces within StorageOS. Namespaces that do not pre-exist within StorageOS will be created.\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"vsphereVolume\": {\n        \"description\": \"VsphereVolume represents a vSphere volume attached and mounted on kubelets host machine\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"storagePolicyID\": {\n          \"description\": \"Storage Policy Based Management (SPBM) profile ID associated with the StoragePolicyName.\",\n          \"type\": \"string\"\n         },\n         \"storagePolicyName\": {\n          \"description\": \"Storage Policy Based Management (SPBM) profile name.\",\n          \"type\": \"string\"\n         },\n         \"volumePath\": {\n          \"description\": \"Path that identifies vSphere volume vmdk\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumePath\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Grafana Agent\",\n \"type\": \"object\"\n}"}}