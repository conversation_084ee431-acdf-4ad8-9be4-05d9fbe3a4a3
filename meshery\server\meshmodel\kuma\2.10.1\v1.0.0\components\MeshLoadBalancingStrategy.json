{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Mesh Load Balancing Strategy", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kuma", "displayName": "<PERSON><PERSON>", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Cloud Native Network"}, "subCategory": "Service Mesh", "metadata": {"isAnnotation": false, "primaryColor": "#291953", "secondaryColor": "#6942c9", "shape": "circle", "source_uri": "https://github.com/kumahq/charts/releases/download/kuma-2.10.1/kuma-2.10.1.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1\" data-name=\"Layer 1\" viewBox=\"0 0 1173.18 1173.18\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#291953;}.cls-2{fill:none;}</style></defs><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_2\" data-name=\"Layer 2\"><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"></path><rect xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"></rect></g></g></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1\" data-name=\"Layer 1\" viewBox=\"0 0 1173.18 1173.18\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff;}.cls-2{fill:none;}</style></defs><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_2\" data-name=\"Layer 2\"><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"></path><rect xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"></rect></g></g></svg>"}, "model": {"version": "2.10.1"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#291953", "secondaryColor": "#6942c9", "shape": "circle", "svgColor": "<svg id=\"Layer_1\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1173.18 1173.18\"><defs><style>.cls-1{fill:#291953;}.cls-2{fill:none;}</style></defs><g id=\"Layer_2\" data-name=\"Layer 2\"><g id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"/><path class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"/><rect class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"/></g></g></svg>", "svgComplete": "", "svgWhite": "<svg id=\"Layer_1\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1173.18 1173.18\" width='1173.18' height='1173.18'><defs><style>.cls-1{fill:#fff;}.cls-2{fill:none;}</style></defs><g id=\"Layer_2\" data-name=\"Layer 2\"><g id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"/><path class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"/><rect class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"/></g></g></svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/kumahq/charts/releases/download/kuma-2.10.1/kuma-2.10.1.tgz"}, "configuration": null, "component": {"version": "kuma.io/v1alpha1", "kind": "MeshLoadBalancingStrategy", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Spec is the specification of the Kuma MeshLoadBalancingStrategy resource.\",\n   \"properties\": {\n    \"targetRef\": {\n     \"description\": \"TargetRef is a reference to the resource the policy takes an effect on.\\nThe resource could be either a real store object or virtual resource\\ndefined inplace.\",\n     \"properties\": {\n      \"kind\": {\n       \"description\": \"Kind of the referenced resource\",\n       \"enum\": [\n        \"Mesh\",\n        \"MeshSubset\",\n        \"MeshGateway\",\n        \"MeshService\",\n        \"MeshExternalService\",\n        \"MeshMultiZoneService\",\n        \"MeshServiceSubset\",\n        \"MeshHTTPRoute\",\n        \"Dataplane\"\n       ],\n       \"type\": \"string\"\n      },\n      \"labels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n       \"type\": \"object\"\n      },\n      \"mesh\": {\n       \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n       \"type\": \"string\"\n      },\n      \"name\": {\n       \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n       \"type\": \"string\"\n      },\n      \"namespace\": {\n       \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n       \"type\": \"string\"\n      },\n      \"proxyTypes\": {\n       \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n       \"items\": {\n        \"enum\": [\n         \"Sidecar\",\n         \"Gateway\"\n        ],\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      },\n      \"sectionName\": {\n       \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n       \"type\": \"string\"\n      },\n      \"tags\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n       \"type\": \"object\"\n      }\n     },\n     \"required\": [\n      \"kind\"\n     ],\n     \"type\": \"object\"\n    },\n    \"to\": {\n     \"description\": \"To list makes a match between the consumed services and corresponding configurations\",\n     \"items\": {\n      \"properties\": {\n       \"default\": {\n        \"description\": \"Default is a configuration specific to the group of destinations referenced in\\n'targetRef'\",\n        \"properties\": {\n         \"loadBalancer\": {\n          \"description\": \"LoadBalancer allows to specify load balancing algorithm.\",\n          \"properties\": {\n           \"leastRequest\": {\n            \"description\": \"LeastRequest selects N random available hosts as specified in 'choiceCount' (2 by default)\\nand picks the host which has the fewest active requests\",\n            \"properties\": {\n             \"activeRequestBias\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"ActiveRequestBias refers to dynamic weights applied when hosts have varying load\\nbalancing weights. A higher value here aggressively reduces the weight of endpoints\\nthat are currently handling active requests. In essence, the higher the ActiveRequestBias\\nvalue, the more forcefully it reduces the load balancing weight of endpoints that are\\nactively serving requests.\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"choiceCount\": {\n              \"description\": \"ChoiceCount is the number of random healthy hosts from which the host with\\nthe fewest active requests will be chosen. Defaults to 2 so that Envoy performs\\ntwo-choice selection if the field is not set.\",\n              \"format\": \"int32\",\n              \"minimum\": 2,\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"maglev\": {\n            \"description\": \"Maglev implements consistent hashing to upstream hosts. Maglev can be used as\\na drop in replacement for the ring hash load balancer any place in which\\nconsistent hashing is desired.\",\n            \"properties\": {\n             \"hashPolicies\": {\n              \"description\": \"HashPolicies specify a list of request/connection properties that are used to calculate a hash.\\nThese hash policies are executed in the specified order. If a hash policy has the “terminal” attribute\\nset to true, and there is already a hash generated, the hash is returned immediately,\\nignoring the rest of the hash policy list.\",\n              \"items\": {\n               \"properties\": {\n                \"connection\": {\n                 \"properties\": {\n                  \"sourceIP\": {\n                   \"description\": \"Hash on source IP address.\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"cookie\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The name of the cookie that will be used to obtain the hash key.\",\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  },\n                  \"path\": {\n                   \"description\": \"The name of the path for the cookie.\",\n                   \"type\": \"string\"\n                  },\n                  \"ttl\": {\n                   \"description\": \"If specified, a cookie with the TTL will be generated if the cookie is not present.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"filterState\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The name of the Object in the per-request filterState, which is\\nan Envoy::Hashable object. If there is no data associated with the key,\\nor the stored object is not Envoy::Hashable, no hash will be produced.\",\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"header\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The name of the request header that will be used to obtain the hash key.\",\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"queryParameter\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The name of the URL query parameter that will be used to obtain the hash key.\\nIf the parameter is not present, no hash will be produced. Query parameter names\\nare case-sensitive.\",\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"terminal\": {\n                 \"description\": \"Terminal is a flag that short-circuits the hash computing. This field provides\\na ‘fallback’ style of configuration: “if a terminal policy doesn’t work, fallback\\nto rest of the policy list”, it saves time when the terminal policy works.\\nIf true, and there is already a hash computed, ignore rest of the list of hash polices.\",\n                 \"type\": \"boolean\"\n                },\n                \"type\": {\n                 \"enum\": [\n                  \"Header\",\n                  \"Cookie\",\n                  \"Connection\",\n                  \"SourceIP\",\n                  \"QueryParameter\",\n                  \"FilterState\"\n                 ],\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"tableSize\": {\n              \"description\": \"The table size for Maglev hashing. Maglev aims for “minimal disruption”\\nrather than an absolute guarantee. Minimal disruption means that when\\nthe set of upstream hosts change, a connection will likely be sent\\nto the same upstream as it was before. Increasing the table size reduces\\nthe amount of disruption. The table size must be prime number limited to 5000011.\\nIf it is not specified, the default is 65537.\",\n              \"format\": \"int32\",\n              \"maximum\": 5000011,\n              \"minimum\": 1,\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"random\": {\n            \"description\": \"Random selects a random available host. The random load balancer generally\\nperforms better than round-robin if no health checking policy is configured.\\nRandom selection avoids bias towards the host in the set that comes after a failed host.\",\n            \"type\": \"object\"\n           },\n           \"ringHash\": {\n            \"description\": \"RingHash  implements consistent hashing to upstream hosts. Each host is mapped\\nonto a circle (the “ring”) by hashing its address; each request is then routed\\nto a host by hashing some property of the request, and finding the nearest\\ncorresponding host clockwise around the ring.\",\n            \"properties\": {\n             \"hashFunction\": {\n              \"description\": \"HashFunction is a function used to hash hosts onto the ketama ring.\\nThe value defaults to XX_HASH. Available values – XX_HASH, MURMUR_HASH_2.\",\n              \"enum\": [\n               \"XXHash\",\n               \"MurmurHash2\"\n              ],\n              \"type\": \"string\"\n             },\n             \"hashPolicies\": {\n              \"description\": \"HashPolicies specify a list of request/connection properties that are used to calculate a hash.\\nThese hash policies are executed in the specified order. If a hash policy has the “terminal” attribute\\nset to true, and there is already a hash generated, the hash is returned immediately,\\nignoring the rest of the hash policy list.\",\n              \"items\": {\n               \"properties\": {\n                \"connection\": {\n                 \"properties\": {\n                  \"sourceIP\": {\n                   \"description\": \"Hash on source IP address.\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"cookie\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The name of the cookie that will be used to obtain the hash key.\",\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  },\n                  \"path\": {\n                   \"description\": \"The name of the path for the cookie.\",\n                   \"type\": \"string\"\n                  },\n                  \"ttl\": {\n                   \"description\": \"If specified, a cookie with the TTL will be generated if the cookie is not present.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"filterState\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The name of the Object in the per-request filterState, which is\\nan Envoy::Hashable object. If there is no data associated with the key,\\nor the stored object is not Envoy::Hashable, no hash will be produced.\",\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"header\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The name of the request header that will be used to obtain the hash key.\",\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"queryParameter\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The name of the URL query parameter that will be used to obtain the hash key.\\nIf the parameter is not present, no hash will be produced. Query parameter names\\nare case-sensitive.\",\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"terminal\": {\n                 \"description\": \"Terminal is a flag that short-circuits the hash computing. This field provides\\na ‘fallback’ style of configuration: “if a terminal policy doesn’t work, fallback\\nto rest of the policy list”, it saves time when the terminal policy works.\\nIf true, and there is already a hash computed, ignore rest of the list of hash polices.\",\n                 \"type\": \"boolean\"\n                },\n                \"type\": {\n                 \"enum\": [\n                  \"Header\",\n                  \"Cookie\",\n                  \"Connection\",\n                  \"SourceIP\",\n                  \"QueryParameter\",\n                  \"FilterState\"\n                 ],\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"maxRingSize\": {\n              \"description\": \"Maximum hash ring size. Defaults to 8M entries, and limited to 8M entries,\\nbut can be lowered to further constrain resource use.\",\n              \"format\": \"int32\",\n              \"maximum\": 8000000,\n              \"minimum\": 1,\n              \"type\": \"integer\"\n             },\n             \"minRingSize\": {\n              \"description\": \"Minimum hash ring size. The larger the ring is (that is,\\nthe more hashes there are for each provided host) the better the request distribution\\nwill reflect the desired weights. Defaults to 1024 entries, and limited to 8M entries.\",\n              \"format\": \"int32\",\n              \"maximum\": 8000000,\n              \"minimum\": 1,\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"roundRobin\": {\n            \"description\": \"RoundRobin is a load balancing algorithm that distributes requests\\nacross available upstream hosts in round-robin order.\",\n            \"type\": \"object\"\n           },\n           \"type\": {\n            \"enum\": [\n             \"RoundRobin\",\n             \"LeastRequest\",\n             \"RingHash\",\n             \"Random\",\n             \"Maglev\"\n            ],\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"localityAwareness\": {\n          \"description\": \"LocalityAwareness contains configuration for locality aware load balancing.\",\n          \"properties\": {\n           \"crossZone\": {\n            \"description\": \"CrossZone defines locality aware load balancing priorities when dataplane proxies inside local zone\\nare unavailable\",\n            \"properties\": {\n             \"failover\": {\n              \"description\": \"Failover defines list of load balancing rules in order of priority\",\n              \"items\": {\n               \"properties\": {\n                \"from\": {\n                 \"description\": \"From defines the list of zones to which the rule applies\",\n                 \"properties\": {\n                  \"zones\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\"\n                  }\n                 },\n                 \"required\": [\n                  \"zones\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"to\": {\n                 \"description\": \"To defines to which zones the traffic should be load balanced\",\n                 \"properties\": {\n                  \"type\": {\n                   \"description\": \"Type defines how target zones will be picked from available zones\",\n                   \"enum\": [\n                    \"None\",\n                    \"Only\",\n                    \"Any\",\n                    \"AnyExcept\"\n                   ],\n                   \"type\": \"string\"\n                  },\n                  \"zones\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\"\n                  }\n                 },\n                 \"required\": [\n                  \"type\"\n                 ],\n                 \"type\": \"object\"\n                }\n               },\n               \"required\": [\n                \"to\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"failoverThreshold\": {\n              \"description\": \"FailoverThreshold defines the percentage of live destination dataplane proxies below which load balancing to the\\nnext priority starts.\\nExample: If you configure failoverThreshold to 70, and you have deployed 10 destination dataplane proxies.\\nLoad balancing to next priority will start when number of live destination dataplane proxies drops below 7.\\nDefault 50\",\n              \"properties\": {\n               \"percentage\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"percentage\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"disabled\": {\n            \"description\": \"Disabled allows to disable locality-aware load balancing.\\nWhen disabled requests are distributed across all endpoints regardless of locality.\",\n            \"type\": \"boolean\"\n           },\n           \"localZone\": {\n            \"description\": \"LocalZone defines locality aware load balancing priorities between dataplane proxies inside a zone\",\n            \"properties\": {\n             \"affinityTags\": {\n              \"description\": \"AffinityTags list of tags for local zone load balancing.\",\n              \"items\": {\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"Key defines tag for which affinity is configured\",\n                 \"type\": \"string\"\n                },\n                \"weight\": {\n                 \"description\": \"Weight of the tag used for load balancing. The bigger the weight the bigger the priority.\\nPercentage of local traffic load balanced to tag is computed by dividing weight by sum of weights from all tags.\\nFor example with two affinity tags first with weight 80 and second with weight 20,\\nthen 80% of traffic will be redirected to the first tag, and 20% of traffic will be redirected to second one.\\nSetting weights is not mandatory. When weights are not set control plane will compute default weight based on list order.\\nDefault: If you do not specify weight we will adjust them so that 90% traffic goes to first tag, 9% to next, and 1% to third and so on.\",\n                 \"format\": \"int32\",\n                 \"type\": \"integer\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"targetRef\": {\n        \"description\": \"TargetRef is a reference to the resource that represents a group of\\ndestinations.\",\n        \"properties\": {\n         \"kind\": {\n          \"description\": \"Kind of the referenced resource\",\n          \"enum\": [\n           \"Mesh\",\n           \"MeshSubset\",\n           \"MeshGateway\",\n           \"MeshService\",\n           \"MeshExternalService\",\n           \"MeshMultiZoneService\",\n           \"MeshServiceSubset\",\n           \"MeshHTTPRoute\",\n           \"Dataplane\"\n          ],\n          \"type\": \"string\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n          \"type\": \"object\"\n         },\n         \"mesh\": {\n          \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n          \"type\": \"string\"\n         },\n         \"proxyTypes\": {\n          \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n          \"items\": {\n           \"enum\": [\n            \"Sidecar\",\n            \"Gateway\"\n           ],\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"sectionName\": {\n          \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n          \"type\": \"string\"\n         },\n         \"tags\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"kind\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"targetRef\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Mesh Load Balancing Strategy\",\n \"type\": \"object\"\n}"}}