{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Workflow", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubevela", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "App Definition and Development"}, "subCategory": "Application Definition & Image Build", "metadata": {"isAnnotation": false, "primaryColor": "#006fff", "secondaryColor": "#45B4FF\n", "shape": "circle", "source_uri": "git://github.com/kubevela/kubevela/master/charts/vela-core/crds", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"图层_1\" data-name=\"图层 1\" viewBox=\"0 0 122.5 122.5\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#006fff;}</style></defs><title xmlns=\"http://www.w3.org/2000/svg\">KubeVela </title><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"图层_1\" data-name=\"图层 1\" viewBox=\"0 0 122.5 122.5\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff;}</style></defs><title xmlns=\"http://www.w3.org/2000/svg\">KubeVela </title><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"></path></svg>"}, "model": {"version": "v1.10.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#006fff", "secondaryColor": "#45B4FF\n", "shape": "circle", "svgColor": "<svg id=\"图层_1\" data-name=\"图层 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 122.5 122.5\"><defs><style>.cls-1{fill:#006fff;}</style></defs><title><PERSON><PERSON><PERSON><PERSON> </title><path d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"/><path class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"/><path class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"/><path d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"/></svg>", "svgComplete": "", "svgWhite": "<svg id=\"图层_1\" data-name=\"图层 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 122.5 122.5\"><defs><style>.cls-1{fill:#fff;}</style></defs><title><PERSON><PERSON><PERSON><PERSON> </title><path class=\"cls-1\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"/><path class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"/><path class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"/><path class=\"cls-1\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/kubevela/kubevela/master/charts/vela-core/crds"}, "configuration": null, "component": {"version": "core.oam.dev/v1alpha1", "kind": "Workflow", "schema": "{\n \"description\": \"Workflow is the Schema for the workflow API\",\n \"properties\": {\n  \"mode\": {\n   \"description\": \"WorkflowExecuteMode defines the mode of workflow execution\",\n   \"properties\": {\n    \"steps\": {\n     \"description\": \"Steps is the mode of workflow steps execution\",\n     \"type\": \"string\"\n    },\n    \"subSteps\": {\n     \"description\": \"SubSteps is the mode of workflow sub steps execution\",\n     \"type\": \"string\"\n    }\n   },\n   \"type\": \"object\"\n  },\n  \"steps\": {\n   \"items\": {\n    \"description\": \"WorkflowStep defines how to execute a workflow step.\",\n    \"properties\": {\n     \"dependsOn\": {\n      \"description\": \"DependsOn is the dependency of the step\",\n      \"items\": {\n       \"type\": \"string\"\n      },\n      \"type\": \"array\"\n     },\n     \"if\": {\n      \"description\": \"If is the if condition of the step\",\n      \"type\": \"string\"\n     },\n     \"inputs\": {\n      \"description\": \"Inputs is the inputs of the step\",\n      \"items\": {\n       \"properties\": {\n        \"from\": {\n         \"type\": \"string\"\n        },\n        \"parameterKey\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"from\",\n        \"parameterKey\"\n       ],\n       \"type\": \"object\"\n      },\n      \"type\": \"array\"\n     },\n     \"meta\": {\n      \"description\": \"Meta is the meta data of the workflow step.\",\n      \"properties\": {\n       \"alias\": {\n        \"type\": \"string\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"name\": {\n      \"description\": \"Name is the unique name of the workflow step.\",\n      \"type\": \"string\"\n     },\n     \"outputs\": {\n      \"description\": \"Outputs is the outputs of the step\",\n      \"items\": {\n       \"properties\": {\n        \"name\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"name\",\n        \"valueFrom\"\n       ],\n       \"type\": \"object\"\n      },\n      \"type\": \"array\"\n     },\n     \"properties\": {\n      \"description\": \"Properties is the properties of the step\",\n      \"format\": \"textarea\",\n      \"type\": \"string\"\n     },\n     \"subSteps\": {\n      \"items\": {\n       \"description\": \"WorkflowStepBase defines the workflow step base\",\n       \"properties\": {\n        \"dependsOn\": {\n         \"description\": \"DependsOn is the dependency of the step\",\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"if\": {\n         \"description\": \"If is the if condition of the step\",\n         \"type\": \"string\"\n        },\n        \"inputs\": {\n         \"description\": \"Inputs is the inputs of the step\",\n         \"items\": {\n          \"properties\": {\n           \"from\": {\n            \"type\": \"string\"\n           },\n           \"parameterKey\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"from\",\n           \"parameterKey\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"meta\": {\n         \"description\": \"Meta is the meta data of the workflow step.\",\n         \"properties\": {\n          \"alias\": {\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"name\": {\n         \"description\": \"Name is the unique name of the workflow step.\",\n         \"type\": \"string\"\n        },\n        \"outputs\": {\n         \"description\": \"Outputs is the outputs of the step\",\n         \"items\": {\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"valueFrom\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\",\n           \"valueFrom\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"properties\": {\n         \"description\": \"Properties is the properties of the step\",\n         \"format\": \"textarea\",\n         \"type\": \"string\"\n        },\n        \"timeout\": {\n         \"description\": \"Timeout is the timeout of the step\",\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"description\": \"Type is the type of the workflow step.\",\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"name\",\n        \"type\"\n       ],\n       \"type\": \"object\"\n      },\n      \"type\": \"array\"\n     },\n     \"timeout\": {\n      \"description\": \"Timeout is the timeout of the step\",\n      \"type\": \"string\"\n     },\n     \"type\": {\n      \"description\": \"Type is the type of the workflow step.\",\n      \"type\": \"string\"\n     }\n    },\n    \"required\": [\n     \"name\",\n     \"type\"\n    ],\n    \"type\": \"object\"\n   },\n   \"type\": \"array\"\n  }\n },\n \"title\": \"Workflow\",\n \"type\": \"object\"\n}"}}