{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "<PERSON><PERSON>", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kuma", "displayName": "<PERSON><PERSON>", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Cloud Native Network"}, "subCategory": "Service Mesh", "metadata": {"isAnnotation": false, "primaryColor": "#291953", "secondaryColor": "#6942c9", "shape": "circle", "source_uri": "https://github.com/kumahq/charts/releases/download/kuma-2.11.0/kuma-2.11.0.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1\" data-name=\"Layer 1\" viewBox=\"0 0 1173.18 1173.18\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#291953;}.cls-2{fill:none;}</style></defs><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_2\" data-name=\"Layer 2\"><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"></path><rect xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"></rect></g></g></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1\" data-name=\"Layer 1\" viewBox=\"0 0 1173.18 1173.18\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff;}.cls-2{fill:none;}</style></defs><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_2\" data-name=\"Layer 2\"><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"></path><rect xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"></rect></g></g></svg>"}, "model": {"version": "2.11.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#291953", "secondaryColor": "#6942c9", "shape": "circle", "svgColor": "<svg id=\"Layer_1\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1173.18 1173.18\"><defs><style>.cls-1{fill:#291953;}.cls-2{fill:none;}</style></defs><g id=\"Layer_2\" data-name=\"Layer 2\"><g id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"/><path class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"/><rect class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"/></g></g></svg>", "svgComplete": "", "svgWhite": "<svg id=\"Layer_1\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1173.18 1173.18\" width='1173.18' height='1173.18'><defs><style>.cls-1{fill:#fff;}.cls-2{fill:none;}</style></defs><g id=\"Layer_2\" data-name=\"Layer 2\"><g id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"/><path class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"/><rect class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"/></g></g></svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/kumahq/charts/releases/download/kuma-2.11.0/kuma-2.11.0.tgz"}, "configuration": null, "component": {"version": "kuma.io/v1alpha1", "kind": "MeshRetry", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Spec is the specification of the Kuma MeshRetry resource.\",\n   \"properties\": {\n    \"targetRef\": {\n     \"description\": \"TargetRef is a reference to the resource the policy takes an effect on.\\nThe resource could be either a real store object or virtual resource\\ndefined inplace.\",\n     \"properties\": {\n      \"kind\": {\n       \"description\": \"Kind of the referenced resource\",\n       \"enum\": [\n        \"Mesh\",\n        \"MeshSubset\",\n        \"MeshGateway\",\n        \"MeshService\",\n        \"MeshExternalService\",\n        \"MeshMultiZoneService\",\n        \"MeshServiceSubset\",\n        \"MeshHTTPRoute\",\n        \"Dataplane\"\n       ],\n       \"type\": \"string\"\n      },\n      \"labels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n       \"type\": \"object\"\n      },\n      \"mesh\": {\n       \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n       \"type\": \"string\"\n      },\n      \"name\": {\n       \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n       \"type\": \"string\"\n      },\n      \"namespace\": {\n       \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n       \"type\": \"string\"\n      },\n      \"proxyTypes\": {\n       \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n       \"items\": {\n        \"enum\": [\n         \"Sidecar\",\n         \"Gateway\"\n        ],\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      },\n      \"sectionName\": {\n       \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n       \"type\": \"string\"\n      },\n      \"tags\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n       \"type\": \"object\"\n      }\n     },\n     \"required\": [\n      \"kind\"\n     ],\n     \"type\": \"object\"\n    },\n    \"to\": {\n     \"description\": \"To list makes a match between the consumed services and corresponding configurations\",\n     \"items\": {\n      \"properties\": {\n       \"default\": {\n        \"description\": \"Default is a configuration specific to the group of destinations referenced in\\n'targetRef'\",\n        \"properties\": {\n         \"grpc\": {\n          \"description\": \"GRPC defines a configuration of retries for GRPC traffic\",\n          \"properties\": {\n           \"backOff\": {\n            \"description\": \"BackOff is a configuration of durations which will be used in an exponential\\nbackoff strategy between retries.\",\n            \"properties\": {\n             \"baseInterval\": {\n              \"description\": \"BaseInterval is an amount of time which should be taken between retries.\\nMust be greater than zero. Values less than 1 ms are rounded up to 1 ms.\\nIf not specified then the default value is \\\"25ms\\\".\",\n              \"type\": \"string\"\n             },\n             \"maxInterval\": {\n              \"description\": \"MaxInterval is a maximal amount of time which will be taken between retries.\\nDefault is 10 times the \\\"BaseInterval\\\".\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"numRetries\": {\n            \"description\": \"NumRetries is the number of attempts that will be made on failed (and\\nretriable) requests. If not set, the default value is 1.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"perTryTimeout\": {\n            \"description\": \"PerTryTimeout is the maximum amount of time each retry attempt can take\\nbefore it times out. If not set, the global request timeout for the route\\nwill be used. Setting this value to 0 will disable the per-try timeout.\",\n            \"type\": \"string\"\n           },\n           \"rateLimitedBackOff\": {\n            \"description\": \"RateLimitedBackOff is a configuration of backoff which will be used when\\nthe upstream returns one of the headers configured.\",\n            \"properties\": {\n             \"maxInterval\": {\n              \"description\": \"MaxInterval is a maximal amount of time which will be taken between retries.\\nIf not specified then the default value is \\\"300s\\\".\",\n              \"type\": \"string\"\n             },\n             \"resetHeaders\": {\n              \"description\": \"ResetHeaders specifies the list of headers (like Retry-After or X-RateLimit-Reset)\\nto match against the response. Headers are tried in order, and matched\\ncase-insensitive. The first header to be parsed successfully is used.\\nIf no headers match the default exponential BackOff is used instead.\",\n              \"items\": {\n               \"properties\": {\n                \"format\": {\n                 \"description\": \"The format of the reset header.\",\n                 \"enum\": [\n                  \"Seconds\",\n                  \"UnixTimestamp\"\n                 ],\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"description\": \"The Name of the reset header.\",\n                 \"maxLength\": 256,\n                 \"minLength\": 1,\n                 \"pattern\": \"^[a-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"format\",\n                \"name\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"retryOn\": {\n            \"description\": \"RetryOn is a list of conditions which will cause a retry.\",\n            \"example\": [\n             \"Canceled\",\n             \"DeadlineExceeded\",\n             \"Internal\",\n             \"ResourceExhausted\",\n             \"Unavailable\"\n            ],\n            \"items\": {\n             \"enum\": [\n              \"Canceled\",\n              \"DeadlineExceeded\",\n              \"Internal\",\n              \"ResourceExhausted\",\n              \"Unavailable\"\n             ],\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"http\": {\n          \"description\": \"HTTP defines a configuration of retries for HTTP traffic\",\n          \"properties\": {\n           \"backOff\": {\n            \"description\": \"BackOff is a configuration of durations which will be used in exponential\\nbackoff strategy between retries.\",\n            \"properties\": {\n             \"baseInterval\": {\n              \"description\": \"BaseInterval is an amount of time which should be taken between retries.\\nMust be greater than zero. Values less than 1 ms are rounded up to 1 ms.\\nIf not specified then the default value is \\\"25ms\\\".\",\n              \"type\": \"string\"\n             },\n             \"maxInterval\": {\n              \"description\": \"MaxInterval is a maximal amount of time which will be taken between retries.\\nDefault is 10 times the \\\"BaseInterval\\\".\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"hostSelection\": {\n            \"description\": \"HostSelection is a list of predicates that dictate how hosts should be selected\\nwhen requests are retried.\",\n            \"items\": {\n             \"properties\": {\n              \"predicate\": {\n               \"description\": \"Type is requested predicate mode.\",\n               \"enum\": [\n                \"OmitPreviousHosts\",\n                \"OmitHostsWithTags\",\n                \"OmitPreviousPriorities\"\n               ],\n               \"type\": \"string\"\n              },\n              \"tags\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"description\": \"Tags is a map of metadata to match against for selecting the omitted hosts. Required if Type is\\nOmitHostsWithTags\",\n               \"type\": \"object\"\n              },\n              \"updateFrequency\": {\n               \"default\": 2,\n               \"description\": \"UpdateFrequency is how often the priority load should be updated based on previously attempted priorities.\\nUsed for OmitPreviousPriorities.\",\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              }\n             },\n             \"required\": [\n              \"predicate\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"hostSelectionMaxAttempts\": {\n            \"description\": \"HostSelectionMaxAttempts is the maximum number of times host selection will be\\nreattempted before giving up, at which point the host that was last selected will\\nbe routed to. If unspecified, this will default to retrying once.\",\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"numRetries\": {\n            \"description\": \"NumRetries is the number of attempts that will be made on failed (and\\nretriable) requests.  If not set, the default value is 1.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"perTryTimeout\": {\n            \"description\": \"PerTryTimeout is the amount of time after which retry attempt should time out.\\nIf left unspecified, the global route timeout for the request will be used.\\nConsequently, when using a 5xx based retry policy, a request that times out\\nwill not be retried as the total timeout budget would have been exhausted.\\nSetting this timeout to 0 will disable it.\",\n            \"type\": \"string\"\n           },\n           \"rateLimitedBackOff\": {\n            \"description\": \"RateLimitedBackOff is a configuration of backoff which will be used\\nwhen the upstream returns one of the headers configured.\",\n            \"properties\": {\n             \"maxInterval\": {\n              \"description\": \"MaxInterval is a maximal amount of time which will be taken between retries.\\nIf not specified then the default value is \\\"300s\\\".\",\n              \"type\": \"string\"\n             },\n             \"resetHeaders\": {\n              \"description\": \"ResetHeaders specifies the list of headers (like Retry-After or X-RateLimit-Reset)\\nto match against the response. Headers are tried in order, and matched\\ncase-insensitive. The first header to be parsed successfully is used.\\nIf no headers match the default exponential BackOff is used instead.\",\n              \"items\": {\n               \"properties\": {\n                \"format\": {\n                 \"description\": \"The format of the reset header.\",\n                 \"enum\": [\n                  \"Seconds\",\n                  \"UnixTimestamp\"\n                 ],\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"description\": \"The Name of the reset header.\",\n                 \"maxLength\": 256,\n                 \"minLength\": 1,\n                 \"pattern\": \"^[a-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"format\",\n                \"name\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"retriableRequestHeaders\": {\n            \"description\": \"RetriableRequestHeaders is an HTTP headers which must be present in the request\\nfor retries to be attempted.\",\n            \"items\": {\n             \"description\": \"HeaderMatch describes how to select an HTTP route by matching HTTP request\\nheaders.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name is the name of the HTTP Header to be matched. Name MUST be lower case\\nas they will be handled with case insensitivity (See https://tools.ietf.org/html/rfc7230#section-3.2).\",\n               \"maxLength\": 256,\n               \"minLength\": 1,\n               \"pattern\": \"^[a-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n               \"type\": \"string\"\n              },\n              \"type\": {\n               \"default\": \"Exact\",\n               \"description\": \"Type specifies how to match against the value of the header.\",\n               \"enum\": [\n                \"Exact\",\n                \"Present\",\n                \"RegularExpression\",\n                \"Absent\",\n                \"Prefix\"\n               ],\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"Value is the value of HTTP Header to be matched.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"retriableResponseHeaders\": {\n            \"description\": \"RetriableResponseHeaders is an HTTP response headers that trigger a retry\\nif present in the response. A retry will be triggered if any of the header\\nmatches the upstream response headers.\",\n            \"items\": {\n             \"description\": \"HeaderMatch describes how to select an HTTP route by matching HTTP request\\nheaders.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name is the name of the HTTP Header to be matched. Name MUST be lower case\\nas they will be handled with case insensitivity (See https://tools.ietf.org/html/rfc7230#section-3.2).\",\n               \"maxLength\": 256,\n               \"minLength\": 1,\n               \"pattern\": \"^[a-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n               \"type\": \"string\"\n              },\n              \"type\": {\n               \"default\": \"Exact\",\n               \"description\": \"Type specifies how to match against the value of the header.\",\n               \"enum\": [\n                \"Exact\",\n                \"Present\",\n                \"RegularExpression\",\n                \"Absent\",\n                \"Prefix\"\n               ],\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"Value is the value of HTTP Header to be matched.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"retryOn\": {\n            \"description\": \"RetryOn is a list of conditions which will cause a retry. Available values are:\\n[5XX, GatewayError, Reset, Retriable4xx, ConnectFailure, EnvoyRatelimited,\\nRefusedStream, Http3PostConnectFailure, HttpMethodConnect, HttpMethodDelete,\\nHttpMethodGet, HttpMethodHead, HttpMethodOptions, HttpMethodPatch,\\nHttpMethodPost, HttpMethodPut, HttpMethodTrace].\\nAlso, any HTTP status code (500, 503, etc.).\",\n            \"example\": [\n             \"5XX\",\n             \"GatewayError\",\n             \"Reset\",\n             \"Retriable4xx\",\n             \"ConnectFailure\",\n             \"EnvoyRatelimited\",\n             \"RefusedStream\",\n             \"Http3PostConnectFailure\",\n             \"HttpMethodConnect\",\n             \"HttpMethodDelete\",\n             \"HttpMethodGet\",\n             \"HttpMethodHead\",\n             \"HttpMethodOptions\",\n             \"HttpMethodPatch\",\n             \"HttpMethodPost\",\n             \"HttpMethodPut\",\n             \"HttpMethodTrace\",\n             \"500\",\n             \"503\"\n            ],\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"tcp\": {\n          \"description\": \"TCP defines a configuration of retries for TCP traffic\",\n          \"properties\": {\n           \"maxConnectAttempt\": {\n            \"description\": \"MaxConnectAttempt is a maximal amount of TCP connection attempts\\nwhich will be made before giving up\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"targetRef\": {\n        \"description\": \"TargetRef is a reference to the resource that represents a group of\\ndestinations.\",\n        \"properties\": {\n         \"kind\": {\n          \"description\": \"Kind of the referenced resource\",\n          \"enum\": [\n           \"Mesh\",\n           \"MeshSubset\",\n           \"MeshGateway\",\n           \"MeshService\",\n           \"MeshExternalService\",\n           \"MeshMultiZoneService\",\n           \"MeshServiceSubset\",\n           \"MeshHTTPRoute\",\n           \"Dataplane\"\n          ],\n          \"type\": \"string\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n          \"type\": \"object\"\n         },\n         \"mesh\": {\n          \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n          \"type\": \"string\"\n         },\n         \"proxyTypes\": {\n          \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n          \"items\": {\n           \"enum\": [\n            \"Sidecar\",\n            \"Gateway\"\n           ],\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"sectionName\": {\n          \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n          \"type\": \"string\"\n         },\n         \"tags\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"kind\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"targetRef\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Mesh Retry\",\n \"type\": \"object\"\n}"}}