{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Component Definition", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubevela", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "App Definition and Development"}, "subCategory": "Application Definition & Image Build", "metadata": {"isAnnotation": false, "primaryColor": "#006fff", "secondaryColor": "#45B4FF\n", "shape": "circle", "source_uri": "git://github.com/kubevela/kubevela/master/charts/vela-core/crds", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"图层_1\" data-name=\"图层 1\" viewBox=\"0 0 122.5 122.5\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#006fff;}</style></defs><title xmlns=\"http://www.w3.org/2000/svg\">KubeVela </title><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"图层_1\" data-name=\"图层 1\" viewBox=\"0 0 122.5 122.5\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff;}</style></defs><title xmlns=\"http://www.w3.org/2000/svg\">KubeVela </title><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"></path></svg>"}, "model": {"version": "v1.10.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#006fff", "secondaryColor": "#45B4FF\n", "shape": "circle", "svgColor": "<svg id=\"图层_1\" data-name=\"图层 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 122.5 122.5\"><defs><style>.cls-1{fill:#006fff;}</style></defs><title><PERSON><PERSON><PERSON><PERSON> </title><path d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"/><path class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"/><path class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"/><path d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"/></svg>", "svgComplete": "", "svgWhite": "<svg id=\"图层_1\" data-name=\"图层 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 122.5 122.5\"><defs><style>.cls-1{fill:#fff;}</style></defs><title><PERSON><PERSON><PERSON><PERSON> </title><path class=\"cls-1\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"/><path class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"/><path class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"/><path class=\"cls-1\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/kubevela/kubevela/master/charts/vela-core/crds"}, "configuration": null, "component": {"version": "core.oam.dev/v1beta1", "kind": "ComponentDefinition", "schema": "{\n \"description\": \"ComponentDefinition is the Schema for the componentdefinitions API\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"ComponentDefinitionSpec defines the desired state of ComponentDefinition\",\n   \"properties\": {\n    \"childResourceKinds\": {\n     \"description\": \"ChildResourceKinds are the list of GVK of the child resources this workload generates\",\n     \"items\": {\n      \"description\": \"A ChildResourceKind defines a child Kubernetes resource kind with a selector\",\n      \"properties\": {\n       \"apiVersion\": {\n        \"description\": \"APIVersion of the child resource\",\n        \"type\": \"string\"\n       },\n       \"kind\": {\n        \"description\": \"Kind of the child resource\",\n        \"type\": \"string\"\n       },\n       \"selector\": {\n        \"additionalProperties\": {\n         \"type\": \"string\"\n        },\n        \"description\": \"Selector to select the child resources that the workload wants to expose to traits\",\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"apiVersion\",\n       \"kind\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"extension\": {\n     \"description\": \"Extension is used for extension needs by OAM platform builders\",\n     \"format\": \"textarea\",\n     \"type\": \"string\"\n    },\n    \"podSpecPath\": {\n     \"description\": \"PodSpecPath indicates where/if this workload has K8s podSpec field\\nif one workload has podSpec, trait can do lot's of assumption such as port, env, volume fields.\",\n     \"type\": \"string\"\n    },\n    \"revisionLabel\": {\n     \"description\": \"RevisionLabel indicates which label for underlying resources(e.g. pods) of this workload\\ncan be used by trait to create resource selectors(e.g. label selector for pods).\",\n     \"type\": \"string\"\n    },\n    \"schematic\": {\n     \"description\": \"Schematic defines the data format and template of the encapsulation of the workload\",\n     \"properties\": {\n      \"cue\": {\n       \"description\": \"CUE defines the encapsulation in CUE format\",\n       \"properties\": {\n        \"template\": {\n         \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"template\"\n       ],\n       \"type\": \"object\"\n      },\n      \"terraform\": {\n       \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n       \"properties\": {\n        \"configuration\": {\n         \"description\": \"Configuration is Terraform Configuration\",\n         \"type\": \"string\"\n        },\n        \"customRegion\": {\n         \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n         \"type\": \"string\"\n        },\n        \"deleteResource\": {\n         \"default\": true,\n         \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n         \"type\": \"boolean\"\n        },\n        \"gitCredentialsSecretReference\": {\n         \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"name is unique within a namespace to reference a secret resource.\",\n           \"type\": \"string\"\n          },\n          \"namespace\": {\n           \"description\": \"namespace defines the space within which the secret name must be unique.\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"path\": {\n         \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n         \"type\": \"string\"\n        },\n        \"providerRef\": {\n         \"description\": \"ProviderReference specifies the reference to Provider\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name of the referenced object.\",\n           \"type\": \"string\"\n          },\n          \"namespace\": {\n           \"default\": \"default\",\n           \"description\": \"Namespace of the referenced object.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": {\n         \"default\": \"hcl\",\n         \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n         \"enum\": [\n          \"hcl\",\n          \"json\",\n          \"remote\"\n         ],\n         \"type\": \"string\"\n        },\n        \"writeConnectionSecretToRef\": {\n         \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name of the secret.\",\n           \"type\": \"string\"\n          },\n          \"namespace\": {\n           \"description\": \"Namespace of the secret.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"required\": [\n        \"configuration\"\n       ],\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"status\": {\n     \"description\": \"Status defines the custom health policy and status message for workload\",\n     \"properties\": {\n      \"customStatus\": {\n       \"description\": \"CustomStatus defines the custom status message that could display to user\",\n       \"type\": \"string\"\n      },\n      \"healthPolicy\": {\n       \"description\": \"HealthPolicy defines the health check policy for the abstraction\",\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"version\": {\n     \"type\": \"string\"\n    },\n    \"workload\": {\n     \"description\": \"Workload is a workload type descriptor\",\n     \"properties\": {\n      \"definition\": {\n       \"description\": \"Definition mutually exclusive to workload.type, a embedded WorkloadDefinition\",\n       \"properties\": {\n        \"apiVersion\": {\n         \"type\": \"string\"\n        },\n        \"kind\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"apiVersion\",\n        \"kind\"\n       ],\n       \"type\": \"object\"\n      },\n      \"type\": {\n       \"description\": \"Type ref to a WorkloadDefinition via name\",\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    }\n   },\n   \"required\": [\n    \"workload\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Component Definition\",\n \"type\": \"object\"\n}"}}