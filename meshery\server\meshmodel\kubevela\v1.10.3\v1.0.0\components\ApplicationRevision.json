{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Application Revision", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubevela", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "App Definition and Development"}, "subCategory": "Application Definition & Image Build", "metadata": {"isAnnotation": false, "primaryColor": "#006fff", "secondaryColor": "#45B4FF\n", "shape": "circle", "source_uri": "git://github.com/kubevela/kubevela/master/charts/vela-core/crds", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"图层_1\" data-name=\"图层 1\" viewBox=\"0 0 122.5 122.5\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#006fff;}</style></defs><title xmlns=\"http://www.w3.org/2000/svg\">KubeVela </title><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"图层_1\" data-name=\"图层 1\" viewBox=\"0 0 122.5 122.5\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff;}</style></defs><title xmlns=\"http://www.w3.org/2000/svg\">KubeVela </title><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"></path></svg>"}, "model": {"version": "v1.10.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#006fff", "secondaryColor": "#45B4FF\n", "shape": "circle", "svgColor": "<svg id=\"图层_1\" data-name=\"图层 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 122.5 122.5\"><defs><style>.cls-1{fill:#006fff;}</style></defs><title><PERSON><PERSON><PERSON><PERSON> </title><path d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"/><path class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"/><path class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"/><path d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"/></svg>", "svgComplete": "", "svgWhite": "<svg id=\"图层_1\" data-name=\"图层 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 122.5 122.5\"><defs><style>.cls-1{fill:#fff;}</style></defs><title><PERSON><PERSON><PERSON><PERSON> </title><path class=\"cls-1\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"/><path class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"/><path class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"/><path class=\"cls-1\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/kubevela/kubevela/master/charts/vela-core/crds"}, "configuration": null, "component": {"version": "core.oam.dev/v1beta1", "kind": "ApplicationRevision", "schema": "{\n \"description\": \"ApplicationRevision is the Schema for the ApplicationRevision API\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"ApplicationRevisionSpec is the spec of ApplicationRevision\",\n   \"properties\": {\n    \"application\": {\n     \"description\": \"Application records the snapshot of the created/modified Application\",\n     \"properties\": {\n      \"apiVersion\": {\n       \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n       \"type\": \"string\"\n      },\n      \"kind\": {\n       \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n       \"type\": \"string\"\n      },\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"finalizers\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"name\": {\n         \"type\": \"string\"\n        },\n        \"namespace\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"description\": \"ApplicationSpec is the spec of Application\",\n       \"properties\": {\n        \"components\": {\n         \"items\": {\n          \"description\": \"ApplicationComponent describe the component of application\",\n          \"properties\": {\n           \"dependsOn\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"externalRevision\": {\n            \"description\": \"ExternalRevision specified the component revisionName\",\n            \"type\": \"string\"\n           },\n           \"inputs\": {\n            \"description\": \"StepInputs defines variable input of WorkflowStep\",\n            \"items\": {\n             \"description\": \"InputItem defines an input variable of WorkflowStep\",\n             \"properties\": {\n              \"from\": {\n               \"type\": \"string\"\n              },\n              \"parameterKey\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"from\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"outputs\": {\n            \"description\": \"StepOutputs defines output variable of WorkflowStep\",\n            \"items\": {\n             \"description\": \"OutputItem defines an output variable of WorkflowStep\",\n             \"properties\": {\n              \"name\": {\n               \"type\": \"string\"\n              },\n              \"valueFrom\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"valueFrom\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"properties\": {\n            \"format\": \"textarea\",\n            \"type\": \"string\"\n           },\n           \"scopes\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"description\": \"scopes in ApplicationComponent defines the component-level scopes\\nthe format is \\u003cscope-type:scope-instance-name\\u003e pairs, the key represents type of `ScopeDefinition` while the value represent the name of scope instance.\",\n            \"format\": \"textarea\",\n            \"type\": \"string\"\n           },\n           \"traits\": {\n            \"description\": \"Traits define the trait of one component, the type must be array to keep the order.\",\n            \"items\": {\n             \"description\": \"ApplicationTrait defines the trait of application\",\n             \"properties\": {\n              \"properties\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"type\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"type\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"type\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\",\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"policies\": {\n         \"description\": \"Policies defines the global policies for all components in the app, e.g. security, metrics, gitops,\\nmulti-cluster placement rules, etc.\\nPolicies are applied after components are rendered and before workflow steps are executed.\",\n         \"items\": {\n          \"description\": \"AppPolicy defines a global policy for all components in the app.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name is the unique name of the policy.\",\n            \"type\": \"string\"\n           },\n           \"properties\": {\n            \"format\": \"textarea\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type is the type of the policy\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"workflow\": {\n         \"description\": \"Workflow defines how to customize the control logic.\\nIf workflow is specified, Vela won't apply any resource, but provide rendered output in AppRevision.\\nWorkflow steps are executed in array order, and each step:\\n- will have a context in annotation.\\n- should mark \\\"finish\\\" phase in status.conditions.\",\n         \"properties\": {\n          \"mode\": {\n           \"description\": \"WorkflowExecuteMode defines the mode of workflow execution\",\n           \"properties\": {\n            \"steps\": {\n             \"description\": \"Steps is the mode of workflow steps execution\",\n             \"type\": \"string\"\n            },\n            \"subSteps\": {\n             \"description\": \"SubSteps is the mode of workflow sub steps execution\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"ref\": {\n           \"type\": \"string\"\n          },\n          \"steps\": {\n           \"items\": {\n            \"description\": \"WorkflowStep defines how to execute a workflow step.\",\n            \"properties\": {\n             \"dependsOn\": {\n              \"description\": \"DependsOn is the dependency of the step\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"if\": {\n              \"description\": \"If is the if condition of the step\",\n              \"type\": \"string\"\n             },\n             \"inputs\": {\n              \"description\": \"Inputs is the inputs of the step\",\n              \"items\": {\n               \"description\": \"InputItem defines an input variable of WorkflowStep\",\n               \"properties\": {\n                \"from\": {\n                 \"type\": \"string\"\n                },\n                \"parameterKey\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"from\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"meta\": {\n              \"description\": \"Meta is the meta data of the workflow step.\",\n              \"properties\": {\n               \"alias\": {\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"mode\": {\n              \"description\": \"Mode is only valid for sub steps, it defines the mode of the sub steps\",\n              \"nullable\": true,\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name is the unique name of the workflow step.\",\n              \"type\": \"string\"\n             },\n             \"outputs\": {\n              \"description\": \"Outputs is the outputs of the step\",\n              \"items\": {\n               \"description\": \"OutputItem defines an output variable of WorkflowStep\",\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"valueFrom\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"valueFrom\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"properties\": {\n              \"description\": \"Properties is the properties of the step\",\n              \"format\": \"textarea\",\n              \"type\": \"string\"\n             },\n             \"subSteps\": {\n              \"items\": {\n               \"description\": \"WorkflowStepBase defines the workflow step base\",\n               \"properties\": {\n                \"dependsOn\": {\n                 \"description\": \"DependsOn is the dependency of the step\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"if\": {\n                 \"description\": \"If is the if condition of the step\",\n                 \"type\": \"string\"\n                },\n                \"inputs\": {\n                 \"description\": \"Inputs is the inputs of the step\",\n                 \"items\": {\n                  \"description\": \"InputItem defines an input variable of WorkflowStep\",\n                  \"properties\": {\n                   \"from\": {\n                    \"type\": \"string\"\n                   },\n                   \"parameterKey\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"from\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"meta\": {\n                 \"description\": \"Meta is the meta data of the workflow step.\",\n                 \"properties\": {\n                  \"alias\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"name\": {\n                 \"description\": \"Name is the unique name of the workflow step.\",\n                 \"type\": \"string\"\n                },\n                \"outputs\": {\n                 \"description\": \"Outputs is the outputs of the step\",\n                 \"items\": {\n                  \"description\": \"OutputItem defines an output variable of WorkflowStep\",\n                  \"properties\": {\n                   \"name\": {\n                    \"type\": \"string\"\n                   },\n                   \"valueFrom\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"name\",\n                   \"valueFrom\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"properties\": {\n                 \"description\": \"Properties is the properties of the step\",\n                 \"format\": \"textarea\",\n                 \"type\": \"string\"\n                },\n                \"timeout\": {\n                 \"description\": \"Timeout is the timeout of the step\",\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"description\": \"Type is the type of the workflow step.\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"timeout\": {\n              \"description\": \"Timeout is the timeout of the step\",\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"description\": \"Type is the type of the workflow step.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"type\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"required\": [\n        \"components\"\n       ],\n       \"type\": \"object\"\n      },\n      \"status\": {\n       \"description\": \"AppStatus defines the observed state of Application\",\n       \"properties\": {\n        \"appliedResources\": {\n         \"description\": \"AppliedResources record the resources that the  workflow step apply.\",\n         \"items\": {\n          \"description\": \"ClusterObjectReference defines the object reference with cluster.\",\n          \"properties\": {\n           \"apiVersion\": {\n            \"description\": \"API version of the referent.\",\n            \"type\": \"string\"\n           },\n           \"cluster\": {\n            \"type\": \"string\"\n           },\n           \"creator\": {\n            \"type\": \"string\"\n           },\n           \"fieldPath\": {\n            \"description\": \"If referring to a piece of an object instead of an entire object, this string\\nshould contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].\\nFor example, if the object reference is to a container within a pod, this would take on a value like:\\n\\\"spec.containers{name}\\\" (where \\\"name\\\" refers to the name of the container that triggered\\nthe event) or if no container name is specified \\\"spec.containers[2]\\\" (container with\\nindex 2 in this pod). This syntax is chosen only to have some well-defined way of\\nreferencing a part of an object.\\nTODO: this design is not final and this field is subject to change in the future.\",\n            \"type\": \"string\"\n           },\n           \"kind\": {\n            \"description\": \"Kind of the referent.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"namespace\": {\n            \"description\": \"Namespace of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/\",\n            \"type\": \"string\"\n           },\n           \"resourceVersion\": {\n            \"description\": \"Specific resourceVersion to which this reference is made, if any.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency\",\n            \"type\": \"string\"\n           },\n           \"uid\": {\n            \"description\": \"UID of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"type\": \"array\"\n        },\n        \"components\": {\n         \"description\": \"Components record the related Components created by Application Controller\",\n         \"items\": {\n          \"description\": \"ObjectReference contains enough information to let you inspect or modify the referred object.\\n---\\nNew uses of this type are discouraged because of difficulty describing its usage when embedded in APIs.\\n 1. Ignored fields.  It includes many fields which are not generally honored.  For instance, ResourceVersion and FieldPath are both very rarely valid in actual usage.\\n 2. Invalid usage help.  It is impossible to add specific help for individual usage.  In most embedded usages, there are particular\\n    restrictions like, \\\"must refer only to types A and B\\\" or \\\"UID not honored\\\" or \\\"name must be restricted\\\".\\n    Those cannot be well described when embedded.\\n 3. Inconsistent validation.  Because the usages are different, the validation rules are different by usage, which makes it hard for users to predict what will happen.\\n 4. The fields are both imprecise and overly precise.  Kind is not a precise mapping to a URL. This can produce ambiguity\\n    during interpretation and require a REST mapping.  In most cases, the dependency is on the group,resource tuple\\n    and the version of the actual struct is irrelevant.\\n 5. We cannot easily change it.  Because this type is embedded in many locations, updates to this type\\n    will affect numerous schemas.  Don't make new APIs embed an underspecified API type they do not control.\\n\\n\\nInstead of using this type, create a locally provided and used type that is well-focused on your reference.\\nFor example, ServiceReferences for admission registration: https://github.com/kubernetes/api/blob/release-1.17/admissionregistration/v1/types.go#L533 .\",\n          \"properties\": {\n           \"apiVersion\": {\n            \"description\": \"API version of the referent.\",\n            \"type\": \"string\"\n           },\n           \"fieldPath\": {\n            \"description\": \"If referring to a piece of an object instead of an entire object, this string\\nshould contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].\\nFor example, if the object reference is to a container within a pod, this would take on a value like:\\n\\\"spec.containers{name}\\\" (where \\\"name\\\" refers to the name of the container that triggered\\nthe event) or if no container name is specified \\\"spec.containers[2]\\\" (container with\\nindex 2 in this pod). This syntax is chosen only to have some well-defined way of\\nreferencing a part of an object.\\nTODO: this design is not final and this field is subject to change in the future.\",\n            \"type\": \"string\"\n           },\n           \"kind\": {\n            \"description\": \"Kind of the referent.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"namespace\": {\n            \"description\": \"Namespace of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/\",\n            \"type\": \"string\"\n           },\n           \"resourceVersion\": {\n            \"description\": \"Specific resourceVersion to which this reference is made, if any.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency\",\n            \"type\": \"string\"\n           },\n           \"uid\": {\n            \"description\": \"UID of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"type\": \"array\"\n        },\n        \"conditions\": {\n         \"description\": \"Conditions of the resource.\",\n         \"items\": {\n          \"description\": \"A Condition that may apply to a resource.\",\n          \"properties\": {\n           \"lastTransitionTime\": {\n            \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n            \"format\": \"date-time\",\n            \"type\": \"string\"\n           },\n           \"message\": {\n            \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n            \"type\": \"string\"\n           },\n           \"reason\": {\n            \"description\": \"A Reason for this condition's last transition from one status to another.\",\n            \"type\": \"string\"\n           },\n           \"status\": {\n            \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"lastTransitionTime\",\n           \"reason\",\n           \"status\",\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"latestRevision\": {\n         \"description\": \"LatestRevision of the application configuration it generates\",\n         \"properties\": {\n          \"name\": {\n           \"type\": \"string\"\n          },\n          \"revision\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"revisionHash\": {\n           \"description\": \"RevisionHash record the hash value of the spec of ApplicationRevision object.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\",\n          \"revision\"\n         ],\n         \"type\": \"object\"\n        },\n        \"observedGeneration\": {\n         \"description\": \"The generation observed by the application controller.\",\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"policy\": {\n         \"description\": \"PolicyStatus records the status of policy\\nDeprecated This field is only used by EnvBinding Policy which is deprecated.\",\n         \"items\": {\n          \"description\": \"PolicyStatus records the status of policy\\nDeprecated\",\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"status\": {\n            \"format\": \"textarea\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\",\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"services\": {\n         \"description\": \"Services record the status of the application services\",\n         \"items\": {\n          \"description\": \"ApplicationComponentStatus record the health status of App component\",\n          \"properties\": {\n           \"cluster\": {\n            \"type\": \"string\"\n           },\n           \"env\": {\n            \"type\": \"string\"\n           },\n           \"healthy\": {\n            \"type\": \"boolean\"\n           },\n           \"message\": {\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"namespace\": {\n            \"type\": \"string\"\n           },\n           \"scopes\": {\n            \"items\": {\n             \"description\": \"ObjectReference contains enough information to let you inspect or modify the referred object.\\n---\\nNew uses of this type are discouraged because of difficulty describing its usage when embedded in APIs.\\n 1. Ignored fields.  It includes many fields which are not generally honored.  For instance, ResourceVersion and FieldPath are both very rarely valid in actual usage.\\n 2. Invalid usage help.  It is impossible to add specific help for individual usage.  In most embedded usages, there are particular\\n    restrictions like, \\\"must refer only to types A and B\\\" or \\\"UID not honored\\\" or \\\"name must be restricted\\\".\\n    Those cannot be well described when embedded.\\n 3. Inconsistent validation.  Because the usages are different, the validation rules are different by usage, which makes it hard for users to predict what will happen.\\n 4. The fields are both imprecise and overly precise.  Kind is not a precise mapping to a URL. This can produce ambiguity\\n    during interpretation and require a REST mapping.  In most cases, the dependency is on the group,resource tuple\\n    and the version of the actual struct is irrelevant.\\n 5. We cannot easily change it.  Because this type is embedded in many locations, updates to this type\\n    will affect numerous schemas.  Don't make new APIs embed an underspecified API type they do not control.\\n\\n\\nInstead of using this type, create a locally provided and used type that is well-focused on your reference.\\nFor example, ServiceReferences for admission registration: https://github.com/kubernetes/api/blob/release-1.17/admissionregistration/v1/types.go#L533 .\",\n             \"properties\": {\n              \"apiVersion\": {\n               \"description\": \"API version of the referent.\",\n               \"type\": \"string\"\n              },\n              \"fieldPath\": {\n               \"description\": \"If referring to a piece of an object instead of an entire object, this string\\nshould contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].\\nFor example, if the object reference is to a container within a pod, this would take on a value like:\\n\\\"spec.containers{name}\\\" (where \\\"name\\\" refers to the name of the container that triggered\\nthe event) or if no container name is specified \\\"spec.containers[2]\\\" (container with\\nindex 2 in this pod). This syntax is chosen only to have some well-defined way of\\nreferencing a part of an object.\\nTODO: this design is not final and this field is subject to change in the future.\",\n               \"type\": \"string\"\n              },\n              \"kind\": {\n               \"description\": \"Kind of the referent.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"Namespace of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/\",\n               \"type\": \"string\"\n              },\n              \"resourceVersion\": {\n               \"description\": \"Specific resourceVersion to which this reference is made, if any.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency\",\n               \"type\": \"string\"\n              },\n              \"uid\": {\n               \"description\": \"UID of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"type\": \"array\"\n           },\n           \"traits\": {\n            \"items\": {\n             \"description\": \"ApplicationTraitStatus records the trait health status\",\n             \"properties\": {\n              \"healthy\": {\n               \"type\": \"boolean\"\n              },\n              \"message\": {\n               \"type\": \"string\"\n              },\n              \"type\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"healthy\",\n              \"type\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"workloadDefinition\": {\n            \"description\": \"WorkloadDefinition is the definition of a WorkloadDefinition, such as deployments/apps.v1\",\n            \"properties\": {\n             \"apiVersion\": {\n              \"type\": \"string\"\n             },\n             \"kind\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"apiVersion\",\n             \"kind\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"healthy\",\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"status\": {\n         \"description\": \"ApplicationPhase is a label for the condition of an application at the current time\",\n         \"type\": \"string\"\n        },\n        \"workflow\": {\n         \"description\": \"Workflow record the status of workflow\",\n         \"properties\": {\n          \"appRevision\": {\n           \"type\": \"string\"\n          },\n          \"contextBackend\": {\n           \"description\": \"ObjectReference contains enough information to let you inspect or modify the referred object.\\n---\\nNew uses of this type are discouraged because of difficulty describing its usage when embedded in APIs.\\n 1. Ignored fields.  It includes many fields which are not generally honored.  For instance, ResourceVersion and FieldPath are both very rarely valid in actual usage.\\n 2. Invalid usage help.  It is impossible to add specific help for individual usage.  In most embedded usages, there are particular\\n    restrictions like, \\\"must refer only to types A and B\\\" or \\\"UID not honored\\\" or \\\"name must be restricted\\\".\\n    Those cannot be well described when embedded.\\n 3. Inconsistent validation.  Because the usages are different, the validation rules are different by usage, which makes it hard for users to predict what will happen.\\n 4. The fields are both imprecise and overly precise.  Kind is not a precise mapping to a URL. This can produce ambiguity\\n    during interpretation and require a REST mapping.  In most cases, the dependency is on the group,resource tuple\\n    and the version of the actual struct is irrelevant.\\n 5. We cannot easily change it.  Because this type is embedded in many locations, updates to this type\\n    will affect numerous schemas.  Don't make new APIs embed an underspecified API type they do not control.\\n\\n\\nInstead of using this type, create a locally provided and used type that is well-focused on your reference.\\nFor example, ServiceReferences for admission registration: https://github.com/kubernetes/api/blob/release-1.17/admissionregistration/v1/types.go#L533 .\",\n           \"properties\": {\n            \"apiVersion\": {\n             \"description\": \"API version of the referent.\",\n             \"type\": \"string\"\n            },\n            \"fieldPath\": {\n             \"description\": \"If referring to a piece of an object instead of an entire object, this string\\nshould contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].\\nFor example, if the object reference is to a container within a pod, this would take on a value like:\\n\\\"spec.containers{name}\\\" (where \\\"name\\\" refers to the name of the container that triggered\\nthe event) or if no container name is specified \\\"spec.containers[2]\\\" (container with\\nindex 2 in this pod). This syntax is chosen only to have some well-defined way of\\nreferencing a part of an object.\\nTODO: this design is not final and this field is subject to change in the future.\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind of the referent.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"namespace\": {\n             \"description\": \"Namespace of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/\",\n             \"type\": \"string\"\n            },\n            \"resourceVersion\": {\n             \"description\": \"Specific resourceVersion to which this reference is made, if any.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency\",\n             \"type\": \"string\"\n            },\n            \"uid\": {\n             \"description\": \"UID of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"endTime\": {\n           \"format\": \"date-time\",\n           \"nullable\": true,\n           \"type\": \"string\"\n          },\n          \"finished\": {\n           \"type\": \"boolean\"\n          },\n          \"message\": {\n           \"type\": \"string\"\n          },\n          \"mode\": {\n           \"type\": \"string\"\n          },\n          \"startTime\": {\n           \"format\": \"date-time\",\n           \"type\": \"string\"\n          },\n          \"status\": {\n           \"description\": \"WorkflowRunPhase is a label for the condition of a WorkflowRun at the current time\",\n           \"type\": \"string\"\n          },\n          \"steps\": {\n           \"items\": {\n            \"description\": \"WorkflowStepStatus record the status of a workflow step, include step status and subStep status\",\n            \"properties\": {\n             \"firstExecuteTime\": {\n              \"description\": \"FirstExecuteTime is the first time this step execution.\",\n              \"format\": \"date-time\",\n              \"type\": \"string\"\n             },\n             \"id\": {\n              \"type\": \"string\"\n             },\n             \"lastExecuteTime\": {\n              \"description\": \"LastExecuteTime is the last time this step execution.\",\n              \"format\": \"date-time\",\n              \"type\": \"string\"\n             },\n             \"message\": {\n              \"description\": \"A human readable message indicating details about why the workflowStep is in this state.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"type\": \"string\"\n             },\n             \"phase\": {\n              \"description\": \"WorkflowStepPhase describes the phase of a workflow step.\",\n              \"type\": \"string\"\n             },\n             \"reason\": {\n              \"description\": \"A brief CamelCase message indicating details about why the workflowStep is in this state.\",\n              \"type\": \"string\"\n             },\n             \"subSteps\": {\n              \"items\": {\n               \"description\": \"StepStatus record the base status of workflow step, which could be workflow step or subStep\",\n               \"properties\": {\n                \"firstExecuteTime\": {\n                 \"description\": \"FirstExecuteTime is the first time this step execution.\",\n                 \"format\": \"date-time\",\n                 \"type\": \"string\"\n                },\n                \"id\": {\n                 \"type\": \"string\"\n                },\n                \"lastExecuteTime\": {\n                 \"description\": \"LastExecuteTime is the last time this step execution.\",\n                 \"format\": \"date-time\",\n                 \"type\": \"string\"\n                },\n                \"message\": {\n                 \"description\": \"A human readable message indicating details about why the workflowStep is in this state.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"phase\": {\n                 \"description\": \"WorkflowStepPhase describes the phase of a workflow step.\",\n                 \"type\": \"string\"\n                },\n                \"reason\": {\n                 \"description\": \"A brief CamelCase message indicating details about why the workflowStep is in this state.\",\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"id\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"id\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\"\n          },\n          \"suspend\": {\n           \"type\": \"boolean\"\n          },\n          \"suspendState\": {\n           \"type\": \"string\"\n          },\n          \"terminated\": {\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"finished\",\n          \"mode\",\n          \"suspend\",\n          \"terminated\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"componentDefinitions\": {\n     \"additionalProperties\": {\n      \"description\": \"ComponentDefinition is the Schema for the componentdefinitions API\",\n      \"properties\": {\n       \"apiVersion\": {\n        \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n        \"type\": \"string\"\n       },\n       \"kind\": {\n        \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n        \"type\": \"string\"\n       },\n       \"metadata\": {\n        \"properties\": {\n         \"annotations\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"finalizers\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"spec\": {\n        \"description\": \"ComponentDefinitionSpec defines the desired state of ComponentDefinition\",\n        \"properties\": {\n         \"childResourceKinds\": {\n          \"description\": \"ChildResourceKinds are the list of GVK of the child resources this workload generates\",\n          \"items\": {\n           \"description\": \"A ChildResourceKind defines a child Kubernetes resource kind with a selector\",\n           \"properties\": {\n            \"apiVersion\": {\n             \"description\": \"APIVersion of the child resource\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind of the child resource\",\n             \"type\": \"string\"\n            },\n            \"selector\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"description\": \"Selector to select the child resources that the workload wants to expose to traits\",\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"apiVersion\",\n            \"kind\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"extension\": {\n          \"description\": \"Extension is used for extension needs by OAM platform builders\",\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"podSpecPath\": {\n          \"description\": \"PodSpecPath indicates where/if this workload has K8s podSpec field\\nif one workload has podSpec, trait can do lot's of assumption such as port, env, volume fields.\",\n          \"type\": \"string\"\n         },\n         \"revisionLabel\": {\n          \"description\": \"RevisionLabel indicates which label for underlying resources(e.g. pods) of this workload\\ncan be used by trait to create resource selectors(e.g. label selector for pods).\",\n          \"type\": \"string\"\n         },\n         \"schematic\": {\n          \"description\": \"Schematic defines the data format and template of the encapsulation of the workload\",\n          \"properties\": {\n           \"cue\": {\n            \"description\": \"CUE defines the encapsulation in CUE format\",\n            \"properties\": {\n             \"template\": {\n              \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"template\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terraform\": {\n            \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n            \"properties\": {\n             \"configuration\": {\n              \"description\": \"Configuration is Terraform Configuration\",\n              \"type\": \"string\"\n             },\n             \"customRegion\": {\n              \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n              \"type\": \"string\"\n             },\n             \"deleteResource\": {\n              \"default\": true,\n              \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n              \"type\": \"boolean\"\n             },\n             \"gitCredentialsSecretReference\": {\n              \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"name is unique within a namespace to reference a secret resource.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"namespace defines the space within which the secret name must be unique.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"path\": {\n              \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n              \"type\": \"string\"\n             },\n             \"providerRef\": {\n              \"description\": \"ProviderReference specifies the reference to Provider\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referenced object.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"default\": \"default\",\n                \"description\": \"Namespace of the referenced object.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": {\n              \"default\": \"hcl\",\n              \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n              \"enum\": [\n               \"hcl\",\n               \"json\",\n               \"remote\"\n              ],\n              \"type\": \"string\"\n             },\n             \"writeConnectionSecretToRef\": {\n              \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the secret.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace of the secret.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"configuration\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"status\": {\n          \"description\": \"Status defines the custom health policy and status message for workload\",\n          \"properties\": {\n           \"customStatus\": {\n            \"description\": \"CustomStatus defines the custom status message that could display to user\",\n            \"type\": \"string\"\n           },\n           \"healthPolicy\": {\n            \"description\": \"HealthPolicy defines the health check policy for the abstraction\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"version\": {\n          \"type\": \"string\"\n         },\n         \"workload\": {\n          \"description\": \"Workload is a workload type descriptor\",\n          \"properties\": {\n           \"definition\": {\n            \"description\": \"Definition mutually exclusive to workload.type, a embedded WorkloadDefinition\",\n            \"properties\": {\n             \"apiVersion\": {\n              \"type\": \"string\"\n             },\n             \"kind\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"apiVersion\",\n             \"kind\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": {\n            \"description\": \"Type ref to a WorkloadDefinition via name\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"workload\"\n        ],\n        \"type\": \"object\"\n       },\n       \"status\": {\n        \"description\": \"ComponentDefinitionStatus is the status of ComponentDefinition\",\n        \"properties\": {\n         \"conditions\": {\n          \"description\": \"Conditions of the resource.\",\n          \"items\": {\n           \"description\": \"A Condition that may apply to a resource.\",\n           \"properties\": {\n            \"lastTransitionTime\": {\n             \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n             \"format\": \"date-time\",\n             \"type\": \"string\"\n            },\n            \"message\": {\n             \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n             \"type\": \"string\"\n            },\n            \"reason\": {\n             \"description\": \"A Reason for this condition's last transition from one status to another.\",\n             \"type\": \"string\"\n            },\n            \"status\": {\n             \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"lastTransitionTime\",\n            \"reason\",\n            \"status\",\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"configMapRef\": {\n          \"description\": \"ConfigMapRef refer to a ConfigMap which contains OpenAPI V3 JSON schema of Component parameters.\",\n          \"type\": \"string\"\n         },\n         \"latestRevision\": {\n          \"description\": \"LatestRevision of the component definition\",\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"revision\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"revisionHash\": {\n            \"description\": \"RevisionHash record the hash value of the spec of ApplicationRevision object.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\",\n           \"revision\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"description\": \"ComponentDefinitions records the snapshot of the componentDefinitions related with the created/modified Application\",\n     \"type\": \"object\"\n    },\n    \"compression\": {\n     \"description\": \"Compression represents the compressed components in apprev in base64 (if compression is enabled).\",\n     \"properties\": {\n      \"data\": {\n       \"type\": \"string\"\n      },\n      \"type\": {\n       \"description\": \"Type the compression type\",\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"policies\": {\n     \"additionalProperties\": {\n      \"description\": \"Policy is the Schema for the policy API\",\n      \"properties\": {\n       \"apiVersion\": {\n        \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n        \"type\": \"string\"\n       },\n       \"kind\": {\n        \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n        \"type\": \"string\"\n       },\n       \"metadata\": {\n        \"properties\": {\n         \"annotations\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"finalizers\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"properties\": {\n        \"format\": \"textarea\",\n        \"type\": \"string\"\n       },\n       \"type\": {\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"type\"\n      ],\n      \"type\": \"object\"\n     },\n     \"description\": \"Policies records the external policies\",\n     \"type\": \"object\"\n    },\n    \"policyDefinitions\": {\n     \"additionalProperties\": {\n      \"description\": \"PolicyDefinition is the Schema for the policydefinitions API\",\n      \"properties\": {\n       \"apiVersion\": {\n        \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n        \"type\": \"string\"\n       },\n       \"kind\": {\n        \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n        \"type\": \"string\"\n       },\n       \"metadata\": {\n        \"properties\": {\n         \"annotations\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"finalizers\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"spec\": {\n        \"description\": \"PolicyDefinitionSpec defines the desired state of PolicyDefinition\",\n        \"properties\": {\n         \"definitionRef\": {\n          \"description\": \"Reference to the CustomResourceDefinition that defines this trait kind.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referenced CustomResourceDefinition.\",\n            \"type\": \"string\"\n           },\n           \"version\": {\n            \"description\": \"Version indicate which version should be used if CRD has multiple versions\\nby default it will use the first one if not specified\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"manageHealthCheck\": {\n          \"description\": \"ManageHealthCheck means the policy will handle health checking and skip application controller\\nbuilt-in health checking.\",\n          \"type\": \"boolean\"\n         },\n         \"schematic\": {\n          \"description\": \"Schematic defines the data format and template of the encapsulation of the policy definition.\\nOnly CUE schematic is supported for now.\",\n          \"properties\": {\n           \"cue\": {\n            \"description\": \"CUE defines the encapsulation in CUE format\",\n            \"properties\": {\n             \"template\": {\n              \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"template\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terraform\": {\n            \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n            \"properties\": {\n             \"configuration\": {\n              \"description\": \"Configuration is Terraform Configuration\",\n              \"type\": \"string\"\n             },\n             \"customRegion\": {\n              \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n              \"type\": \"string\"\n             },\n             \"deleteResource\": {\n              \"default\": true,\n              \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n              \"type\": \"boolean\"\n             },\n             \"gitCredentialsSecretReference\": {\n              \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"name is unique within a namespace to reference a secret resource.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"namespace defines the space within which the secret name must be unique.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"path\": {\n              \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n              \"type\": \"string\"\n             },\n             \"providerRef\": {\n              \"description\": \"ProviderReference specifies the reference to Provider\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referenced object.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"default\": \"default\",\n                \"description\": \"Namespace of the referenced object.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": {\n              \"default\": \"hcl\",\n              \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n              \"enum\": [\n               \"hcl\",\n               \"json\",\n               \"remote\"\n              ],\n              \"type\": \"string\"\n             },\n             \"writeConnectionSecretToRef\": {\n              \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the secret.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace of the secret.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"configuration\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"version\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"status\": {\n        \"description\": \"PolicyDefinitionStatus is the status of PolicyDefinition\",\n        \"properties\": {\n         \"conditions\": {\n          \"description\": \"Conditions of the resource.\",\n          \"items\": {\n           \"description\": \"A Condition that may apply to a resource.\",\n           \"properties\": {\n            \"lastTransitionTime\": {\n             \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n             \"format\": \"date-time\",\n             \"type\": \"string\"\n            },\n            \"message\": {\n             \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n             \"type\": \"string\"\n            },\n            \"reason\": {\n             \"description\": \"A Reason for this condition's last transition from one status to another.\",\n             \"type\": \"string\"\n            },\n            \"status\": {\n             \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"lastTransitionTime\",\n            \"reason\",\n            \"status\",\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"configMapRef\": {\n          \"description\": \"ConfigMapRef refer to a ConfigMap which contains OpenAPI V3 JSON schema of Component parameters.\",\n          \"type\": \"string\"\n         },\n         \"latestRevision\": {\n          \"description\": \"LatestRevision of the component definition\",\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"revision\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"revisionHash\": {\n            \"description\": \"RevisionHash record the hash value of the spec of ApplicationRevision object.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\",\n           \"revision\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"description\": \"PolicyDefinitions records the snapshot of the PolicyDefinitions related with the created/modified Application\",\n     \"type\": \"object\"\n    },\n    \"referredObjects\": {\n     \"description\": \"ReferredObjects records the referred objects used in the ref-object typed components\",\n     \"format\": \"textarea\",\n     \"items\": {\n      \"description\": \"ReferredObject the referred Kubernetes object\",\n      \"format\": \"textarea\",\n      \"type\": \"string\",\n      \"x-kubernetes-embedded-resource\": true\n     },\n     \"type\": \"string\"\n    },\n    \"traitDefinitions\": {\n     \"additionalProperties\": {\n      \"description\": \"A TraitDefinition registers a kind of Kubernetes custom resource as a valid\\nOAM trait kind by referencing its CustomResourceDefinition. The CRD is used\\nto validate the schema of the trait when it is embedded in an OAM\\nApplicationConfiguration.\",\n      \"properties\": {\n       \"apiVersion\": {\n        \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n        \"type\": \"string\"\n       },\n       \"kind\": {\n        \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n        \"type\": \"string\"\n       },\n       \"metadata\": {\n        \"properties\": {\n         \"annotations\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"finalizers\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"spec\": {\n        \"description\": \"A TraitDefinitionSpec defines the desired state of a TraitDefinition.\",\n        \"properties\": {\n         \"appliesToWorkloads\": {\n          \"description\": \"AppliesToWorkloads specifies the list of workload kinds this trait\\napplies to. Workload kinds are specified in resource.group/version format,\\ne.g. server.core.oam.dev/v1alpha2. Traits that omit this field apply to\\nall workload kinds.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"conflictsWith\": {\n          \"description\": \"ConflictsWith specifies the list of traits(CRD name, Definition name, CRD group)\\nwhich could not apply to the same workloads with this trait.\\nTraits that omit this field can work with any other traits.\\nExample rules:\\n\\\"service\\\" # Trait definition name\\n\\\"services.k8s.io\\\" # API resource/crd name\\n\\\"*.networking.k8s.io\\\" # API group\\n\\\"labelSelector:foo=bar\\\" # label selector\\nlabelSelector format: https://pkg.go.dev/k8s.io/apimachinery/pkg/labels#Parse\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"controlPlaneOnly\": {\n          \"description\": \"ControlPlaneOnly defines which cluster is dispatched to\",\n          \"type\": \"boolean\"\n         },\n         \"definitionRef\": {\n          \"description\": \"Reference to the CustomResourceDefinition that defines this trait kind.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referenced CustomResourceDefinition.\",\n            \"type\": \"string\"\n           },\n           \"version\": {\n            \"description\": \"Version indicate which version should be used if CRD has multiple versions\\nby default it will use the first one if not specified\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"extension\": {\n          \"description\": \"Extension is used for extension needs by OAM platform builders\",\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"manageWorkload\": {\n          \"description\": \"ManageWorkload defines the trait would be responsible for creating the workload\",\n          \"type\": \"boolean\"\n         },\n         \"podDisruptive\": {\n          \"description\": \"PodDisruptive specifies whether using the trait will cause the pod to restart or not.\",\n          \"type\": \"boolean\"\n         },\n         \"revisionEnabled\": {\n          \"description\": \"Revision indicates whether a trait is aware of component revision\",\n          \"type\": \"boolean\"\n         },\n         \"schematic\": {\n          \"description\": \"Schematic defines the data format and template of the encapsulation of the trait.\\nOnly CUE and Kube schematic are supported for now.\",\n          \"properties\": {\n           \"cue\": {\n            \"description\": \"CUE defines the encapsulation in CUE format\",\n            \"properties\": {\n             \"template\": {\n              \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"template\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terraform\": {\n            \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n            \"properties\": {\n             \"configuration\": {\n              \"description\": \"Configuration is Terraform Configuration\",\n              \"type\": \"string\"\n             },\n             \"customRegion\": {\n              \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n              \"type\": \"string\"\n             },\n             \"deleteResource\": {\n              \"default\": true,\n              \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n              \"type\": \"boolean\"\n             },\n             \"gitCredentialsSecretReference\": {\n              \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"name is unique within a namespace to reference a secret resource.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"namespace defines the space within which the secret name must be unique.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"path\": {\n              \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n              \"type\": \"string\"\n             },\n             \"providerRef\": {\n              \"description\": \"ProviderReference specifies the reference to Provider\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referenced object.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"default\": \"default\",\n                \"description\": \"Namespace of the referenced object.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": {\n              \"default\": \"hcl\",\n              \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n              \"enum\": [\n               \"hcl\",\n               \"json\",\n               \"remote\"\n              ],\n              \"type\": \"string\"\n             },\n             \"writeConnectionSecretToRef\": {\n              \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the secret.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace of the secret.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"configuration\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"stage\": {\n          \"description\": \"Stage defines the stage information to which this trait resource processing belongs.\\nCurrently, PreDispatch and PostDispatch are provided, which are used to control resource\\npre-process and post-process respectively.\",\n          \"type\": \"string\"\n         },\n         \"status\": {\n          \"description\": \"Status defines the custom health policy and status message for trait\",\n          \"properties\": {\n           \"customStatus\": {\n            \"description\": \"CustomStatus defines the custom status message that could display to user\",\n            \"type\": \"string\"\n           },\n           \"healthPolicy\": {\n            \"description\": \"HealthPolicy defines the health check policy for the abstraction\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"version\": {\n          \"type\": \"string\"\n         },\n         \"workloadRefPath\": {\n          \"description\": \"WorkloadRefPath indicates where/if a trait accepts a workloadRef object\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"status\": {\n        \"description\": \"TraitDefinitionStatus is the status of TraitDefinition\",\n        \"properties\": {\n         \"conditions\": {\n          \"description\": \"Conditions of the resource.\",\n          \"items\": {\n           \"description\": \"A Condition that may apply to a resource.\",\n           \"properties\": {\n            \"lastTransitionTime\": {\n             \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n             \"format\": \"date-time\",\n             \"type\": \"string\"\n            },\n            \"message\": {\n             \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n             \"type\": \"string\"\n            },\n            \"reason\": {\n             \"description\": \"A Reason for this condition's last transition from one status to another.\",\n             \"type\": \"string\"\n            },\n            \"status\": {\n             \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"lastTransitionTime\",\n            \"reason\",\n            \"status\",\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"configMapRef\": {\n          \"description\": \"ConfigMapRef refer to a ConfigMap which contains OpenAPI V3 JSON schema of Component parameters.\",\n          \"type\": \"string\"\n         },\n         \"latestRevision\": {\n          \"description\": \"LatestRevision of the component definition\",\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"revision\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"revisionHash\": {\n            \"description\": \"RevisionHash record the hash value of the spec of ApplicationRevision object.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\",\n           \"revision\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"description\": \"TraitDefinitions records the snapshot of the traitDefinitions related with the created/modified Application\",\n     \"type\": \"object\"\n    },\n    \"workflow\": {\n     \"description\": \"Workflow records the external workflow\",\n     \"properties\": {\n      \"apiVersion\": {\n       \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n       \"type\": \"string\"\n      },\n      \"kind\": {\n       \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n       \"type\": \"string\"\n      },\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"finalizers\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"name\": {\n         \"type\": \"string\"\n        },\n        \"namespace\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"mode\": {\n       \"description\": \"WorkflowExecuteMode defines the mode of workflow execution\",\n       \"properties\": {\n        \"steps\": {\n         \"description\": \"Steps is the mode of workflow steps execution\",\n         \"type\": \"string\"\n        },\n        \"subSteps\": {\n         \"description\": \"SubSteps is the mode of workflow sub steps execution\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"steps\": {\n       \"items\": {\n        \"description\": \"WorkflowStep defines how to execute a workflow step.\",\n        \"properties\": {\n         \"dependsOn\": {\n          \"description\": \"DependsOn is the dependency of the step\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"if\": {\n          \"description\": \"If is the if condition of the step\",\n          \"type\": \"string\"\n         },\n         \"inputs\": {\n          \"description\": \"Inputs is the inputs of the step\",\n          \"items\": {\n           \"description\": \"InputItem defines an input variable of WorkflowStep\",\n           \"properties\": {\n            \"from\": {\n             \"type\": \"string\"\n            },\n            \"parameterKey\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"from\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"meta\": {\n          \"description\": \"Meta is the meta data of the workflow step.\",\n          \"properties\": {\n           \"alias\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"mode\": {\n          \"description\": \"Mode is only valid for sub steps, it defines the mode of the sub steps\",\n          \"nullable\": true,\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name is the unique name of the workflow step.\",\n          \"type\": \"string\"\n         },\n         \"outputs\": {\n          \"description\": \"Outputs is the outputs of the step\",\n          \"items\": {\n           \"description\": \"OutputItem defines an output variable of WorkflowStep\",\n           \"properties\": {\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"valueFrom\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"name\",\n            \"valueFrom\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"properties\": {\n          \"description\": \"Properties is the properties of the step\",\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"subSteps\": {\n          \"items\": {\n           \"description\": \"WorkflowStepBase defines the workflow step base\",\n           \"properties\": {\n            \"dependsOn\": {\n             \"description\": \"DependsOn is the dependency of the step\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"if\": {\n             \"description\": \"If is the if condition of the step\",\n             \"type\": \"string\"\n            },\n            \"inputs\": {\n             \"description\": \"Inputs is the inputs of the step\",\n             \"items\": {\n              \"description\": \"InputItem defines an input variable of WorkflowStep\",\n              \"properties\": {\n               \"from\": {\n                \"type\": \"string\"\n               },\n               \"parameterKey\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"from\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"meta\": {\n             \"description\": \"Meta is the meta data of the workflow step.\",\n             \"properties\": {\n              \"alias\": {\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"name\": {\n             \"description\": \"Name is the unique name of the workflow step.\",\n             \"type\": \"string\"\n            },\n            \"outputs\": {\n             \"description\": \"Outputs is the outputs of the step\",\n             \"items\": {\n              \"description\": \"OutputItem defines an output variable of WorkflowStep\",\n              \"properties\": {\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"valueFrom\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\",\n               \"valueFrom\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"properties\": {\n             \"description\": \"Properties is the properties of the step\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"timeout\": {\n             \"description\": \"Timeout is the timeout of the step\",\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"description\": \"Type is the type of the workflow step.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"timeout\": {\n          \"description\": \"Timeout is the timeout of the step\",\n          \"type\": \"string\"\n         },\n         \"type\": {\n          \"description\": \"Type is the type of the workflow step.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"type\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"workflowStepDefinitions\": {\n     \"additionalProperties\": {\n      \"description\": \"WorkflowStepDefinition is the Schema for the workflowstepdefinitions API\",\n      \"properties\": {\n       \"apiVersion\": {\n        \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n        \"type\": \"string\"\n       },\n       \"kind\": {\n        \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n        \"type\": \"string\"\n       },\n       \"metadata\": {\n        \"properties\": {\n         \"annotations\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"finalizers\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"spec\": {\n        \"description\": \"WorkflowStepDefinitionSpec defines the desired state of WorkflowStepDefinition\",\n        \"properties\": {\n         \"definitionRef\": {\n          \"description\": \"Reference to the CustomResourceDefinition that defines this trait kind.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referenced CustomResourceDefinition.\",\n            \"type\": \"string\"\n           },\n           \"version\": {\n            \"description\": \"Version indicate which version should be used if CRD has multiple versions\\nby default it will use the first one if not specified\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"schematic\": {\n          \"description\": \"Schematic defines the data format and template of the encapsulation of the workflow step definition.\\nOnly CUE schematic is supported for now.\",\n          \"properties\": {\n           \"cue\": {\n            \"description\": \"CUE defines the encapsulation in CUE format\",\n            \"properties\": {\n             \"template\": {\n              \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"template\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terraform\": {\n            \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n            \"properties\": {\n             \"configuration\": {\n              \"description\": \"Configuration is Terraform Configuration\",\n              \"type\": \"string\"\n             },\n             \"customRegion\": {\n              \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n              \"type\": \"string\"\n             },\n             \"deleteResource\": {\n              \"default\": true,\n              \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n              \"type\": \"boolean\"\n             },\n             \"gitCredentialsSecretReference\": {\n              \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"name is unique within a namespace to reference a secret resource.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"namespace defines the space within which the secret name must be unique.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"path\": {\n              \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n              \"type\": \"string\"\n             },\n             \"providerRef\": {\n              \"description\": \"ProviderReference specifies the reference to Provider\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referenced object.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"default\": \"default\",\n                \"description\": \"Namespace of the referenced object.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": {\n              \"default\": \"hcl\",\n              \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n              \"enum\": [\n               \"hcl\",\n               \"json\",\n               \"remote\"\n              ],\n              \"type\": \"string\"\n             },\n             \"writeConnectionSecretToRef\": {\n              \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the secret.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace of the secret.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"configuration\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"version\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"status\": {\n        \"description\": \"WorkflowStepDefinitionStatus is the status of WorkflowStepDefinition\",\n        \"properties\": {\n         \"conditions\": {\n          \"description\": \"Conditions of the resource.\",\n          \"items\": {\n           \"description\": \"A Condition that may apply to a resource.\",\n           \"properties\": {\n            \"lastTransitionTime\": {\n             \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n             \"format\": \"date-time\",\n             \"type\": \"string\"\n            },\n            \"message\": {\n             \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n             \"type\": \"string\"\n            },\n            \"reason\": {\n             \"description\": \"A Reason for this condition's last transition from one status to another.\",\n             \"type\": \"string\"\n            },\n            \"status\": {\n             \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"lastTransitionTime\",\n            \"reason\",\n            \"status\",\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"configMapRef\": {\n          \"description\": \"ConfigMapRef refer to a ConfigMap which contains OpenAPI V3 JSON schema of Component parameters.\",\n          \"type\": \"string\"\n         },\n         \"latestRevision\": {\n          \"description\": \"LatestRevision of the component definition\",\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"revision\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"revisionHash\": {\n            \"description\": \"RevisionHash record the hash value of the spec of ApplicationRevision object.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\",\n           \"revision\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"description\": \"WorkflowStepDefinitions records the snapshot of the WorkflowStepDefinitions related with the created/modified Application\",\n     \"type\": \"object\"\n    },\n    \"workloadDefinitions\": {\n     \"additionalProperties\": {\n      \"description\": \"A WorkloadDefinition registers a kind of Kubernetes custom resource as a\\nvalid OAM workload kind by referencing its CustomResourceDefinition. The CRD\\nis used to validate the schema of the workload when it is embedded in an OAM\\nComponent.\",\n      \"properties\": {\n       \"apiVersion\": {\n        \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n        \"type\": \"string\"\n       },\n       \"kind\": {\n        \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n        \"type\": \"string\"\n       },\n       \"metadata\": {\n        \"properties\": {\n         \"annotations\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"finalizers\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"spec\": {\n        \"description\": \"A WorkloadDefinitionSpec defines the desired state of a WorkloadDefinition.\",\n        \"properties\": {\n         \"childResourceKinds\": {\n          \"description\": \"ChildResourceKinds are the list of GVK of the child resources this workload generates\",\n          \"items\": {\n           \"description\": \"A ChildResourceKind defines a child Kubernetes resource kind with a selector\",\n           \"properties\": {\n            \"apiVersion\": {\n             \"description\": \"APIVersion of the child resource\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind of the child resource\",\n             \"type\": \"string\"\n            },\n            \"selector\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"description\": \"Selector to select the child resources that the workload wants to expose to traits\",\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"apiVersion\",\n            \"kind\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"definitionRef\": {\n          \"description\": \"Reference to the CustomResourceDefinition that defines this workload kind.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referenced CustomResourceDefinition.\",\n            \"type\": \"string\"\n           },\n           \"version\": {\n            \"description\": \"Version indicate which version should be used if CRD has multiple versions\\nby default it will use the first one if not specified\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"extension\": {\n          \"description\": \"Extension is used for extension needs by OAM platform builders\",\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"podSpecPath\": {\n          \"description\": \"PodSpecPath indicates where/if this workload has K8s podSpec field\\nif one workload has podSpec, trait can do lot's of assumption such as port, env, volume fields.\",\n          \"type\": \"string\"\n         },\n         \"revisionLabel\": {\n          \"description\": \"RevisionLabel indicates which label for underlying resources(e.g. pods) of this workload\\ncan be used by trait to create resource selectors(e.g. label selector for pods).\",\n          \"type\": \"string\"\n         },\n         \"schematic\": {\n          \"description\": \"Schematic defines the data format and template of the encapsulation of the workload\",\n          \"properties\": {\n           \"cue\": {\n            \"description\": \"CUE defines the encapsulation in CUE format\",\n            \"properties\": {\n             \"template\": {\n              \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"template\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terraform\": {\n            \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n            \"properties\": {\n             \"configuration\": {\n              \"description\": \"Configuration is Terraform Configuration\",\n              \"type\": \"string\"\n             },\n             \"customRegion\": {\n              \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n              \"type\": \"string\"\n             },\n             \"deleteResource\": {\n              \"default\": true,\n              \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n              \"type\": \"boolean\"\n             },\n             \"gitCredentialsSecretReference\": {\n              \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"name is unique within a namespace to reference a secret resource.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"namespace defines the space within which the secret name must be unique.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"path\": {\n              \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n              \"type\": \"string\"\n             },\n             \"providerRef\": {\n              \"description\": \"ProviderReference specifies the reference to Provider\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referenced object.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"default\": \"default\",\n                \"description\": \"Namespace of the referenced object.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": {\n              \"default\": \"hcl\",\n              \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n              \"enum\": [\n               \"hcl\",\n               \"json\",\n               \"remote\"\n              ],\n              \"type\": \"string\"\n             },\n             \"writeConnectionSecretToRef\": {\n              \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the secret.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace of the secret.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"configuration\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"status\": {\n          \"description\": \"Status defines the custom health policy and status message for workload\",\n          \"properties\": {\n           \"customStatus\": {\n            \"description\": \"CustomStatus defines the custom status message that could display to user\",\n            \"type\": \"string\"\n           },\n           \"healthPolicy\": {\n            \"description\": \"HealthPolicy defines the health check policy for the abstraction\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"definitionRef\"\n        ],\n        \"type\": \"object\"\n       },\n       \"status\": {\n        \"description\": \"WorkloadDefinitionStatus is the status of WorkloadDefinition\",\n        \"properties\": {\n         \"conditions\": {\n          \"description\": \"Conditions of the resource.\",\n          \"items\": {\n           \"description\": \"A Condition that may apply to a resource.\",\n           \"properties\": {\n            \"lastTransitionTime\": {\n             \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n             \"format\": \"date-time\",\n             \"type\": \"string\"\n            },\n            \"message\": {\n             \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n             \"type\": \"string\"\n            },\n            \"reason\": {\n             \"description\": \"A Reason for this condition's last transition from one status to another.\",\n             \"type\": \"string\"\n            },\n            \"status\": {\n             \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"lastTransitionTime\",\n            \"reason\",\n            \"status\",\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"description\": \"WorkloadDefinitions records the snapshot of the workloadDefinitions related with the created/modified Application\",\n     \"type\": \"object\"\n    }\n   },\n   \"required\": [\n    \"application\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Application Revision\",\n \"type\": \"object\"\n}"}}