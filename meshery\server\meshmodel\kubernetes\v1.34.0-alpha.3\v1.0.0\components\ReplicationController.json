{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Replication Controller", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubernetes", "displayName": "Kubernetes", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Orchestration & Management"}, "subCategory": "Scheduling & Orchestration", "metadata": {"isAnnotation": false, "primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-0.17 0.08 230.10 223.35\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}.cls-2{fill:#326ce5}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.466a3.59 3.59 0 0 0-.855-.065 3.685 3.685 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53 43.53 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.656 3.656 0 0 0-.733.065l-.037-.045-21.785 3.698a43.695 43.695 0 0 0 17.54 21.946l8.442-20.4-.066-.08a3.683 3.683 0 0 0 .207-2.29zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.2l-10.738-19.406zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.039a3.725 3.725 0 0 0 5.913 2.84l.065.027 18.036-12.788a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.921a3.72 3.72 0 0 0 5.904-2.85l.092-.043 1.253-22.14a44.682 44.682 0 0 0-4.501.776 43.467 43.467 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.729a3.72 3.72 0 0 0 1.462-6.396l.018-.088-16.574-14.824a43.454 43.454 0 0 0-6.168 27.51l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.934 1.514-6.581-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.167l-17.422-75.675a13.602 13.602 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0L38.76 48.437a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.506 13.506 0 0 0 .491 1.718 13.1 13.1 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.423 13.423 0 0 0 2.574 2.133 13.924 13.924 0 0 0 3.857 1.677 13.298 13.298 0 0 0 3.43.473h.759l77.504-.018a12.993 12.993 0 0 0 1.41-.083 13.47 13.47 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.941 13.941 0 0 0 1.479-.822 13.303 13.303 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67 13.67 0 0 0 .5-1.724 13.282 13.282 0 0 0-.004-5.81zm-73.147 29.432a14.516 14.516 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.372 39.372 0 0 0 3.742 6.7 35.06 35.06 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.275 35.275 0 0 1-1.225-3.868 39.305 39.305 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.15-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.73a3.429 3.429 0 0 0-1.819.887 29.517 29.517 0 0 0-3.268 7.582 34.9 34.9 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.177 35.177 0 0 1 2.262-3.373 41.228 41.228 0 0 0 3.82-6.866 4.188 4.188 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68 4.68 0 0 0-2.428-.855 39.524 39.524 0 0 0-7.356 2.165 35.589 35.589 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.605.605 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.388 39.388 0 0 0 7.564-1.242 5.835 5.835 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.366 39.366 0 0 0-6.277-4.41 35.25 35.25 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.253 35.253 0 0 1 2.78 2.95 39.383 39.383 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082A54.289 54.289 0 0 1 102.86 61.89a55.052 55.052 0 0 1 7.63-1.173l.1-1.784a4.6 4.6 0 0 0 1.37-2.184 39.476 39.476 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.08-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.86.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.533 39.533 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.69 4.69 0 0 0 2.56-.278 39.532 39.532 0 0 0 5.69-5.148 35.004 35.004 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.095 35.095 0 0 1-3.507 2.046 39.495 39.495 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.745 4.745 0 0 0 1.82 1.83 39.464 39.464 0 0 0 7.568 1.246 35.64 35.64 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.03-.348-.075-.785-.151-1.09-.231a35.14 35.14 0 0 1-3.785-1.462 39.477 39.477 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.92 54.92 0 0 1-24.328 30.605z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M225.407 135.107L206.4 52.547a14.838 14.838 0 0 0-7.958-9.9l-76.935-36.73a14.825 14.825 0 0 0-12.771 0L31.808 42.669a14.838 14.838 0 0 0-7.961 9.895L4.873 135.129a14.668 14.668 0 0 0 1.995 11.185c.261.4.538.788.838 1.162l53.246 66.205a14.98 14.98 0 0 0 11.499 5.487l85.387-.02a14.986 14.986 0 0 0 11.5-5.48l53.227-66.211a14.72 14.72 0 0 0 2.842-12.347zm-9.197 3.866a13.677 13.677 0 0 1-.498 1.723 13.184 13.184 0 0 1-2.11 3.786l-47.299 58.838-1.486 1.852a13.305 13.305 0 0 1-3.24 2.865 13.945 13.945 0 0 1-1.474.822q-.513.237-1.045.43a13.873 13.873 0 0 1-1.383.445 13.473 13.473 0 0 1-1.989.379 12.988 12.988 0 0 1-1.41.082l-77.504.018h-.76a13.298 13.298 0 0 1-3.429-.472 13.925 13.925 0 0 1-3.855-1.679 13.424 13.424 0 0 1-2.576-2.132c-.233-.246-.468-.487-.68-.75l-48.805-60.679q-.408-.514-.765-1.066a13.102 13.102 0 0 1-1.343-2.726 13.505 13.505 0 0 1-.491-1.719 13.315 13.315 0 0 1 .004-5.809l17.394-75.675a13.598 13.598 0 0 1 7.295-9.07l70.508-33.685a13.589 13.589 0 0 1 11.705 0l70.519 33.67a13.602 13.602 0 0 1 7.293 9.073l17.422 75.674a13.282 13.282 0 0 1 .002 5.807z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M185.814 127.106c-.36-.083-.874-.225-1.227-.29a35.642 35.642 0 0 0-4.046-.326 39.464 39.464 0 0 1-7.57-1.242 4.745 4.745 0 0 1-1.82-1.832l-1.704-.496a54.837 54.837 0 0 0-8.79-37.983c.436-.396 1.262-1.127 1.495-1.342a3.338 3.338 0 0 1 .792-2.39 39.495 39.495 0 0 1 6.277-4.41 35.095 35.095 0 0 0 3.507-2.046c.272-.202.644-.522.929-.75a4.242 4.242 0 1 0-5.256-6.6c-.288.227-.68.525-.936.747a35.004 35.004 0 0 0-2.789 2.95 39.533 39.533 0 0 1-5.69 5.148 4.69 4.69 0 0 1-2.56.278l-1.606 1.147a55.309 55.309 0 0 0-35.032-16.927c-.039-.561-.087-1.577-.102-1.884a3.344 3.344 0 0 1-1.377-2.11 39.533 39.533 0 0 1 .478-7.656 35.112 35.112 0 0 0 .575-4.012c.013-.34-.007-.834-.007-1.201a4.242 4.242 0 1 0-8.441.004c0 .04.009.078.01.118-.004.35-.021.774-.01 1.08a35.476 35.476 0 0 0 .576 4.015 39.475 39.475 0 0 1 .47 7.654 4.601 4.601 0 0 1-1.37 2.182l-.1 1.786a55.052 55.052 0 0 0-7.63 1.173 54.289 54.289 0 0 0-27.574 15.754q-.77-.531-1.526-1.082a3.333 3.333 0 0 1-2.506-.243 39.383 39.383 0 0 1-5.69-5.141 35.255 35.255 0 0 0-2.777-2.95c-.257-.22-.65-.52-.938-.75a5.03 5.03 0 0 0-2.942-1.11 4.066 4.066 0 0 0-3.363 1.49 4.473 4.473 0 0 0 1.038 6.222l.065.046c.276.226.616.515.872.702a35.256 35.256 0 0 0 3.499 2.048 39.367 39.367 0 0 1 6.276 4.412 4.69 4.69 0 0 1 .849 2.434l1.351 1.208a54.613 54.613 0 0 0-8.611 38.073l-1.767.514a5.835 5.835 0 0 1-1.814 1.827 39.39 39.39 0 0 1-7.565 1.247 35.266 35.266 0 0 0-4.049.326c-.324.06-.774.174-1.127.262l-.037.008-.06.018a4.236 4.236 0 1 0 1.875 8.224l.063-.01c.028-.006.052-.02.08-.025.348-.08.786-.163 1.092-.246a35.59 35.59 0 0 0 3.786-1.451 39.527 39.527 0 0 1 7.358-2.165 4.68 4.68 0 0 1 2.426.857l1.84-.315a54.922 54.922 0 0 0 24.34 30.387l-.769 1.84a4.188 4.188 0 0 1 .377 2.387 41.228 41.228 0 0 1-3.82 6.864 35.183 35.183 0 0 0-2.263 3.372c-.168.318-.381.805-.542 1.138a4.238 4.238 0 1 0 7.621 3.616l.007-.008v-.01c.16-.33.387-.763.522-1.072a34.903 34.903 0 0 0 1.218-3.868 29.517 29.517 0 0 1 3.268-7.582 3.43 3.43 0 0 1 1.819-.888l.957-1.73a54.617 54.617 0 0 0 38.946.099c.268.478.768 1.392.9 1.623a3.335 3.335 0 0 1 2.155 1.31 39.306 39.306 0 0 1 2.898 7.096 35.275 35.275 0 0 0 1.225 3.868c.137.312.36.75.522 1.082a4.24 4.24 0 1 0 7.63-3.607c-.161-.333-.383-.82-.55-1.136a35.06 35.06 0 0 0-2.263-3.364 39.372 39.372 0 0 1-3.742-6.7 3.314 3.314 0 0 1 .324-2.49 14.519 14.519 0 0 1-.703-1.703 54.92 54.92 0 0 0 24.328-30.605c.546.087 1.497.253 1.806.316a3.337 3.337 0 0 1 2.36-.877 39.476 39.476 0 0 1 7.36 2.165 35.135 35.135 0 0 0 3.788 1.462c.305.08.74.156 1.09.233.029.008.055.02.083.028l.06.009a4.236 4.236 0 1 0 1.878-8.224zm-40.1-42.987l-18.037 12.787-.063-.03a3.723 3.723 0 0 1-5.913-2.838l-.02-.01-1.253-22.103a43.85 43.85 0 0 1 25.285 12.194zm-33.978 24.228h6.788l4.22 5.276-1.513 6.58-6.096 2.934-6.114-2.94-1.516-6.583zm-6.386-35.648a44.672 44.672 0 0 1 4.503-.774l-1.255 22.137-.092.044a3.72 3.72 0 0 1-5.904 2.852l-.035.02-18.154-12.872a43.467 43.467 0 0 1 20.937-11.407zm-27.52 19.68l16.574 14.824-.018.09a3.72 3.72 0 0 1-1.462 6.395l-.017.072-21.245 6.13a43.454 43.454 0 0 1 6.168-27.51zm22.191 39.38l-8.441 20.397a43.696 43.696 0 0 1-17.536-21.948l21.783-3.7.037.049a3.655 3.655 0 0 1 .73-.065 3.72 3.72 0 0 1 3.364 5.185zm24.916 26.23a43.637 43.637 0 0 1-23.815-1.223l10.713-19.372h.018a3.725 3.725 0 0 1 6.557-.006h.08l10.74 19.404q-2.091.698-4.293 1.199zm13.841-5.751l-8.528-20.605.026-.037a3.725 3.725 0 0 1 1.803-4.823 3.685 3.685 0 0 1 1.425-.37 3.59 3.59 0 0 1 .855.063l.037-.046 21.977 3.714a43.53 43.53 0 0 1-17.595 22.105zm19.903-32.42l-21.352-6.15-.02-.09a3.725 3.725 0 0 1-1.46-6.395l-.008-.043 16.482-14.751a44.279 44.279 0 0 1 6.357 27.43z\" class=\"cls-1\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"9.70 9.20 210.86 204.86\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.46551a3.59023 3.59023 0 0 0-.855-.065 3.68515 3.68515 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53012 43.53012 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.6556 3.6556 0 0 0-.733.065l-.037-.045-21.785 3.698a43.69506 43.69506 0 0 0 17.54 21.946l8.442-20.399-.066-.08a3.68318 3.68318 0 0 0 .207-2.291zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.199l-10.738-19.407zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.0385a3.725 3.725 0 0 0 5.913 2.84l.065.028 18.036-12.789a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.922a3.72 3.72 0 0 0 5.904-2.85l.092-.044 1.253-22.139a44.68209 44.68209 0 0 0-4.501.775 43.4669 43.4669 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.728a3.72 3.72 0 0 0 1.462-6.396l.018-.087-16.574-14.825a43.454 43.454 0 0 0-6.168 27.511l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.933 1.514-6.582-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.16651l-17.422-75.675a13.60207 13.60207 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0l-70.507 33.688a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.50607 13.50607 0 0 0 .491 1.718 13.0998 13.0998 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.42334 13.42334 0 0 0 2.574 2.133 13.9237 13.9237 0 0 0 3.857 1.677 13.29785 13.29785 0 0 0 3.43.473h.759l77.504-.018a12.99345 12.99345 0 0 0 1.41-.083 13.46921 13.46921 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.94127 13.94127 0 0 0 1.479-.822 13.30347 13.30347 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67036 13.67036 0 0 0 .5-1.724 13.28215 13.28215 0 0 0-.004-5.809zm-73.147 29.432a14.51575 14.51575 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.37244 39.37244 0 0 0 3.742 6.7 35.06044 35.06044 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.27528 35.27528 0 0 1-1.225-3.868 39.3046 39.3046 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.149-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.731a3.429 3.429 0 0 0-1.819.886 29.51728 29.51728 0 0 0-3.268 7.582 34.89931 34.89931 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.17706 35.17706 0 0 1 2.262-3.373 41.22786 41.22786 0 0 0 3.82-6.866 4.18792 4.18792 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68007 4.68007 0 0 0-2.428-.855 39.52352 39.52352 0 0 0-7.356 2.165 35.58886 35.58886 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.60518.60518 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.38835 39.38835 0 0 0 7.564-1.242 5.83506 5.83506 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.36558 39.36558 0 0 0-6.277-4.41 35.2503 35.2503 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.25285 35.25285 0 0 1 2.78 2.95 39.38314 39.38314 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082a54.28892 54.28892 0 0 1 27.577-15.754 55.05181 55.05181 0 0 1 7.63-1.173l.1-1.784a4.6001 4.6001 0 0 0 1.37-2.184 39.47551 39.47551 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.079-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.861.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.53346 39.53346 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.6901 4.6901 0 0 0 2.56-.278 39.53152 39.53152 0 0 0 5.69-5.148 35.00382 35.00382 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.09523 35.09523 0 0 1-3.507 2.046 39.49476 39.49476 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.7449 4.7449 0 0 0 1.82 1.831 39.46448 39.46448 0 0 0 7.568 1.245 35.64041 35.64041 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.029-.348-.076-.785-.152-1.09-.232a35.1407 35.1407 0 0 1-3.785-1.462 39.47672 39.47672 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.91994 54.91994 0 0 1-24.328 30.605z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "cut-rectangle", "svgColor": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M20.719 19.3957H24.4084V17.0492H22.5637H20.719V19.3957ZM76.9997 57.748V54.0587H81.9041V49.2145H83.7488H85.5935V55.748C85.5935 56.8526 84.6981 57.748 83.5935 57.748H76.9997ZM22.5637 14.0337H20.719V7.50012C20.719 6.39555 21.6144 5.50012 22.719 5.50012H30.9624V7.34482V9.18951H24.4084V14.0337H22.5637ZM34.7671 7.34482V5.50012H51.2539V7.34482V9.18951H34.7671V7.34482ZM55.0586 7.34482V5.50012H71.5454V7.34482V9.18951H55.0586V7.34482ZM75.3501 7.34482V5.50012H83.5935C84.6981 5.50012 85.5935 6.39555 85.5935 7.50012V14.0337H83.7488H81.9041V9.18951H75.3501V7.34482ZM83.7488 17.0492H85.5935V30.1163H83.7488H81.9041V17.0492H83.7488ZM83.7488 33.1318H85.5935V46.199H83.7488H81.9041V33.1318H83.7488Z\" fill=\"#326CE5\"/>\n<mask id=\"path-2-inside-1_26091_15819\" fill=\"white\">\n<rect x=\"12.1252\" y=\"19.3957\" width=\"64.8745\" height=\"52.2479\" rx=\"2\"/>\n</mask>\n<rect x=\"12.1252\" y=\"19.3957\" width=\"64.8745\" height=\"52.2479\" rx=\"2\" stroke=\"#326CE5\" stroke-width=\"7.37879\" stroke-linejoin=\"round\" stroke-dasharray=\"15.59 3.6\" mask=\"url(#path-2-inside-1_26091_15819)\"/>\n<rect x=\"4.40649\" y=\"32.1616\" width=\"64.8745\" height=\"52.2479\" rx=\"3\" fill=\"white\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M69.281 34.2519C69.281 33.1474 68.3856 32.2519 67.281 32.2519H6.40649C5.30192 32.2519 4.40649 33.1474 4.40649 34.2519V82.4998C4.40649 83.6044 5.30193 84.4998 6.4065 84.4998H67.281C68.3856 84.4998 69.281 83.6044 69.281 82.4998V34.2519ZM53.7501 61.7391L52.351 61.0666C51.5324 60.6732 51.0976 59.778 51.166 58.8723C51.2166 58.2026 51.2166 57.5304 51.166 56.8607C51.0976 55.955 51.5324 55.0598 52.351 54.6664L53.7501 53.9939C54.5858 53.5233 54.9084 52.5422 54.5775 51.6727C53.8411 49.7424 52.4346 47.9716 51.0943 46.4242C50.4821 45.7142 49.4148 45.5388 48.5874 46.0014L47.3728 46.6775C46.6533 47.0779 45.769 46.9852 45.0865 46.5245C44.4196 46.0744 43.7103 45.6777 42.9661 45.3399C42.1874 44.9865 41.6376 44.2383 41.6376 43.3831V42.1328C41.6376 41.1995 40.9343 40.4098 39.9829 40.2503C37.8731 39.9073 35.7302 39.9233 33.6949 40.2583C32.7517 40.4178 32.0733 41.2155 32.0733 42.1328V43.3846C32.0733 44.2429 31.5194 44.9929 30.7378 45.3476C29.9959 45.6843 29.2888 46.0816 28.6238 46.5317C27.9418 46.9932 27.0576 47.0859 26.3381 46.6854L25.1235 46.0094C24.2878 45.5467 23.2288 45.7222 22.6166 46.4321C21.2763 47.9796 19.8615 49.7504 19.1251 51.6807C18.7859 52.5501 19.1582 53.5313 19.9939 53.9939L21.3747 54.6695C22.1858 55.0664 22.6145 55.9571 22.5457 56.8575C22.4943 57.5309 22.494 58.2063 22.5449 58.8784C22.6133 59.7812 22.1827 60.6745 21.3685 61.0705L19.9939 61.7391C19.1582 62.2097 18.7942 63.1908 19.1251 64.0603C19.8615 65.9826 21.2763 67.7534 22.6166 69.3009C23.2288 70.0108 24.2961 70.1863 25.1235 69.7236L26.3397 69.0507C27.0586 68.653 27.9404 68.7466 28.6213 69.2064C29.2891 69.6574 29.9995 70.0548 30.7447 70.3931C31.5235 70.7465 32.0733 71.4947 32.0733 72.3499V73.6002C32.0733 74.5335 32.7766 75.3232 33.728 75.4827C35.8378 75.8337 37.9724 75.8177 40.0077 75.4827C40.9509 75.3232 41.6376 74.5255 41.6376 73.6002V72.3564C41.6376 71.4981 42.1915 70.748 42.9731 70.3933C43.7149 70.0567 44.4221 69.6593 45.0871 69.2093C45.7691 68.7478 46.6533 68.6551 47.3728 69.0556L48.5874 69.7316C49.423 70.1942 50.4821 70.0188 51.0943 69.3088C52.4346 67.7614 53.8825 65.9906 54.6189 64.0603C54.9498 63.1829 54.5858 62.2017 53.7501 61.7391ZM41.0168 62.237C41.159 62.1378 41.2907 62.0106 41.3948 61.8719C45.9126 55.8513 38.947 49.1372 32.7023 53.496C32.5601 53.5952 32.4284 53.7224 32.3244 53.8611C27.8065 59.8817 34.7722 66.5958 41.0168 62.237Z\" fill=\"#326CE5\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.0461 16.9755H22.1948V7.5C22.1948 6.39543 23.0903 5.5 24.1948 5.5H32.2385V7.35132V9.20263H25.8975V16.9755H24.0461ZM35.9509 7.35132V5.5H52.0382V7.35132V9.20263H35.9509V7.35132ZM55.7507 7.35132V5.5H71.8379V7.35132V9.20263H55.7507V7.35132ZM75.5504 7.35132V5.5H83.594C84.6986 5.5 85.594 6.39543 85.594 7.5V16.9755H83.7427H81.8914V9.20263H75.5504V7.35132ZM83.7427 21.3489H85.594V40.2999H83.7427H81.8914V21.3489H83.7427ZM83.7427 44.6732H85.594V54.1488C85.594 55.2533 84.6986 56.1488 83.594 56.1488H76.2737V52.4461H81.8914V44.6732H83.7427Z\" fill=\"white\"/>\n<mask id=\"path-2-inside-1_26091_15973\" fill=\"white\">\n<rect x=\"12.8745\" y=\"18.8327\" width=\"63.3992\" height=\"50.6488\" rx=\"2\"/>\n</mask>\n<rect x=\"12.8745\" y=\"18.8327\" width=\"63.3992\" height=\"50.6488\" rx=\"2\" stroke=\"white\" stroke-width=\"7.40526\" stroke-linejoin=\"round\" stroke-dasharray=\"15.64 3.61\" mask=\"url(#path-2-inside-1_26091_15973)\"/>\n<rect x=\"4.40576\" y=\"32.1666\" width=\"63.3992\" height=\"50.6488\" rx=\"3.38711\" fill=\"#326CE5\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M6.40576 31.9865C5.30119 31.9865 4.40576 32.882 4.40576 33.9865V82.5C4.40576 83.6046 5.30119 84.5 6.40576 84.5H66.0683C67.1729 84.5 68.0683 83.6046 68.0683 82.5V33.9865C68.0683 32.882 67.1729 31.9865 66.0683 31.9865H6.40576ZM52.7494 61.7394L52.2576 61.503C50.9043 60.8526 50.2032 59.3683 50.2032 57.8667C50.2032 56.3652 50.9043 54.8809 52.2576 54.2305L52.7494 53.9941C53.585 53.5235 53.9077 52.5424 53.5767 51.673C52.8404 49.7426 51.4339 47.9718 50.0936 46.4244C49.4813 45.7145 48.414 45.539 47.5867 46.0016L47.1994 46.2172C45.9809 46.8954 44.4887 46.7237 43.284 46.0213C43.1422 45.9387 42.9988 45.8583 42.8538 45.7803C41.5786 45.0937 40.6368 43.84 40.6368 42.3917V42.133C40.6368 41.1998 39.9336 40.4101 38.9821 40.2506C36.8724 39.9076 34.7295 39.9235 32.6942 40.2585C31.751 40.4181 31.0726 41.2157 31.0726 42.133V42.3888C31.0726 43.8423 30.1238 45.0996 28.845 45.7907C28.7032 45.8674 28.5629 45.9463 28.4242 46.0274C27.2203 46.7313 25.7285 46.9034 24.51 46.2251L24.1227 46.0096C23.2871 45.547 22.2281 45.7225 21.6159 46.4324C20.2755 47.9798 18.8607 49.7506 18.1244 51.6809C17.7852 52.5504 18.1575 53.5315 18.9931 53.9941L19.467 54.226C20.8134 54.8848 21.5067 56.3705 21.5062 57.8695C21.5057 59.3724 20.8083 60.8565 19.4567 61.5139L18.9931 61.7394C18.1575 62.21 17.7934 63.1911 18.1244 64.0605C18.8607 65.9829 20.2755 67.7537 21.6159 69.3011C22.2281 70.011 23.2954 70.1865 24.1227 69.7239L24.5126 69.5082C25.7301 68.8346 27.2183 69.0077 28.42 69.709C28.5636 69.7928 28.7088 69.8742 28.8556 69.9532C30.1308 70.6398 31.0726 71.8935 31.0726 73.3418V73.6005C31.0726 74.5337 31.7758 75.3234 32.7273 75.4829C34.8371 75.8339 36.9716 75.818 39.0069 75.4829C39.9501 75.3234 40.6368 74.5258 40.6368 73.6005V73.3527C40.6368 71.8991 41.5857 70.6419 42.8644 69.9507C43.0062 69.8741 43.1465 69.7952 43.2852 69.7141C44.4891 69.0102 45.9809 68.8381 47.1994 69.5163L47.5867 69.7318C48.4223 70.1945 49.4813 70.019 50.0936 69.3091C51.4339 67.7616 52.8818 65.9909 53.6181 64.0605C53.9491 63.1831 53.585 62.202 52.7494 61.7394ZM39.8664 62.3396C40.1105 62.1763 40.33 61.9646 40.5014 61.7261C44.7647 55.7919 38.0062 49.2784 31.8513 53.3939C31.6072 53.5572 31.3877 53.7689 31.2163 54.0074C26.9529 59.9416 33.7115 66.4551 39.8664 62.3396Z\" fill=\"white\"/>\n</svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3"}, "configuration": null, "component": {"version": "v1", "kind": "ReplicationController", "schema": "{\"description\":\"Replication<PERSON>ontroller represents the configuration of a replication controller.\",\"properties\":{\"spec\":{\"allOf\":[{\"description\":\"ReplicationControllerSpec is the specification of a replication controller.\",\"properties\":{\"minReadySeconds\":{\"default\":0,\"description\":\"Minimum number of seconds for which a newly created pod should be ready without any of its container crashing, for it to be considered available. Defaults to 0 (pod will be considered available as soon as it is ready)\",\"format\":\"int32\",\"type\":\"integer\"},\"replicas\":{\"default\":1,\"description\":\"Replicas is the number of desired replicas. This is a pointer to distinguish between explicit zero and unspecified. Defaults to 1. More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#what-is-a-replicationcontroller\",\"format\":\"int32\",\"type\":\"integer\"},\"selector\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"Selector is a label query over pods that should match the Replicas count. If Selector is empty, it is defaulted to the labels present on the Pod template. Label keys and values that must match in order to be controlled by this replication controller, if empty defaulted to labels on Pod template. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#label-selectors\",\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"},\"template\":{\"allOf\":[{\"description\":\"PodTemplateSpec describes the data a pod should have when created from a template\",\"properties\":{\"metadata\":{\"allOf\":[{\"description\":\"ObjectMeta is metadata that all persisted resources must have, which includes all objects users must create.\",\"properties\":{\"annotations\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations\",\"type\":\"object\"},\"creationTimestamp\":{\"allOf\":[{\"description\":\"Time is a wrapper around time.Time which supports correct marshaling to YAML and JSON.  Wrappers are provided for many of the factory methods that the time package offers.\",\"format\":\"date-time\",\"type\":\"string\"}],\"description\":\"CreationTimestamp is a timestamp representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations. Clients may not set this value. It is represented in RFC3339 form and is in UTC.\\n\\nPopulated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata\"},\"deletionGracePeriodSeconds\":{\"description\":\"Number of seconds allowed for this object to gracefully terminate before it will be removed from the system. Only set when deletionTimestamp is also set. May only be shortened. Read-only.\",\"format\":\"int64\",\"type\":\"integer\"},\"deletionTimestamp\":{\"allOf\":[{\"description\":\"Time is a wrapper around time.Time which supports correct marshaling to YAML and JSON.  Wrappers are provided for many of the factory methods that the time package offers.\",\"format\":\"date-time\",\"type\":\"string\"}],\"description\":\"DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This field is set by the server when a graceful deletion is requested by the user, and is not directly settable by a client. The resource is expected to be deleted (no longer visible from resource lists, and not reachable by name) after the time in this field, once the finalizers list is empty. As long as the finalizers list contains items, deletion is blocked. Once the deletionTimestamp is set, this value may not be unset or be set further into the future, although it may be shortened or the resource may be deleted prior to this time. For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react by sending a graceful termination signal to the containers in the pod. After that 30 seconds, the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup, remove the pod from the API. In the presence of network partitions, this object may still exist after this timestamp, until an administrator or automated process can determine the resource is fully terminated. If not set, graceful deletion of the object has not been requested.\\n\\nPopulated by the system when a graceful deletion is requested. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata\"},\"finalizers\":{\"description\":\"Must be empty before the object is deleted from the registry. Each entry is an identifier for the responsible component that will remove the entry from the list. If the deletionTimestamp of the object is non-nil, entries in this list can only be removed. Finalizers may be processed and removed in any order.  Order is NOT enforced because it introduces significant risk of stuck finalizers. finalizers is a shared field, any actor with permission can reorder it. If the finalizer list is processed in order, then this can lead to a situation in which the component responsible for the first finalizer in the list is waiting for a signal (field value, external system, or other) produced by a component responsible for a finalizer later in the list, resulting in a deadlock. Without enforced ordering finalizers are free to order amongst themselves and are not vulnerable to ordering changes in the list.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"set\",\"x-kubernetes-patch-strategy\":\"merge\"},\"generateName\":{\"description\":\"GenerateName is an optional prefix, used by the server, to generate a unique name ONLY IF the Name field has not been provided. If this field is used, the name returned to the client will be different than the name passed. This value will also be combined with a unique suffix. The provided value has the same validation rules as the Name field, and may be truncated by the length of the suffix required to make the value unique on the server.\\n\\nIf this field is specified and the generated name exists, the server will return a 409.\\n\\nApplied only if Name is not specified. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency\",\"type\":\"string\"},\"generation\":{\"description\":\"A sequence number representing a specific generation of the desired state. Populated by the system. Read-only.\",\"format\":\"int64\",\"type\":\"integer\"},\"labels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels\",\"type\":\"object\"},\"managedFields\":{\"description\":\"ManagedFields maps workflow-id and version to the set of fields that are managed by that workflow. This is mostly for internal housekeeping, and users typically shouldn't need to set or understand this field. A workflow can be the user's name, a controller's name, or the name of a specific apply path like \\\"ci-cd\\\". The set of fields is always in the version that the workflow used when modifying the object.\",\"items\":{\"allOf\":[{\"description\":\"ManagedFieldsEntry is a workflow-id, a FieldSet and the group version of the resource that the fieldset applies to.\",\"properties\":{\"apiVersion\":{\"description\":\"APIVersion defines the version of this resource that this field set applies to. The format is \\\"group/version\\\" just like the top-level APIVersion field. It is necessary to track the version of a field set because it cannot be automatically converted.\",\"type\":\"string\"},\"fieldsType\":{\"description\":\"FieldsType is the discriminator for the different fields format and version. There is currently only one possible value: \\\"FieldsV1\\\"\",\"type\":\"string\"},\"fieldsV1\":{\"allOf\":[{\"description\":\"FieldsV1 stores a set of fields in a data structure like a Trie, in JSON format.\\n\\nEach key is either a '.' representing the field itself, and will always map to an empty set, or a string representing a sub-field or item. The string will follow one of these four formats: 'f:\\u003cname\\u003e', where \\u003cname\\u003e is the name of a field in a struct, or key in a map 'v:\\u003cvalue\\u003e', where \\u003cvalue\\u003e is the exact json formatted value of a list item 'i:\\u003cindex\\u003e', where \\u003cindex\\u003e is position of a item in a list 'k:\\u003ckeys\\u003e', where \\u003ckeys\\u003e is a map of  a list item's key fields to their unique values If a key maps to an empty Fields value, the field that key represents is part of the set.\\n\\nThe exact format is defined in sigs.k8s.io/structured-merge-diff\",\"type\":\"object\"}],\"description\":\"FieldsV1 holds the first JSON version format as described in the \\\"FieldsV1\\\" type.\"},\"manager\":{\"description\":\"Manager is an identifier of the workflow managing these fields.\",\"type\":\"string\"},\"operation\":{\"description\":\"Operation is the type of operation which lead to this ManagedFieldsEntry being created. The only valid values for this field are 'Apply' and 'Update'.\",\"type\":\"string\"},\"subresource\":{\"description\":\"Subresource is the name of the subresource used to update that object, or empty string if the object was updated through the main resource. The value of this field is used to distinguish between managers, even if they share the same name. For example, a status update will be distinct from a regular update using the same manager name. Note that the APIVersion field is not related to the Subresource field and it always corresponds to the version of the main resource.\",\"type\":\"string\"},\"time\":{\"allOf\":[{\"description\":\"Time is a wrapper around time.Time which supports correct marshaling to YAML and JSON.  Wrappers are provided for many of the factory methods that the time package offers.\",\"format\":\"date-time\",\"type\":\"string\"}],\"description\":\"Time is the timestamp of when the ManagedFields entry was added. The timestamp will also be updated if a field is added, the manager changes any of the owned fields value or removes a field. The timestamp does not update when a field is removed from the entry because another manager took it over.\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"name\":{\"description\":\"Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names\",\"type\":\"string\"},\"namespace\":{\"description\":\"Namespace defines the space within which each name must be unique. An empty namespace is equivalent to the \\\"default\\\" namespace, but \\\"default\\\" is the canonical representation. Not all objects are required to be scoped to a namespace - the value of this field for those objects will be empty.\\n\\nMust be a DNS_LABEL. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces\",\"type\":\"string\"},\"ownerReferences\":{\"description\":\"List of objects depended by this object. If ALL objects in the list have been deleted, this object will be garbage collected. If this object is managed by a controller, then an entry in this list will point to this controller, with the controller field set to true. There cannot be more than one managing controller.\",\"items\":{\"allOf\":[{\"description\":\"OwnerReference contains enough information to let you identify an owning object. An owning object must be in the same namespace as the dependent, or be cluster-scoped, so there is no namespace field.\",\"properties\":{\"apiVersion\":{\"default\":\"\",\"description\":\"API version of the referent.\",\"type\":\"string\"},\"blockOwnerDeletion\":{\"description\":\"If true, AND if the owner has the \\\"foregroundDeletion\\\" finalizer, then the owner cannot be deleted from the key-value store until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion for how the garbage collector interacts with this field and enforces the foreground deletion. Defaults to false. To set this field, a user needs \\\"delete\\\" permission of the owner, otherwise 422 (Unprocessable Entity) will be returned.\",\"type\":\"boolean\"},\"controller\":{\"description\":\"If true, this reference points to the managing controller.\",\"type\":\"boolean\"},\"kind\":{\"default\":\"\",\"description\":\"Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names\",\"type\":\"string\"},\"uid\":{\"default\":\"\",\"description\":\"UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids\",\"type\":\"string\"}},\"required\":[\"apiVersion\",\"kind\",\"name\",\"uid\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"uid\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"uid\",\"x-kubernetes-patch-strategy\":\"merge\"},\"resourceVersion\":{\"description\":\"An opaque value that represents the internal version of this object that can be used by clients to determine when objects have changed. May be used for optimistic concurrency, change detection, and the watch operation on a resource or set of resources. Clients must treat these values as opaque and passed unmodified back to the server. They may only be valid for a particular resource or set of resources.\\n\\nPopulated by the system. Read-only. Value must be treated as opaque by clients and . More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency\",\"type\":\"string\"},\"selfLink\":{\"description\":\"Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.\",\"type\":\"string\"},\"uid\":{\"description\":\"UID is the unique in time and space value for this object. It is typically generated by the server on successful creation of a resource and is not allowed to change on PUT operations.\\n\\nPopulated by the system. Read-only. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata\"},\"spec\":{\"allOf\":[{\"description\":\"PodSpec is a description of a pod.\",\"properties\":{\"activeDeadlineSeconds\":{\"description\":\"Optional duration in seconds the pod may be active on the node relative to StartTime before the system will actively try to mark it failed and kill associated containers. Value must be a positive integer.\",\"format\":\"int64\",\"type\":\"integer\"},\"affinity\":{\"allOf\":[{\"description\":\"Affinity is a group of affinity scheduling rules.\",\"properties\":{\"nodeAffinity\":{\"allOf\":[{\"description\":\"Node affinity is a group of node affinity scheduling rules.\",\"properties\":{\"preferredDuringSchedulingIgnoredDuringExecution\":{\"description\":\"The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \\\"weight\\\" to the sum if the node matches the corresponding matchExpressions; the node(s) with the highest sum are the most preferred.\",\"items\":{\"allOf\":[{\"description\":\"An empty preferred scheduling term matches all objects with implicit weight 0 (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).\",\"properties\":{\"preference\":{\"allOf\":[{\"description\":\"A null or empty node selector term matches no objects. The requirements of them are ANDed. The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.\",\"properties\":{\"matchExpressions\":{\"description\":\"A list of node selector requirements by node's labels.\",\"items\":{\"allOf\":[{\"description\":\"A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"Represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\"type\":\"string\"},\"values\":{\"description\":\"An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchFields\":{\"description\":\"A list of node selector requirements by node's fields.\",\"items\":{\"allOf\":[{\"description\":\"A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"Represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\"type\":\"string\"},\"values\":{\"description\":\"An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"default\":{},\"description\":\"A node selector term, associated with the corresponding weight.\"},\"weight\":{\"default\":0,\"description\":\"Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.\",\"format\":\"int32\",\"type\":\"integer\"}},\"required\":[\"weight\",\"preference\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"requiredDuringSchedulingIgnoredDuringExecution\":{\"allOf\":[{\"description\":\"A node selector represents the union of the results of one or more label queries over a set of nodes; that is, it represents the OR of the selectors represented by the node selector terms.\",\"properties\":{\"nodeSelectorTerms\":{\"description\":\"Required. A list of node selector terms. The terms are ORed.\",\"items\":{\"allOf\":[{\"description\":\"A null or empty node selector term matches no objects. The requirements of them are ANDed. The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.\",\"properties\":{\"matchExpressions\":{\"description\":\"A list of node selector requirements by node's labels.\",\"items\":{\"allOf\":[{\"description\":\"A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"Represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\"type\":\"string\"},\"values\":{\"description\":\"An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchFields\":{\"description\":\"A list of node selector requirements by node's fields.\",\"items\":{\"allOf\":[{\"description\":\"A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"Represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\"type\":\"string\"},\"values\":{\"description\":\"An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"nodeSelectorTerms\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to an update), the system may or may not try to eventually evict the pod from its node.\"}},\"type\":\"object\"}],\"description\":\"Describes node affinity scheduling rules for the pod.\"},\"podAffinity\":{\"allOf\":[{\"description\":\"Pod affinity is a group of inter pod affinity scheduling rules.\",\"properties\":{\"preferredDuringSchedulingIgnoredDuringExecution\":{\"description\":\"The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \\\"weight\\\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.\",\"items\":{\"allOf\":[{\"description\":\"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\"properties\":{\"podAffinityTerm\":{\"allOf\":[{\"description\":\"Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key \\u003ctopologyKey\\u003e matches that of any node on which a pod of the set of pods is running\",\"properties\":{\"labelSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"A label query over a set of resources, in this case pods. If it's null, this PodAffinityTerm matches with no Pods.\"},\"matchLabelKeys\":{\"description\":\"MatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both matchLabelKeys and labelSelector. Also, matchLabelKeys cannot be set when labelSelector isn't set.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"mismatchLabelKeys\":{\"description\":\"MismatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both mismatchLabelKeys and labelSelector. Also, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"namespaceSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \\\"this pod's namespace\\\". An empty selector ({}) matches all namespaces.\"},\"namespaces\":{\"description\":\"namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"topologyKey\":{\"default\":\"\",\"description\":\"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.\",\"type\":\"string\"}},\"required\":[\"topologyKey\"],\"type\":\"object\"}],\"default\":{},\"description\":\"Required. A pod affinity term, associated with the corresponding weight.\"},\"weight\":{\"default\":0,\"description\":\"weight associated with matching the corresponding podAffinityTerm, in the range 1-100.\",\"format\":\"int32\",\"type\":\"integer\"}},\"required\":[\"weight\",\"podAffinityTerm\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"requiredDuringSchedulingIgnoredDuringExecution\":{\"description\":\"If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.\",\"items\":{\"allOf\":[{\"description\":\"Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key \\u003ctopologyKey\\u003e matches that of any node on which a pod of the set of pods is running\",\"properties\":{\"labelSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"A label query over a set of resources, in this case pods. If it's null, this PodAffinityTerm matches with no Pods.\"},\"matchLabelKeys\":{\"description\":\"MatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both matchLabelKeys and labelSelector. Also, matchLabelKeys cannot be set when labelSelector isn't set.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"mismatchLabelKeys\":{\"description\":\"MismatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both mismatchLabelKeys and labelSelector. Also, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"namespaceSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \\\"this pod's namespace\\\". An empty selector ({}) matches all namespaces.\"},\"namespaces\":{\"description\":\"namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"topologyKey\":{\"default\":\"\",\"description\":\"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.\",\"type\":\"string\"}},\"required\":[\"topologyKey\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).\"},\"podAntiAffinity\":{\"allOf\":[{\"description\":\"Pod anti affinity is a group of inter pod anti affinity scheduling rules.\",\"properties\":{\"preferredDuringSchedulingIgnoredDuringExecution\":{\"description\":\"The scheduler will prefer to schedule pods to nodes that satisfy the anti-affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling anti-affinity expressions, etc.), compute a sum by iterating through the elements of this field and subtracting \\\"weight\\\" from the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.\",\"items\":{\"allOf\":[{\"description\":\"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\"properties\":{\"podAffinityTerm\":{\"allOf\":[{\"description\":\"Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key \\u003ctopologyKey\\u003e matches that of any node on which a pod of the set of pods is running\",\"properties\":{\"labelSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"A label query over a set of resources, in this case pods. If it's null, this PodAffinityTerm matches with no Pods.\"},\"matchLabelKeys\":{\"description\":\"MatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both matchLabelKeys and labelSelector. Also, matchLabelKeys cannot be set when labelSelector isn't set.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"mismatchLabelKeys\":{\"description\":\"MismatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both mismatchLabelKeys and labelSelector. Also, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"namespaceSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \\\"this pod's namespace\\\". An empty selector ({}) matches all namespaces.\"},\"namespaces\":{\"description\":\"namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"topologyKey\":{\"default\":\"\",\"description\":\"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.\",\"type\":\"string\"}},\"required\":[\"topologyKey\"],\"type\":\"object\"}],\"default\":{},\"description\":\"Required. A pod affinity term, associated with the corresponding weight.\"},\"weight\":{\"default\":0,\"description\":\"weight associated with matching the corresponding podAffinityTerm, in the range 1-100.\",\"format\":\"int32\",\"type\":\"integer\"}},\"required\":[\"weight\",\"podAffinityTerm\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"requiredDuringSchedulingIgnoredDuringExecution\":{\"description\":\"If the anti-affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the anti-affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.\",\"items\":{\"allOf\":[{\"description\":\"Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key \\u003ctopologyKey\\u003e matches that of any node on which a pod of the set of pods is running\",\"properties\":{\"labelSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"A label query over a set of resources, in this case pods. If it's null, this PodAffinityTerm matches with no Pods.\"},\"matchLabelKeys\":{\"description\":\"MatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both matchLabelKeys and labelSelector. Also, matchLabelKeys cannot be set when labelSelector isn't set.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"mismatchLabelKeys\":{\"description\":\"MismatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both mismatchLabelKeys and labelSelector. Also, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"namespaceSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \\\"this pod's namespace\\\". An empty selector ({}) matches all namespaces.\"},\"namespaces\":{\"description\":\"namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"topologyKey\":{\"default\":\"\",\"description\":\"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.\",\"type\":\"string\"}},\"required\":[\"topologyKey\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).\"}},\"type\":\"object\"}],\"description\":\"If specified, the pod's scheduling constraints\"},\"automountServiceAccountToken\":{\"description\":\"AutomountServiceAccountToken indicates whether a service account token should be automatically mounted.\",\"type\":\"boolean\"},\"containers\":{\"description\":\"List of containers belonging to the pod. Containers cannot currently be added or removed. There must be at least one container in a Pod. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"A single application container that you want to run within a pod.\",\"properties\":{\"args\":{\"description\":\"Arguments to the entrypoint. The container image's CMD is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"command\":{\"description\":\"Entrypoint array. Not executed within a shell. The container image's ENTRYPOINT is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"env\":{\"description\":\"List of environment variables to set in the container. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"EnvVar represents an environment variable present in a Container.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the environment variable. May consist of any printable ASCII characters except '='.\",\"type\":\"string\"},\"value\":{\"description\":\"Variable references $(VAR_NAME) are expanded using the previously defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to \\\"\\\".\",\"type\":\"string\"},\"valueFrom\":{\"allOf\":[{\"description\":\"EnvVarSource represents a source for the value of an EnvVar.\",\"properties\":{\"configMapKeyRef\":{\"allOf\":[{\"description\":\"Selects a key from a ConfigMap.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The key to select.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the ConfigMap or its key must be defined\",\"type\":\"boolean\"}},\"required\":[\"key\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a key of a ConfigMap.\"},\"fieldRef\":{\"allOf\":[{\"description\":\"ObjectFieldSelector selects an APIVersioned field of an object.\",\"properties\":{\"apiVersion\":{\"description\":\"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\"type\":\"string\"},\"fieldPath\":{\"default\":\"\",\"description\":\"Path of the field to select in the specified API version.\",\"type\":\"string\"}},\"required\":[\"fieldPath\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\"},\"resourceFieldRef\":{\"allOf\":[{\"description\":\"ResourceFieldSelector represents container resources (cpu, memory) and their output format\",\"properties\":{\"containerName\":{\"description\":\"Container name: required for volumes, optional for env vars\",\"type\":\"string\"},\"divisor\":{\"allOf\":[{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]}],\"description\":\"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\"},\"resource\":{\"default\":\"\",\"description\":\"Required: resource to select\",\"type\":\"string\"}},\"required\":[\"resource\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\"},\"secretKeyRef\":{\"allOf\":[{\"description\":\"SecretKeySelector selects a key of a Secret.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The key of the secret to select from.  Must be a valid secret key.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the Secret or its key must be defined\",\"type\":\"boolean\"}},\"required\":[\"key\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a key of a secret in the pod's namespace\"}},\"type\":\"object\"}],\"description\":\"Source for the environment variable's value. Cannot be used if value is not empty.\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"},\"envFrom\":{\"description\":\"List of sources to populate environment variables in the container. The keys defined within a source may consist of any printable ASCII characters except '='. When a key exists in multiple sources, the value associated with the last source will take precedence. Values defined by an Env with a duplicate key will take precedence. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"EnvFromSource represents the source of a set of ConfigMaps or Secrets\",\"properties\":{\"configMapRef\":{\"allOf\":[{\"description\":\"ConfigMapEnvSource selects a ConfigMap to populate the environment variables with.\\n\\nThe contents of the target ConfigMap's Data field will represent the key-value pairs as environment variables.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the ConfigMap must be defined\",\"type\":\"boolean\"}},\"type\":\"object\"}],\"description\":\"The ConfigMap to select from\"},\"prefix\":{\"description\":\"Optional text to prepend to the name of each environment variable. May consist of any printable ASCII characters except '='.\",\"type\":\"string\"},\"secretRef\":{\"allOf\":[{\"description\":\"SecretEnvSource selects a Secret to populate the environment variables with.\\n\\nThe contents of the target Secret's Data field will represent the key-value pairs as environment variables.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the Secret must be defined\",\"type\":\"boolean\"}},\"type\":\"object\"}],\"description\":\"The Secret to select from\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"image\":{\"description\":\"Container image name. More info: https://kubernetes.io/docs/concepts/containers/images This field is optional to allow higher level config management to default or override container images in workload controllers like Deployments and StatefulSets.\",\"type\":\"string\"},\"imagePullPolicy\":{\"description\":\"Image pull policy. One of Always, Never, IfNotPresent. Defaults to Always if :latest tag is specified, or IfNotPresent otherwise. Cannot be updated. More info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\"type\":\"string\"},\"lifecycle\":{\"allOf\":[{\"description\":\"Lifecycle describes actions that the management system should take in response to container lifecycle events. For the PostStart and PreStop lifecycle handlers, management of the container blocks until the action is complete, unless the container process fails, in which case the handler is aborted.\",\"properties\":{\"postStart\":{\"allOf\":[{\"description\":\"LifecycleHandler defines a specific action that should be taken in a lifecycle hook. One and only one of the fields, except TCPSocket must be specified.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"sleep\":{\"allOf\":[{\"description\":\"SleepAction describes a \\\"sleep\\\" action.\",\"properties\":{\"seconds\":{\"default\":0,\"description\":\"Seconds is the number of seconds to sleep.\",\"format\":\"int64\",\"type\":\"integer\"}},\"required\":[\"seconds\"],\"type\":\"object\"}],\"description\":\"Sleep represents a duration that the container should sleep.\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for backward compatibility. There is no validation of this field and lifecycle hooks will fail at runtime when it is specified.\"}},\"type\":\"object\"}],\"description\":\"PostStart is called immediately after a container is created. If the handler fails, the container is terminated and restarted according to its restart policy. Other management of the container blocks until the hook completes. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\"},\"preStop\":{\"allOf\":[{\"description\":\"LifecycleHandler defines a specific action that should be taken in a lifecycle hook. One and only one of the fields, except TCPSocket must be specified.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"sleep\":{\"allOf\":[{\"description\":\"SleepAction describes a \\\"sleep\\\" action.\",\"properties\":{\"seconds\":{\"default\":0,\"description\":\"Seconds is the number of seconds to sleep.\",\"format\":\"int64\",\"type\":\"integer\"}},\"required\":[\"seconds\"],\"type\":\"object\"}],\"description\":\"Sleep represents a duration that the container should sleep.\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for backward compatibility. There is no validation of this field and lifecycle hooks will fail at runtime when it is specified.\"}},\"type\":\"object\"}],\"description\":\"PreStop is called immediately before a container is terminated due to an API request or management event such as liveness/startup probe failure, preemption, resource contention, etc. The handler is not called if the container crashes or exits. The Pod's termination grace period countdown begins before the PreStop hook is executed. Regardless of the outcome of the handler, the container will eventually terminate within the Pod's termination grace period (unless delayed by finalizers). Other management of the container blocks until the hook completes or until the termination grace period is reached. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\"},\"stopSignal\":{\"description\":\"StopSignal defines which signal will be sent to a container when it is being stopped. If not specified, the default is defined by the container runtime in use. StopSignal can only be set for Pods with a non-empty .spec.os.name\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"Actions that the management system should take in response to container lifecycle events. Cannot be updated.\"},\"livenessProbe\":{\"allOf\":[{\"description\":\"Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"failureThreshold\":{\"description\":\"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"grpc\":{\"allOf\":[{\"description\":\"GRPCAction specifies an action involving a GRPC service.\",\"properties\":{\"port\":{\"default\":0,\"description\":\"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\"format\":\"int32\",\"type\":\"integer\"},\"service\":{\"default\":\"\",\"description\":\"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"GRPC specifies a GRPC HealthCheckRequest.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"initialDelaySeconds\":{\"description\":\"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"},\"periodSeconds\":{\"description\":\"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"successThreshold\":{\"description\":\"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"TCPSocket specifies a connection to a TCP port.\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\"format\":\"int64\",\"type\":\"integer\"},\"timeoutSeconds\":{\"description\":\"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"}},\"type\":\"object\"}],\"description\":\"Periodic probe of container liveness. Container will be restarted if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\"},\"name\":{\"default\":\"\",\"description\":\"Name of the container specified as a DNS_LABEL. Each container in a pod must have a unique name (DNS_LABEL). Cannot be updated.\",\"type\":\"string\"},\"ports\":{\"description\":\"List of ports to expose from the container. Not specifying a port here DOES NOT prevent that port from being exposed. Any port which is listening on the default \\\"0.0.0.0\\\" address inside a container will be accessible from the network. Modifying this array with strategic merge patch may corrupt the data. For more information See https://github.com/kubernetes/kubernetes/issues/108255. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"ContainerPort represents a network port in a single container.\",\"properties\":{\"containerPort\":{\"default\":0,\"description\":\"Number of port to expose on the pod's IP address. This must be a valid port number, 0 \\u003c x \\u003c 65536.\",\"format\":\"int32\",\"type\":\"integer\"},\"hostIP\":{\"description\":\"What host IP to bind the external port to.\",\"type\":\"string\"},\"hostPort\":{\"description\":\"Number of port to expose on the host. If specified, this must be a valid port number, 0 \\u003c x \\u003c 65536. If HostNetwork is specified, this must match ContainerPort. Most containers do not need this.\",\"format\":\"int32\",\"type\":\"integer\"},\"name\":{\"description\":\"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each named port in a pod must have a unique name. Name for the port that can be referred to by services.\",\"type\":\"string\"},\"protocol\":{\"default\":\"TCP\",\"description\":\"Protocol for port. Must be UDP, TCP, or SCTP. Defaults to \\\"TCP\\\".\",\"type\":\"string\"}},\"required\":[\"containerPort\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"containerPort\",\"protocol\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"containerPort\",\"x-kubernetes-patch-strategy\":\"merge\"},\"readinessProbe\":{\"allOf\":[{\"description\":\"Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"failureThreshold\":{\"description\":\"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"grpc\":{\"allOf\":[{\"description\":\"GRPCAction specifies an action involving a GRPC service.\",\"properties\":{\"port\":{\"default\":0,\"description\":\"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\"format\":\"int32\",\"type\":\"integer\"},\"service\":{\"default\":\"\",\"description\":\"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"GRPC specifies a GRPC HealthCheckRequest.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"initialDelaySeconds\":{\"description\":\"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"},\"periodSeconds\":{\"description\":\"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"successThreshold\":{\"description\":\"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"TCPSocket specifies a connection to a TCP port.\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\"format\":\"int64\",\"type\":\"integer\"},\"timeoutSeconds\":{\"description\":\"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"}},\"type\":\"object\"}],\"description\":\"Periodic probe of container service readiness. Container will be removed from service endpoints if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\"},\"resizePolicy\":{\"description\":\"Resources resize policy for the container.\",\"items\":{\"allOf\":[{\"description\":\"ContainerResizePolicy represents resource resize policy for the container.\",\"properties\":{\"resourceName\":{\"default\":\"\",\"description\":\"Name of the resource to which this resource resize policy applies. Supported values: cpu, memory.\",\"type\":\"string\"},\"restartPolicy\":{\"default\":\"\",\"description\":\"Restart policy to apply when specified resource is resized. If not specified, it defaults to NotRequired.\",\"type\":\"string\"}},\"required\":[\"resourceName\",\"restartPolicy\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"resources\":{\"allOf\":[{\"description\":\"ResourceRequirements describes the compute resource requirements.\",\"properties\":{\"claims\":{\"description\":\"Claims lists the names of resources, defined in spec.resourceClaims, that are used by this container.\\n\\nThis is an alpha field and requires enabling the DynamicResourceAllocation feature gate.\\n\\nThis field is immutable. It can only be set for containers.\",\"items\":{\"allOf\":[{\"description\":\"ResourceClaim references one entry in PodSpec.ResourceClaims.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name must match the name of one entry in pod.spec.resourceClaims of the Pod where this field is used. It makes that resource available inside a container.\",\"type\":\"string\"},\"request\":{\"description\":\"Request is the name chosen for a request in the referenced claim. If empty, everything from the claim is made available, otherwise only the result of this request.\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\"},\"limits\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"},\"requests\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. Requests cannot exceed Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"Compute Resources required by this container. Cannot be updated. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\"},\"restartPolicy\":{\"description\":\"RestartPolicy defines the restart behavior of individual containers in a pod. This field may only be set for init containers, and the only allowed value is \\\"Always\\\". For non-init containers or when this field is not specified, the restart behavior is defined by the Pod's restart policy and the container type. Setting the RestartPolicy as \\\"Always\\\" for the init container will have the following effect: this init container will be continually restarted on exit until all regular containers have terminated. Once all regular containers have completed, all init containers with restartPolicy \\\"Always\\\" will be shut down. This lifecycle differs from normal init containers and is often referred to as a \\\"sidecar\\\" container. Although this init container still starts in the init container sequence, it does not wait for the container to complete before proceeding to the next init container. Instead, the next init container starts immediately after this init container is started, or after any startupProbe has successfully completed.\",\"type\":\"string\"},\"securityContext\":{\"allOf\":[{\"description\":\"SecurityContext holds security configuration that will be applied to a container. Some fields are present in both SecurityContext and PodSecurityContext.  When both are set, the values in SecurityContext take precedence.\",\"properties\":{\"allowPrivilegeEscalation\":{\"description\":\"AllowPrivilegeEscalation controls whether a process can gain more privileges than its parent process. This bool directly controls if the no_new_privs flag will be set on the container process. AllowPrivilegeEscalation is true always when the container is: 1) run as Privileged 2) has CAP_SYS_ADMIN Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"boolean\"},\"appArmorProfile\":{\"allOf\":[{\"description\":\"AppArmorProfile defines a pod or container's AppArmor settings.\",\"properties\":{\"localhostProfile\":{\"description\":\"localhostProfile indicates a profile loaded on the node that should be used. The profile must be preconfigured on the node to work. Must match the loaded name of the profile. Must be set if and only if type is \\\"Localhost\\\".\",\"type\":\"string\"},\"type\":{\"default\":\"\",\"description\":\"type indicates which kind of AppArmor profile will be applied. Valid options are:\\n  Localhost - a profile pre-loaded on the node.\\n  RuntimeDefault - the container runtime's default profile.\\n  Unconfined - no AppArmor enforcement.\",\"type\":\"string\"}},\"required\":[\"type\"],\"type\":\"object\",\"x-kubernetes-unions\":[{\"discriminator\":\"type\",\"fields-to-discriminateBy\":{\"localhostProfile\":\"LocalhostProfile\"}}]}],\"description\":\"appArmorProfile is the AppArmor options to use by this container. If set, this profile overrides the pod's appArmorProfile. Note that this field cannot be set when spec.os.name is windows.\"},\"capabilities\":{\"allOf\":[{\"description\":\"Adds and removes POSIX capabilities from running containers.\",\"properties\":{\"add\":{\"description\":\"Added capabilities\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"drop\":{\"description\":\"Removed capabilities\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"The capabilities to add/drop when running containers. Defaults to the default set of capabilities granted by the container runtime. Note that this field cannot be set when spec.os.name is windows.\"},\"privileged\":{\"description\":\"Run container in privileged mode. Processes in privileged containers are essentially equivalent to root on the host. Defaults to false. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"boolean\"},\"procMount\":{\"description\":\"procMount denotes the type of proc mount to use for the containers. The default value is Default which uses the container runtime defaults for readonly paths and masked paths. This requires the ProcMountType feature flag to be enabled. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"string\"},\"readOnlyRootFilesystem\":{\"description\":\"Whether this container has a read-only root filesystem. Default is false. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"boolean\"},\"runAsGroup\":{\"description\":\"The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\"format\":\"int64\",\"type\":\"integer\"},\"runAsNonRoot\":{\"description\":\"Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\"type\":\"boolean\"},\"runAsUser\":{\"description\":\"The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\"format\":\"int64\",\"type\":\"integer\"},\"seLinuxOptions\":{\"allOf\":[{\"description\":\"SELinuxOptions are the labels to be applied to the container\",\"properties\":{\"level\":{\"description\":\"Level is SELinux level label that applies to the container.\",\"type\":\"string\"},\"role\":{\"description\":\"Role is a SELinux role label that applies to the container.\",\"type\":\"string\"},\"type\":{\"description\":\"Type is a SELinux type label that applies to the container.\",\"type\":\"string\"},\"user\":{\"description\":\"User is a SELinux user label that applies to the container.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"The SELinux context to be applied to the container. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\"},\"seccompProfile\":{\"allOf\":[{\"description\":\"SeccompProfile defines a pod/container's seccomp profile settings. Only one profile source may be set.\",\"properties\":{\"localhostProfile\":{\"description\":\"localhostProfile indicates a profile defined in a file on the node should be used. The profile must be preconfigured on the node to work. Must be a descending path, relative to the kubelet's configured seccomp profile location. Must be set if type is \\\"Localhost\\\". Must NOT be set for any other type.\",\"type\":\"string\"},\"type\":{\"default\":\"\",\"description\":\"type indicates which kind of seccomp profile will be applied. Valid options are:\\n\\nLocalhost - a profile defined in a file on the node should be used. RuntimeDefault - the container runtime default profile should be used. Unconfined - no profile should be applied.\",\"type\":\"string\"}},\"required\":[\"type\"],\"type\":\"object\",\"x-kubernetes-unions\":[{\"discriminator\":\"type\",\"fields-to-discriminateBy\":{\"localhostProfile\":\"LocalhostProfile\"}}]}],\"description\":\"The seccomp options to use by this container. If seccomp options are provided at both the pod \\u0026 container level, the container options override the pod options. Note that this field cannot be set when spec.os.name is windows.\"},\"windowsOptions\":{\"allOf\":[{\"description\":\"WindowsSecurityContextOptions contain Windows-specific options and credentials.\",\"properties\":{\"gmsaCredentialSpec\":{\"description\":\"GMSACredentialSpec is where the GMSA admission webhook (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the GMSA credential spec named by the GMSACredentialSpecName field.\",\"type\":\"string\"},\"gmsaCredentialSpecName\":{\"description\":\"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\"type\":\"string\"},\"hostProcess\":{\"description\":\"HostProcess determines if a container should be run as a 'Host Process' container. All of a Pod's containers must have the same effective HostProcess value (it is not allowed to have a mix of HostProcess containers and non-HostProcess containers). In addition, if HostProcess is true then HostNetwork must also be set to true.\",\"type\":\"boolean\"},\"runAsUserName\":{\"description\":\"The UserName in Windows to run the entrypoint of the container process. Defaults to the user specified in image metadata if unspecified. May also be set in PodSecurityContext. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"The Windows specific settings applied to all containers. If unspecified, the options from the PodSecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.\"}},\"type\":\"object\"}],\"description\":\"SecurityContext defines the security options the container should be run with. If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext. More info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/\"},\"startupProbe\":{\"allOf\":[{\"description\":\"Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"failureThreshold\":{\"description\":\"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"grpc\":{\"allOf\":[{\"description\":\"GRPCAction specifies an action involving a GRPC service.\",\"properties\":{\"port\":{\"default\":0,\"description\":\"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\"format\":\"int32\",\"type\":\"integer\"},\"service\":{\"default\":\"\",\"description\":\"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"GRPC specifies a GRPC HealthCheckRequest.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"initialDelaySeconds\":{\"description\":\"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"},\"periodSeconds\":{\"description\":\"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"successThreshold\":{\"description\":\"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"TCPSocket specifies a connection to a TCP port.\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\"format\":\"int64\",\"type\":\"integer\"},\"timeoutSeconds\":{\"description\":\"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"}},\"type\":\"object\"}],\"description\":\"StartupProbe indicates that the Pod has successfully initialized. If specified, no other probes are executed until this completes successfully. If this probe fails, the Pod will be restarted, just as if the livenessProbe failed. This can be used to provide different probe parameters at the beginning of a Pod's lifecycle, when it might take a long time to load data or warm a cache, than during steady-state operation. This cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\"},\"stdin\":{\"description\":\"Whether this container should allocate a buffer for stdin in the container runtime. If this is not set, reads from stdin in the container will always result in EOF. Default is false.\",\"type\":\"boolean\"},\"stdinOnce\":{\"description\":\"Whether the container runtime should close the stdin channel after it has been opened by a single attach. When stdin is true the stdin stream will remain open across multiple attach sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the first client attaches to stdin, and then remains open and accepts data until the client disconnects, at which time stdin is closed and remains closed until the container is restarted. If this flag is false, a container processes that reads from stdin will never receive an EOF. Default is false\",\"type\":\"boolean\"},\"terminationMessagePath\":{\"description\":\"Optional: Path at which the file to which the container's termination message will be written is mounted into the container's filesystem. Message written is intended to be brief final status, such as an assertion failure message. Will be truncated by the node if greater than 4096 bytes. The total message length across all containers will be limited to 12kb. Defaults to /dev/termination-log. Cannot be updated.\",\"type\":\"string\"},\"terminationMessagePolicy\":{\"description\":\"Indicate how the termination message should be populated. File will use the contents of terminationMessagePath to populate the container status message on both success and failure. FallbackToLogsOnError will use the last chunk of container log output if the termination message file is empty and the container exited with an error. The log output is limited to 2048 bytes or 80 lines, whichever is smaller. Defaults to File. Cannot be updated.\",\"type\":\"string\"},\"tty\":{\"description\":\"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true. Default is false.\",\"type\":\"boolean\"},\"volumeDevices\":{\"description\":\"volumeDevices is the list of block devices to be used by the container.\",\"items\":{\"allOf\":[{\"description\":\"volumeDevice describes a mapping of a raw block device within a container.\",\"properties\":{\"devicePath\":{\"default\":\"\",\"description\":\"devicePath is the path inside of the container that the device will be mapped to.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"name must match the name of a persistentVolumeClaim in the pod\",\"type\":\"string\"}},\"required\":[\"name\",\"devicePath\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"devicePath\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"devicePath\",\"x-kubernetes-patch-strategy\":\"merge\"},\"volumeMounts\":{\"description\":\"Pod volumes to mount into the container's filesystem. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"VolumeMount describes a mounting of a Volume within a container.\",\"properties\":{\"mountPath\":{\"default\":\"\",\"description\":\"Path within the container at which the volume should be mounted.  Must not contain ':'.\",\"type\":\"string\"},\"mountPropagation\":{\"description\":\"mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10. When RecursiveReadOnly is set to IfPossible or to Enabled, MountPropagation must be None or unspecified (which defaults to None).\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"This must match the Name of a Volume.\",\"type\":\"string\"},\"readOnly\":{\"description\":\"Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.\",\"type\":\"boolean\"},\"recursiveReadOnly\":{\"description\":\"RecursiveReadOnly specifies whether read-only mounts should be handled recursively.\\n\\nIf ReadOnly is false, this field has no meaning and must be unspecified.\\n\\nIf ReadOnly is true, and this field is set to Disabled, the mount is not made recursively read-only.  If this field is set to IfPossible, the mount is made recursively read-only, if it is supported by the container runtime.  If this field is set to Enabled, the mount is made recursively read-only if it is supported by the container runtime, otherwise the pod will not be started and an error will be generated to indicate the reason.\\n\\nIf this field is set to IfPossible or Enabled, MountPropagation must be set to None (or be unspecified, which defaults to None).\\n\\nIf this field is not specified, it is treated as an equivalent of Disabled.\",\"type\":\"string\"},\"subPath\":{\"description\":\"Path within the volume from which the container's volume should be mounted. Defaults to \\\"\\\" (volume's root).\",\"type\":\"string\"},\"subPathExpr\":{\"description\":\"Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \\\"\\\" (volume's root). SubPathExpr and SubPath are mutually exclusive.\",\"type\":\"string\"}},\"required\":[\"name\",\"mountPath\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"mountPath\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"mountPath\",\"x-kubernetes-patch-strategy\":\"merge\"},\"workingDir\":{\"description\":\"Container's working directory. If not specified, the container runtime's default will be used, which might be configured in the container image. Cannot be updated.\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"},\"dnsConfig\":{\"allOf\":[{\"description\":\"PodDNSConfig defines the DNS parameters of a pod in addition to those generated from DNSPolicy.\",\"properties\":{\"nameservers\":{\"description\":\"A list of DNS name server IP addresses. This will be appended to the base nameservers generated from DNSPolicy. Duplicated nameservers will be removed.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"options\":{\"description\":\"A list of DNS resolver options. This will be merged with the base options generated from DNSPolicy. Duplicated entries will be removed. Resolution options given in Options will override those that appear in the base DNSPolicy.\",\"items\":{\"allOf\":[{\"description\":\"PodDNSConfigOption defines DNS resolver options of a pod.\",\"properties\":{\"name\":{\"description\":\"Name is this DNS resolver option's name. Required.\",\"type\":\"string\"},\"value\":{\"description\":\"Value is this DNS resolver option's value.\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"searches\":{\"description\":\"A list of DNS search domains for host-name lookup. This will be appended to the base search paths generated from DNSPolicy. Duplicated search paths will be removed.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Specifies the DNS parameters of a pod. Parameters specified here will be merged to the generated DNS configuration based on DNSPolicy.\"},\"dnsPolicy\":{\"description\":\"Set DNS policy for the pod. Defaults to \\\"ClusterFirst\\\". Valid values are 'ClusterFirstWithHostNet', 'ClusterFirst', 'Default' or 'None'. DNS parameters given in DNSConfig will be merged with the policy selected with DNSPolicy. To have DNS options set along with hostNetwork, you have to specify DNS policy explicitly to 'ClusterFirstWithHostNet'.\",\"type\":\"string\"},\"enableServiceLinks\":{\"description\":\"EnableServiceLinks indicates whether information about services should be injected into pod's environment variables, matching the syntax of Docker links. Optional: Defaults to true.\",\"type\":\"boolean\"},\"ephemeralContainers\":{\"description\":\"List of ephemeral containers run in this pod. Ephemeral containers may be run in an existing pod to perform user-initiated actions such as debugging. This list cannot be specified when creating a pod, and it cannot be modified by updating the pod spec. In order to add an ephemeral container to an existing pod, use the pod's ephemeralcontainers subresource.\",\"items\":{\"allOf\":[{\"description\":\"An EphemeralContainer is a temporary container that you may add to an existing Pod for user-initiated activities such as debugging. Ephemeral containers have no resource or scheduling guarantees, and they will not be restarted when they exit or when a Pod is removed or restarted. The kubelet may evict a Pod if an ephemeral container causes the Pod to exceed its resource allocation.\\n\\nTo add an ephemeral container, use the ephemeralcontainers subresource of an existing Pod. Ephemeral containers may not be removed or restarted.\",\"properties\":{\"args\":{\"description\":\"Arguments to the entrypoint. The image's CMD is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"command\":{\"description\":\"Entrypoint array. Not executed within a shell. The image's ENTRYPOINT is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"env\":{\"description\":\"List of environment variables to set in the container. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"EnvVar represents an environment variable present in a Container.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the environment variable. May consist of any printable ASCII characters except '='.\",\"type\":\"string\"},\"value\":{\"description\":\"Variable references $(VAR_NAME) are expanded using the previously defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to \\\"\\\".\",\"type\":\"string\"},\"valueFrom\":{\"allOf\":[{\"description\":\"EnvVarSource represents a source for the value of an EnvVar.\",\"properties\":{\"configMapKeyRef\":{\"allOf\":[{\"description\":\"Selects a key from a ConfigMap.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The key to select.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the ConfigMap or its key must be defined\",\"type\":\"boolean\"}},\"required\":[\"key\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a key of a ConfigMap.\"},\"fieldRef\":{\"allOf\":[{\"description\":\"ObjectFieldSelector selects an APIVersioned field of an object.\",\"properties\":{\"apiVersion\":{\"description\":\"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\"type\":\"string\"},\"fieldPath\":{\"default\":\"\",\"description\":\"Path of the field to select in the specified API version.\",\"type\":\"string\"}},\"required\":[\"fieldPath\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\"},\"resourceFieldRef\":{\"allOf\":[{\"description\":\"ResourceFieldSelector represents container resources (cpu, memory) and their output format\",\"properties\":{\"containerName\":{\"description\":\"Container name: required for volumes, optional for env vars\",\"type\":\"string\"},\"divisor\":{\"allOf\":[{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]}],\"description\":\"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\"},\"resource\":{\"default\":\"\",\"description\":\"Required: resource to select\",\"type\":\"string\"}},\"required\":[\"resource\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\"},\"secretKeyRef\":{\"allOf\":[{\"description\":\"SecretKeySelector selects a key of a Secret.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The key of the secret to select from.  Must be a valid secret key.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the Secret or its key must be defined\",\"type\":\"boolean\"}},\"required\":[\"key\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a key of a secret in the pod's namespace\"}},\"type\":\"object\"}],\"description\":\"Source for the environment variable's value. Cannot be used if value is not empty.\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"},\"envFrom\":{\"description\":\"List of sources to populate environment variables in the container. The keys defined within a source may consist of any printable ASCII characters except '='. When a key exists in multiple sources, the value associated with the last source will take precedence. Values defined by an Env with a duplicate key will take precedence. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"EnvFromSource represents the source of a set of ConfigMaps or Secrets\",\"properties\":{\"configMapRef\":{\"allOf\":[{\"description\":\"ConfigMapEnvSource selects a ConfigMap to populate the environment variables with.\\n\\nThe contents of the target ConfigMap's Data field will represent the key-value pairs as environment variables.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the ConfigMap must be defined\",\"type\":\"boolean\"}},\"type\":\"object\"}],\"description\":\"The ConfigMap to select from\"},\"prefix\":{\"description\":\"Optional text to prepend to the name of each environment variable. May consist of any printable ASCII characters except '='.\",\"type\":\"string\"},\"secretRef\":{\"allOf\":[{\"description\":\"SecretEnvSource selects a Secret to populate the environment variables with.\\n\\nThe contents of the target Secret's Data field will represent the key-value pairs as environment variables.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the Secret must be defined\",\"type\":\"boolean\"}},\"type\":\"object\"}],\"description\":\"The Secret to select from\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"image\":{\"description\":\"Container image name. More info: https://kubernetes.io/docs/concepts/containers/images\",\"type\":\"string\"},\"imagePullPolicy\":{\"description\":\"Image pull policy. One of Always, Never, IfNotPresent. Defaults to Always if :latest tag is specified, or IfNotPresent otherwise. Cannot be updated. More info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\"type\":\"string\"},\"lifecycle\":{\"allOf\":[{\"description\":\"Lifecycle describes actions that the management system should take in response to container lifecycle events. For the PostStart and PreStop lifecycle handlers, management of the container blocks until the action is complete, unless the container process fails, in which case the handler is aborted.\",\"properties\":{\"postStart\":{\"allOf\":[{\"description\":\"LifecycleHandler defines a specific action that should be taken in a lifecycle hook. One and only one of the fields, except TCPSocket must be specified.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"sleep\":{\"allOf\":[{\"description\":\"SleepAction describes a \\\"sleep\\\" action.\",\"properties\":{\"seconds\":{\"default\":0,\"description\":\"Seconds is the number of seconds to sleep.\",\"format\":\"int64\",\"type\":\"integer\"}},\"required\":[\"seconds\"],\"type\":\"object\"}],\"description\":\"Sleep represents a duration that the container should sleep.\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for backward compatibility. There is no validation of this field and lifecycle hooks will fail at runtime when it is specified.\"}},\"type\":\"object\"}],\"description\":\"PostStart is called immediately after a container is created. If the handler fails, the container is terminated and restarted according to its restart policy. Other management of the container blocks until the hook completes. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\"},\"preStop\":{\"allOf\":[{\"description\":\"LifecycleHandler defines a specific action that should be taken in a lifecycle hook. One and only one of the fields, except TCPSocket must be specified.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"sleep\":{\"allOf\":[{\"description\":\"SleepAction describes a \\\"sleep\\\" action.\",\"properties\":{\"seconds\":{\"default\":0,\"description\":\"Seconds is the number of seconds to sleep.\",\"format\":\"int64\",\"type\":\"integer\"}},\"required\":[\"seconds\"],\"type\":\"object\"}],\"description\":\"Sleep represents a duration that the container should sleep.\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for backward compatibility. There is no validation of this field and lifecycle hooks will fail at runtime when it is specified.\"}},\"type\":\"object\"}],\"description\":\"PreStop is called immediately before a container is terminated due to an API request or management event such as liveness/startup probe failure, preemption, resource contention, etc. The handler is not called if the container crashes or exits. The Pod's termination grace period countdown begins before the PreStop hook is executed. Regardless of the outcome of the handler, the container will eventually terminate within the Pod's termination grace period (unless delayed by finalizers). Other management of the container blocks until the hook completes or until the termination grace period is reached. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\"},\"stopSignal\":{\"description\":\"StopSignal defines which signal will be sent to a container when it is being stopped. If not specified, the default is defined by the container runtime in use. StopSignal can only be set for Pods with a non-empty .spec.os.name\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"Lifecycle is not allowed for ephemeral containers.\"},\"livenessProbe\":{\"allOf\":[{\"description\":\"Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"failureThreshold\":{\"description\":\"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"grpc\":{\"allOf\":[{\"description\":\"GRPCAction specifies an action involving a GRPC service.\",\"properties\":{\"port\":{\"default\":0,\"description\":\"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\"format\":\"int32\",\"type\":\"integer\"},\"service\":{\"default\":\"\",\"description\":\"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"GRPC specifies a GRPC HealthCheckRequest.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"initialDelaySeconds\":{\"description\":\"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"},\"periodSeconds\":{\"description\":\"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"successThreshold\":{\"description\":\"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"TCPSocket specifies a connection to a TCP port.\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\"format\":\"int64\",\"type\":\"integer\"},\"timeoutSeconds\":{\"description\":\"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"}},\"type\":\"object\"}],\"description\":\"Probes are not allowed for ephemeral containers.\"},\"name\":{\"default\":\"\",\"description\":\"Name of the ephemeral container specified as a DNS_LABEL. This name must be unique among all containers, init containers and ephemeral containers.\",\"type\":\"string\"},\"ports\":{\"description\":\"Ports are not allowed for ephemeral containers.\",\"items\":{\"allOf\":[{\"description\":\"ContainerPort represents a network port in a single container.\",\"properties\":{\"containerPort\":{\"default\":0,\"description\":\"Number of port to expose on the pod's IP address. This must be a valid port number, 0 \\u003c x \\u003c 65536.\",\"format\":\"int32\",\"type\":\"integer\"},\"hostIP\":{\"description\":\"What host IP to bind the external port to.\",\"type\":\"string\"},\"hostPort\":{\"description\":\"Number of port to expose on the host. If specified, this must be a valid port number, 0 \\u003c x \\u003c 65536. If HostNetwork is specified, this must match ContainerPort. Most containers do not need this.\",\"format\":\"int32\",\"type\":\"integer\"},\"name\":{\"description\":\"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each named port in a pod must have a unique name. Name for the port that can be referred to by services.\",\"type\":\"string\"},\"protocol\":{\"default\":\"TCP\",\"description\":\"Protocol for port. Must be UDP, TCP, or SCTP. Defaults to \\\"TCP\\\".\",\"type\":\"string\"}},\"required\":[\"containerPort\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"containerPort\",\"protocol\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"containerPort\",\"x-kubernetes-patch-strategy\":\"merge\"},\"readinessProbe\":{\"allOf\":[{\"description\":\"Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"failureThreshold\":{\"description\":\"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"grpc\":{\"allOf\":[{\"description\":\"GRPCAction specifies an action involving a GRPC service.\",\"properties\":{\"port\":{\"default\":0,\"description\":\"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\"format\":\"int32\",\"type\":\"integer\"},\"service\":{\"default\":\"\",\"description\":\"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"GRPC specifies a GRPC HealthCheckRequest.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"initialDelaySeconds\":{\"description\":\"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"},\"periodSeconds\":{\"description\":\"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"successThreshold\":{\"description\":\"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"TCPSocket specifies a connection to a TCP port.\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\"format\":\"int64\",\"type\":\"integer\"},\"timeoutSeconds\":{\"description\":\"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"}},\"type\":\"object\"}],\"description\":\"Probes are not allowed for ephemeral containers.\"},\"resizePolicy\":{\"description\":\"Resources resize policy for the container.\",\"items\":{\"allOf\":[{\"description\":\"ContainerResizePolicy represents resource resize policy for the container.\",\"properties\":{\"resourceName\":{\"default\":\"\",\"description\":\"Name of the resource to which this resource resize policy applies. Supported values: cpu, memory.\",\"type\":\"string\"},\"restartPolicy\":{\"default\":\"\",\"description\":\"Restart policy to apply when specified resource is resized. If not specified, it defaults to NotRequired.\",\"type\":\"string\"}},\"required\":[\"resourceName\",\"restartPolicy\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"resources\":{\"allOf\":[{\"description\":\"ResourceRequirements describes the compute resource requirements.\",\"properties\":{\"claims\":{\"description\":\"Claims lists the names of resources, defined in spec.resourceClaims, that are used by this container.\\n\\nThis is an alpha field and requires enabling the DynamicResourceAllocation feature gate.\\n\\nThis field is immutable. It can only be set for containers.\",\"items\":{\"allOf\":[{\"description\":\"ResourceClaim references one entry in PodSpec.ResourceClaims.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name must match the name of one entry in pod.spec.resourceClaims of the Pod where this field is used. It makes that resource available inside a container.\",\"type\":\"string\"},\"request\":{\"description\":\"Request is the name chosen for a request in the referenced claim. If empty, everything from the claim is made available, otherwise only the result of this request.\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\"},\"limits\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"},\"requests\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. Requests cannot exceed Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"Resources are not allowed for ephemeral containers. Ephemeral containers use spare resources already allocated to the pod.\"},\"restartPolicy\":{\"description\":\"Restart policy for the container to manage the restart behavior of each container within a pod. This may only be set for init containers. You cannot set this field on ephemeral containers.\",\"type\":\"string\"},\"securityContext\":{\"allOf\":[{\"description\":\"SecurityContext holds security configuration that will be applied to a container. Some fields are present in both SecurityContext and PodSecurityContext.  When both are set, the values in SecurityContext take precedence.\",\"properties\":{\"allowPrivilegeEscalation\":{\"description\":\"AllowPrivilegeEscalation controls whether a process can gain more privileges than its parent process. This bool directly controls if the no_new_privs flag will be set on the container process. AllowPrivilegeEscalation is true always when the container is: 1) run as Privileged 2) has CAP_SYS_ADMIN Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"boolean\"},\"appArmorProfile\":{\"allOf\":[{\"description\":\"AppArmorProfile defines a pod or container's AppArmor settings.\",\"properties\":{\"localhostProfile\":{\"description\":\"localhostProfile indicates a profile loaded on the node that should be used. The profile must be preconfigured on the node to work. Must match the loaded name of the profile. Must be set if and only if type is \\\"Localhost\\\".\",\"type\":\"string\"},\"type\":{\"default\":\"\",\"description\":\"type indicates which kind of AppArmor profile will be applied. Valid options are:\\n  Localhost - a profile pre-loaded on the node.\\n  RuntimeDefault - the container runtime's default profile.\\n  Unconfined - no AppArmor enforcement.\",\"type\":\"string\"}},\"required\":[\"type\"],\"type\":\"object\",\"x-kubernetes-unions\":[{\"discriminator\":\"type\",\"fields-to-discriminateBy\":{\"localhostProfile\":\"LocalhostProfile\"}}]}],\"description\":\"appArmorProfile is the AppArmor options to use by this container. If set, this profile overrides the pod's appArmorProfile. Note that this field cannot be set when spec.os.name is windows.\"},\"capabilities\":{\"allOf\":[{\"description\":\"Adds and removes POSIX capabilities from running containers.\",\"properties\":{\"add\":{\"description\":\"Added capabilities\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"drop\":{\"description\":\"Removed capabilities\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"The capabilities to add/drop when running containers. Defaults to the default set of capabilities granted by the container runtime. Note that this field cannot be set when spec.os.name is windows.\"},\"privileged\":{\"description\":\"Run container in privileged mode. Processes in privileged containers are essentially equivalent to root on the host. Defaults to false. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"boolean\"},\"procMount\":{\"description\":\"procMount denotes the type of proc mount to use for the containers. The default value is Default which uses the container runtime defaults for readonly paths and masked paths. This requires the ProcMountType feature flag to be enabled. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"string\"},\"readOnlyRootFilesystem\":{\"description\":\"Whether this container has a read-only root filesystem. Default is false. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"boolean\"},\"runAsGroup\":{\"description\":\"The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\"format\":\"int64\",\"type\":\"integer\"},\"runAsNonRoot\":{\"description\":\"Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\"type\":\"boolean\"},\"runAsUser\":{\"description\":\"The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\"format\":\"int64\",\"type\":\"integer\"},\"seLinuxOptions\":{\"allOf\":[{\"description\":\"SELinuxOptions are the labels to be applied to the container\",\"properties\":{\"level\":{\"description\":\"Level is SELinux level label that applies to the container.\",\"type\":\"string\"},\"role\":{\"description\":\"Role is a SELinux role label that applies to the container.\",\"type\":\"string\"},\"type\":{\"description\":\"Type is a SELinux type label that applies to the container.\",\"type\":\"string\"},\"user\":{\"description\":\"User is a SELinux user label that applies to the container.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"The SELinux context to be applied to the container. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\"},\"seccompProfile\":{\"allOf\":[{\"description\":\"SeccompProfile defines a pod/container's seccomp profile settings. Only one profile source may be set.\",\"properties\":{\"localhostProfile\":{\"description\":\"localhostProfile indicates a profile defined in a file on the node should be used. The profile must be preconfigured on the node to work. Must be a descending path, relative to the kubelet's configured seccomp profile location. Must be set if type is \\\"Localhost\\\". Must NOT be set for any other type.\",\"type\":\"string\"},\"type\":{\"default\":\"\",\"description\":\"type indicates which kind of seccomp profile will be applied. Valid options are:\\n\\nLocalhost - a profile defined in a file on the node should be used. RuntimeDefault - the container runtime default profile should be used. Unconfined - no profile should be applied.\",\"type\":\"string\"}},\"required\":[\"type\"],\"type\":\"object\",\"x-kubernetes-unions\":[{\"discriminator\":\"type\",\"fields-to-discriminateBy\":{\"localhostProfile\":\"LocalhostProfile\"}}]}],\"description\":\"The seccomp options to use by this container. If seccomp options are provided at both the pod \\u0026 container level, the container options override the pod options. Note that this field cannot be set when spec.os.name is windows.\"},\"windowsOptions\":{\"allOf\":[{\"description\":\"WindowsSecurityContextOptions contain Windows-specific options and credentials.\",\"properties\":{\"gmsaCredentialSpec\":{\"description\":\"GMSACredentialSpec is where the GMSA admission webhook (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the GMSA credential spec named by the GMSACredentialSpecName field.\",\"type\":\"string\"},\"gmsaCredentialSpecName\":{\"description\":\"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\"type\":\"string\"},\"hostProcess\":{\"description\":\"HostProcess determines if a container should be run as a 'Host Process' container. All of a Pod's containers must have the same effective HostProcess value (it is not allowed to have a mix of HostProcess containers and non-HostProcess containers). In addition, if HostProcess is true then HostNetwork must also be set to true.\",\"type\":\"boolean\"},\"runAsUserName\":{\"description\":\"The UserName in Windows to run the entrypoint of the container process. Defaults to the user specified in image metadata if unspecified. May also be set in PodSecurityContext. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"The Windows specific settings applied to all containers. If unspecified, the options from the PodSecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.\"}},\"type\":\"object\"}],\"description\":\"Optional: SecurityContext defines the security options the ephemeral container should be run with. If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.\"},\"startupProbe\":{\"allOf\":[{\"description\":\"Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"failureThreshold\":{\"description\":\"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"grpc\":{\"allOf\":[{\"description\":\"GRPCAction specifies an action involving a GRPC service.\",\"properties\":{\"port\":{\"default\":0,\"description\":\"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\"format\":\"int32\",\"type\":\"integer\"},\"service\":{\"default\":\"\",\"description\":\"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"GRPC specifies a GRPC HealthCheckRequest.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"initialDelaySeconds\":{\"description\":\"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"},\"periodSeconds\":{\"description\":\"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"successThreshold\":{\"description\":\"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"TCPSocket specifies a connection to a TCP port.\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\"format\":\"int64\",\"type\":\"integer\"},\"timeoutSeconds\":{\"description\":\"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"}},\"type\":\"object\"}],\"description\":\"Probes are not allowed for ephemeral containers.\"},\"stdin\":{\"description\":\"Whether this container should allocate a buffer for stdin in the container runtime. If this is not set, reads from stdin in the container will always result in EOF. Default is false.\",\"type\":\"boolean\"},\"stdinOnce\":{\"description\":\"Whether the container runtime should close the stdin channel after it has been opened by a single attach. When stdin is true the stdin stream will remain open across multiple attach sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the first client attaches to stdin, and then remains open and accepts data until the client disconnects, at which time stdin is closed and remains closed until the container is restarted. If this flag is false, a container processes that reads from stdin will never receive an EOF. Default is false\",\"type\":\"boolean\"},\"targetContainerName\":{\"description\":\"If set, the name of the container from PodSpec that this ephemeral container targets. The ephemeral container will be run in the namespaces (IPC, PID, etc) of this container. If not set then the ephemeral container uses the namespaces configured in the Pod spec.\\n\\nThe container runtime must implement support for this feature. If the runtime does not support namespace targeting then the result of setting this field is undefined.\",\"type\":\"string\"},\"terminationMessagePath\":{\"description\":\"Optional: Path at which the file to which the container's termination message will be written is mounted into the container's filesystem. Message written is intended to be brief final status, such as an assertion failure message. Will be truncated by the node if greater than 4096 bytes. The total message length across all containers will be limited to 12kb. Defaults to /dev/termination-log. Cannot be updated.\",\"type\":\"string\"},\"terminationMessagePolicy\":{\"description\":\"Indicate how the termination message should be populated. File will use the contents of terminationMessagePath to populate the container status message on both success and failure. FallbackToLogsOnError will use the last chunk of container log output if the termination message file is empty and the container exited with an error. The log output is limited to 2048 bytes or 80 lines, whichever is smaller. Defaults to File. Cannot be updated.\",\"type\":\"string\"},\"tty\":{\"description\":\"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true. Default is false.\",\"type\":\"boolean\"},\"volumeDevices\":{\"description\":\"volumeDevices is the list of block devices to be used by the container.\",\"items\":{\"allOf\":[{\"description\":\"volumeDevice describes a mapping of a raw block device within a container.\",\"properties\":{\"devicePath\":{\"default\":\"\",\"description\":\"devicePath is the path inside of the container that the device will be mapped to.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"name must match the name of a persistentVolumeClaim in the pod\",\"type\":\"string\"}},\"required\":[\"name\",\"devicePath\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"devicePath\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"devicePath\",\"x-kubernetes-patch-strategy\":\"merge\"},\"volumeMounts\":{\"description\":\"Pod volumes to mount into the container's filesystem. Subpath mounts are not allowed for ephemeral containers. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"VolumeMount describes a mounting of a Volume within a container.\",\"properties\":{\"mountPath\":{\"default\":\"\",\"description\":\"Path within the container at which the volume should be mounted.  Must not contain ':'.\",\"type\":\"string\"},\"mountPropagation\":{\"description\":\"mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10. When RecursiveReadOnly is set to IfPossible or to Enabled, MountPropagation must be None or unspecified (which defaults to None).\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"This must match the Name of a Volume.\",\"type\":\"string\"},\"readOnly\":{\"description\":\"Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.\",\"type\":\"boolean\"},\"recursiveReadOnly\":{\"description\":\"RecursiveReadOnly specifies whether read-only mounts should be handled recursively.\\n\\nIf ReadOnly is false, this field has no meaning and must be unspecified.\\n\\nIf ReadOnly is true, and this field is set to Disabled, the mount is not made recursively read-only.  If this field is set to IfPossible, the mount is made recursively read-only, if it is supported by the container runtime.  If this field is set to Enabled, the mount is made recursively read-only if it is supported by the container runtime, otherwise the pod will not be started and an error will be generated to indicate the reason.\\n\\nIf this field is set to IfPossible or Enabled, MountPropagation must be set to None (or be unspecified, which defaults to None).\\n\\nIf this field is not specified, it is treated as an equivalent of Disabled.\",\"type\":\"string\"},\"subPath\":{\"description\":\"Path within the volume from which the container's volume should be mounted. Defaults to \\\"\\\" (volume's root).\",\"type\":\"string\"},\"subPathExpr\":{\"description\":\"Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \\\"\\\" (volume's root). SubPathExpr and SubPath are mutually exclusive.\",\"type\":\"string\"}},\"required\":[\"name\",\"mountPath\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"mountPath\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"mountPath\",\"x-kubernetes-patch-strategy\":\"merge\"},\"workingDir\":{\"description\":\"Container's working directory. If not specified, the container runtime's default will be used, which might be configured in the container image. Cannot be updated.\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"},\"hostAliases\":{\"description\":\"HostAliases is an optional list of hosts and IPs that will be injected into the pod's hosts file if specified.\",\"items\":{\"allOf\":[{\"description\":\"HostAlias holds the mapping between IP and hostnames that will be injected as an entry in the pod's hosts file.\",\"properties\":{\"hostnames\":{\"description\":\"Hostnames for the above IP address.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"ip\":{\"default\":\"\",\"description\":\"IP address of the host file entry.\",\"type\":\"string\"}},\"required\":[\"ip\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"ip\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"ip\",\"x-kubernetes-patch-strategy\":\"merge\"},\"hostIPC\":{\"description\":\"Use the host's ipc namespace. Optional: Default to false.\",\"type\":\"boolean\"},\"hostNetwork\":{\"description\":\"Host networking requested for this pod. Use the host's network namespace. Default to false.\",\"type\":\"boolean\"},\"hostPID\":{\"description\":\"Use the host's pid namespace. Optional: Default to false.\",\"type\":\"boolean\"},\"hostUsers\":{\"description\":\"Use the host's user namespace. Optional: Default to true. If set to true or not present, the pod will be run in the host user namespace, useful for when the pod needs a feature only available to the host user namespace, such as loading a kernel module with CAP_SYS_MODULE. When set to false, a new userns is created for the pod. Setting false is useful for mitigating container breakout vulnerabilities even allowing users to run their containers as root without actually having root privileges on the host. This field is alpha-level and is only honored by servers that enable the UserNamespacesSupport feature.\",\"type\":\"boolean\"},\"hostname\":{\"description\":\"Specifies the hostname of the Pod If not specified, the pod's hostname will be set to a system-defined value.\",\"type\":\"string\"},\"imagePullSecrets\":{\"description\":\"ImagePullSecrets is an optional list of references to secrets in the same namespace to use for pulling any of the images used by this PodSpec. If specified, these secrets will be passed to individual puller implementations for them to use. More info: https://kubernetes.io/docs/concepts/containers/images#specifying-imagepullsecrets-on-a-pod\",\"items\":{\"allOf\":[{\"description\":\"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"},\"initContainers\":{\"description\":\"List of initialization containers belonging to the pod. Init containers are executed in order prior to containers being started. If any init container fails, the pod is considered to have failed and is handled according to its restartPolicy. The name for an init container or normal container must be unique among all containers. Init containers may not have Lifecycle actions, Readiness probes, Liveness probes, or Startup probes. The resourceRequirements of an init container are taken into account during scheduling by finding the highest request/limit for each resource type, and then using the max of that value or the sum of the normal containers. Limits are applied to init containers in a similar fashion. Init containers cannot currently be added or removed. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/\",\"items\":{\"allOf\":[{\"description\":\"A single application container that you want to run within a pod.\",\"properties\":{\"args\":{\"description\":\"Arguments to the entrypoint. The container image's CMD is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"command\":{\"description\":\"Entrypoint array. Not executed within a shell. The container image's ENTRYPOINT is used if this is not provided. Variable references $(VAR_NAME) are expanded using the container's environment. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Cannot be updated. More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"env\":{\"description\":\"List of environment variables to set in the container. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"EnvVar represents an environment variable present in a Container.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the environment variable. May consist of any printable ASCII characters except '='.\",\"type\":\"string\"},\"value\":{\"description\":\"Variable references $(VAR_NAME) are expanded using the previously defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to \\\"\\\".\",\"type\":\"string\"},\"valueFrom\":{\"allOf\":[{\"description\":\"EnvVarSource represents a source for the value of an EnvVar.\",\"properties\":{\"configMapKeyRef\":{\"allOf\":[{\"description\":\"Selects a key from a ConfigMap.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The key to select.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the ConfigMap or its key must be defined\",\"type\":\"boolean\"}},\"required\":[\"key\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a key of a ConfigMap.\"},\"fieldRef\":{\"allOf\":[{\"description\":\"ObjectFieldSelector selects an APIVersioned field of an object.\",\"properties\":{\"apiVersion\":{\"description\":\"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\"type\":\"string\"},\"fieldPath\":{\"default\":\"\",\"description\":\"Path of the field to select in the specified API version.\",\"type\":\"string\"}},\"required\":[\"fieldPath\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\"},\"resourceFieldRef\":{\"allOf\":[{\"description\":\"ResourceFieldSelector represents container resources (cpu, memory) and their output format\",\"properties\":{\"containerName\":{\"description\":\"Container name: required for volumes, optional for env vars\",\"type\":\"string\"},\"divisor\":{\"allOf\":[{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]}],\"description\":\"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\"},\"resource\":{\"default\":\"\",\"description\":\"Required: resource to select\",\"type\":\"string\"}},\"required\":[\"resource\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\"},\"secretKeyRef\":{\"allOf\":[{\"description\":\"SecretKeySelector selects a key of a Secret.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"The key of the secret to select from.  Must be a valid secret key.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the Secret or its key must be defined\",\"type\":\"boolean\"}},\"required\":[\"key\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a key of a secret in the pod's namespace\"}},\"type\":\"object\"}],\"description\":\"Source for the environment variable's value. Cannot be used if value is not empty.\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"},\"envFrom\":{\"description\":\"List of sources to populate environment variables in the container. The keys defined within a source may consist of any printable ASCII characters except '='. When a key exists in multiple sources, the value associated with the last source will take precedence. Values defined by an Env with a duplicate key will take precedence. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"EnvFromSource represents the source of a set of ConfigMaps or Secrets\",\"properties\":{\"configMapRef\":{\"allOf\":[{\"description\":\"ConfigMapEnvSource selects a ConfigMap to populate the environment variables with.\\n\\nThe contents of the target ConfigMap's Data field will represent the key-value pairs as environment variables.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the ConfigMap must be defined\",\"type\":\"boolean\"}},\"type\":\"object\"}],\"description\":\"The ConfigMap to select from\"},\"prefix\":{\"description\":\"Optional text to prepend to the name of each environment variable. May consist of any printable ASCII characters except '='.\",\"type\":\"string\"},\"secretRef\":{\"allOf\":[{\"description\":\"SecretEnvSource selects a Secret to populate the environment variables with.\\n\\nThe contents of the target Secret's Data field will represent the key-value pairs as environment variables.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"Specify whether the Secret must be defined\",\"type\":\"boolean\"}},\"type\":\"object\"}],\"description\":\"The Secret to select from\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"image\":{\"description\":\"Container image name. More info: https://kubernetes.io/docs/concepts/containers/images This field is optional to allow higher level config management to default or override container images in workload controllers like Deployments and StatefulSets.\",\"type\":\"string\"},\"imagePullPolicy\":{\"description\":\"Image pull policy. One of Always, Never, IfNotPresent. Defaults to Always if :latest tag is specified, or IfNotPresent otherwise. Cannot be updated. More info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\"type\":\"string\"},\"lifecycle\":{\"allOf\":[{\"description\":\"Lifecycle describes actions that the management system should take in response to container lifecycle events. For the PostStart and PreStop lifecycle handlers, management of the container blocks until the action is complete, unless the container process fails, in which case the handler is aborted.\",\"properties\":{\"postStart\":{\"allOf\":[{\"description\":\"LifecycleHandler defines a specific action that should be taken in a lifecycle hook. One and only one of the fields, except TCPSocket must be specified.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"sleep\":{\"allOf\":[{\"description\":\"SleepAction describes a \\\"sleep\\\" action.\",\"properties\":{\"seconds\":{\"default\":0,\"description\":\"Seconds is the number of seconds to sleep.\",\"format\":\"int64\",\"type\":\"integer\"}},\"required\":[\"seconds\"],\"type\":\"object\"}],\"description\":\"Sleep represents a duration that the container should sleep.\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for backward compatibility. There is no validation of this field and lifecycle hooks will fail at runtime when it is specified.\"}},\"type\":\"object\"}],\"description\":\"PostStart is called immediately after a container is created. If the handler fails, the container is terminated and restarted according to its restart policy. Other management of the container blocks until the hook completes. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\"},\"preStop\":{\"allOf\":[{\"description\":\"LifecycleHandler defines a specific action that should be taken in a lifecycle hook. One and only one of the fields, except TCPSocket must be specified.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"sleep\":{\"allOf\":[{\"description\":\"SleepAction describes a \\\"sleep\\\" action.\",\"properties\":{\"seconds\":{\"default\":0,\"description\":\"Seconds is the number of seconds to sleep.\",\"format\":\"int64\",\"type\":\"integer\"}},\"required\":[\"seconds\"],\"type\":\"object\"}],\"description\":\"Sleep represents a duration that the container should sleep.\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for backward compatibility. There is no validation of this field and lifecycle hooks will fail at runtime when it is specified.\"}},\"type\":\"object\"}],\"description\":\"PreStop is called immediately before a container is terminated due to an API request or management event such as liveness/startup probe failure, preemption, resource contention, etc. The handler is not called if the container crashes or exits. The Pod's termination grace period countdown begins before the PreStop hook is executed. Regardless of the outcome of the handler, the container will eventually terminate within the Pod's termination grace period (unless delayed by finalizers). Other management of the container blocks until the hook completes or until the termination grace period is reached. More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\"},\"stopSignal\":{\"description\":\"StopSignal defines which signal will be sent to a container when it is being stopped. If not specified, the default is defined by the container runtime in use. StopSignal can only be set for Pods with a non-empty .spec.os.name\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"Actions that the management system should take in response to container lifecycle events. Cannot be updated.\"},\"livenessProbe\":{\"allOf\":[{\"description\":\"Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"failureThreshold\":{\"description\":\"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"grpc\":{\"allOf\":[{\"description\":\"GRPCAction specifies an action involving a GRPC service.\",\"properties\":{\"port\":{\"default\":0,\"description\":\"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\"format\":\"int32\",\"type\":\"integer\"},\"service\":{\"default\":\"\",\"description\":\"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"GRPC specifies a GRPC HealthCheckRequest.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"initialDelaySeconds\":{\"description\":\"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"},\"periodSeconds\":{\"description\":\"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"successThreshold\":{\"description\":\"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"TCPSocket specifies a connection to a TCP port.\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\"format\":\"int64\",\"type\":\"integer\"},\"timeoutSeconds\":{\"description\":\"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"}},\"type\":\"object\"}],\"description\":\"Periodic probe of container liveness. Container will be restarted if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\"},\"name\":{\"default\":\"\",\"description\":\"Name of the container specified as a DNS_LABEL. Each container in a pod must have a unique name (DNS_LABEL). Cannot be updated.\",\"type\":\"string\"},\"ports\":{\"description\":\"List of ports to expose from the container. Not specifying a port here DOES NOT prevent that port from being exposed. Any port which is listening on the default \\\"0.0.0.0\\\" address inside a container will be accessible from the network. Modifying this array with strategic merge patch may corrupt the data. For more information See https://github.com/kubernetes/kubernetes/issues/108255. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"ContainerPort represents a network port in a single container.\",\"properties\":{\"containerPort\":{\"default\":0,\"description\":\"Number of port to expose on the pod's IP address. This must be a valid port number, 0 \\u003c x \\u003c 65536.\",\"format\":\"int32\",\"type\":\"integer\"},\"hostIP\":{\"description\":\"What host IP to bind the external port to.\",\"type\":\"string\"},\"hostPort\":{\"description\":\"Number of port to expose on the host. If specified, this must be a valid port number, 0 \\u003c x \\u003c 65536. If HostNetwork is specified, this must match ContainerPort. Most containers do not need this.\",\"format\":\"int32\",\"type\":\"integer\"},\"name\":{\"description\":\"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each named port in a pod must have a unique name. Name for the port that can be referred to by services.\",\"type\":\"string\"},\"protocol\":{\"default\":\"TCP\",\"description\":\"Protocol for port. Must be UDP, TCP, or SCTP. Defaults to \\\"TCP\\\".\",\"type\":\"string\"}},\"required\":[\"containerPort\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"containerPort\",\"protocol\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"containerPort\",\"x-kubernetes-patch-strategy\":\"merge\"},\"readinessProbe\":{\"allOf\":[{\"description\":\"Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"failureThreshold\":{\"description\":\"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"grpc\":{\"allOf\":[{\"description\":\"GRPCAction specifies an action involving a GRPC service.\",\"properties\":{\"port\":{\"default\":0,\"description\":\"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\"format\":\"int32\",\"type\":\"integer\"},\"service\":{\"default\":\"\",\"description\":\"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"GRPC specifies a GRPC HealthCheckRequest.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"initialDelaySeconds\":{\"description\":\"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"},\"periodSeconds\":{\"description\":\"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"successThreshold\":{\"description\":\"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"TCPSocket specifies a connection to a TCP port.\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\"format\":\"int64\",\"type\":\"integer\"},\"timeoutSeconds\":{\"description\":\"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"}},\"type\":\"object\"}],\"description\":\"Periodic probe of container service readiness. Container will be removed from service endpoints if the probe fails. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\"},\"resizePolicy\":{\"description\":\"Resources resize policy for the container.\",\"items\":{\"allOf\":[{\"description\":\"ContainerResizePolicy represents resource resize policy for the container.\",\"properties\":{\"resourceName\":{\"default\":\"\",\"description\":\"Name of the resource to which this resource resize policy applies. Supported values: cpu, memory.\",\"type\":\"string\"},\"restartPolicy\":{\"default\":\"\",\"description\":\"Restart policy to apply when specified resource is resized. If not specified, it defaults to NotRequired.\",\"type\":\"string\"}},\"required\":[\"resourceName\",\"restartPolicy\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"resources\":{\"allOf\":[{\"description\":\"ResourceRequirements describes the compute resource requirements.\",\"properties\":{\"claims\":{\"description\":\"Claims lists the names of resources, defined in spec.resourceClaims, that are used by this container.\\n\\nThis is an alpha field and requires enabling the DynamicResourceAllocation feature gate.\\n\\nThis field is immutable. It can only be set for containers.\",\"items\":{\"allOf\":[{\"description\":\"ResourceClaim references one entry in PodSpec.ResourceClaims.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name must match the name of one entry in pod.spec.resourceClaims of the Pod where this field is used. It makes that resource available inside a container.\",\"type\":\"string\"},\"request\":{\"description\":\"Request is the name chosen for a request in the referenced claim. If empty, everything from the claim is made available, otherwise only the result of this request.\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\"},\"limits\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"},\"requests\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. Requests cannot exceed Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"Compute Resources required by this container. Cannot be updated. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\"},\"restartPolicy\":{\"description\":\"RestartPolicy defines the restart behavior of individual containers in a pod. This field may only be set for init containers, and the only allowed value is \\\"Always\\\". For non-init containers or when this field is not specified, the restart behavior is defined by the Pod's restart policy and the container type. Setting the RestartPolicy as \\\"Always\\\" for the init container will have the following effect: this init container will be continually restarted on exit until all regular containers have terminated. Once all regular containers have completed, all init containers with restartPolicy \\\"Always\\\" will be shut down. This lifecycle differs from normal init containers and is often referred to as a \\\"sidecar\\\" container. Although this init container still starts in the init container sequence, it does not wait for the container to complete before proceeding to the next init container. Instead, the next init container starts immediately after this init container is started, or after any startupProbe has successfully completed.\",\"type\":\"string\"},\"securityContext\":{\"allOf\":[{\"description\":\"SecurityContext holds security configuration that will be applied to a container. Some fields are present in both SecurityContext and PodSecurityContext.  When both are set, the values in SecurityContext take precedence.\",\"properties\":{\"allowPrivilegeEscalation\":{\"description\":\"AllowPrivilegeEscalation controls whether a process can gain more privileges than its parent process. This bool directly controls if the no_new_privs flag will be set on the container process. AllowPrivilegeEscalation is true always when the container is: 1) run as Privileged 2) has CAP_SYS_ADMIN Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"boolean\"},\"appArmorProfile\":{\"allOf\":[{\"description\":\"AppArmorProfile defines a pod or container's AppArmor settings.\",\"properties\":{\"localhostProfile\":{\"description\":\"localhostProfile indicates a profile loaded on the node that should be used. The profile must be preconfigured on the node to work. Must match the loaded name of the profile. Must be set if and only if type is \\\"Localhost\\\".\",\"type\":\"string\"},\"type\":{\"default\":\"\",\"description\":\"type indicates which kind of AppArmor profile will be applied. Valid options are:\\n  Localhost - a profile pre-loaded on the node.\\n  RuntimeDefault - the container runtime's default profile.\\n  Unconfined - no AppArmor enforcement.\",\"type\":\"string\"}},\"required\":[\"type\"],\"type\":\"object\",\"x-kubernetes-unions\":[{\"discriminator\":\"type\",\"fields-to-discriminateBy\":{\"localhostProfile\":\"LocalhostProfile\"}}]}],\"description\":\"appArmorProfile is the AppArmor options to use by this container. If set, this profile overrides the pod's appArmorProfile. Note that this field cannot be set when spec.os.name is windows.\"},\"capabilities\":{\"allOf\":[{\"description\":\"Adds and removes POSIX capabilities from running containers.\",\"properties\":{\"add\":{\"description\":\"Added capabilities\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"drop\":{\"description\":\"Removed capabilities\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"The capabilities to add/drop when running containers. Defaults to the default set of capabilities granted by the container runtime. Note that this field cannot be set when spec.os.name is windows.\"},\"privileged\":{\"description\":\"Run container in privileged mode. Processes in privileged containers are essentially equivalent to root on the host. Defaults to false. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"boolean\"},\"procMount\":{\"description\":\"procMount denotes the type of proc mount to use for the containers. The default value is Default which uses the container runtime defaults for readonly paths and masked paths. This requires the ProcMountType feature flag to be enabled. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"string\"},\"readOnlyRootFilesystem\":{\"description\":\"Whether this container has a read-only root filesystem. Default is false. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"boolean\"},\"runAsGroup\":{\"description\":\"The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\"format\":\"int64\",\"type\":\"integer\"},\"runAsNonRoot\":{\"description\":\"Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\"type\":\"boolean\"},\"runAsUser\":{\"description\":\"The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\",\"format\":\"int64\",\"type\":\"integer\"},\"seLinuxOptions\":{\"allOf\":[{\"description\":\"SELinuxOptions are the labels to be applied to the container\",\"properties\":{\"level\":{\"description\":\"Level is SELinux level label that applies to the container.\",\"type\":\"string\"},\"role\":{\"description\":\"Role is a SELinux role label that applies to the container.\",\"type\":\"string\"},\"type\":{\"description\":\"Type is a SELinux type label that applies to the container.\",\"type\":\"string\"},\"user\":{\"description\":\"User is a SELinux user label that applies to the container.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"The SELinux context to be applied to the container. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in PodSecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is windows.\"},\"seccompProfile\":{\"allOf\":[{\"description\":\"SeccompProfile defines a pod/container's seccomp profile settings. Only one profile source may be set.\",\"properties\":{\"localhostProfile\":{\"description\":\"localhostProfile indicates a profile defined in a file on the node should be used. The profile must be preconfigured on the node to work. Must be a descending path, relative to the kubelet's configured seccomp profile location. Must be set if type is \\\"Localhost\\\". Must NOT be set for any other type.\",\"type\":\"string\"},\"type\":{\"default\":\"\",\"description\":\"type indicates which kind of seccomp profile will be applied. Valid options are:\\n\\nLocalhost - a profile defined in a file on the node should be used. RuntimeDefault - the container runtime default profile should be used. Unconfined - no profile should be applied.\",\"type\":\"string\"}},\"required\":[\"type\"],\"type\":\"object\",\"x-kubernetes-unions\":[{\"discriminator\":\"type\",\"fields-to-discriminateBy\":{\"localhostProfile\":\"LocalhostProfile\"}}]}],\"description\":\"The seccomp options to use by this container. If seccomp options are provided at both the pod \\u0026 container level, the container options override the pod options. Note that this field cannot be set when spec.os.name is windows.\"},\"windowsOptions\":{\"allOf\":[{\"description\":\"WindowsSecurityContextOptions contain Windows-specific options and credentials.\",\"properties\":{\"gmsaCredentialSpec\":{\"description\":\"GMSACredentialSpec is where the GMSA admission webhook (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the GMSA credential spec named by the GMSACredentialSpecName field.\",\"type\":\"string\"},\"gmsaCredentialSpecName\":{\"description\":\"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\"type\":\"string\"},\"hostProcess\":{\"description\":\"HostProcess determines if a container should be run as a 'Host Process' container. All of a Pod's containers must have the same effective HostProcess value (it is not allowed to have a mix of HostProcess containers and non-HostProcess containers). In addition, if HostProcess is true then HostNetwork must also be set to true.\",\"type\":\"boolean\"},\"runAsUserName\":{\"description\":\"The UserName in Windows to run the entrypoint of the container process. Defaults to the user specified in image metadata if unspecified. May also be set in PodSecurityContext. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"The Windows specific settings applied to all containers. If unspecified, the options from the PodSecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.\"}},\"type\":\"object\"}],\"description\":\"SecurityContext defines the security options the container should be run with. If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext. More info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/\"},\"startupProbe\":{\"allOf\":[{\"description\":\"Probe describes a health check to be performed against a container to determine whether it is alive or ready to receive traffic.\",\"properties\":{\"exec\":{\"allOf\":[{\"description\":\"ExecAction describes a \\\"run in container\\\" action.\",\"properties\":{\"command\":{\"description\":\"Command is the command line to execute inside the container, the working directory for the command  is root ('/') in the container's filesystem. The command is simply exec'd, it is not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use a shell, you need to explicitly call out to that shell. Exit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"Exec specifies a command to execute in the container.\"},\"failureThreshold\":{\"description\":\"Minimum consecutive failures for the probe to be considered failed after having succeeded. Defaults to 3. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"grpc\":{\"allOf\":[{\"description\":\"GRPCAction specifies an action involving a GRPC service.\",\"properties\":{\"port\":{\"default\":0,\"description\":\"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\"format\":\"int32\",\"type\":\"integer\"},\"service\":{\"default\":\"\",\"description\":\"Service is the name of the service to place in the gRPC HealthCheckRequest (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"GRPC specifies a GRPC HealthCheckRequest.\"},\"httpGet\":{\"allOf\":[{\"description\":\"HTTPGetAction describes an action based on HTTP Get requests.\",\"properties\":{\"host\":{\"description\":\"Host name to connect to, defaults to the pod IP. You probably want to set \\\"Host\\\" in httpHeaders instead.\",\"type\":\"string\"},\"httpHeaders\":{\"description\":\"Custom headers to set in the request. HTTP allows repeated headers.\",\"items\":{\"allOf\":[{\"description\":\"HTTPHeader describes a custom header to be used in HTTP probes\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"The header field name. This will be canonicalized upon output, so case-variant names will be understood as the same header.\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"The header field value\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"Path to access on the HTTP server.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"},\"scheme\":{\"description\":\"Scheme to use for connecting to the host. Defaults to HTTP.\",\"type\":\"string\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"HTTPGet specifies an HTTP GET request to perform.\"},\"initialDelaySeconds\":{\"description\":\"Number of seconds after the container has started before liveness probes are initiated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"},\"periodSeconds\":{\"description\":\"How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"successThreshold\":{\"description\":\"Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\"format\":\"int32\",\"type\":\"integer\"},\"tcpSocket\":{\"allOf\":[{\"description\":\"TCPSocketAction describes an action based on opening a socket\",\"properties\":{\"host\":{\"description\":\"Optional: Host name to connect to, defaults to the pod IP.\",\"type\":\"string\"},\"port\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"Number or name of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.\"}},\"required\":[\"port\"],\"type\":\"object\"}],\"description\":\"TCPSocket specifies a connection to a TCP port.\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully upon probe failure. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this value overrides the value provided by the pod spec. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate. Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\"format\":\"int64\",\"type\":\"integer\"},\"timeoutSeconds\":{\"description\":\"Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\"format\":\"int32\",\"type\":\"integer\"}},\"type\":\"object\"}],\"description\":\"StartupProbe indicates that the Pod has successfully initialized. If specified, no other probes are executed until this completes successfully. If this probe fails, the Pod will be restarted, just as if the livenessProbe failed. This can be used to provide different probe parameters at the beginning of a Pod's lifecycle, when it might take a long time to load data or warm a cache, than during steady-state operation. This cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\"},\"stdin\":{\"description\":\"Whether this container should allocate a buffer for stdin in the container runtime. If this is not set, reads from stdin in the container will always result in EOF. Default is false.\",\"type\":\"boolean\"},\"stdinOnce\":{\"description\":\"Whether the container runtime should close the stdin channel after it has been opened by a single attach. When stdin is true the stdin stream will remain open across multiple attach sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the first client attaches to stdin, and then remains open and accepts data until the client disconnects, at which time stdin is closed and remains closed until the container is restarted. If this flag is false, a container processes that reads from stdin will never receive an EOF. Default is false\",\"type\":\"boolean\"},\"terminationMessagePath\":{\"description\":\"Optional: Path at which the file to which the container's termination message will be written is mounted into the container's filesystem. Message written is intended to be brief final status, such as an assertion failure message. Will be truncated by the node if greater than 4096 bytes. The total message length across all containers will be limited to 12kb. Defaults to /dev/termination-log. Cannot be updated.\",\"type\":\"string\"},\"terminationMessagePolicy\":{\"description\":\"Indicate how the termination message should be populated. File will use the contents of terminationMessagePath to populate the container status message on both success and failure. FallbackToLogsOnError will use the last chunk of container log output if the termination message file is empty and the container exited with an error. The log output is limited to 2048 bytes or 80 lines, whichever is smaller. Defaults to File. Cannot be updated.\",\"type\":\"string\"},\"tty\":{\"description\":\"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true. Default is false.\",\"type\":\"boolean\"},\"volumeDevices\":{\"description\":\"volumeDevices is the list of block devices to be used by the container.\",\"items\":{\"allOf\":[{\"description\":\"volumeDevice describes a mapping of a raw block device within a container.\",\"properties\":{\"devicePath\":{\"default\":\"\",\"description\":\"devicePath is the path inside of the container that the device will be mapped to.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"name must match the name of a persistentVolumeClaim in the pod\",\"type\":\"string\"}},\"required\":[\"name\",\"devicePath\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"devicePath\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"devicePath\",\"x-kubernetes-patch-strategy\":\"merge\"},\"volumeMounts\":{\"description\":\"Pod volumes to mount into the container's filesystem. Cannot be updated.\",\"items\":{\"allOf\":[{\"description\":\"VolumeMount describes a mounting of a Volume within a container.\",\"properties\":{\"mountPath\":{\"default\":\"\",\"description\":\"Path within the container at which the volume should be mounted.  Must not contain ':'.\",\"type\":\"string\"},\"mountPropagation\":{\"description\":\"mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10. When RecursiveReadOnly is set to IfPossible or to Enabled, MountPropagation must be None or unspecified (which defaults to None).\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"This must match the Name of a Volume.\",\"type\":\"string\"},\"readOnly\":{\"description\":\"Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.\",\"type\":\"boolean\"},\"recursiveReadOnly\":{\"description\":\"RecursiveReadOnly specifies whether read-only mounts should be handled recursively.\\n\\nIf ReadOnly is false, this field has no meaning and must be unspecified.\\n\\nIf ReadOnly is true, and this field is set to Disabled, the mount is not made recursively read-only.  If this field is set to IfPossible, the mount is made recursively read-only, if it is supported by the container runtime.  If this field is set to Enabled, the mount is made recursively read-only if it is supported by the container runtime, otherwise the pod will not be started and an error will be generated to indicate the reason.\\n\\nIf this field is set to IfPossible or Enabled, MountPropagation must be set to None (or be unspecified, which defaults to None).\\n\\nIf this field is not specified, it is treated as an equivalent of Disabled.\",\"type\":\"string\"},\"subPath\":{\"description\":\"Path within the volume from which the container's volume should be mounted. Defaults to \\\"\\\" (volume's root).\",\"type\":\"string\"},\"subPathExpr\":{\"description\":\"Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \\\"\\\" (volume's root). SubPathExpr and SubPath are mutually exclusive.\",\"type\":\"string\"}},\"required\":[\"name\",\"mountPath\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"mountPath\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"mountPath\",\"x-kubernetes-patch-strategy\":\"merge\"},\"workingDir\":{\"description\":\"Container's working directory. If not specified, the container runtime's default will be used, which might be configured in the container image. Cannot be updated.\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"},\"nodeName\":{\"description\":\"NodeName indicates in which node this pod is scheduled. If empty, this pod is a candidate for scheduling by the scheduler defined in schedulerName. Once this field is set, the kubelet for this node becomes responsible for the lifecycle of this pod. This field should not be used to express a desire for the pod to be scheduled on a specific node. https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodename\",\"type\":\"string\"},\"nodeSelector\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"NodeSelector is a selector which must be true for the pod to fit on a node. Selector which must match a node's labels for the pod to be scheduled on that node. More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/\",\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"},\"os\":{\"allOf\":[{\"description\":\"PodOS defines the OS parameters of a pod.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name is the name of the operating system. The currently supported values are linux and windows. Additional value may be defined in future and can be one of: https://github.com/opencontainers/runtime-spec/blob/master/config.md#platform-specific-configuration Clients should expect to handle additional values and treat unrecognized values in this field as os: null\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"description\":\"Specifies the OS of the containers in the pod. Some pod and container fields are restricted if this is set.\\n\\nIf the OS field is set to linux, the following fields must be unset: -securityContext.windowsOptions\\n\\nIf the OS field is set to windows, following fields must be unset: - spec.hostPID - spec.hostIPC - spec.hostUsers - spec.securityContext.appArmorProfile - spec.securityContext.seLinuxOptions - spec.securityContext.seccompProfile - spec.securityContext.fsGroup - spec.securityContext.fsGroupChangePolicy - spec.securityContext.sysctls - spec.shareProcessNamespace - spec.securityContext.runAsUser - spec.securityContext.runAsGroup - spec.securityContext.supplementalGroups - spec.securityContext.supplementalGroupsPolicy - spec.containers[*].securityContext.appArmorProfile - spec.containers[*].securityContext.seLinuxOptions - spec.containers[*].securityContext.seccompProfile - spec.containers[*].securityContext.capabilities - spec.containers[*].securityContext.readOnlyRootFilesystem - spec.containers[*].securityContext.privileged - spec.containers[*].securityContext.allowPrivilegeEscalation - spec.containers[*].securityContext.procMount - spec.containers[*].securityContext.runAsUser - spec.containers[*].securityContext.runAsGroup\"},\"overhead\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Overhead represents the resource overhead associated with running a pod for a given RuntimeClass. This field will be autopopulated at admission time by the RuntimeClass admission controller. If the RuntimeClass admission controller is enabled, overhead must not be set in Pod create requests. The RuntimeClass admission controller will reject Pod create requests which have the overhead already set. If RuntimeClass is configured and selected in the PodSpec, Overhead will be set to the value defined in the corresponding RuntimeClass, otherwise it will remain unset and treated as zero. More info: https://git.k8s.io/enhancements/keps/sig-node/688-pod-overhead/README.md\",\"type\":\"object\"},\"preemptionPolicy\":{\"description\":\"PreemptionPolicy is the Policy for preempting pods with lower priority. One of Never, PreemptLowerPriority. Defaults to PreemptLowerPriority if unset.\",\"type\":\"string\"},\"priority\":{\"description\":\"The priority value. Various system components use this field to find the priority of the pod. When Priority Admission Controller is enabled, it prevents users from setting this field. The admission controller populates this field from PriorityClassName. The higher the value, the higher the priority.\",\"format\":\"int32\",\"type\":\"integer\"},\"priorityClassName\":{\"description\":\"If specified, indicates the pod's priority. \\\"system-node-critical\\\" and \\\"system-cluster-critical\\\" are two special keywords which indicate the highest priorities with the former being the highest priority. Any other name must be defined by creating a PriorityClass object with that name. If not specified, the pod priority will be default or zero if there is no default.\",\"type\":\"string\"},\"readinessGates\":{\"description\":\"If specified, all readiness gates will be evaluated for pod readiness. A pod is ready when all its containers are ready AND all conditions specified in the readiness gates have status equal to \\\"True\\\" More info: https://git.k8s.io/enhancements/keps/sig-network/580-pod-readiness-gates\",\"items\":{\"allOf\":[{\"description\":\"PodReadinessGate contains the reference to a pod condition\",\"properties\":{\"conditionType\":{\"default\":\"\",\"description\":\"ConditionType refers to a condition in the pod's condition list with matching type.\",\"type\":\"string\"}},\"required\":[\"conditionType\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"resourceClaims\":{\"description\":\"ResourceClaims defines which ResourceClaims must be allocated and reserved before the Pod is allowed to start. The resources will be made available to those containers which consume them by name.\\n\\nThis is an alpha field and requires enabling the DynamicResourceAllocation feature gate.\\n\\nThis field is immutable.\",\"items\":{\"allOf\":[{\"description\":\"PodResourceClaim references exactly one ResourceClaim, either directly or by naming a ResourceClaimTemplate which is then turned into a ResourceClaim for the pod.\\n\\nIt adds a name to it that uniquely identifies the ResourceClaim inside the Pod. Containers that need access to the ResourceClaim reference it with this name.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name uniquely identifies this resource claim inside the pod. This must be a DNS_LABEL.\",\"type\":\"string\"},\"resourceClaimName\":{\"description\":\"ResourceClaimName is the name of a ResourceClaim object in the same namespace as this pod.\\n\\nExactly one of ResourceClaimName and ResourceClaimTemplateName must be set.\",\"type\":\"string\"},\"resourceClaimTemplateName\":{\"description\":\"ResourceClaimTemplateName is the name of a ResourceClaimTemplate object in the same namespace as this pod.\\n\\nThe template will be used to create a new ResourceClaim, which will be bound to this pod. When this pod is deleted, the ResourceClaim will also be deleted. The pod name and resource name, along with a generated component, will be used to form a unique name for the ResourceClaim, which will be recorded in pod.status.resourceClaimStatuses.\\n\\nThis field is immutable and no changes will be made to the corresponding ResourceClaim by the control plane after creating the ResourceClaim.\\n\\nExactly one of ResourceClaimName and ResourceClaimTemplateName must be set.\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge,retainKeys\"},\"resources\":{\"allOf\":[{\"description\":\"ResourceRequirements describes the compute resource requirements.\",\"properties\":{\"claims\":{\"description\":\"Claims lists the names of resources, defined in spec.resourceClaims, that are used by this container.\\n\\nThis is an alpha field and requires enabling the DynamicResourceAllocation feature gate.\\n\\nThis field is immutable. It can only be set for containers.\",\"items\":{\"allOf\":[{\"description\":\"ResourceClaim references one entry in PodSpec.ResourceClaims.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name must match the name of one entry in pod.spec.resourceClaims of the Pod where this field is used. It makes that resource available inside a container.\",\"type\":\"string\"},\"request\":{\"description\":\"Request is the name chosen for a request in the referenced claim. If empty, everything from the claim is made available, otherwise only the result of this request.\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\"},\"limits\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"},\"requests\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. Requests cannot exceed Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"}},\"type\":\"object\"}],\"description\":\"Resources is the total amount of CPU and Memory resources required by all containers in the pod. It supports specifying Requests and Limits for \\\"cpu\\\" and \\\"memory\\\" resource names only. ResourceClaims are not supported.\\n\\nThis field enables fine-grained control over resource allocation for the entire pod, allowing resource sharing among containers in a pod.\\n\\nThis is an alpha field and requires enabling the PodLevelResources feature gate.\"},\"restartPolicy\":{\"description\":\"Restart policy for all containers within the pod. One of Always, OnFailure, Never. In some contexts, only a subset of those values may be permitted. Default to Always. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#restart-policy\",\"type\":\"string\"},\"runtimeClassName\":{\"description\":\"RuntimeClassName refers to a RuntimeClass object in the node.k8s.io group, which should be used to run this pod.  If no RuntimeClass resource matches the named class, the pod will not be run. If unset or empty, the \\\"legacy\\\" RuntimeClass will be used, which is an implicit class with an empty definition that uses the default runtime handler. More info: https://git.k8s.io/enhancements/keps/sig-node/585-runtime-class\",\"type\":\"string\"},\"schedulerName\":{\"description\":\"If specified, the pod will be dispatched by specified scheduler. If not specified, the pod will be dispatched by default scheduler.\",\"type\":\"string\"},\"schedulingGates\":{\"description\":\"SchedulingGates is an opaque list of values that if specified will block scheduling the pod. If schedulingGates is not empty, the pod will stay in the SchedulingGated state and the scheduler will not attempt to schedule the pod.\\n\\nSchedulingGates can only be set at pod creation time, and be removed only afterwards.\",\"items\":{\"allOf\":[{\"description\":\"PodSchedulingGate is associated to a Pod to guard its scheduling.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the scheduling gate. Each scheduling gate must have a unique name field.\",\"type\":\"string\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"},\"securityContext\":{\"allOf\":[{\"description\":\"PodSecurityContext holds pod-level security attributes and common container settings. Some fields are also present in container.securityContext.  Field values of container.securityContext take precedence over field values of PodSecurityContext.\",\"properties\":{\"appArmorProfile\":{\"allOf\":[{\"description\":\"AppArmorProfile defines a pod or container's AppArmor settings.\",\"properties\":{\"localhostProfile\":{\"description\":\"localhostProfile indicates a profile loaded on the node that should be used. The profile must be preconfigured on the node to work. Must match the loaded name of the profile. Must be set if and only if type is \\\"Localhost\\\".\",\"type\":\"string\"},\"type\":{\"default\":\"\",\"description\":\"type indicates which kind of AppArmor profile will be applied. Valid options are:\\n  Localhost - a profile pre-loaded on the node.\\n  RuntimeDefault - the container runtime's default profile.\\n  Unconfined - no AppArmor enforcement.\",\"type\":\"string\"}},\"required\":[\"type\"],\"type\":\"object\",\"x-kubernetes-unions\":[{\"discriminator\":\"type\",\"fields-to-discriminateBy\":{\"localhostProfile\":\"LocalhostProfile\"}}]}],\"description\":\"appArmorProfile is the AppArmor options to use by the containers in this pod. Note that this field cannot be set when spec.os.name is windows.\"},\"fsGroup\":{\"description\":\"A special supplemental group that applies to all containers in a pod. Some volume types allow the Kubelet to change the ownership of that volume to be owned by the pod:\\n\\n1. The owning GID will be the FSGroup 2. The setgid bit is set (new files created in the volume will be owned by FSGroup) 3. The permission bits are OR'd with rw-rw----\\n\\nIf unset, the Kubelet will not modify the ownership and permissions of any volume. Note that this field cannot be set when spec.os.name is windows.\",\"format\":\"int64\",\"type\":\"integer\"},\"fsGroupChangePolicy\":{\"description\":\"fsGroupChangePolicy defines behavior of changing ownership and permission of the volume before being exposed inside Pod. This field will only apply to volume types which support fsGroup based ownership(and permissions). It will have no effect on ephemeral volume types such as: secret, configmaps and emptydir. Valid values are \\\"OnRootMismatch\\\" and \\\"Always\\\". If not specified, \\\"Always\\\" is used. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"string\"},\"runAsGroup\":{\"description\":\"The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.\",\"format\":\"int64\",\"type\":\"integer\"},\"runAsNonRoot\":{\"description\":\"Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\"type\":\"boolean\"},\"runAsUser\":{\"description\":\"The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.\",\"format\":\"int64\",\"type\":\"integer\"},\"seLinuxChangePolicy\":{\"description\":\"seLinuxChangePolicy defines how the container's SELinux label is applied to all volumes used by the Pod. It has no effect on nodes that do not support SELinux or to volumes does not support SELinux. Valid values are \\\"MountOption\\\" and \\\"Recursive\\\".\\n\\n\\\"Recursive\\\" means relabeling of all files on all Pod volumes by the container runtime. This may be slow for large volumes, but allows mixing privileged and unprivileged Pods sharing the same volume on the same node.\\n\\n\\\"MountOption\\\" mounts all eligible Pod volumes with `-o context` mount option. This requires all Pods that share the same volume to use the same SELinux label. It is not possible to share the same volume among privileged and unprivileged Pods. Eligible volumes are in-tree FibreChannel and iSCSI volumes, and all CSI volumes whose CSI driver announces SELinux support by setting spec.seLinuxMount: true in their CSIDriver instance. Other volumes are always re-labelled recursively. \\\"MountOption\\\" value is allowed only when SELinuxMount feature gate is enabled.\\n\\nIf not specified and SELinuxMount feature gate is enabled, \\\"MountOption\\\" is used. If not specified and SELinuxMount feature gate is disabled, \\\"MountOption\\\" is used for ReadWriteOncePod volumes and \\\"Recursive\\\" for all other volumes.\\n\\nThis field affects only Pods that have SELinux label set, either in PodSecurityContext or in SecurityContext of all containers.\\n\\nAll Pods that use the same volume should use the same seLinuxChangePolicy, otherwise some pods can get stuck in ContainerCreating state. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"string\"},\"seLinuxOptions\":{\"allOf\":[{\"description\":\"SELinuxOptions are the labels to be applied to the container\",\"properties\":{\"level\":{\"description\":\"Level is SELinux level label that applies to the container.\",\"type\":\"string\"},\"role\":{\"description\":\"Role is a SELinux role label that applies to the container.\",\"type\":\"string\"},\"type\":{\"description\":\"Type is a SELinux type label that applies to the container.\",\"type\":\"string\"},\"user\":{\"description\":\"User is a SELinux user label that applies to the container.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"The SELinux context to be applied to all containers. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.\"},\"seccompProfile\":{\"allOf\":[{\"description\":\"SeccompProfile defines a pod/container's seccomp profile settings. Only one profile source may be set.\",\"properties\":{\"localhostProfile\":{\"description\":\"localhostProfile indicates a profile defined in a file on the node should be used. The profile must be preconfigured on the node to work. Must be a descending path, relative to the kubelet's configured seccomp profile location. Must be set if type is \\\"Localhost\\\". Must NOT be set for any other type.\",\"type\":\"string\"},\"type\":{\"default\":\"\",\"description\":\"type indicates which kind of seccomp profile will be applied. Valid options are:\\n\\nLocalhost - a profile defined in a file on the node should be used. RuntimeDefault - the container runtime default profile should be used. Unconfined - no profile should be applied.\",\"type\":\"string\"}},\"required\":[\"type\"],\"type\":\"object\",\"x-kubernetes-unions\":[{\"discriminator\":\"type\",\"fields-to-discriminateBy\":{\"localhostProfile\":\"LocalhostProfile\"}}]}],\"description\":\"The seccomp options to use by the containers in this pod. Note that this field cannot be set when spec.os.name is windows.\"},\"supplementalGroups\":{\"description\":\"A list of groups applied to the first process run in each container, in addition to the container's primary GID and fsGroup (if specified).  If the SupplementalGroupsPolicy feature is enabled, the supplementalGroupsPolicy field determines whether these are in addition to or instead of any group memberships defined in the container image. If unspecified, no additional groups are added, though group memberships defined in the container image may still be used, depending on the supplementalGroupsPolicy field. Note that this field cannot be set when spec.os.name is windows.\",\"items\":{\"default\":0,\"format\":\"int64\",\"type\":\"integer\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"supplementalGroupsPolicy\":{\"description\":\"Defines how supplemental groups of the first container processes are calculated. Valid values are \\\"Merge\\\" and \\\"Strict\\\". If not specified, \\\"Merge\\\" is used. (Alpha) Using the field requires the SupplementalGroupsPolicy feature gate to be enabled and the container runtime must implement support for this feature. Note that this field cannot be set when spec.os.name is windows.\",\"type\":\"string\"},\"sysctls\":{\"description\":\"Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported sysctls (by the container runtime) might fail to launch. Note that this field cannot be set when spec.os.name is windows.\",\"items\":{\"allOf\":[{\"description\":\"Sysctl defines a kernel parameter to be set\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of a property to set\",\"type\":\"string\"},\"value\":{\"default\":\"\",\"description\":\"Value of a property to set\",\"type\":\"string\"}},\"required\":[\"name\",\"value\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"windowsOptions\":{\"allOf\":[{\"description\":\"WindowsSecurityContextOptions contain Windows-specific options and credentials.\",\"properties\":{\"gmsaCredentialSpec\":{\"description\":\"GMSACredentialSpec is where the GMSA admission webhook (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the GMSA credential spec named by the GMSACredentialSpecName field.\",\"type\":\"string\"},\"gmsaCredentialSpecName\":{\"description\":\"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\"type\":\"string\"},\"hostProcess\":{\"description\":\"HostProcess determines if a container should be run as a 'Host Process' container. All of a Pod's containers must have the same effective HostProcess value (it is not allowed to have a mix of HostProcess containers and non-HostProcess containers). In addition, if HostProcess is true then HostNetwork must also be set to true.\",\"type\":\"boolean\"},\"runAsUserName\":{\"description\":\"The UserName in Windows to run the entrypoint of the container process. Defaults to the user specified in image metadata if unspecified. May also be set in PodSecurityContext. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"The Windows specific settings applied to all containers. If unspecified, the options within a container's SecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.\"}},\"type\":\"object\"}],\"description\":\"SecurityContext holds pod-level security attributes and common container settings. Optional: Defaults to empty.  See type description for default values of each field.\"},\"serviceAccount\":{\"description\":\"DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.\",\"type\":\"string\"},\"serviceAccountName\":{\"description\":\"ServiceAccountName is the name of the ServiceAccount to use to run this pod. More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/\",\"type\":\"string\"},\"setHostnameAsFQDN\":{\"description\":\"If true the pod's hostname will be configured as the pod's FQDN, rather than the leaf name (the default). In Linux containers, this means setting the FQDN in the hostname field of the kernel (the nodename field of struct utsname). In Windows containers, this means setting the registry value of hostname for the registry key HKEY_LOCAL_MACHINE\\\\\\\\SYSTEM\\\\\\\\CurrentControlSet\\\\\\\\Services\\\\\\\\Tcpip\\\\\\\\Parameters to FQDN. If a pod does not have FQDN, this has no effect. Default to false.\",\"type\":\"boolean\"},\"shareProcessNamespace\":{\"description\":\"Share a single process namespace between all of the containers in a pod. When this is set containers will be able to view and signal processes from other containers in the same pod, and the first process in each container will not be assigned PID 1. HostPID and ShareProcessNamespace cannot both be set. Optional: Default to false.\",\"type\":\"boolean\"},\"subdomain\":{\"description\":\"If specified, the fully qualified Pod hostname will be \\\"\\u003chostname\\u003e.\\u003csubdomain\\u003e.\\u003cpod namespace\\u003e.svc.\\u003ccluster domain\\u003e\\\". If not specified, the pod will not have a domainname at all.\",\"type\":\"string\"},\"terminationGracePeriodSeconds\":{\"description\":\"Optional duration in seconds the pod needs to terminate gracefully. May be decreased in delete request. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). If this value is nil, the default grace period will be used instead. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. Defaults to 30 seconds.\",\"format\":\"int64\",\"type\":\"integer\"},\"tolerations\":{\"description\":\"If specified, the pod's tolerations.\",\"items\":{\"allOf\":[{\"description\":\"The pod this Toleration is attached to tolerates any taint that matches the triple \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\"properties\":{\"effect\":{\"description\":\"Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.\",\"type\":\"string\"},\"key\":{\"description\":\"Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys.\",\"type\":\"string\"},\"operator\":{\"description\":\"Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a pod can tolerate all taints of a particular category.\",\"type\":\"string\"},\"tolerationSeconds\":{\"description\":\"TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system.\",\"format\":\"int64\",\"type\":\"integer\"},\"value\":{\"description\":\"Value is the taint value the toleration matches to. If the operator is Exists, the value should be empty, otherwise just a regular string.\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"topologySpreadConstraints\":{\"description\":\"TopologySpreadConstraints describes how a group of pods ought to spread across topology domains. Scheduler will schedule pods in a way which abides by the constraints. All topologySpreadConstraints are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"TopologySpreadConstraint specifies how to spread matching pods among the given topology.\",\"properties\":{\"labelSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"LabelSelector is used to find matching pods. Pods that match this label selector are counted to determine the number of pods in their corresponding topology domain.\"},\"matchLabelKeys\":{\"description\":\"MatchLabelKeys is a set of pod label keys to select the pods over which spreading will be calculated. The keys are used to lookup values from the incoming pod labels, those key-value labels are ANDed with labelSelector to select the group of existing pods over which spreading will be calculated for the incoming pod. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector. MatchLabelKeys cannot be set when LabelSelector isn't set. Keys that don't exist in the incoming pod labels will be ignored. A null or empty list means only match against labelSelector.\\n\\nThis is a beta field and requires the MatchLabelKeysInPodTopologySpread feature gate to be enabled (enabled by default).\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"maxSkew\":{\"default\":0,\"description\":\"MaxSkew describes the degree to which pods may be unevenly distributed. When `whenUnsatisfiable=DoNotSchedule`, it is the maximum permitted difference between the number of matching pods in the target topology and the global minimum. The global minimum is the minimum number of matching pods in an eligible domain or zero if the number of eligible domains is less than MinDomains. For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same labelSelector spread as 2/2/1: In this case, the global minimum is 1. | zone1 | zone2 | zone3 | |  P P  |  P P  |   P   | - if MaxSkew is 1, incoming pod can only be scheduled to zone3 to become 2/2/2; scheduling it onto zone1(zone2) would make the ActualSkew(3-1) on zone1(zone2) violate MaxSkew(1). - if MaxSkew is 2, incoming pod can be scheduled onto any zone. When `whenUnsatisfiable=ScheduleAnyway`, it is used to give higher precedence to topologies that satisfy it. It's a required field. Default value is 1 and 0 is not allowed.\",\"format\":\"int32\",\"type\":\"integer\"},\"minDomains\":{\"description\":\"MinDomains indicates a minimum number of eligible domains. When the number of eligible domains with matching topology keys is less than minDomains, Pod Topology Spread treats \\\"global minimum\\\" as 0, and then the calculation of Skew is performed. And when the number of eligible domains with matching topology keys equals or greater than minDomains, this value has no effect on scheduling. As a result, when the number of eligible domains is less than minDomains, scheduler won't schedule more than maxSkew Pods to those domains. If value is nil, the constraint behaves as if MinDomains is equal to 1. Valid values are integers greater than 0. When value is not nil, WhenUnsatisfiable must be DoNotSchedule.\\n\\nFor example, in a 3-zone cluster, MaxSkew is set to 2, MinDomains is set to 5 and pods with the same labelSelector spread as 2/2/2: | zone1 | zone2 | zone3 | |  P P  |  P P  |  P P  | The number of domains is less than 5(MinDomains), so \\\"global minimum\\\" is treated as 0. In this situation, new pod with the same labelSelector cannot be scheduled, because computed skew will be 3(3 - 0) if new Pod is scheduled to any of the three zones, it will violate MaxSkew.\",\"format\":\"int32\",\"type\":\"integer\"},\"nodeAffinityPolicy\":{\"description\":\"NodeAffinityPolicy indicates how we will treat Pod's nodeAffinity/nodeSelector when calculating pod topology spread skew. Options are: - Honor: only nodes matching nodeAffinity/nodeSelector are included in the calculations. - Ignore: nodeAffinity/nodeSelector are ignored. All nodes are included in the calculations.\\n\\nIf this value is nil, the behavior is equivalent to the Honor policy.\",\"type\":\"string\"},\"nodeTaintsPolicy\":{\"description\":\"NodeTaintsPolicy indicates how we will treat node taints when calculating pod topology spread skew. Options are: - Honor: nodes without taints, along with tainted nodes for which the incoming pod has a toleration, are included. - Ignore: node taints are ignored. All nodes are included.\\n\\nIf this value is nil, the behavior is equivalent to the Ignore policy.\",\"type\":\"string\"},\"topologyKey\":{\"default\":\"\",\"description\":\"TopologyKey is the key of node labels. Nodes that have a label with this key and identical values are considered to be in the same topology. We consider each \\u003ckey, value\\u003e as a \\\"bucket\\\", and try to put balanced number of pods into each bucket. We define a domain as a particular instance of a topology. Also, we define an eligible domain as a domain whose nodes meet the requirements of nodeAffinityPolicy and nodeTaintsPolicy. e.g. If TopologyKey is \\\"kubernetes.io/hostname\\\", each Node is a domain of that topology. And, if TopologyKey is \\\"topology.kubernetes.io/zone\\\", each zone is a domain of that topology. It's a required field.\",\"type\":\"string\"},\"whenUnsatisfiable\":{\"default\":\"\",\"description\":\"WhenUnsatisfiable indicates how to deal with a pod if it doesn't satisfy the spread constraint. - DoNotSchedule (default) tells the scheduler not to schedule it. - ScheduleAnyway tells the scheduler to schedule the pod in any location,\\n  but giving higher precedence to topologies that would help reduce the\\n  skew.\\nA constraint is considered \\\"Unsatisfiable\\\" for an incoming pod if and only if every possible node assignment for that pod would violate \\\"MaxSkew\\\" on some topology. For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same labelSelector spread as 3/1/1: | zone1 | zone2 | zone3 | | P P P |   P   |   P   | If WhenUnsatisfiable is set to DoNotSchedule, incoming pod can only be scheduled to zone2(zone3) to become 3/2/1(3/1/2) as ActualSkew(2-1) on zone2(zone3) satisfies MaxSkew(1). In other words, the cluster can still be imbalanced, but scheduler won't make it *more* imbalanced. It's a required field.\",\"type\":\"string\"}},\"required\":[\"maxSkew\",\"topologyKey\",\"whenUnsatisfiable\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"topologyKey\",\"whenUnsatisfiable\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"topologyKey\",\"x-kubernetes-patch-strategy\":\"merge\"},\"volumes\":{\"description\":\"List of volumes that can be mounted by containers belonging to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes\",\"items\":{\"allOf\":[{\"description\":\"Volume represents a named volume in a pod that may be accessed by any container in the pod.\",\"properties\":{\"awsElasticBlockStore\":{\"allOf\":[{\"description\":\"Represents a Persistent Disk resource in AWS.\\n\\nAn AWS EBS disk must exist before mounting to a container. The disk must also be in the same AWS zone as the kubelet. An AWS EBS disk can only be mounted as read/write once. AWS EBS volumes support ownership management and SELinux relabeling.\",\"properties\":{\"fsType\":{\"description\":\"fsType is the filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\"type\":\"string\"},\"partition\":{\"description\":\"partition is the partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \\\"1\\\". Similarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty).\",\"format\":\"int32\",\"type\":\"integer\"},\"readOnly\":{\"description\":\"readOnly value true will force the readOnly setting in VolumeMounts. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\"type\":\"boolean\"},\"volumeID\":{\"default\":\"\",\"description\":\"volumeID is unique ID of the persistent disk resource in AWS (Amazon EBS volume). More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\"type\":\"string\"}},\"required\":[\"volumeID\"],\"type\":\"object\"}],\"description\":\"awsElasticBlockStore represents an AWS Disk resource that is attached to a kubelet's host machine and then exposed to the pod. Deprecated: AWSElasticBlockStore is deprecated. All operations for the in-tree awsElasticBlockStore type are redirected to the ebs.csi.aws.com CSI driver. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\"},\"azureDisk\":{\"allOf\":[{\"description\":\"AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.\",\"properties\":{\"cachingMode\":{\"default\":\"ReadWrite\",\"description\":\"cachingMode is the Host Caching mode: None, Read Only, Read Write.\",\"type\":\"string\"},\"diskName\":{\"default\":\"\",\"description\":\"diskName is the Name of the data disk in the blob storage\",\"type\":\"string\"},\"diskURI\":{\"default\":\"\",\"description\":\"diskURI is the URI of data disk in the blob storage\",\"type\":\"string\"},\"fsType\":{\"default\":\"ext4\",\"description\":\"fsType is Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\"type\":\"string\"},\"kind\":{\"default\":\"Shared\",\"description\":\"kind expected values are Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared\",\"type\":\"string\"},\"readOnly\":{\"default\":false,\"description\":\"readOnly Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\"type\":\"boolean\"}},\"required\":[\"diskName\",\"diskURI\"],\"type\":\"object\"}],\"description\":\"azureDisk represents an Azure Data Disk mount on the host and bind mount to the pod. Deprecated: AzureDisk is deprecated. All operations for the in-tree azureDisk type are redirected to the disk.csi.azure.com CSI driver.\"},\"azureFile\":{\"allOf\":[{\"description\":\"AzureFile represents an Azure File Service mount on the host and bind mount to the pod.\",\"properties\":{\"readOnly\":{\"description\":\"readOnly defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\"type\":\"boolean\"},\"secretName\":{\"default\":\"\",\"description\":\"secretName is the  name of secret that contains Azure Storage Account Name and Key\",\"type\":\"string\"},\"shareName\":{\"default\":\"\",\"description\":\"shareName is the azure share Name\",\"type\":\"string\"}},\"required\":[\"secretName\",\"shareName\"],\"type\":\"object\"}],\"description\":\"azureFile represents an Azure File Service mount on the host and bind mount to the pod. Deprecated: AzureFile is deprecated. All operations for the in-tree azureFile type are redirected to the file.csi.azure.com CSI driver.\"},\"cephfs\":{\"allOf\":[{\"description\":\"Represents a Ceph Filesystem mount that lasts the lifetime of a pod Cephfs volumes do not support ownership management or SELinux relabeling.\",\"properties\":{\"monitors\":{\"description\":\"monitors is Required: Monitors is a collection of Ceph monitors More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"path\":{\"description\":\"path is Optional: Used as the mounted root, rather than the full Ceph tree, default is /\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly is Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\"type\":\"boolean\"},\"secretFile\":{\"description\":\"secretFile is Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\"type\":\"string\"},\"secretRef\":{\"allOf\":[{\"description\":\"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"secretRef is Optional: SecretRef is reference to the authentication secret for User, default is empty. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\"},\"user\":{\"description\":\"user is optional: User is the rados user name, default is admin More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\"type\":\"string\"}},\"required\":[\"monitors\"],\"type\":\"object\"}],\"description\":\"cephFS represents a Ceph FS mount on the host that shares a pod's lifetime. Deprecated: CephFS is deprecated and the in-tree cephfs type is no longer supported.\"},\"cinder\":{\"allOf\":[{\"description\":\"Represents a cinder volume resource in Openstack. A Cinder volume must exist before mounting to a container. The volume must also be in the same region as the kubelet. Cinder volumes support ownership management and SELinux relabeling.\",\"properties\":{\"fsType\":{\"description\":\"fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\"type\":\"boolean\"},\"secretRef\":{\"allOf\":[{\"description\":\"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"secretRef is optional: points to a secret object containing parameters used to connect to OpenStack.\"},\"volumeID\":{\"default\":\"\",\"description\":\"volumeID used to identify the volume in cinder. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\"type\":\"string\"}},\"required\":[\"volumeID\"],\"type\":\"object\"}],\"description\":\"cinder represents a cinder volume attached and mounted on kubelets host machine. Deprecated: Cinder is deprecated. All operations for the in-tree cinder type are redirected to the cinder.csi.openstack.org CSI driver. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\"},\"configMap\":{\"allOf\":[{\"description\":\"Adapts a ConfigMap into a volume.\\n\\nThe contents of the target ConfigMap's Data field will be presented in a volume as files using the keys in the Data field as the file names, unless the items element is populated with specific mappings of keys to paths. ConfigMap volumes support ownership management and SELinux relabeling.\",\"properties\":{\"defaultMode\":{\"description\":\"defaultMode is optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"items\":{\"description\":\"items if unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\"items\":{\"allOf\":[{\"description\":\"Maps a string key to a path within a volume.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the key to project.\",\"type\":\"string\"},\"mode\":{\"description\":\"mode is Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"path\":{\"default\":\"\",\"description\":\"path is the relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\"type\":\"string\"}},\"required\":[\"key\",\"path\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"optional specify whether the ConfigMap or its keys must be defined\",\"type\":\"boolean\"}},\"type\":\"object\"}],\"description\":\"configMap represents a configMap that should populate this volume\"},\"csi\":{\"allOf\":[{\"description\":\"Represents a source location of a volume to mount, managed by an external CSI driver\",\"properties\":{\"driver\":{\"default\":\"\",\"description\":\"driver is the name of the CSI driver that handles this volume. Consult with your admin for the correct name as registered in the cluster.\",\"type\":\"string\"},\"fsType\":{\"description\":\"fsType to mount. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". If not provided, the empty value is passed to the associated CSI driver which will determine the default filesystem to apply.\",\"type\":\"string\"},\"nodePublishSecretRef\":{\"allOf\":[{\"description\":\"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"nodePublishSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI NodePublishVolume and NodeUnpublishVolume calls. This field is optional, and  may be empty if no secret is required. If the secret object contains more than one secret, all secret references are passed.\"},\"readOnly\":{\"description\":\"readOnly specifies a read-only configuration for the volume. Defaults to false (read/write).\",\"type\":\"boolean\"},\"volumeAttributes\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"volumeAttributes stores driver-specific properties that are passed to the CSI driver. Consult your driver's documentation for supported values.\",\"type\":\"object\"}},\"required\":[\"driver\"],\"type\":\"object\"}],\"description\":\"csi (Container Storage Interface) represents ephemeral storage that is handled by certain external CSI drivers.\"},\"downwardAPI\":{\"allOf\":[{\"description\":\"DownwardAPIVolumeSource represents a volume containing downward API info. Downward API volumes support ownership management and SELinux relabeling.\",\"properties\":{\"defaultMode\":{\"description\":\"Optional: mode bits to use on created files by default. Must be a Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"items\":{\"description\":\"Items is a list of downward API volume file\",\"items\":{\"allOf\":[{\"description\":\"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\"properties\":{\"fieldRef\":{\"allOf\":[{\"description\":\"ObjectFieldSelector selects an APIVersioned field of an object.\",\"properties\":{\"apiVersion\":{\"description\":\"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\"type\":\"string\"},\"fieldPath\":{\"default\":\"\",\"description\":\"Path of the field to select in the specified API version.\",\"type\":\"string\"}},\"required\":[\"fieldPath\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Required: Selects a field of the pod: only annotations, labels, name, namespace and uid are supported.\"},\"mode\":{\"description\":\"Optional: mode bits used to set permissions on this file, must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"path\":{\"default\":\"\",\"description\":\"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\"type\":\"string\"},\"resourceFieldRef\":{\"allOf\":[{\"description\":\"ResourceFieldSelector represents container resources (cpu, memory) and their output format\",\"properties\":{\"containerName\":{\"description\":\"Container name: required for volumes, optional for env vars\",\"type\":\"string\"},\"divisor\":{\"allOf\":[{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]}],\"description\":\"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\"},\"resource\":{\"default\":\"\",\"description\":\"Required: resource to select\",\"type\":\"string\"}},\"required\":[\"resource\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\"}},\"required\":[\"path\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"downwardAPI represents downward API about the pod that should populate this volume\"},\"emptyDir\":{\"allOf\":[{\"description\":\"Represents an empty directory for a pod. Empty directory volumes support ownership management and SELinux relabeling.\",\"properties\":{\"medium\":{\"description\":\"medium represents what type of storage medium should back this directory. The default is \\\"\\\" which means to use the node's default medium. Must be an empty string (default) or Memory. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\"type\":\"string\"},\"sizeLimit\":{\"allOf\":[{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]}],\"description\":\"sizeLimit is the total amount of local storage required for this EmptyDir volume. The size limit is also applicable for memory medium. The maximum usage on memory medium EmptyDir would be the minimum value between the SizeLimit specified here and the sum of memory limits of all containers in a pod. The default is nil which means that the limit is undefined. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\"}},\"type\":\"object\"}],\"description\":\"emptyDir represents a temporary directory that shares a pod's lifetime. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\"},\"ephemeral\":{\"allOf\":[{\"description\":\"Represents an ephemeral volume that is handled by a normal storage driver.\",\"properties\":{\"volumeClaimTemplate\":{\"allOf\":[{\"description\":\"PersistentVolumeClaimTemplate is used to produce PersistentVolumeClaim objects as part of an EphemeralVolumeSource.\",\"properties\":{\"metadata\":{\"allOf\":[{\"description\":\"ObjectMeta is metadata that all persisted resources must have, which includes all objects users must create.\",\"properties\":{\"annotations\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations\",\"type\":\"object\"},\"creationTimestamp\":{\"allOf\":[{\"description\":\"Time is a wrapper around time.Time which supports correct marshaling to YAML and JSON.  Wrappers are provided for many of the factory methods that the time package offers.\",\"format\":\"date-time\",\"type\":\"string\"}],\"description\":\"CreationTimestamp is a timestamp representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations. Clients may not set this value. It is represented in RFC3339 form and is in UTC.\\n\\nPopulated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata\"},\"deletionGracePeriodSeconds\":{\"description\":\"Number of seconds allowed for this object to gracefully terminate before it will be removed from the system. Only set when deletionTimestamp is also set. May only be shortened. Read-only.\",\"format\":\"int64\",\"type\":\"integer\"},\"deletionTimestamp\":{\"allOf\":[{\"description\":\"Time is a wrapper around time.Time which supports correct marshaling to YAML and JSON.  Wrappers are provided for many of the factory methods that the time package offers.\",\"format\":\"date-time\",\"type\":\"string\"}],\"description\":\"DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This field is set by the server when a graceful deletion is requested by the user, and is not directly settable by a client. The resource is expected to be deleted (no longer visible from resource lists, and not reachable by name) after the time in this field, once the finalizers list is empty. As long as the finalizers list contains items, deletion is blocked. Once the deletionTimestamp is set, this value may not be unset or be set further into the future, although it may be shortened or the resource may be deleted prior to this time. For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react by sending a graceful termination signal to the containers in the pod. After that 30 seconds, the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup, remove the pod from the API. In the presence of network partitions, this object may still exist after this timestamp, until an administrator or automated process can determine the resource is fully terminated. If not set, graceful deletion of the object has not been requested.\\n\\nPopulated by the system when a graceful deletion is requested. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata\"},\"finalizers\":{\"description\":\"Must be empty before the object is deleted from the registry. Each entry is an identifier for the responsible component that will remove the entry from the list. If the deletionTimestamp of the object is non-nil, entries in this list can only be removed. Finalizers may be processed and removed in any order.  Order is NOT enforced because it introduces significant risk of stuck finalizers. finalizers is a shared field, any actor with permission can reorder it. If the finalizer list is processed in order, then this can lead to a situation in which the component responsible for the first finalizer in the list is waiting for a signal (field value, external system, or other) produced by a component responsible for a finalizer later in the list, resulting in a deadlock. Without enforced ordering finalizers are free to order amongst themselves and are not vulnerable to ordering changes in the list.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"set\",\"x-kubernetes-patch-strategy\":\"merge\"},\"generateName\":{\"description\":\"GenerateName is an optional prefix, used by the server, to generate a unique name ONLY IF the Name field has not been provided. If this field is used, the name returned to the client will be different than the name passed. This value will also be combined with a unique suffix. The provided value has the same validation rules as the Name field, and may be truncated by the length of the suffix required to make the value unique on the server.\\n\\nIf this field is specified and the generated name exists, the server will return a 409.\\n\\nApplied only if Name is not specified. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency\",\"type\":\"string\"},\"generation\":{\"description\":\"A sequence number representing a specific generation of the desired state. Populated by the system. Read-only.\",\"format\":\"int64\",\"type\":\"integer\"},\"labels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels\",\"type\":\"object\"},\"managedFields\":{\"description\":\"ManagedFields maps workflow-id and version to the set of fields that are managed by that workflow. This is mostly for internal housekeeping, and users typically shouldn't need to set or understand this field. A workflow can be the user's name, a controller's name, or the name of a specific apply path like \\\"ci-cd\\\". The set of fields is always in the version that the workflow used when modifying the object.\",\"items\":{\"allOf\":[{\"description\":\"ManagedFieldsEntry is a workflow-id, a FieldSet and the group version of the resource that the fieldset applies to.\",\"properties\":{\"apiVersion\":{\"description\":\"APIVersion defines the version of this resource that this field set applies to. The format is \\\"group/version\\\" just like the top-level APIVersion field. It is necessary to track the version of a field set because it cannot be automatically converted.\",\"type\":\"string\"},\"fieldsType\":{\"description\":\"FieldsType is the discriminator for the different fields format and version. There is currently only one possible value: \\\"FieldsV1\\\"\",\"type\":\"string\"},\"fieldsV1\":{\"allOf\":[{\"description\":\"FieldsV1 stores a set of fields in a data structure like a Trie, in JSON format.\\n\\nEach key is either a '.' representing the field itself, and will always map to an empty set, or a string representing a sub-field or item. The string will follow one of these four formats: 'f:\\u003cname\\u003e', where \\u003cname\\u003e is the name of a field in a struct, or key in a map 'v:\\u003cvalue\\u003e', where \\u003cvalue\\u003e is the exact json formatted value of a list item 'i:\\u003cindex\\u003e', where \\u003cindex\\u003e is position of a item in a list 'k:\\u003ckeys\\u003e', where \\u003ckeys\\u003e is a map of  a list item's key fields to their unique values If a key maps to an empty Fields value, the field that key represents is part of the set.\\n\\nThe exact format is defined in sigs.k8s.io/structured-merge-diff\",\"type\":\"object\"}],\"description\":\"FieldsV1 holds the first JSON version format as described in the \\\"FieldsV1\\\" type.\"},\"manager\":{\"description\":\"Manager is an identifier of the workflow managing these fields.\",\"type\":\"string\"},\"operation\":{\"description\":\"Operation is the type of operation which lead to this ManagedFieldsEntry being created. The only valid values for this field are 'Apply' and 'Update'.\",\"type\":\"string\"},\"subresource\":{\"description\":\"Subresource is the name of the subresource used to update that object, or empty string if the object was updated through the main resource. The value of this field is used to distinguish between managers, even if they share the same name. For example, a status update will be distinct from a regular update using the same manager name. Note that the APIVersion field is not related to the Subresource field and it always corresponds to the version of the main resource.\",\"type\":\"string\"},\"time\":{\"allOf\":[{\"description\":\"Time is a wrapper around time.Time which supports correct marshaling to YAML and JSON.  Wrappers are provided for many of the factory methods that the time package offers.\",\"format\":\"date-time\",\"type\":\"string\"}],\"description\":\"Time is the timestamp of when the ManagedFields entry was added. The timestamp will also be updated if a field is added, the manager changes any of the owned fields value or removes a field. The timestamp does not update when a field is removed from the entry because another manager took it over.\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"name\":{\"description\":\"Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names\",\"type\":\"string\"},\"namespace\":{\"description\":\"Namespace defines the space within which each name must be unique. An empty namespace is equivalent to the \\\"default\\\" namespace, but \\\"default\\\" is the canonical representation. Not all objects are required to be scoped to a namespace - the value of this field for those objects will be empty.\\n\\nMust be a DNS_LABEL. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces\",\"type\":\"string\"},\"ownerReferences\":{\"description\":\"List of objects depended by this object. If ALL objects in the list have been deleted, this object will be garbage collected. If this object is managed by a controller, then an entry in this list will point to this controller, with the controller field set to true. There cannot be more than one managing controller.\",\"items\":{\"allOf\":[{\"description\":\"OwnerReference contains enough information to let you identify an owning object. An owning object must be in the same namespace as the dependent, or be cluster-scoped, so there is no namespace field.\",\"properties\":{\"apiVersion\":{\"default\":\"\",\"description\":\"API version of the referent.\",\"type\":\"string\"},\"blockOwnerDeletion\":{\"description\":\"If true, AND if the owner has the \\\"foregroundDeletion\\\" finalizer, then the owner cannot be deleted from the key-value store until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion for how the garbage collector interacts with this field and enforces the foreground deletion. Defaults to false. To set this field, a user needs \\\"delete\\\" permission of the owner, otherwise 422 (Unprocessable Entity) will be returned.\",\"type\":\"boolean\"},\"controller\":{\"description\":\"If true, this reference points to the managing controller.\",\"type\":\"boolean\"},\"kind\":{\"default\":\"\",\"description\":\"Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names\",\"type\":\"string\"},\"uid\":{\"default\":\"\",\"description\":\"UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids\",\"type\":\"string\"}},\"required\":[\"apiVersion\",\"kind\",\"name\",\"uid\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"uid\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"uid\",\"x-kubernetes-patch-strategy\":\"merge\"},\"resourceVersion\":{\"description\":\"An opaque value that represents the internal version of this object that can be used by clients to determine when objects have changed. May be used for optimistic concurrency, change detection, and the watch operation on a resource or set of resources. Clients must treat these values as opaque and passed unmodified back to the server. They may only be valid for a particular resource or set of resources.\\n\\nPopulated by the system. Read-only. Value must be treated as opaque by clients and . More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency\",\"type\":\"string\"},\"selfLink\":{\"description\":\"Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.\",\"type\":\"string\"},\"uid\":{\"description\":\"UID is the unique in time and space value for this object. It is typically generated by the server on successful creation of a resource and is not allowed to change on PUT operations.\\n\\nPopulated by the system. Read-only. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"May contain labels and annotations that will be copied into the PVC when creating it. No other fields are allowed and will be rejected during validation.\"},\"spec\":{\"allOf\":[{\"description\":\"PersistentVolumeClaimSpec describes the common attributes of storage devices and allows a Source for provider-specific attributes\",\"properties\":{\"accessModes\":{\"description\":\"accessModes contains the desired access modes the volume should have. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"dataSource\":{\"allOf\":[{\"description\":\"TypedLocalObjectReference contains enough information to let you locate the typed referenced object inside the same namespace.\",\"properties\":{\"apiGroup\":{\"description\":\"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\"type\":\"string\"},\"kind\":{\"default\":\"\",\"description\":\"Kind is the type of resource being referenced\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name is the name of resource being referenced\",\"type\":\"string\"}},\"required\":[\"kind\",\"name\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"dataSource field can be used to specify either: * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot) * An existing PVC (PersistentVolumeClaim) If the provisioner or an external controller can support the specified data source, it will create a new volume based on the contents of the specified data source. When the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef, and dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified. If the namespace is specified, then dataSourceRef will not be copied to dataSource.\"},\"dataSourceRef\":{\"allOf\":[{\"description\":\"TypedObjectReference contains enough information to let you locate the typed referenced object\",\"properties\":{\"apiGroup\":{\"description\":\"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\"type\":\"string\"},\"kind\":{\"default\":\"\",\"description\":\"Kind is the type of resource being referenced\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name is the name of resource being referenced\",\"type\":\"string\"},\"namespace\":{\"description\":\"Namespace is the namespace of resource being referenced Note that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details. (Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\"type\":\"string\"}},\"required\":[\"kind\",\"name\"],\"type\":\"object\"}],\"description\":\"dataSourceRef specifies the object from which to populate the volume with data, if a non-empty volume is desired. This may be any object from a non-empty API group (non core object) or a PersistentVolumeClaim object. When this field is specified, volume binding will only succeed if the type of the specified object matches some installed volume populator or dynamic provisioner. This field will replace the functionality of the dataSource field and as such if both fields are non-empty, they must have the same value. For backwards compatibility, when namespace isn't specified in dataSourceRef, both fields (dataSource and dataSourceRef) will be set to the same value automatically if one of them is empty and the other is non-empty. When namespace is specified in dataSourceRef, dataSource isn't set to the same value and must be empty. There are three important differences between dataSource and dataSourceRef: * While dataSource only allows two specific types of objects, dataSourceRef\\n  allows any non-core object, as well as PersistentVolumeClaim objects.\\n* While dataSource ignores disallowed values (dropping them), dataSourceRef\\n  preserves all values, and generates an error if a disallowed value is\\n  specified.\\n* While dataSource only allows local objects, dataSourceRef allows objects\\n  in any namespaces.\\n(Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled. (Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\"},\"resources\":{\"allOf\":[{\"description\":\"VolumeResourceRequirements describes the storage resource requirements for a volume.\",\"properties\":{\"limits\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"},\"requests\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. Requests cannot exceed Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\"type\":\"object\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"resources represents the minimum resources the volume should have. If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements that are lower than previous value but must still be higher than capacity recorded in the status field of the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\"},\"selector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"selector is a label query over volumes to consider for binding.\"},\"storageClassName\":{\"description\":\"storageClassName is the name of the StorageClass required by the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\"type\":\"string\"},\"volumeAttributesClassName\":{\"description\":\"volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim. If specified, the CSI driver will create or update the volume with the attributes defined in the corresponding VolumeAttributesClass. This has a different purpose than storageClassName, it can be changed after the claim is created. An empty string value means that no VolumeAttributesClass will be applied to the claim but it's not allowed to reset this field to empty string once it is set. If unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass will be set by the persistentvolume controller if it exists. If the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be set to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource exists. More info: https://kubernetes.io/docs/concepts/storage/volume-attributes-classes/ (Beta) Using this field requires the VolumeAttributesClass feature gate to be enabled (off by default).\",\"type\":\"string\"},\"volumeMode\":{\"description\":\"volumeMode defines what type of volume is required by the claim. Value of Filesystem is implied when not included in claim spec.\",\"type\":\"string\"},\"volumeName\":{\"description\":\"volumeName is the binding reference to the PersistentVolume backing this claim.\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"The specification for the PersistentVolumeClaim. The entire content is copied unchanged into the PVC that gets created from this template. The same fields as in a PersistentVolumeClaim are also valid here.\"}},\"required\":[\"spec\"],\"type\":\"object\"}],\"description\":\"Will be used to create a stand-alone PVC to provision the volume. The pod in which this EphemeralVolumeSource is embedded will be the owner of the PVC, i.e. the PVC will be deleted together with the pod.  The name of the PVC will be `\\u003cpod name\\u003e-\\u003cvolume name\\u003e` where `\\u003cvolume name\\u003e` is the name from the `PodSpec.Volumes` array entry. Pod validation will reject the pod if the concatenated name is not valid for a PVC (for example, too long).\\n\\nAn existing PVC with that name that is not owned by the pod will *not* be used for the pod to avoid using an unrelated volume by mistake. Starting the pod is then blocked until the unrelated PVC is removed. If such a pre-created PVC is meant to be used by the pod, the PVC has to updated with an owner reference to the pod once the pod exists. Normally this should not be necessary, but it may be useful when manually reconstructing a broken cluster.\\n\\nThis field is read-only and no changes will be made by Kubernetes to the PVC after it has been created.\\n\\nRequired, must not be nil.\"}},\"type\":\"object\"}],\"description\":\"ephemeral represents a volume that is handled by a cluster storage driver. The volume's lifecycle is tied to the pod that defines it - it will be created before the pod starts, and deleted when the pod is removed.\\n\\nUse this if: a) the volume is only needed while the pod runs, b) features of normal volumes like restoring from snapshot or capacity\\n   tracking are needed,\\nc) the storage driver is specified through a storage class, and d) the storage driver supports dynamic volume provisioning through\\n   a PersistentVolumeClaim (see EphemeralVolumeSource for more\\n   information on the connection between this volume type\\n   and PersistentVolumeClaim).\\n\\nUse PersistentVolumeClaim or one of the vendor-specific APIs for volumes that persist for longer than the lifecycle of an individual pod.\\n\\nUse CSI for light-weight local ephemeral volumes if the CSI driver is meant to be used that way - see the documentation of the driver for more information.\\n\\nA pod can use both types of ephemeral volumes and persistent volumes at the same time.\"},\"fc\":{\"allOf\":[{\"description\":\"Represents a Fibre Channel volume. Fibre Channel volumes can only be mounted as read/write once. Fibre Channel volumes support ownership management and SELinux relabeling.\",\"properties\":{\"fsType\":{\"description\":\"fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\"type\":\"string\"},\"lun\":{\"description\":\"lun is Optional: FC target lun number\",\"format\":\"int32\",\"type\":\"integer\"},\"readOnly\":{\"description\":\"readOnly is Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\"type\":\"boolean\"},\"targetWWNs\":{\"description\":\"targetWWNs is Optional: FC target worldwide names (WWNs)\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"wwids\":{\"description\":\"wwids Optional: FC volume world wide identifiers (wwids) Either wwids or combination of targetWWNs and lun must be set, but not both simultaneously.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"fc represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.\"},\"flexVolume\":{\"allOf\":[{\"description\":\"FlexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin.\",\"properties\":{\"driver\":{\"default\":\"\",\"description\":\"driver is the name of the driver to use for this volume.\",\"type\":\"string\"},\"fsType\":{\"description\":\"fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". The default filesystem depends on FlexVolume script.\",\"type\":\"string\"},\"options\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"options is Optional: this field holds extra command options if any.\",\"type\":\"object\"},\"readOnly\":{\"description\":\"readOnly is Optional: defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\"type\":\"boolean\"},\"secretRef\":{\"allOf\":[{\"description\":\"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"secretRef is Optional: secretRef is reference to the secret object containing sensitive information to pass to the plugin scripts. This may be empty if no secret object is specified. If the secret object contains more than one secret, all secrets are passed to the plugin scripts.\"}},\"required\":[\"driver\"],\"type\":\"object\"}],\"description\":\"flexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin. Deprecated: FlexVolume is deprecated. Consider using a CSIDriver instead.\"},\"flocker\":{\"allOf\":[{\"description\":\"Represents a Flocker volume mounted by the Flocker agent. One and only one of datasetName and datasetUUID should be set. Flocker volumes do not support ownership management or SELinux relabeling.\",\"properties\":{\"datasetName\":{\"description\":\"datasetName is Name of the dataset stored as metadata -\\u003e name on the dataset for Flocker should be considered as deprecated\",\"type\":\"string\"},\"datasetUUID\":{\"description\":\"datasetUUID is the UUID of the dataset. This is unique identifier of a Flocker dataset\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running. Deprecated: Flocker is deprecated and the in-tree flocker type is no longer supported.\"},\"gcePersistentDisk\":{\"allOf\":[{\"description\":\"Represents a Persistent Disk resource in Google Compute Engine.\\n\\nA GCE PD must exist before mounting to a container. The disk must also be in the same GCE project and zone as the kubelet. A GCE PD can only be mounted as read/write once or read-only many times. GCE PDs support ownership management and SELinux relabeling.\",\"properties\":{\"fsType\":{\"description\":\"fsType is filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\"type\":\"string\"},\"partition\":{\"description\":\"partition is the partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \\\"1\\\". Similarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty). More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\"format\":\"int32\",\"type\":\"integer\"},\"pdName\":{\"default\":\"\",\"description\":\"pdName is unique name of the PD resource in GCE. Used to identify the disk in GCE. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\"type\":\"boolean\"}},\"required\":[\"pdName\"],\"type\":\"object\"}],\"description\":\"gcePersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod. Deprecated: GCEPersistentDisk is deprecated. All operations for the in-tree gcePersistentDisk type are redirected to the pd.csi.storage.gke.io CSI driver. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\"},\"gitRepo\":{\"allOf\":[{\"description\":\"Represents a volume that is populated with the contents of a git repository. Git repo volumes do not support ownership management. Git repo volumes support SELinux relabeling.\\n\\nDEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir into the Pod's container.\",\"properties\":{\"directory\":{\"description\":\"directory is the target directory name. Must not contain or start with '..'.  If '.' is supplied, the volume directory will be the git repository.  Otherwise, if specified, the volume will contain the git repository in the subdirectory with the given name.\",\"type\":\"string\"},\"repository\":{\"default\":\"\",\"description\":\"repository is the URL\",\"type\":\"string\"},\"revision\":{\"description\":\"revision is the commit hash for the specified revision.\",\"type\":\"string\"}},\"required\":[\"repository\"],\"type\":\"object\"}],\"description\":\"gitRepo represents a git repository at a particular revision. Deprecated: GitRepo is deprecated. To provision a container with a git repo, mount an EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir into the Pod's container.\"},\"glusterfs\":{\"allOf\":[{\"description\":\"Represents a Glusterfs mount that lasts the lifetime of a pod. Glusterfs volumes do not support ownership management or SELinux relabeling.\",\"properties\":{\"endpoints\":{\"default\":\"\",\"description\":\"endpoints is the endpoint name that details Glusterfs topology.\",\"type\":\"string\"},\"path\":{\"default\":\"\",\"description\":\"path is the Glusterfs volume path. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly here will force the Glusterfs volume to be mounted with read-only permissions. Defaults to false. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\"type\":\"boolean\"}},\"required\":[\"endpoints\",\"path\"],\"type\":\"object\"}],\"description\":\"glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime. Deprecated: Glusterfs is deprecated and the in-tree glusterfs type is no longer supported.\"},\"hostPath\":{\"allOf\":[{\"description\":\"Represents a host path mapped into a pod. Host path volumes do not support ownership management or SELinux relabeling.\",\"properties\":{\"path\":{\"default\":\"\",\"description\":\"path of the directory on the host. If the path is a symlink, it will follow the link to the real path. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\"type\":\"string\"},\"type\":{\"description\":\"type for HostPath Volume Defaults to \\\"\\\" More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\"type\":\"string\"}},\"required\":[\"path\"],\"type\":\"object\"}],\"description\":\"hostPath represents a pre-existing file or directory on the host machine that is directly exposed to the container. This is generally used for system agents or other privileged things that are allowed to see the host machine. Most containers will NOT need this. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\"},\"image\":{\"allOf\":[{\"description\":\"ImageVolumeSource represents a image volume resource.\",\"properties\":{\"pullPolicy\":{\"description\":\"Policy for pulling OCI objects. Possible values are: Always: the kubelet always attempts to pull the reference. Container creation will fail If the pull fails. Never: the kubelet never pulls the reference and only uses a local image or artifact. Container creation will fail if the reference isn't present. IfNotPresent: the kubelet pulls if the reference isn't already present on disk. Container creation will fail if the reference isn't present and the pull fails. Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.\",\"type\":\"string\"},\"reference\":{\"description\":\"Required: Image or artifact reference to be used. Behaves in the same way as pod.spec.containers[*].image. Pull secrets will be assembled in the same way as for the container image by looking up node credentials, SA image pull secrets, and pod spec image pull secrets. More info: https://kubernetes.io/docs/concepts/containers/images This field is optional to allow higher level config management to default or override container images in workload controllers like Deployments and StatefulSets.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"image represents an OCI object (a container image or artifact) pulled and mounted on the kubelet's host machine. The volume is resolved at pod startup depending on which PullPolicy value is provided:\\n\\n- Always: the kubelet always attempts to pull the reference. Container creation will fail If the pull fails. - Never: the kubelet never pulls the reference and only uses a local image or artifact. Container creation will fail if the reference isn't present. - IfNotPresent: the kubelet pulls if the reference isn't already present on disk. Container creation will fail if the reference isn't present and the pull fails.\\n\\nThe volume gets re-resolved if the pod gets deleted and recreated, which means that new remote content will become available on pod recreation. A failure to resolve or pull the image during pod startup will block containers from starting and may add significant latency. Failures will be retried using normal volume backoff and will be reported on the pod reason and message. The types of objects that may be mounted by this volume are defined by the container runtime implementation on a host machine and at minimum must include all valid types supported by the container image field. The OCI object gets mounted in a single directory (spec.containers[*].volumeMounts.mountPath) by merging the manifest layers in the same way as for container images. The volume will be mounted read-only (ro) and non-executable files (noexec). Sub path mounts for containers are not supported (spec.containers[*].volumeMounts.subpath) before 1.33. The field spec.securityContext.fsGroupChangePolicy has no effect on this volume type.\"},\"iscsi\":{\"allOf\":[{\"description\":\"Represents an ISCSI disk. ISCSI volumes can only be mounted as read/write once. ISCSI volumes support ownership management and SELinux relabeling.\",\"properties\":{\"chapAuthDiscovery\":{\"description\":\"chapAuthDiscovery defines whether support iSCSI Discovery CHAP authentication\",\"type\":\"boolean\"},\"chapAuthSession\":{\"description\":\"chapAuthSession defines whether support iSCSI Session CHAP authentication\",\"type\":\"boolean\"},\"fsType\":{\"description\":\"fsType is the filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi\",\"type\":\"string\"},\"initiatorName\":{\"description\":\"initiatorName is the custom iSCSI Initiator Name. If initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface \\u003ctarget portal\\u003e:\\u003cvolume name\\u003e will be created for the connection.\",\"type\":\"string\"},\"iqn\":{\"default\":\"\",\"description\":\"iqn is the target iSCSI Qualified Name.\",\"type\":\"string\"},\"iscsiInterface\":{\"default\":\"default\",\"description\":\"iscsiInterface is the interface Name that uses an iSCSI transport. Defaults to 'default' (tcp).\",\"type\":\"string\"},\"lun\":{\"default\":0,\"description\":\"lun represents iSCSI Target Lun number.\",\"format\":\"int32\",\"type\":\"integer\"},\"portals\":{\"description\":\"portals is the iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"readOnly\":{\"description\":\"readOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false.\",\"type\":\"boolean\"},\"secretRef\":{\"allOf\":[{\"description\":\"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"secretRef is the CHAP Secret for iSCSI target and initiator authentication\"},\"targetPortal\":{\"default\":\"\",\"description\":\"targetPortal is iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).\",\"type\":\"string\"}},\"required\":[\"targetPortal\",\"iqn\",\"lun\"],\"type\":\"object\"}],\"description\":\"iscsi represents an ISCSI Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes/#iscsi\"},\"name\":{\"default\":\"\",\"description\":\"name of the volume. Must be a DNS_LABEL and unique within the pod. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"nfs\":{\"allOf\":[{\"description\":\"Represents an NFS mount that lasts the lifetime of a pod. NFS volumes do not support ownership management or SELinux relabeling.\",\"properties\":{\"path\":{\"default\":\"\",\"description\":\"path that is exported by the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly here will force the NFS export to be mounted with read-only permissions. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\"type\":\"boolean\"},\"server\":{\"default\":\"\",\"description\":\"server is the hostname or IP address of the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\"type\":\"string\"}},\"required\":[\"server\",\"path\"],\"type\":\"object\"}],\"description\":\"nfs represents an NFS mount on the host that shares a pod's lifetime More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\"},\"persistentVolumeClaim\":{\"allOf\":[{\"description\":\"PersistentVolumeClaimVolumeSource references the user's PVC in the same namespace. This volume finds the bound PV and mounts that volume for the pod. A PersistentVolumeClaimVolumeSource is, essentially, a wrapper around another type of volume that is owned by someone else (the system).\",\"properties\":{\"claimName\":{\"default\":\"\",\"description\":\"claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly Will force the ReadOnly setting in VolumeMounts. Default false.\",\"type\":\"boolean\"}},\"required\":[\"claimName\"],\"type\":\"object\"}],\"description\":\"persistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\"},\"photonPersistentDisk\":{\"allOf\":[{\"description\":\"Represents a Photon Controller persistent disk resource.\",\"properties\":{\"fsType\":{\"description\":\"fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\"type\":\"string\"},\"pdID\":{\"default\":\"\",\"description\":\"pdID is the ID that identifies Photon Controller persistent disk\",\"type\":\"string\"}},\"required\":[\"pdID\"],\"type\":\"object\"}],\"description\":\"photonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine. Deprecated: PhotonPersistentDisk is deprecated and the in-tree photonPersistentDisk type is no longer supported.\"},\"portworxVolume\":{\"allOf\":[{\"description\":\"PortworxVolumeSource represents a Portworx volume resource.\",\"properties\":{\"fsType\":{\"description\":\"fSType represents the filesystem type to mount Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\"type\":\"boolean\"},\"volumeID\":{\"default\":\"\",\"description\":\"volumeID uniquely identifies a Portworx volume\",\"type\":\"string\"}},\"required\":[\"volumeID\"],\"type\":\"object\"}],\"description\":\"portworxVolume represents a portworx volume attached and mounted on kubelets host machine. Deprecated: PortworxVolume is deprecated. All operations for the in-tree portworxVolume type are redirected to the pxd.portworx.com CSI driver when the CSIMigrationPortworx feature-gate is on.\"},\"projected\":{\"allOf\":[{\"description\":\"Represents a projected volume source\",\"properties\":{\"defaultMode\":{\"description\":\"defaultMode are the mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"sources\":{\"description\":\"sources is the list of volume projections. Each entry in this list handles one source.\",\"items\":{\"allOf\":[{\"description\":\"Projection that may be projected along with other supported volume types. Exactly one of these fields must be set.\",\"properties\":{\"clusterTrustBundle\":{\"allOf\":[{\"description\":\"ClusterTrustBundleProjection describes how to select a set of ClusterTrustBundle objects and project their contents into the pod filesystem.\",\"properties\":{\"labelSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Select all ClusterTrustBundles that match this label selector.  Only has effect if signerName is set.  Mutually-exclusive with name.  If unset, interpreted as \\\"match nothing\\\".  If set but empty, interpreted as \\\"match everything\\\".\"},\"name\":{\"description\":\"Select a single ClusterTrustBundle by object name.  Mutually-exclusive with signerName and labelSelector.\",\"type\":\"string\"},\"optional\":{\"description\":\"If true, don't block pod startup if the referenced ClusterTrustBundle(s) aren't available.  If using name, then the named ClusterTrustBundle is allowed not to exist.  If using signerName, then the combination of signerName and labelSelector is allowed to match zero ClusterTrustBundles.\",\"type\":\"boolean\"},\"path\":{\"default\":\"\",\"description\":\"Relative path from the volume root to write the bundle.\",\"type\":\"string\"},\"signerName\":{\"description\":\"Select all ClusterTrustBundles that match this signer name. Mutually-exclusive with name.  The contents of all selected ClusterTrustBundles will be unified and deduplicated.\",\"type\":\"string\"}},\"required\":[\"path\"],\"type\":\"object\"}],\"description\":\"ClusterTrustBundle allows a pod to access the `.spec.trustBundle` field of ClusterTrustBundle objects in an auto-updating file.\\n\\nAlpha, gated by the ClusterTrustBundleProjection feature gate.\\n\\nClusterTrustBundle objects can either be selected by name, or by the combination of signer name and a label selector.\\n\\nKubelet performs aggressive normalization of the PEM contents written into the pod filesystem.  Esoteric PEM features such as inter-block comments and block headers are stripped.  Certificates are deduplicated. The ordering of certificates within the file is arbitrary, and Kubelet may change the order over time.\"},\"configMap\":{\"allOf\":[{\"description\":\"Adapts a ConfigMap into a projected volume.\\n\\nThe contents of the target ConfigMap's Data field will be presented in a projected volume as files using the keys in the Data field as the file names, unless the items element is populated with specific mappings of keys to paths. Note that this is identical to a configmap volume source without the default mode.\",\"properties\":{\"items\":{\"description\":\"items if unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\"items\":{\"allOf\":[{\"description\":\"Maps a string key to a path within a volume.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the key to project.\",\"type\":\"string\"},\"mode\":{\"description\":\"mode is Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"path\":{\"default\":\"\",\"description\":\"path is the relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\"type\":\"string\"}},\"required\":[\"key\",\"path\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"optional specify whether the ConfigMap or its keys must be defined\",\"type\":\"boolean\"}},\"type\":\"object\"}],\"description\":\"configMap information about the configMap data to project\"},\"downwardAPI\":{\"allOf\":[{\"description\":\"Represents downward API info for projecting into a projected volume. Note that this is identical to a downwardAPI volume source without the default mode.\",\"properties\":{\"items\":{\"description\":\"Items is a list of DownwardAPIVolume file\",\"items\":{\"allOf\":[{\"description\":\"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\"properties\":{\"fieldRef\":{\"allOf\":[{\"description\":\"ObjectFieldSelector selects an APIVersioned field of an object.\",\"properties\":{\"apiVersion\":{\"description\":\"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\"type\":\"string\"},\"fieldPath\":{\"default\":\"\",\"description\":\"Path of the field to select in the specified API version.\",\"type\":\"string\"}},\"required\":[\"fieldPath\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Required: Selects a field of the pod: only annotations, labels, name, namespace and uid are supported.\"},\"mode\":{\"description\":\"Optional: mode bits used to set permissions on this file, must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"path\":{\"default\":\"\",\"description\":\"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\"type\":\"string\"},\"resourceFieldRef\":{\"allOf\":[{\"description\":\"ResourceFieldSelector represents container resources (cpu, memory) and their output format\",\"properties\":{\"containerName\":{\"description\":\"Container name: required for volumes, optional for env vars\",\"type\":\"string\"},\"divisor\":{\"allOf\":[{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]}],\"description\":\"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\"},\"resource\":{\"default\":\"\",\"description\":\"Required: resource to select\",\"type\":\"string\"}},\"required\":[\"resource\"],\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\"}},\"required\":[\"path\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"downwardAPI information about the downwardAPI data to project\"},\"secret\":{\"allOf\":[{\"description\":\"Adapts a secret into a projected volume.\\n\\nThe contents of the target Secret's Data field will be presented in a projected volume as files using the keys in the Data field as the file names. Note that this is identical to a secret volume source without the default mode.\",\"properties\":{\"items\":{\"description\":\"items if unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\"items\":{\"allOf\":[{\"description\":\"Maps a string key to a path within a volume.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the key to project.\",\"type\":\"string\"},\"mode\":{\"description\":\"mode is Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"path\":{\"default\":\"\",\"description\":\"path is the relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\"type\":\"string\"}},\"required\":[\"key\",\"path\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"},\"optional\":{\"description\":\"optional field specify whether the Secret or its key must be defined\",\"type\":\"boolean\"}},\"type\":\"object\"}],\"description\":\"secret information about the secret data to project\"},\"serviceAccountToken\":{\"allOf\":[{\"description\":\"ServiceAccountTokenProjection represents a projected service account token volume. This projection can be used to insert a service account token into the pods runtime filesystem for use against APIs (Kubernetes API Server or otherwise).\",\"properties\":{\"audience\":{\"description\":\"audience is the intended audience of the token. A recipient of a token must identify itself with an identifier specified in the audience of the token, and otherwise should reject the token. The audience defaults to the identifier of the apiserver.\",\"type\":\"string\"},\"expirationSeconds\":{\"description\":\"expirationSeconds is the requested duration of validity of the service account token. As the token approaches expiration, the kubelet volume plugin will proactively rotate the service account token. The kubelet will start trying to rotate the token if the token is older than 80 percent of its time to live or if the token is older than 24 hours.Defaults to 1 hour and must be at least 10 minutes.\",\"format\":\"int64\",\"type\":\"integer\"},\"path\":{\"default\":\"\",\"description\":\"path is the path relative to the mount point of the file to project the token into.\",\"type\":\"string\"}},\"required\":[\"path\"],\"type\":\"object\"}],\"description\":\"serviceAccountToken is information about the serviceAccountToken data to project\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"projected items for all in one resources secrets, configmaps, and downward API\"},\"quobyte\":{\"allOf\":[{\"description\":\"Represents a Quobyte mount that lasts the lifetime of a pod. Quobyte volumes do not support ownership management or SELinux relabeling.\",\"properties\":{\"group\":{\"description\":\"group to map volume access to Default is no group\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly here will force the Quobyte volume to be mounted with read-only permissions. Defaults to false.\",\"type\":\"boolean\"},\"registry\":{\"default\":\"\",\"description\":\"registry represents a single or multiple Quobyte Registry services specified as a string as host:port pair (multiple entries are separated with commas) which acts as the central registry for volumes\",\"type\":\"string\"},\"tenant\":{\"description\":\"tenant owning the given Quobyte volume in the Backend Used with dynamically provisioned Quobyte volumes, value is set by the plugin\",\"type\":\"string\"},\"user\":{\"description\":\"user to map volume access to Defaults to serivceaccount user\",\"type\":\"string\"},\"volume\":{\"default\":\"\",\"description\":\"volume is a string that references an already created Quobyte volume by name.\",\"type\":\"string\"}},\"required\":[\"registry\",\"volume\"],\"type\":\"object\"}],\"description\":\"quobyte represents a Quobyte mount on the host that shares a pod's lifetime. Deprecated: Quobyte is deprecated and the in-tree quobyte type is no longer supported.\"},\"rbd\":{\"allOf\":[{\"description\":\"Represents a Rados Block Device mount that lasts the lifetime of a pod. RBD volumes support ownership management and SELinux relabeling.\",\"properties\":{\"fsType\":{\"description\":\"fsType is the filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#rbd\",\"type\":\"string\"},\"image\":{\"default\":\"\",\"description\":\"image is the rados image name. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\"type\":\"string\"},\"keyring\":{\"default\":\"/etc/ceph/keyring\",\"description\":\"keyring is the path to key ring for RBDUser. Default is /etc/ceph/keyring. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\"type\":\"string\"},\"monitors\":{\"description\":\"monitors is a collection of Ceph monitors. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"pool\":{\"default\":\"rbd\",\"description\":\"pool is the rados pool name. Default is rbd. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\"type\":\"boolean\"},\"secretRef\":{\"allOf\":[{\"description\":\"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"secretRef is name of the authentication secret for RBDUser. If provided overrides keyring. Default is nil. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\"},\"user\":{\"default\":\"admin\",\"description\":\"user is the rados user name. Default is admin. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\"type\":\"string\"}},\"required\":[\"monitors\",\"image\"],\"type\":\"object\"}],\"description\":\"rbd represents a Rados Block Device mount on the host that shares a pod's lifetime. Deprecated: RBD is deprecated and the in-tree rbd type is no longer supported.\"},\"scaleIO\":{\"allOf\":[{\"description\":\"ScaleIOVolumeSource represents a persistent ScaleIO volume\",\"properties\":{\"fsType\":{\"default\":\"xfs\",\"description\":\"fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Default is \\\"xfs\\\".\",\"type\":\"string\"},\"gateway\":{\"default\":\"\",\"description\":\"gateway is the host address of the ScaleIO API Gateway.\",\"type\":\"string\"},\"protectionDomain\":{\"description\":\"protectionDomain is the name of the ScaleIO Protection Domain for the configured storage.\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\"type\":\"boolean\"},\"secretRef\":{\"allOf\":[{\"description\":\"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"secretRef references to the secret for ScaleIO user and other sensitive information. If this is not provided, Login operation will fail.\"},\"sslEnabled\":{\"description\":\"sslEnabled Flag enable/disable SSL communication with Gateway, default false\",\"type\":\"boolean\"},\"storageMode\":{\"default\":\"ThinProvisioned\",\"description\":\"storageMode indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned. Default is ThinProvisioned.\",\"type\":\"string\"},\"storagePool\":{\"description\":\"storagePool is the ScaleIO Storage Pool associated with the protection domain.\",\"type\":\"string\"},\"system\":{\"default\":\"\",\"description\":\"system is the name of the storage system as configured in ScaleIO.\",\"type\":\"string\"},\"volumeName\":{\"description\":\"volumeName is the name of a volume already created in the ScaleIO system that is associated with this volume source.\",\"type\":\"string\"}},\"required\":[\"gateway\",\"system\",\"secretRef\"],\"type\":\"object\"}],\"description\":\"scaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes. Deprecated: ScaleIO is deprecated and the in-tree scaleIO type is no longer supported.\"},\"secret\":{\"allOf\":[{\"description\":\"Adapts a Secret into a volume.\\n\\nThe contents of the target Secret's Data field will be presented in a volume as files using the keys in the Data field as the file names. Secret volumes support ownership management and SELinux relabeling.\",\"properties\":{\"defaultMode\":{\"description\":\"defaultMode is Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"items\":{\"description\":\"items If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\"items\":{\"allOf\":[{\"description\":\"Maps a string key to a path within a volume.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the key to project.\",\"type\":\"string\"},\"mode\":{\"description\":\"mode is Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\"format\":\"int32\",\"type\":\"integer\"},\"path\":{\"default\":\"\",\"description\":\"path is the relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\"type\":\"string\"}},\"required\":[\"key\",\"path\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"optional\":{\"description\":\"optional field specify whether the Secret or its keys must be defined\",\"type\":\"boolean\"},\"secretName\":{\"description\":\"secretName is the name of the secret in the pod's namespace to use. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"secret represents a secret that should populate this volume. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret\"},\"storageos\":{\"allOf\":[{\"description\":\"Represents a StorageOS persistent volume resource.\",\"properties\":{\"fsType\":{\"description\":\"fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\"type\":\"string\"},\"readOnly\":{\"description\":\"readOnly defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\"type\":\"boolean\"},\"secretRef\":{\"allOf\":[{\"description\":\"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"Name of the referent. This field is effectively required, but due to backwards compatibility is allowed to be empty. Instances of this type with an empty value here are almost certainly wrong. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\"type\":\"string\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"secretRef specifies the secret to use for obtaining the StorageOS API credentials.  If not specified, default values will be attempted.\"},\"volumeName\":{\"description\":\"volumeName is the human-readable name of the StorageOS volume.  Volume names are only unique within a namespace.\",\"type\":\"string\"},\"volumeNamespace\":{\"description\":\"volumeNamespace specifies the scope of the volume within StorageOS.  If no namespace is specified then the Pod's namespace will be used.  This allows the Kubernetes name scoping to be mirrored within StorageOS for tighter integration. Set VolumeName to any name to override the default behaviour. Set to \\\"default\\\" if you are not using namespaces within StorageOS. Namespaces that do not pre-exist within StorageOS will be created.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"storageOS represents a StorageOS volume attached and mounted on Kubernetes nodes. Deprecated: StorageOS is deprecated and the in-tree storageos type is no longer supported.\"},\"vsphereVolume\":{\"allOf\":[{\"description\":\"Represents a vSphere volume resource.\",\"properties\":{\"fsType\":{\"description\":\"fsType is filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\"type\":\"string\"},\"storagePolicyID\":{\"description\":\"storagePolicyID is the storage Policy Based Management (SPBM) profile ID associated with the StoragePolicyName.\",\"type\":\"string\"},\"storagePolicyName\":{\"description\":\"storagePolicyName is the storage Policy Based Management (SPBM) profile name.\",\"type\":\"string\"},\"volumePath\":{\"default\":\"\",\"description\":\"volumePath is the path that identifies vSphere volume vmdk\",\"type\":\"string\"}},\"required\":[\"volumePath\"],\"type\":\"object\"}],\"description\":\"vsphereVolume represents a vSphere volume attached and mounted on kubelets host machine. Deprecated: VsphereVolume is deprecated. All operations for the in-tree vsphereVolume type are redirected to the csi.vsphere.vmware.com CSI driver.\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge,retainKeys\"}},\"required\":[\"containers\"],\"type\":\"object\"}],\"default\":{},\"description\":\"Specification of the desired behavior of the pod. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status\"}},\"type\":\"object\"}],\"description\":\"Template is the object that describes the pod that will be created if insufficient replicas are detected. This takes precedence over a TemplateRef. The only allowed template.spec.restartPolicy value is \\\"Always\\\". More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#pod-template\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"Spec defines the specification of the desired behavior of the replication controller. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status\"}},\"type\":\"object\",\"x-kubernetes-group-version-kind\":[{\"group\":\"\",\"kind\":\"ReplicationController\",\"version\":\"v1\"}]}"}}