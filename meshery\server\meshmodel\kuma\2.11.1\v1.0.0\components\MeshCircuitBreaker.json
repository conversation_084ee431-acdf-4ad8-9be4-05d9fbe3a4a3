{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Mesh Circuit Breaker", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kuma", "displayName": "<PERSON><PERSON>", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Cloud Native Network"}, "subCategory": "Service Mesh", "metadata": {"isAnnotation": false, "primaryColor": "#291953", "secondaryColor": "#6942c9", "shape": "circle", "source_uri": "https://github.com/kumahq/charts/releases/download/kuma-2.11.1/kuma-2.11.1.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1\" data-name=\"Layer 1\" viewBox=\"0 0 1173.18 1173.18\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#291953;}.cls-2{fill:none;}</style></defs><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_2\" data-name=\"Layer 2\"><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"></path><rect xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"></rect></g></g></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1\" data-name=\"Layer 1\" viewBox=\"0 0 1173.18 1173.18\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff;}.cls-2{fill:none;}</style></defs><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_2\" data-name=\"Layer 2\"><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"></path><rect xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"></rect></g></g></svg>"}, "model": {"version": "2.11.1"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#291953", "secondaryColor": "#6942c9", "shape": "circle", "svgColor": "<svg id=\"Layer_1\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1173.18 1173.18\"><defs><style>.cls-1{fill:#291953;}.cls-2{fill:none;}</style></defs><g id=\"Layer_2\" data-name=\"Layer 2\"><g id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"/><path class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"/><rect class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"/></g></g></svg>", "svgComplete": "", "svgWhite": "<svg id=\"Layer_1\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1173.18 1173.18\" width='1173.18' height='1173.18'><defs><style>.cls-1{fill:#fff;}.cls-2{fill:none;}</style></defs><g id=\"Layer_2\" data-name=\"Layer 2\"><g id=\"Layer_1-2\" data-name=\"Layer 1-2\"><path class=\"cls-1\" d=\"M887.62,422.54a6.21,6.21,0,0,1,1-5.9c24.85-31.37,47.4-67.46,47.4-95.14C936,260,900.91,210,824.51,210c-37.85,0-65.61,12.3-83.86,32.11a6.39,6.39,0,0,1-6.68,1.8,570.26,570.26,0,0,0-89.24-21.12,6.24,6.24,0,0,0-7,5.35,6.14,6.14,0,0,0,.16,2.45c6.31,23.66,44.2,174,74.71,288.44,18.45,69.26-29.36,137.3-101,137.09H567.19c-72.42,0-116.38-68.28-99.69-136.35,28.17-115,66.76-264.17,73-288.77a6.19,6.19,0,0,0-4.37-7.59,6,6,0,0,0-2.39-.16,486.69,486.69,0,0,0-103.38,23.66,6.37,6.37,0,0,1-7-1.93c-18.24-21.45-46.7-34.86-86.11-34.86-76.4,0-111.5,49.91-111.5,111.5,0,32.28,30.67,76,59.87,110.31a6.36,6.36,0,0,1,1.15,6.07l-49.7,144.35a1.14,1.14,0,0,0,0,.45c-1.31,5-20.51,90.22,125.32,225.79C406,849.23,558,995.66,585.35,1021.83a6.16,6.16,0,0,0,8.49,0c28.09-26.13,185.77-172.48,229.65-213.24,157.55-146.93,120-226.24,120-226.24Z\"/><path class=\"cls-1\" d=\"M619.23,560.53H559.85a17.8,17.8,0,0,1-17.8-17.79v-.09l-7.38-73.11a17.8,17.8,0,0,1,17.8-17.8h73.85a17.8,17.8,0,0,1,17.84,17.76v0l-7.09,73.11a17.8,17.8,0,0,1-17.72,17.88Z\"/><rect class=\"cls-2\" width=\"1173.18\" height=\"1173.18\"/></g></g></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/kumahq/charts/releases/download/kuma-2.11.1/kuma-2.11.1.tgz"}, "configuration": null, "component": {"version": "kuma.io/v1alpha1", "kind": "MeshCircuitBreaker", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Spec is the specification of the Kuma MeshCircuitBreaker resource.\",\n   \"properties\": {\n    \"from\": {\n     \"description\": \"From list makes a match between clients and corresponding configurations\",\n     \"items\": {\n      \"properties\": {\n       \"default\": {\n        \"description\": \"Default is a configuration specific to the group of destinations\\nreferenced in 'targetRef'\",\n        \"properties\": {\n         \"connectionLimits\": {\n          \"description\": \"ConnectionLimits contains configuration of each circuit breaking limit,\\nwhich when exceeded makes the circuit breaker to become open (no traffic\\nis allowed like no current is allowed in the circuits when physical\\ncircuit breaker ir open)\",\n          \"properties\": {\n           \"maxConnectionPools\": {\n            \"description\": \"The maximum number of connection pools per cluster that are concurrently\\nsupported at once. Set this for clusters which create a large number of\\nconnection pools.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxConnections\": {\n            \"description\": \"The maximum number of connections allowed to be made to the upstream\\ncluster.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxPendingRequests\": {\n            \"description\": \"The maximum number of pending requests that are allowed to the upstream\\ncluster. This limit is applied as a connection limit for non-HTTP\\ntraffic.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxRequests\": {\n            \"description\": \"The maximum number of parallel requests that are allowed to be made\\nto the upstream cluster. This limit does not apply to non-HTTP traffic.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxRetries\": {\n            \"description\": \"The maximum number of parallel retries that will be allowed to\\nthe upstream cluster.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"outlierDetection\": {\n          \"description\": \"OutlierDetection contains the configuration of the process of dynamically\\ndetermining whether some number of hosts in an upstream cluster are\\nperforming unlike the others and removing them from the healthy load\\nbalancing set. Performance might be along different axes such as\\nconsecutive failures, temporal success rate, temporal latency, etc.\\nOutlier detection is a form of passive health checking.\",\n          \"properties\": {\n           \"baseEjectionTime\": {\n            \"description\": \"The base time that a host is ejected for. The real time is equal to\\nthe base time multiplied by the number of times the host has been\\nejected.\",\n            \"type\": \"string\"\n           },\n           \"detectors\": {\n            \"description\": \"Contains configuration for supported outlier detectors\",\n            \"properties\": {\n             \"failurePercentage\": {\n              \"description\": \"Failure Percentage based outlier detection functions similarly to success\\nrate detection, in that it relies on success rate data from each host in\\na cluster. However, rather than compare those values to the mean success\\nrate of the cluster as a whole, they are compared to a flat\\nuser-configured threshold. This threshold is configured via the\\noutlierDetection.failurePercentageThreshold field.\\nThe other configuration fields for failure percentage based detection are\\nsimilar to the fields for success rate detection. As with success rate\\ndetection, detection will not be performed for a host if its request\\nvolume over the aggregation interval is less than the\\noutlierDetection.detectors.failurePercentage.requestVolume value.\\nDetection also will not be performed for a cluster if the number of hosts\\nwith the minimum required request volume in an interval is less than the\\noutlierDetection.detectors.failurePercentage.minimumHosts value.\",\n              \"properties\": {\n               \"minimumHosts\": {\n                \"description\": \"The minimum number of hosts in a cluster in order to perform failure\\npercentage-based ejection. If the total number of hosts in the cluster is\\nless than this value, failure percentage-based ejection will not be\\nperformed.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"requestVolume\": {\n                \"description\": \"The minimum number of total requests that must be collected in one\\ninterval (as defined by the interval duration above) to perform failure\\npercentage-based ejection for this host. If the volume is lower than this\\nsetting, failure percentage-based ejection will not be performed for this\\nhost.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"threshold\": {\n                \"description\": \"The failure percentage to use when determining failure percentage-based\\noutlier detection. If the failure percentage of a given host is greater\\nthan or equal to this value, it will be ejected.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"gatewayFailures\": {\n              \"description\": \"In the default mode (outlierDetection.splitExternalLocalOriginErrors is\\nfalse) this detection type takes into account a subset of 5xx errors,\\ncalled \\\"gateway errors\\\" (502, 503 or 504 status code) and local origin\\nfailures, such as timeout, TCP reset etc.\\nIn split mode (outlierDetection.splitExternalLocalOriginErrors is true)\\nthis detection type takes into account a subset of 5xx errors, called\\n\\\"gateway errors\\\" (502, 503 or 504 status code) and is supported only by\\nthe http router.\",\n              \"properties\": {\n               \"consecutive\": {\n                \"description\": \"The number of consecutive gateway failures (502, 503, 504 status codes)\\nbefore a consecutive gateway failure ejection occurs.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"localOriginFailures\": {\n              \"description\": \"This detection type is enabled only when\\noutlierDetection.splitExternalLocalOriginErrors is true and takes into\\naccount only locally originated errors (timeout, reset, etc).\\nIf Envoy repeatedly cannot connect to an upstream host or communication\\nwith the upstream host is repeatedly interrupted, it will be ejected.\\nVarious locally originated problems are detected: timeout, TCP reset,\\nICMP errors, etc. This detection type is supported by http router and\\ntcp proxy.\",\n              \"properties\": {\n               \"consecutive\": {\n                \"description\": \"The number of consecutive locally originated failures before ejection\\noccurs. Parameter takes effect only when splitExternalAndLocalErrors\\nis set to true.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"successRate\": {\n              \"description\": \"Success Rate based outlier detection aggregates success rate data from\\nevery host in a cluster. Then at given intervals ejects hosts based on\\nstatistical outlier detection. Success Rate outlier detection will not be\\ncalculated for a host if its request volume over the aggregation interval\\nis less than the outlierDetection.detectors.successRate.requestVolume\\nvalue.\\nMoreover, detection will not be performed for a cluster if the number of\\nhosts with the minimum required request volume in an interval is less\\nthan the outlierDetection.detectors.successRate.minimumHosts value.\\nIn the default configuration mode\\n(outlierDetection.splitExternalLocalOriginErrors is false) this detection\\ntype takes into account all types of errors: locally and externally\\noriginated.\\nIn split mode (outlierDetection.splitExternalLocalOriginErrors is true),\\nlocally originated errors and externally originated (transaction) errors\\nare counted and treated separately.\",\n              \"properties\": {\n               \"minimumHosts\": {\n                \"description\": \"The number of hosts in a cluster that must have enough request volume to\\ndetect success rate outliers. If the number of hosts is less than this\\nsetting, outlier detection via success rate statistics is not performed\\nfor any host in the cluster.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"requestVolume\": {\n                \"description\": \"The minimum number of total requests that must be collected in one\\ninterval (as defined by the interval duration configured in\\noutlierDetection section) to include this host in success rate based\\noutlier detection. If the volume is lower than this setting, outlier\\ndetection via success rate statistics is not performed for that host.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"standardDeviationFactor\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"This factor is used to determine the ejection threshold for success rate\\noutlier ejection. The ejection threshold is the difference between\\nthe mean success rate, and the product of this factor and the standard\\ndeviation of the mean success rate: mean - (standard_deviation *\\nsuccess_rate_standard_deviation_factor).\\nEither int or decimal represented as string.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"totalFailures\": {\n              \"description\": \"In the default mode (outlierDetection.splitExternalAndLocalErrors is\\nfalse) this detection type takes into account all generated errors:\\nlocally originated and externally originated (transaction) errors.\\nIn split mode (outlierDetection.splitExternalLocalOriginErrors is true)\\nthis detection type takes into account only externally originated\\n(transaction) errors, ignoring locally originated errors.\\nIf an upstream host is an HTTP-server, only 5xx types of error are taken\\ninto account (see Consecutive Gateway Failure for exceptions).\\nProperly formatted responses, even when they carry an operational error\\n(like index not found, access denied) are not taken into account.\",\n              \"properties\": {\n               \"consecutive\": {\n                \"description\": \"The number of consecutive server-side error responses (for HTTP traffic,\\n5xx responses; for TCP traffic, connection failures; for Redis, failure\\nto respond PONG; etc.) before a consecutive total failure ejection\\noccurs.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"disabled\": {\n            \"description\": \"When set to true, outlierDetection configuration won't take any effect\",\n            \"type\": \"boolean\"\n           },\n           \"healthyPanicThreshold\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Allows to configure panic threshold for Envoy cluster. If not specified,\\nthe default is 50%. To disable panic mode, set to 0%.\\nEither int or decimal represented as string.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"interval\": {\n            \"description\": \"The time interval between ejection analysis sweeps. This can result in\\nboth new ejections and hosts being returned to service.\",\n            \"type\": \"string\"\n           },\n           \"maxEjectionPercent\": {\n            \"description\": \"The maximum % of an upstream cluster that can be ejected due to outlier\\ndetection. Defaults to 10% but will eject at least one host regardless of\\nthe value.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"splitExternalAndLocalErrors\": {\n            \"description\": \"Determines whether to distinguish local origin failures from external\\nerrors. If set to true the following configuration parameters are taken\\ninto account: detectors.localOriginFailures.consecutive\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"targetRef\": {\n        \"description\": \"TargetRef is a reference to the resource that represents a group of\\ndestinations.\",\n        \"properties\": {\n         \"kind\": {\n          \"description\": \"Kind of the referenced resource\",\n          \"enum\": [\n           \"Mesh\",\n           \"MeshSubset\",\n           \"MeshGateway\",\n           \"MeshService\",\n           \"MeshExternalService\",\n           \"MeshMultiZoneService\",\n           \"MeshServiceSubset\",\n           \"MeshHTTPRoute\",\n           \"Dataplane\"\n          ],\n          \"type\": \"string\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n          \"type\": \"object\"\n         },\n         \"mesh\": {\n          \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n          \"type\": \"string\"\n         },\n         \"proxyTypes\": {\n          \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n          \"items\": {\n           \"enum\": [\n            \"Sidecar\",\n            \"Gateway\"\n           ],\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"sectionName\": {\n          \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n          \"type\": \"string\"\n         },\n         \"tags\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"kind\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"targetRef\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"rules\": {\n     \"description\": \"Rules defines inbound circuit breaker configurations. Currently limited to\\nselecting all inbound traffic, as L7 matching is not yet implemented.\",\n     \"items\": {\n      \"properties\": {\n       \"default\": {\n        \"description\": \"Default contains configuration of the inbound circuit breaker\",\n        \"properties\": {\n         \"connectionLimits\": {\n          \"description\": \"ConnectionLimits contains configuration of each circuit breaking limit,\\nwhich when exceeded makes the circuit breaker to become open (no traffic\\nis allowed like no current is allowed in the circuits when physical\\ncircuit breaker ir open)\",\n          \"properties\": {\n           \"maxConnectionPools\": {\n            \"description\": \"The maximum number of connection pools per cluster that are concurrently\\nsupported at once. Set this for clusters which create a large number of\\nconnection pools.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxConnections\": {\n            \"description\": \"The maximum number of connections allowed to be made to the upstream\\ncluster.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxPendingRequests\": {\n            \"description\": \"The maximum number of pending requests that are allowed to the upstream\\ncluster. This limit is applied as a connection limit for non-HTTP\\ntraffic.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxRequests\": {\n            \"description\": \"The maximum number of parallel requests that are allowed to be made\\nto the upstream cluster. This limit does not apply to non-HTTP traffic.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxRetries\": {\n            \"description\": \"The maximum number of parallel retries that will be allowed to\\nthe upstream cluster.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"outlierDetection\": {\n          \"description\": \"OutlierDetection contains the configuration of the process of dynamically\\ndetermining whether some number of hosts in an upstream cluster are\\nperforming unlike the others and removing them from the healthy load\\nbalancing set. Performance might be along different axes such as\\nconsecutive failures, temporal success rate, temporal latency, etc.\\nOutlier detection is a form of passive health checking.\",\n          \"properties\": {\n           \"baseEjectionTime\": {\n            \"description\": \"The base time that a host is ejected for. The real time is equal to\\nthe base time multiplied by the number of times the host has been\\nejected.\",\n            \"type\": \"string\"\n           },\n           \"detectors\": {\n            \"description\": \"Contains configuration for supported outlier detectors\",\n            \"properties\": {\n             \"failurePercentage\": {\n              \"description\": \"Failure Percentage based outlier detection functions similarly to success\\nrate detection, in that it relies on success rate data from each host in\\na cluster. However, rather than compare those values to the mean success\\nrate of the cluster as a whole, they are compared to a flat\\nuser-configured threshold. This threshold is configured via the\\noutlierDetection.failurePercentageThreshold field.\\nThe other configuration fields for failure percentage based detection are\\nsimilar to the fields for success rate detection. As with success rate\\ndetection, detection will not be performed for a host if its request\\nvolume over the aggregation interval is less than the\\noutlierDetection.detectors.failurePercentage.requestVolume value.\\nDetection also will not be performed for a cluster if the number of hosts\\nwith the minimum required request volume in an interval is less than the\\noutlierDetection.detectors.failurePercentage.minimumHosts value.\",\n              \"properties\": {\n               \"minimumHosts\": {\n                \"description\": \"The minimum number of hosts in a cluster in order to perform failure\\npercentage-based ejection. If the total number of hosts in the cluster is\\nless than this value, failure percentage-based ejection will not be\\nperformed.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"requestVolume\": {\n                \"description\": \"The minimum number of total requests that must be collected in one\\ninterval (as defined by the interval duration above) to perform failure\\npercentage-based ejection for this host. If the volume is lower than this\\nsetting, failure percentage-based ejection will not be performed for this\\nhost.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"threshold\": {\n                \"description\": \"The failure percentage to use when determining failure percentage-based\\noutlier detection. If the failure percentage of a given host is greater\\nthan or equal to this value, it will be ejected.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"gatewayFailures\": {\n              \"description\": \"In the default mode (outlierDetection.splitExternalLocalOriginErrors is\\nfalse) this detection type takes into account a subset of 5xx errors,\\ncalled \\\"gateway errors\\\" (502, 503 or 504 status code) and local origin\\nfailures, such as timeout, TCP reset etc.\\nIn split mode (outlierDetection.splitExternalLocalOriginErrors is true)\\nthis detection type takes into account a subset of 5xx errors, called\\n\\\"gateway errors\\\" (502, 503 or 504 status code) and is supported only by\\nthe http router.\",\n              \"properties\": {\n               \"consecutive\": {\n                \"description\": \"The number of consecutive gateway failures (502, 503, 504 status codes)\\nbefore a consecutive gateway failure ejection occurs.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"localOriginFailures\": {\n              \"description\": \"This detection type is enabled only when\\noutlierDetection.splitExternalLocalOriginErrors is true and takes into\\naccount only locally originated errors (timeout, reset, etc).\\nIf Envoy repeatedly cannot connect to an upstream host or communication\\nwith the upstream host is repeatedly interrupted, it will be ejected.\\nVarious locally originated problems are detected: timeout, TCP reset,\\nICMP errors, etc. This detection type is supported by http router and\\ntcp proxy.\",\n              \"properties\": {\n               \"consecutive\": {\n                \"description\": \"The number of consecutive locally originated failures before ejection\\noccurs. Parameter takes effect only when splitExternalAndLocalErrors\\nis set to true.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"successRate\": {\n              \"description\": \"Success Rate based outlier detection aggregates success rate data from\\nevery host in a cluster. Then at given intervals ejects hosts based on\\nstatistical outlier detection. Success Rate outlier detection will not be\\ncalculated for a host if its request volume over the aggregation interval\\nis less than the outlierDetection.detectors.successRate.requestVolume\\nvalue.\\nMoreover, detection will not be performed for a cluster if the number of\\nhosts with the minimum required request volume in an interval is less\\nthan the outlierDetection.detectors.successRate.minimumHosts value.\\nIn the default configuration mode\\n(outlierDetection.splitExternalLocalOriginErrors is false) this detection\\ntype takes into account all types of errors: locally and externally\\noriginated.\\nIn split mode (outlierDetection.splitExternalLocalOriginErrors is true),\\nlocally originated errors and externally originated (transaction) errors\\nare counted and treated separately.\",\n              \"properties\": {\n               \"minimumHosts\": {\n                \"description\": \"The number of hosts in a cluster that must have enough request volume to\\ndetect success rate outliers. If the number of hosts is less than this\\nsetting, outlier detection via success rate statistics is not performed\\nfor any host in the cluster.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"requestVolume\": {\n                \"description\": \"The minimum number of total requests that must be collected in one\\ninterval (as defined by the interval duration configured in\\noutlierDetection section) to include this host in success rate based\\noutlier detection. If the volume is lower than this setting, outlier\\ndetection via success rate statistics is not performed for that host.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"standardDeviationFactor\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"This factor is used to determine the ejection threshold for success rate\\noutlier ejection. The ejection threshold is the difference between\\nthe mean success rate, and the product of this factor and the standard\\ndeviation of the mean success rate: mean - (standard_deviation *\\nsuccess_rate_standard_deviation_factor).\\nEither int or decimal represented as string.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"totalFailures\": {\n              \"description\": \"In the default mode (outlierDetection.splitExternalAndLocalErrors is\\nfalse) this detection type takes into account all generated errors:\\nlocally originated and externally originated (transaction) errors.\\nIn split mode (outlierDetection.splitExternalLocalOriginErrors is true)\\nthis detection type takes into account only externally originated\\n(transaction) errors, ignoring locally originated errors.\\nIf an upstream host is an HTTP-server, only 5xx types of error are taken\\ninto account (see Consecutive Gateway Failure for exceptions).\\nProperly formatted responses, even when they carry an operational error\\n(like index not found, access denied) are not taken into account.\",\n              \"properties\": {\n               \"consecutive\": {\n                \"description\": \"The number of consecutive server-side error responses (for HTTP traffic,\\n5xx responses; for TCP traffic, connection failures; for Redis, failure\\nto respond PONG; etc.) before a consecutive total failure ejection\\noccurs.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"disabled\": {\n            \"description\": \"When set to true, outlierDetection configuration won't take any effect\",\n            \"type\": \"boolean\"\n           },\n           \"healthyPanicThreshold\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Allows to configure panic threshold for Envoy cluster. If not specified,\\nthe default is 50%. To disable panic mode, set to 0%.\\nEither int or decimal represented as string.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"interval\": {\n            \"description\": \"The time interval between ejection analysis sweeps. This can result in\\nboth new ejections and hosts being returned to service.\",\n            \"type\": \"string\"\n           },\n           \"maxEjectionPercent\": {\n            \"description\": \"The maximum % of an upstream cluster that can be ejected due to outlier\\ndetection. Defaults to 10% but will eject at least one host regardless of\\nthe value.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"splitExternalAndLocalErrors\": {\n            \"description\": \"Determines whether to distinguish local origin failures from external\\nerrors. If set to true the following configuration parameters are taken\\ninto account: detectors.localOriginFailures.consecutive\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"targetRef\": {\n     \"description\": \"TargetRef is a reference to the resource the policy takes an effect on.\\nThe resource could be either a real store object or virtual resource\\ndefined in place.\",\n     \"properties\": {\n      \"kind\": {\n       \"description\": \"Kind of the referenced resource\",\n       \"enum\": [\n        \"Mesh\",\n        \"MeshSubset\",\n        \"MeshGateway\",\n        \"MeshService\",\n        \"MeshExternalService\",\n        \"MeshMultiZoneService\",\n        \"MeshServiceSubset\",\n        \"MeshHTTPRoute\",\n        \"Dataplane\"\n       ],\n       \"type\": \"string\"\n      },\n      \"labels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n       \"type\": \"object\"\n      },\n      \"mesh\": {\n       \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n       \"type\": \"string\"\n      },\n      \"name\": {\n       \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n       \"type\": \"string\"\n      },\n      \"namespace\": {\n       \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n       \"type\": \"string\"\n      },\n      \"proxyTypes\": {\n       \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n       \"items\": {\n        \"enum\": [\n         \"Sidecar\",\n         \"Gateway\"\n        ],\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      },\n      \"sectionName\": {\n       \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n       \"type\": \"string\"\n      },\n      \"tags\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n       \"type\": \"object\"\n      }\n     },\n     \"required\": [\n      \"kind\"\n     ],\n     \"type\": \"object\"\n    },\n    \"to\": {\n     \"description\": \"To list makes a match between the consumed services and corresponding\\nconfigurations\",\n     \"items\": {\n      \"properties\": {\n       \"default\": {\n        \"description\": \"Default is a configuration specific to the group of destinations\\nreferenced in 'targetRef'\",\n        \"properties\": {\n         \"connectionLimits\": {\n          \"description\": \"ConnectionLimits contains configuration of each circuit breaking limit,\\nwhich when exceeded makes the circuit breaker to become open (no traffic\\nis allowed like no current is allowed in the circuits when physical\\ncircuit breaker ir open)\",\n          \"properties\": {\n           \"maxConnectionPools\": {\n            \"description\": \"The maximum number of connection pools per cluster that are concurrently\\nsupported at once. Set this for clusters which create a large number of\\nconnection pools.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxConnections\": {\n            \"description\": \"The maximum number of connections allowed to be made to the upstream\\ncluster.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxPendingRequests\": {\n            \"description\": \"The maximum number of pending requests that are allowed to the upstream\\ncluster. This limit is applied as a connection limit for non-HTTP\\ntraffic.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxRequests\": {\n            \"description\": \"The maximum number of parallel requests that are allowed to be made\\nto the upstream cluster. This limit does not apply to non-HTTP traffic.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"maxRetries\": {\n            \"description\": \"The maximum number of parallel retries that will be allowed to\\nthe upstream cluster.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"outlierDetection\": {\n          \"description\": \"OutlierDetection contains the configuration of the process of dynamically\\ndetermining whether some number of hosts in an upstream cluster are\\nperforming unlike the others and removing them from the healthy load\\nbalancing set. Performance might be along different axes such as\\nconsecutive failures, temporal success rate, temporal latency, etc.\\nOutlier detection is a form of passive health checking.\",\n          \"properties\": {\n           \"baseEjectionTime\": {\n            \"description\": \"The base time that a host is ejected for. The real time is equal to\\nthe base time multiplied by the number of times the host has been\\nejected.\",\n            \"type\": \"string\"\n           },\n           \"detectors\": {\n            \"description\": \"Contains configuration for supported outlier detectors\",\n            \"properties\": {\n             \"failurePercentage\": {\n              \"description\": \"Failure Percentage based outlier detection functions similarly to success\\nrate detection, in that it relies on success rate data from each host in\\na cluster. However, rather than compare those values to the mean success\\nrate of the cluster as a whole, they are compared to a flat\\nuser-configured threshold. This threshold is configured via the\\noutlierDetection.failurePercentageThreshold field.\\nThe other configuration fields for failure percentage based detection are\\nsimilar to the fields for success rate detection. As with success rate\\ndetection, detection will not be performed for a host if its request\\nvolume over the aggregation interval is less than the\\noutlierDetection.detectors.failurePercentage.requestVolume value.\\nDetection also will not be performed for a cluster if the number of hosts\\nwith the minimum required request volume in an interval is less than the\\noutlierDetection.detectors.failurePercentage.minimumHosts value.\",\n              \"properties\": {\n               \"minimumHosts\": {\n                \"description\": \"The minimum number of hosts in a cluster in order to perform failure\\npercentage-based ejection. If the total number of hosts in the cluster is\\nless than this value, failure percentage-based ejection will not be\\nperformed.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"requestVolume\": {\n                \"description\": \"The minimum number of total requests that must be collected in one\\ninterval (as defined by the interval duration above) to perform failure\\npercentage-based ejection for this host. If the volume is lower than this\\nsetting, failure percentage-based ejection will not be performed for this\\nhost.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"threshold\": {\n                \"description\": \"The failure percentage to use when determining failure percentage-based\\noutlier detection. If the failure percentage of a given host is greater\\nthan or equal to this value, it will be ejected.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"gatewayFailures\": {\n              \"description\": \"In the default mode (outlierDetection.splitExternalLocalOriginErrors is\\nfalse) this detection type takes into account a subset of 5xx errors,\\ncalled \\\"gateway errors\\\" (502, 503 or 504 status code) and local origin\\nfailures, such as timeout, TCP reset etc.\\nIn split mode (outlierDetection.splitExternalLocalOriginErrors is true)\\nthis detection type takes into account a subset of 5xx errors, called\\n\\\"gateway errors\\\" (502, 503 or 504 status code) and is supported only by\\nthe http router.\",\n              \"properties\": {\n               \"consecutive\": {\n                \"description\": \"The number of consecutive gateway failures (502, 503, 504 status codes)\\nbefore a consecutive gateway failure ejection occurs.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"localOriginFailures\": {\n              \"description\": \"This detection type is enabled only when\\noutlierDetection.splitExternalLocalOriginErrors is true and takes into\\naccount only locally originated errors (timeout, reset, etc).\\nIf Envoy repeatedly cannot connect to an upstream host or communication\\nwith the upstream host is repeatedly interrupted, it will be ejected.\\nVarious locally originated problems are detected: timeout, TCP reset,\\nICMP errors, etc. This detection type is supported by http router and\\ntcp proxy.\",\n              \"properties\": {\n               \"consecutive\": {\n                \"description\": \"The number of consecutive locally originated failures before ejection\\noccurs. Parameter takes effect only when splitExternalAndLocalErrors\\nis set to true.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"successRate\": {\n              \"description\": \"Success Rate based outlier detection aggregates success rate data from\\nevery host in a cluster. Then at given intervals ejects hosts based on\\nstatistical outlier detection. Success Rate outlier detection will not be\\ncalculated for a host if its request volume over the aggregation interval\\nis less than the outlierDetection.detectors.successRate.requestVolume\\nvalue.\\nMoreover, detection will not be performed for a cluster if the number of\\nhosts with the minimum required request volume in an interval is less\\nthan the outlierDetection.detectors.successRate.minimumHosts value.\\nIn the default configuration mode\\n(outlierDetection.splitExternalLocalOriginErrors is false) this detection\\ntype takes into account all types of errors: locally and externally\\noriginated.\\nIn split mode (outlierDetection.splitExternalLocalOriginErrors is true),\\nlocally originated errors and externally originated (transaction) errors\\nare counted and treated separately.\",\n              \"properties\": {\n               \"minimumHosts\": {\n                \"description\": \"The number of hosts in a cluster that must have enough request volume to\\ndetect success rate outliers. If the number of hosts is less than this\\nsetting, outlier detection via success rate statistics is not performed\\nfor any host in the cluster.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"requestVolume\": {\n                \"description\": \"The minimum number of total requests that must be collected in one\\ninterval (as defined by the interval duration configured in\\noutlierDetection section) to include this host in success rate based\\noutlier detection. If the volume is lower than this setting, outlier\\ndetection via success rate statistics is not performed for that host.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"standardDeviationFactor\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"This factor is used to determine the ejection threshold for success rate\\noutlier ejection. The ejection threshold is the difference between\\nthe mean success rate, and the product of this factor and the standard\\ndeviation of the mean success rate: mean - (standard_deviation *\\nsuccess_rate_standard_deviation_factor).\\nEither int or decimal represented as string.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"totalFailures\": {\n              \"description\": \"In the default mode (outlierDetection.splitExternalAndLocalErrors is\\nfalse) this detection type takes into account all generated errors:\\nlocally originated and externally originated (transaction) errors.\\nIn split mode (outlierDetection.splitExternalLocalOriginErrors is true)\\nthis detection type takes into account only externally originated\\n(transaction) errors, ignoring locally originated errors.\\nIf an upstream host is an HTTP-server, only 5xx types of error are taken\\ninto account (see Consecutive Gateway Failure for exceptions).\\nProperly formatted responses, even when they carry an operational error\\n(like index not found, access denied) are not taken into account.\",\n              \"properties\": {\n               \"consecutive\": {\n                \"description\": \"The number of consecutive server-side error responses (for HTTP traffic,\\n5xx responses; for TCP traffic, connection failures; for Redis, failure\\nto respond PONG; etc.) before a consecutive total failure ejection\\noccurs.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"disabled\": {\n            \"description\": \"When set to true, outlierDetection configuration won't take any effect\",\n            \"type\": \"boolean\"\n           },\n           \"healthyPanicThreshold\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Allows to configure panic threshold for Envoy cluster. If not specified,\\nthe default is 50%. To disable panic mode, set to 0%.\\nEither int or decimal represented as string.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"interval\": {\n            \"description\": \"The time interval between ejection analysis sweeps. This can result in\\nboth new ejections and hosts being returned to service.\",\n            \"type\": \"string\"\n           },\n           \"maxEjectionPercent\": {\n            \"description\": \"The maximum % of an upstream cluster that can be ejected due to outlier\\ndetection. Defaults to 10% but will eject at least one host regardless of\\nthe value.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"splitExternalAndLocalErrors\": {\n            \"description\": \"Determines whether to distinguish local origin failures from external\\nerrors. If set to true the following configuration parameters are taken\\ninto account: detectors.localOriginFailures.consecutive\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"targetRef\": {\n        \"description\": \"TargetRef is a reference to the resource that represents a group of\\ndestinations.\",\n        \"properties\": {\n         \"kind\": {\n          \"description\": \"Kind of the referenced resource\",\n          \"enum\": [\n           \"Mesh\",\n           \"MeshSubset\",\n           \"MeshGateway\",\n           \"MeshService\",\n           \"MeshExternalService\",\n           \"MeshMultiZoneService\",\n           \"MeshServiceSubset\",\n           \"MeshHTTPRoute\",\n           \"Dataplane\"\n          ],\n          \"type\": \"string\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n          \"type\": \"object\"\n         },\n         \"mesh\": {\n          \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n          \"type\": \"string\"\n         },\n         \"proxyTypes\": {\n          \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n          \"items\": {\n           \"enum\": [\n            \"Sidecar\",\n            \"Gateway\"\n           ],\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"sectionName\": {\n          \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n          \"type\": \"string\"\n         },\n         \"tags\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"kind\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"targetRef\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Mesh Circuit Breaker\",\n \"type\": \"object\"\n}"}}