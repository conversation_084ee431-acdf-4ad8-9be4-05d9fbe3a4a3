{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "SSP", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubevirt", "displayName": "Kubevir<PERSON>", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "App Definition and Development"}, "subCategory": "Application Definition & Image Build", "metadata": {"isAnnotation": false, "primaryColor": "#00aab2", "secondaryColor": "#45EFF7", "shape": "circle", "source_uri": "git://github.com/kubevirt/ssp-operator/main/config/crd/bases", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.20 3.05 361.40 349.90\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:none}.cls-2{fill:#00aab2}.cls-3{fill:#fff}.cls-4{fill:#00797f}.cls-5{fill:#3accc5}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M192.7065 271.38809l6.773-13.54613a9.49468 9.49468 0 0 1 5.649-4.40547 9.348 9.348 0 0 0-5.83716 4.40547l-6.773 13.54613A9.23964 9.23964 0 0 0 197.8555 284.34c-.27577-.103-.55028-.21621-.82177-.34655a9.40792 9.40792 0 0 1-4.32723-12.60536zm25.96331-29.91426a9.40808 9.40808 0 0 1-4.32723-12.60541l13.358-26.90394a9.3473 9.3473 0 0 1 6.301-4.843 9.12 9.12 0 0 0-6.48913 5.03114l-13.54613 26.904a9.43238 9.43238 0 0 0 12.74781 12.53488 9.4831 9.4831 0 0 1-8.04432-.11767zM187.25048 18.90445q.56443.28221 1.12878.56443l115.89414 55.5012c1.317.75258 2.63394 1.6933 3.951 2.634a19.91834 19.91834 0 0 1 2.25765 1.88136l-13.71334 27.09217h.16729l-8.91839 17.65839L310.73 79.54979c-.02052-.02061-.03937-.04381-.05989-.06433a19.93238 19.93238 0 0 0-2.25765-1.88136 20.6252 20.6252 0 0 0-3.95094-2.634L188.56741 19.46888q-.56431-.28221-1.12878-.56443a12.1699 12.1699 0 0 0-2.44588-.75258c-.17616-.04406-.38056-.05989-.57256-.08812.12817.03108.25634.05612.38442.08812a9.31469 9.31469 0 0 1 2.44587.75258zm-8.27817-1.31701a47.95077 47.95077 0 0 0-7.33746.37629 9.44272 9.44272 0 0 0-2.25765.37629c.94064-.18815 1.69322-.18815 2.44579-.37629a45.53642 45.53642 0 0 1 7.14932-.37629zm28.4091 131.88598a9.52851 9.52851 0 0 0 2.79276.9403 9.22039 9.22039 0 0 1-2.60462-.9403 9.76617 9.76617 0 0 1-3.95094-12.79342 9.474 9.474 0 0 0 3.7628 12.79342zm61.52172-24.08187h-.18813a9.56425 9.56425 0 0 0 4.51529 6.20867 9.03611 9.03611 0 0 0 6.74379.97758 9.46073 9.46073 0 0 1-11.07095-7.18625zm-34.61769-6.39674L220.175 145.52256l14.29863-26.52775a9.53885 9.53885 0 0 0-3.951-12.79347 8.73982 8.73982 0 0 0-4.32723-1.12886h-.18815a8.73961 8.73961 0 0 1 4.32724 1.12886 9.42567 9.42567 0 0 1 3.95095 12.79347zM49.39156 76.94735c-.04474.03711-.087.07615-.13144.11351L103.29093 185.629l-1.47031-3.00185zM148.117 277.408l.162-.242-29.068-59.113 28.906 59.355zm100.09078-161.42341a9.31556 9.31556 0 0 1 9.40695-9.407h-.18814a9.407 9.407 0 1 0 0 18.81392h.18814a9.31561 9.31561 0 0 1-9.40695-9.40692zm-42.89587 57.75893l-14.11048 26.52766a9.36557 9.36557 0 0 1-16.98852-.714 9.3451 9.3451 0 0 0 4.38311 4.85314 9.53881 9.53881 0 0 0 12.79355-3.951L205.5 173.93166a9.40166 9.40166 0 0 0-1.80136-11.26092 9.43369 9.43369 0 0 1 1.61327 11.07278zm-49.29261 48.91631a9.48444 9.48444 0 0 1 17.44212 1.64782 9.43241 9.43241 0 0 0-17.44212-1.836l-7.5256 14.29862-53.0554-112.5075 53.0554 112.69569zM148.494 276.844l30.761 61.71h.094l-30.807-61.8-.048.09zm11.852-95.575l10.536-19.378-30.478-56.254h-.087l30.377 56.254-10.348 19.378zm81.84125-7.71363l13.358-26.904v-.18814a8.93718 8.93718 0 0 1 2.17371-2.86631 9.18734 9.18734 0 0 0-2.36185 3.05445l-13.358 26.904a9.26028 9.26028 0 0 0 3.19837 11.85284 8.77683 8.77683 0 0 0 4.61824 1.66012 9.61806 9.61806 0 0 1-3.30123-.90763 9.408 9.408 0 0 1-4.32724-12.60533z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M344.74183 223.16136c-.0093.02094-.00787.04188-.01835.06282a24.52526 24.52526 0 0 1-3.7628 8.09L320.64161 256.525l-59.82851 73.93886a19.68752 19.68752 0 0 1-12.79347 7.5256 39.608 39.608 0 0 1-4.70352.37629h-63.77937l28.57873-56.42c-.06676.07045-.13126.14316-.19979.21126l-28.56709 56.39685v.18814H243.316a39.60816 39.60816 0 0 0 4.70352-.37628 19.24954 19.24954 0 0 0 12.79347-7.5256l59.82851-73.93891 20.31907-25.21065a26.42206 26.42206 0 0 0 3.7628-8.09l.00008-.00067a5.29569 5.29569 0 0 1 .14618-.8768 1.70441 1.70441 0 0 1-.1278.43827zm.90688-7.66952a24.86724 24.86724 0 0 1-.73708 6.60356c0 .14634-.00729.29277-.01835.43912-.00159.02069-.0005.04138-.00259.06207.0041-.04138.02077-.08285.02077-.12423l.00008-.00067a25.54405 25.54405 0 0 0 .73717-6.97985z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M115.38108 338.55389a10.72708 10.72708 0 0 1-2.44579-.18814 15.93175 15.93175 0 0 1-3.19837-.75258 28.27655 28.27655 0 0 1-11.85283-6.96108L71.1682 297.91575l-10.47015-12.977 10.282 12.78887 26.71589 33.11254a29.8032 29.8032 0 0 0 12.041 6.96116l.00193.00051a19.70422 19.70422 0 0 0 3.19644.75207c.75249 0 1.69321.18814 2.44579.18814h63.96751l-.09382-.18814z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M52.87071 74.57478q-1.12257.64534-2.24541 1.37288c-.31371.23455-.616.4748-.905.71572-.11392.09114-.21687.19116-.3287.284l52.42906 105.67982-37.80173-77.1784h76.19663l.10162.18814h.08653l30.4786 56.25377-10.53582 19.37844-.37629.18814-30.85489-57.19449H95.4383l53.0554 112.50754 7.5256-14.29862a9.43241 9.43241 0 0 1 17.44212 1.836c.01734.05671.02916.11459.04541.17155.04825.17952.09842.35828.136.54056a9.317 9.317 0 0 1-.87915 6.48268l-13.7342 25.77509-10.48715 19.4761 30.80622 61.80026 28.56713-56.39696a9.50054 9.50054 0 0 1-10.06018 2.183 9.23964 9.23964 0 0 1-5.33715-12.95188l6.773-13.54613a9.348 9.348 0 0 1 5.83716-4.40547 9.36462 9.36462 0 0 1 11.46049 11.76803l12.7933-25.41136a9.61839 9.61839 0 0 1-2.6682 1.79834 9.43238 9.43238 0 0 1-12.74781-12.53488l13.54613-26.904a9.12 9.12 0 0 1 6.48913-5.03114 9.25737 9.25737 0 0 1 11.0551 11.65187l12.52793-24.64738a9.52394 9.52394 0 0 1-7.76887 2.94229 8.77683 8.77683 0 0 1-4.61824-1.66012 9.26028 9.26028 0 0 1-3.19837-11.85284l13.358-26.904a9.18734 9.18734 0 0 1 2.36185-3.05445 9.54993 9.54993 0 0 1 10.43162-1.2727 9.1824 9.1824 0 0 1 4.13909 12.60533l-3.85931 7.815 16.76712-33.09294a9.52346 9.52346 0 0 1-5.00593 2.88926c-.07254.01617-.14491.02253-.21746.03694a9.03611 9.03611 0 0 1-6.74379-.97758 9.56425 9.56425 0 0 1-4.51529-6.20867h-11.2884a9.407 9.407 0 1 1 0-18.81392h39.34207L310.482 79.48546a19.91834 19.91834 0 0 0-2.25765-1.88136c-1.317-.94072-2.63394-1.88144-3.951-2.634L188.37926 19.46888q-.5643-.28221-1.12878-.56443c-.75257-.18814-1.50515-.56443-2.44587-.75258-.12808-.032-.25625-.057-.38442-.08812-.628-.0924-1.29682-.14408-1.87323-.28816a22.76746 22.76746 0 0 0-3.57465-.18815 45.53642 45.53642 0 0 0-7.14932.37629c-.75257.18814-1.50515.18814-2.44579.37629L53.67121 74.21759c-.27543.10328-.53763.23103-.8005.35719zM203.61861 136.68l16.55639-31.60752h6.02045a8.73982 8.73982 0 0 1 4.32723 1.12886 9.53885 9.53885 0 0 1 3.951 12.79347L220.175 145.52256a9.24039 9.24039 0 0 1-10.00079 4.89116 9.28868 9.28868 0 0 1-6.5556-13.73372zm-29.16168 54.7487l14.11056-26.52774a9.59585 9.59585 0 0 1 16.93251 9.0307l-14.11048 26.52766a9.53881 9.53881 0 0 1-12.79355 3.951 9.3451 9.3451 0 0 1-4.38311-4.85314 9.95367 9.95367 0 0 1 .24407-8.12853z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M187.25048 18.90445a9.31469 9.31469 0 0 0-2.44587-.75258 12.16962 12.16962 0 0 1 2.44587.75258z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M187.25048 18.90445a12.16962 12.16962 0 0 0-2.44587-.75258c.94072.18813 1.6933.56444 2.44587.75258zM50.6253 75.94766q1.1227-.7254 2.24541-1.37288a13.59871 13.59871 0 0 0-2.24541 1.37288z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M353.00158 212.31215l-.18815-.75257L323.84 87.1992v-.56443a31.87872 31.87872 0 0 0-15.8038-18.43771h-.37628l-115.89406-55.5012a27.43753 27.43753 0 0 0-12.79355-2.63394 45.80265 45.80265 0 0 0-11.2884.94064l-.75258.18815-.75249.37628L50.2847 67.44456a28.234 28.234 0 0 0-15.61555 19.19021L6.26 211.55958v.37629a31.70153 31.70153 0 0 0 5.456 23.89372l.18814.18815.18815.18814 79.95943 99.33779.18806.37629.37629.18814a34.82016 34.82016 0 0 0 22.38866 10.15953H243.128c9.97139 0 18.24956-3.95094 23.51743-10.91211l80.14757-99.14964.18814-.18814.18814-.18815a32.69776 32.69776 0 0 0 5.8323-23.51744zm-8.08995 10.15954c0 .04188-.01684.08368-.02094.12557-.00419.04172-.01441.08352-.02086.12515a5.30229 5.30229 0 0 0-.14635.87814v-.00067l-.00008.00067a26.42206 26.42206 0 0 1-3.7628 8.09l-20.31907 25.21065-59.82853 73.93895a19.24954 19.24954 0 0 1-12.79347 7.5256 39.60816 39.60816 0 0 1-4.70352.37628h-127.935a10.72708 10.72708 0 0 1-2.44579-.18814 19.67872 19.67872 0 0 1-3.19837-.75249l.00193.00042-.00193-.00051a29.8032 29.8032 0 0 1-12.041-6.96116l-26.7158-33.11254-10.282-12.78887-42.96155-53.24815a26.87508 26.87508 0 0 1-4.32714-11.10026 11.57224 11.57224 0 0 1-.18815-2.44587 21.45879 21.45879 0 0 1 .37621-4.89158L42.0066 88.32807a20.05066 20.05066 0 0 1 7.14931-11.47655l.04306.08645c.11334-.08905.21763-.188.33315-.27459a17.2548 17.2548 0 0 1 3.95094-2.44579L169.3772 18.34a9.44272 9.44272 0 0 1 2.25765-.37629 47.95077 47.95077 0 0 1 7.33746-.37629 22.76746 22.76746 0 0 1 3.57465.18815 8.01732 8.01732 0 0 1 1.87323.28816c.192.02823.3964.04406.57256.08812a12.1699 12.1699 0 0 1 2.44588.75258q.56444.28221 1.12878.56443l115.89414 55.5012a20.6252 20.6252 0 0 1 3.95094 2.634 19.93238 19.93238 0 0 1 2.25765 1.88136c.02052.02052.03937.04372.05989.06433l.12834-.25248a23.64466 23.64466 0 0 1 5.07972 7.14931c.18815.75258.56444 1.50507.75258 2.25765l28.78539 124.17227c.10421.8856.15506 1.75462.17265 2.61534a25.54405 25.54405 0 0 1-.73717 6.97985l-.00008.00067z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M49.344 76.85152c-.04473.0377-.08519.07941-.12967.11736l.04582.092c.0444-.03736.0867-.0764.13144-.11351zM115.38108 338.742c-.75258 0-1.6933-.18814-2.44579-.18814a10.72742 10.72742 0 0 0 2.44579.18814zm-5.64416-.9406a19.67872 19.67872 0 0 0 3.19837.75249 19.70422 19.70422 0 0 1-3.19644-.75207zm235.15377-115.20414c.0041-.04189.02094-.08369.02094-.12557l-.00017.00067c0 .04138-.01667.08285-.02077.12423zm-.16721 1.00262v.00067a5.30229 5.30229 0 0 1 .14635-.87814l-.00017.00067a5.29569 5.29569 0 0 0-.14618.8768z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M115.38108 338.742a10.72742 10.72742 0 0 1-2.44579-.18814 10.72708 10.72708 0 0 0 2.44579.18814zM49.344 76.85152l.18815-.18814c-.11552.08661-.21981.18554-.33315.27459l.01533.03091c.04445-.03795.08491-.07966.12967-.11736zm133.203-59.07593c.57641.14408 1.24522.19576 1.87323.28816a8.01732 8.01732 0 0 0-1.87323-.28816z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M49.39156 76.94735c.11183-.09281.21478-.19283.3287-.284.28909-.24092.59133-.48117.905-.71572-.36439.23547-.72879.45545-1.09318.71572l-.18815.18814zm4.27965-2.72976L169.3772 18.34 53.48306 74.21759c-.20414.1166-.4082.23983-.61235.35719.26287-.12616.52507-.25391.8005-.35719z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M52.87071 74.57478c.20415-.11736.40821-.24059.61235-.35719a17.2548 17.2548 0 0 0-3.95094 2.44579c.36439-.26027.72879-.48025 1.09318-.71572a13.59871 13.59871 0 0 1 2.24541-1.37288z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M179.255 338.554l.094.188v-.188h-.094zm-81.37091-7.90191a28.27655 28.27655 0 0 0 11.85283 6.96108 15.93175 15.93175 0 0 0 3.19837.75258 10.72708 10.72708 0 0 0 2.44579.18814h63.87369L148.4937 276.8441l-.18814.37629-.02664-.0542-.16151.24226-28.90618-59.3558-10.03874-20.41541-5.88156-12.00824L49.26012 77.06086a21.01233 21.01233 0 0 0-7.06546 11.26721L13.59742 213.25288a21.45879 21.45879 0 0 0-.37621 4.89158 9.15983 9.15983 0 0 0 .18815 2.25773 25.00431 25.00431 0 0 0 4.32714 11.10017l53.4317 66.22525-29.61294-36.59907 19.14279 23.8102 10.47015 12.977z\" class=\"cls-4\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M71.168 297.728l-53.431-66.226 23.818 29.627 29.613 36.599zM42.19466 88.32807a21.01233 21.01233 0 0 1 7.06546-11.26721l-.04582-.092a20.62117 20.62117 0 0 0-7.01964 11.35921zM17.737 231.691l42.961 53.248-19.143-23.81-23.818-29.438z\" class=\"cls-4\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M49.199 76.938l-.04306-.08645a20.05066 20.05066 0 0 0-7.14934 11.47652L13.59742 213.25288 42.19466 88.32807A19.29686 19.29686 0 0 1 49.199 76.938zM13.40936 220.40219a9.15983 9.15983 0 0 1-.18815-2.25773 11.57224 11.57224 0 0 0 .18815 2.44587 25.15547 25.15547 0 0 0 4.32714 10.912 25.00431 25.00431 0 0 1-4.32714-11.10014z\" class=\"cls-4\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M17.7365 231.50236a25.15547 25.15547 0 0 1-4.32714-10.912 26.87508 26.87508 0 0 0 4.32714 11.10026l23.81876 29.438zM42.19466 88.32807a20.62117 20.62117 0 0 1 7.01964-11.35919l-.0153-.03088a19.29686 19.29686 0 0 0-7.00434 11.39007z\" class=\"cls-4\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M287.529 125.2034l-.94072 2.634-.18814.18815a9.499 9.499 0 0 1-1.19756 1.62092L268.33384 162.94l-3.56979 7.22889-5.64416 11.85275a9.87788 9.87788 0 0 1-1.52082 2.08835L244.93846 209.156a9.70415 9.70415 0 0 1-.49348 1.2747l-3.38652 6.585-9.78324 20.31907a9.87179 9.87179 0 0 1-1.71944 2.299L216.582 265.226c-.05545.17282-.10379.3463-.1698.51786-.18814.18814-.18814.18814-.18814.37629l-6.58488 13.73419a9.87935 9.87935 0 0 1-1.52367 2.09145l-28.57873 56.42h63.77937a39.608 39.608 0 0 0 4.70352-.37629 19.68752 19.68752 0 0 0 12.79347-7.5256l59.82847-73.9389 20.31907-25.21073a24.52526 24.52526 0 0 0 3.7628-8.09c.01048-.02094.00905-.04188.01835-.06282a1.85575 1.85575 0 0 1 .15145-.62684c.01106-.14635.01835-.29278.01835-.43912a24.86724 24.86724 0 0 0 .73708-6.60356c-.02446-.75375-.08067-1.50222-.17273-2.239l-.05529-.2374L316.50244 88.8925a10.60693 10.60693 0 0 0-.75257-2.25773 23.56266 23.56266 0 0 0-5.01984-7.085L288.01761 124.236zm28.97354-36.12284c-.18815-.75249-.56444-1.50507-.75258-2.25765a10.60819 10.60819 0 0 1 .75257 2.25765z\" class=\"cls-5\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M344.86966 222.72308c.00729-.04214.01692-.08436.021-.12649.00209-.02069.001-.04138.00259-.06207a1.85575 1.85575 0 0 0-.15145.62684 1.70441 1.70441 0 0 0 .12786-.43828zM315.75 86.82291c.18814.75258.56443 1.50516.75258 2.25765a10.60819 10.60819 0 0 0-.75258-2.25765zm-86.19422 152.81086c-.05595.05563-.11635.1053-.17348.15942l-12.7933 25.41136-.007.02144zM257.59907 184.11l-.01449.01608-12.52793 24.64731c-.03493.12808-.07773.25524-.11819.38266zm27.60393-54.464l-.006.006-16.767 33.093-.096.195 16.869-33.294z\" class=\"cls-5\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M316.50244 88.8925l28.91825 124.123.05529.2374c.09206.73674.14827 1.48521.17273 2.239-.01759-.86072-.06844-1.72974-.17265-2.61534L316.69067 88.70427c-.18814-.75258-.56443-1.50507-.75258-2.25765a23.64466 23.64466 0 0 0-5.07972-7.14931l-.12834.25248a23.56266 23.56266 0 0 1 5.01984 7.085 10.60693 10.60693 0 0 1 .75257 2.25771zm28.36722 133.83058l.00017-.00067c.00645-.04163.01667-.08343.02086-.12515v-.00067c-.00411.04213-.01369.08435-.02103.12649z\" class=\"cls-5\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M188.75555 165.089l-14.11048 26.5278a9.34134 9.34134 0 1 0 16.55636 8.65438l14.11048-26.52766a9.43369 9.43369 0 0 0-1.61322-11.07278 9.5211 9.5211 0 0 0-14.94314 2.41831z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M174.64507 191.6168l14.11048-26.5278a9.5211 9.5211 0 0 1 14.94314-2.41831 9.511 9.511 0 0 0-15.1312 2.23017l-14.11056 26.52774a9.95367 9.95367 0 0 0-.244 8.12848 9.3729 9.3729 0 0 1 .43214-7.94028zm41.76625 73.94577l-.1873.36942c.18471-.18471.18722-.19124.1873-.36942zM209.451 279.478l6.773-13.54605a9.59732 9.59732 0 0 0-4.13909-11.47654 9.29443 9.29443 0 0 0-6.95639-1.019 9.49468 9.49468 0 0 0-5.649 4.40547l-6.773 13.54613a9.40792 9.40792 0 0 0 4.32723 12.60533c.27149.13034.546.24351.82177.34655a9.5626 9.5626 0 0 0 10.65477-3.35678l-.59459 1.17385c.06853-.0681.133-.14081.19979-.21126l.583-1.15082c.18807-.3762.56436-.94064.75251-1.31688z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M212.08493 254.45545A9.59732 9.59732 0 0 1 216.224 265.932l.1873-.36942.00084-.00687.17684-.35115a9.36462 9.36462 0 0 0-11.46046-11.76806 9.29443 9.29443 0 0 1 6.95641 1.01895zM197.8555 284.34a9.50054 9.50054 0 0 0 10.06018-2.18293l.59459-1.17385A9.5626 9.5626 0 0 1 197.8555 284.34z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.224 266.12014c0-.18815 0-.18815.18814-.37629.066-.17156.11435-.345.1698-.51786l-.17064.33658c-.00008.17818-.00259.18471-.1873.36942l-6.773 13.54601c-.18814.3762-.56443.94064-.75257 1.31692l-.583 1.15082a9.87935 9.87935 0 0 0 1.52367-2.09145z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.41132 265.56257l.17064-.33658.007-.02144-.17684.35115zM148.4937 276.656l10.53582-19.56658 13.7342-25.77508a9.43315 9.43315 0 0 0 .87915-6.29454c-.03762-.18228-.08779-.361-.136-.54056-.01625-.057-.02807-.11484-.04541-.17155a9.48444 9.48444 0 0 0-17.44212-1.64782l-7.5256 14.29863L95.4383 124.26277h33.677l30.85489 57.19449.37629-.18814 10.34768-19.37844-30.377-56.25377H64.207l45.1536 92.18848 9.8506 20.22726 29.06769 59.11354.21478-.32209.04867-.09047z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M101.821 182.627l1.47 3.002 6.07 12.196-7.54-15.198zm70.94272 48.6873l-13.7342 25.77508L148.4937 276.656l.04867.09767 10.48715-19.4761 13.7342-25.77509a9.317 9.317 0 0 0 .87915-6.48268 9.43315 9.43315 0 0 1-.87915 6.2945z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"0\" d=\"M173.46142 224.30765c.01734.05671.02916.11459.04541.17155\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M64.207 105.637h76.11l-.101-.188H64.019l37.802 77.178 7.54 15.198-45.154-92.188zm39.084 79.992l5.881 12.008 10.039 20.416-9.85-20.228-6.07-12.196zm45.015 91.591l.188-.376-.215.322.027.054zm137.70644-149.17684l.19954-.39388 1.50515-2.82216.30048-.59116 8.91834-17.65833h-.16729l-9.4279 18.62577 9.407-18.62577h-39.13303a9.407 9.407 0 0 0 0 18.81392h11.2884a9.46073 9.46073 0 0 0 11.07094 7.18625 9.65911 9.65911 0 0 0 6.03837-4.53464z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M296.769 106.578h-.021l-9.407 18.625 9.428-18.625zm-16.79493 25.9998c.07255-.01441.14492-.02077.21746-.03694a9.52346 9.52346 0 0 0 5.00593-2.88926l.815-1.60844a9.65911 9.65911 0 0 1-6.03839 4.53464zm6.23793-4.92852l-.19954.39388.0114-.01759-.82127 1.62092a9.499 9.499 0 0 0 1.19756-1.62092l.18814-.18815.94072-2.634.48862-.96744-.30048.59116z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M286.02384 128.02557l-.0114.01759-.815 1.60844.00511-.00511zM258.17 182.97423l.19727-.38819a4.11213 4.11213 0 0 0 .56443-.94072l5.6442-11.28832 7.5256-15.23935a9.4245 9.4245 0 0 0-14.38256-11.52077 8.93718 8.93718 0 0 0-2.17371 2.86631v.18814l-13.358 26.904a9.408 9.408 0 0 0 4.32723 12.60533 9.61806 9.61806 0 0 0 3.30123.90763 9.47039 9.47039 0 0 0 8.35431-4.09406z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M267.96241 142.51234a9.18239 9.18239 0 0 1 4.13909 12.60532l-7.5256 15.23934.18815-.37629 3.66629-7.23618 3.85931-7.815a9.1824 9.1824 0 0 0-4.13909-12.60533 9.54993 9.54993 0 0 0-10.43162 1.2727 9.21467 9.21467 0 0 1 10.24347-1.08456zm-18.1467 44.55599a9.52394 9.52394 0 0 0 7.76887-2.94229l.58546-1.15181a9.47039 9.47039 0 0 1-8.35433 4.0941z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M258.93174 181.64532a4.11213 4.11213 0 0 1-.56443.94072l-.19727.38819.00913-.0119-.5801 1.14763a9.87788 9.87788 0 0 0 1.52082-2.08835l5.64416-11.85275 3.56979-7.22889-3.75794 7.417z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M264.576 170.357l3.758-7.417.096-.195-3.666 7.236-.188.376zm-6.39683 12.60533l-.00913.0119-.58546 1.15181.01449-.01608zm-28.47436 56.19011l.44154-.877c.18815-.37629.56444-.94072.75258-1.317l10.15953-20.31907 3.80151-7.47911a9.49138 9.49138 0 0 0-10.85842-12.0388 9.3473 9.3473 0 0 0-6.301 4.843l-13.358 26.90394a9.4298 9.4298 0 0 0 12.37152 12.72311 9.11894 9.11894 0 0 0 2.99074-2.43907z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M226.7141 241.59153a9.61839 9.61839 0 0 0 2.6682-1.79834l.32251-.64075a9.11894 9.11894 0 0 1-2.99071 2.43909zm13.40364-43.578a9.24534 9.24534 0 0 1 4.74226 11.14675l.19668-.38693a9.25737 9.25737 0 0 0-11.0551-11.65187 9.302 9.302 0 0 1 6.11616.89205z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M244.25683 210.61894l-3.38651 6.58488 3.82906-7.57494c.06015-.15556.10924-.31212.16059-.4686l-3.80151 7.47911-10.15953 20.31907c-.18814.37629-.56443.94072-.75258 1.317l-.44154.877c.08377-.10505.17373-.20306.2534-.31254l-.40243.79387a9.87179 9.87179 0 0 0 1.71944-2.299l9.78324-20.31907 3.38652-6.585a9.70415 9.70415 0 0 0 .49348-1.2747l-.23908.47287a9.76715 9.76715 0 0 1-.44255.99005z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M244.69938 209.62888l.23908-.47287c.04046-.12742.08326-.25458.11819-.38266l-.19668.38693c-.05135.15648-.10044.31304-.16059.4686zm-14.74117 29.21102c-.07967.10948-.16963.20749-.2534.31254l-.32251.64075c.05713-.05412.11753-.10379.17348-.15942zm14.29862-28.22096a9.76715 9.76715 0 0 0 .44255-.99006l-3.82906 7.57494zm-36.68728-61.14552a9.22039 9.22039 0 0 0 2.60462.9403 9.57056 9.57056 0 0 0 10.00079-4.89116l14.11048-26.52775a9.42567 9.42567 0 0 0-3.95094-12.79347 8.73961 8.73961 0 0 0-4.32724-1.12886H220.175L203.61861 136.68a9.76617 9.76617 0 0 0 3.95094 12.79342z\" class=\"cls-3\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M210.17417 150.41372a9.24039 9.24039 0 0 0 10.00079-4.89116 9.57056 9.57056 0 0 1-10.00079 4.89116z\" class=\"cls-3\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"6.10 10.85 345.80 334.30\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M115.09287 338.742l-1.852-.07941a14.868 14.868 0 0 0 1.852.07941z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M345.18784 212.87648L316.40244 88.7043c-.18817-.75263-.56445-1.50513-.75256-2.25769a23.64542 23.64542 0 0 0-5.07978-7.14929l-.12829.2525c-.02051-.02063-.03937-.04382-.05988-.06439a19.93834 19.93834 0 0 0-2.25769-1.88135 20.626 20.626 0 0 0-3.95092-2.634L188.27915 19.46889q-.56424-.28227-1.12878-.56446a12.17166 12.17166 0 0 0-2.44587-.75256c-.17614-.04407-.38055-.05988-.57257-.08814-.628-.0924-1.29681-.144-1.87323-.28814a22.76456 22.76456 0 0 0-3.57464-.18817 47.94466 47.94466 0 0 0-7.33747.37634 9.44163 9.44163 0 0 0-2.25759.37624L53.383 74.2176c-.27545.10328-.5376.231-.80047.35718a13.604 13.604 0 0 0-2.24543 1.37287c-.31372.23455-.616.47479-.905.71576-.114.09112-.21692.1911-.32873.28393-.04474.03711-.087.07617-.13141.11353l-.04584-.092-.01532-.031-.04309-.08642a20.05086 20.05086 0 0 0-7.14929 11.47656L13.30918 213.25288a21.45849 21.45849 0 0 0-.37622 4.8916 11.57114 11.57114 0 0 0 .18817 2.44587 26.87493 26.87493 0 0 0 4.32715 11.10022l42.96155 53.24816 10.282 12.78888 26.71586 33.11249a29.80244 29.80244 0 0 0 12.041 6.96118l.00189.00049a19.69944 19.69944 0 0 0 3.19647.75208c.7525 0 1.69318.18817 2.4458.18817h127.93496a39.614 39.614 0 0 0 4.70349-.37628 19.24937 19.24937 0 0 0 12.79346-7.52564l59.82855-73.9389 20.319-25.21063a26.421 26.421 0 0 0 3.76282-8.09l.00012-.00067a3.98126 3.98126 0 0 1 .12451-.77362c.007-.0343.01569-.06861.02161-.10291l.00006-.00024.00012-.00067c.00647-.04163.01666-.08344.02087-.12519v-.00067c.00409-.04138.02076-.08282.02076-.1242l.00012-.00068a25.54436 25.54436 0 0 0 .73712-6.97985c-.01755-.86075-.06842-1.72977-.17258-2.61539zm-87.877-28.76654l-.01447.01611a9.5482 9.5482 0 0 1-11.07013 2.03467 9.408 9.408 0 0 1-4.32721-12.60535l13.358-26.90405v-.18811a8.937 8.937 0 0 1 2.17371-2.86633 9.55005 9.55005 0 0 1 10.43164-1.27271 9.18249 9.18249 0 0 1 4.1391 12.60535l-3.85931 7.815-.0965.19544-3.56982 7.22888-5.64417 11.85278a9.87529 9.87529 0 0 1-1.52085 2.08832zm-13.15412 26.3208l-3.38647 6.585-9.78326 20.319a9.87121 9.87121 0 0 1-1.71943 2.29907c-.056.05561-.11633.10529-.17346.15937a9.61743 9.61743 0 0 1-2.66821 1.79834 9.42977 9.42977 0 0 1-12.37152-12.72309l13.358-26.90393a9.56395 9.56395 0 0 1 12.60535-4.1391 9.45293 9.45293 0 0 1 4.75073 10.94794c-.03491.12811-.0777.25525-.11817.38269a9.69652 9.69652 0 0 1-.49357 1.27471zM207.62748 282.157a9.50065 9.50065 0 0 1-10.06024 2.183c-.27576-.10309-.55024-.21625-.82172-.34656a9.408 9.408 0 0 1-4.32727-12.60534l6.773-13.54615a9.49475 9.49475 0 0 1 5.649-4.40545 9.36464 9.36464 0 0 1 11.46045 11.76806l-.007.02143c-.05548.17285-.10382.34631-.1698.51788-.18817.18811-.18817.18811-.18817.37628l-6.58484 13.73419a9.87977 9.87977 0 0 1-1.52368 2.09143c-.06674.07042-.13121.14317-.19973.21123zm2.25852-131.74329a9.21794 9.21794 0 0 1-2.60461-.94031 9.76615 9.76615 0 0 1-3.951-12.79345l16.5564-31.60749h5.83221a8.73886 8.73886 0 0 1 4.32721 1.12891 9.4256 9.4256 0 0 1 3.951 12.79346l-14.11047 26.52771a9.24039 9.24039 0 0 1-10.00074 4.89117zm77.3548-25.21033l-.94074 2.634-.18811.18817a9.4997 9.4997 0 0 1-1.19757 1.62091l-.00512.00513a9.52354 9.52354 0 0 1-5.00592 2.88922c-.07251.01617-.1449.02252-.21747.03692a9.46066 9.46066 0 0 1-11.07093-7.18622h-11.28843a9.407 9.407 0 1 1 0-18.8139h39.32123l-8.91833 17.65839zm-98.96149 39.69751a9.46795 9.46795 0 1 1 16.74439 8.84265l-14.11048 26.52765a9.3656 9.3656 0 0 1-16.98852-.714 9.95378 9.95378 0 0 1 .244-8.12848zm-86.74688 17.72626l-37.8018-77.17841h76.19665l.10163.18818 30.377 56.25378-10.34771 19.37842-.37628.18817-30.85486-57.19452h-33.677l53.05542 112.69568 7.52557-14.29865a9.48448 9.48448 0 0 1 17.44214 1.64783c.01734.0567.02918.11463.04541.17157.04822.17951.09839.35828.136.54059a9.317 9.317 0 0 1-.87915 6.48267l-13.73419 25.77508-10.48718 19.47608-.04865.09045-.18817.37628-.02661-.0542-29.06765-59.11352-10.03876-20.41541-5.88152-12.00822z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "v0.24.0-alpha.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#00aab2", "secondaryColor": "#45EFF7", "shape": "circle", "svgColor": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.20 3.05 361.40 349.90\"><defs><style>.cls-1{fill:none}.cls-2{fill:#00aab2}.cls-3{fill:#fff}.cls-4{fill:#00797f}.cls-5{fill:#3accc5}</style></defs><path d=\"M192.7065 271.38809l6.773-13.54613a9.49468 9.49468 0 0 1 5.649-4.40547 9.348 9.348 0 0 0-5.83716 4.40547l-6.773 13.54613A9.23964 9.23964 0 0 0 197.8555 284.34c-.27577-.103-.55028-.21621-.82177-.34655a9.40792 9.40792 0 0 1-4.32723-12.60536zm25.96331-29.91426a9.40808 9.40808 0 0 1-4.32723-12.60541l13.358-26.90394a9.3473 9.3473 0 0 1 6.301-4.843 9.12 9.12 0 0 0-6.48913 5.03114l-13.54613 26.904a9.43238 9.43238 0 0 0 12.74781 12.53488 9.4831 9.4831 0 0 1-8.04432-.11767zM187.25048 18.90445q.56443.28221 1.12878.56443l115.89414 55.5012c1.317.75258 2.63394 1.6933 3.951 2.634a19.91834 19.91834 0 0 1 2.25765 1.88136l-13.71334 27.09217h.16729l-8.91839 17.65839L310.73 79.54979c-.02052-.02061-.03937-.04381-.05989-.06433a19.93238 19.93238 0 0 0-2.25765-1.88136 20.6252 20.6252 0 0 0-3.95094-2.634L188.56741 19.46888q-.56431-.28221-1.12878-.56443a12.1699 12.1699 0 0 0-2.44588-.75258c-.17616-.04406-.38056-.05989-.57256-.08812.12817.03108.25634.05612.38442.08812a9.31469 9.31469 0 0 1 2.44587.75258zm-8.27817-1.31701a47.95077 47.95077 0 0 0-7.33746.37629 9.44272 9.44272 0 0 0-2.25765.37629c.94064-.18815 1.69322-.18815 2.44579-.37629a45.53642 45.53642 0 0 1 7.14932-.37629zm28.4091 131.88598a9.52851 9.52851 0 0 0 2.79276.9403 9.22039 9.22039 0 0 1-2.60462-.9403 9.76617 9.76617 0 0 1-3.95094-12.79342 9.474 9.474 0 0 0 3.7628 12.79342zm61.52172-24.08187h-.18813a9.56425 9.56425 0 0 0 4.51529 6.20867 9.03611 9.03611 0 0 0 6.74379.97758 9.46073 9.46073 0 0 1-11.07095-7.18625zm-34.61769-6.39674L220.175 145.52256l14.29863-26.52775a9.53885 9.53885 0 0 0-3.951-12.79347 8.73982 8.73982 0 0 0-4.32723-1.12886h-.18815a8.73961 8.73961 0 0 1 4.32724 1.12886 9.42567 9.42567 0 0 1 3.95095 12.79347zM49.39156 76.94735c-.04474.03711-.087.07615-.13144.11351L103.29093 185.629l-1.47031-3.00185zM148.117 277.408l.162-.242-29.068-59.113 28.906 59.355zm100.09078-161.42341a9.31556 9.31556 0 0 1 9.40695-9.407h-.18814a9.407 9.407 0 1 0 0 18.81392h.18814a9.31561 9.31561 0 0 1-9.40695-9.40692zm-42.89587 57.75893l-14.11048 26.52766a9.36557 9.36557 0 0 1-16.98852-.714 9.3451 9.3451 0 0 0 4.38311 4.85314 9.53881 9.53881 0 0 0 12.79355-3.951L205.5 173.93166a9.40166 9.40166 0 0 0-1.80136-11.26092 9.43369 9.43369 0 0 1 1.61327 11.07278zm-49.29261 48.91631a9.48444 9.48444 0 0 1 17.44212 1.64782 9.43241 9.43241 0 0 0-17.44212-1.836l-7.5256 14.29862-53.0554-112.5075 53.0554 112.69569zM148.494 276.844l30.761 61.71h.094l-30.807-61.8-.048.09zm11.852-95.575l10.536-19.378-30.478-56.254h-.087l30.377 56.254-10.348 19.378zm81.84125-7.71363l13.358-26.904v-.18814a8.93718 8.93718 0 0 1 2.17371-2.86631 9.18734 9.18734 0 0 0-2.36185 3.05445l-13.358 26.904a9.26028 9.26028 0 0 0 3.19837 11.85284 8.77683 8.77683 0 0 0 4.61824 1.66012 9.61806 9.61806 0 0 1-3.30123-.90763 9.408 9.408 0 0 1-4.32724-12.60533z\" class=\"cls-1\"/><path d=\"M344.74183 223.16136c-.0093.02094-.00787.04188-.01835.06282a24.52526 24.52526 0 0 1-3.7628 8.09L320.64161 256.525l-59.82851 73.93886a19.68752 19.68752 0 0 1-12.79347 7.5256 39.608 39.608 0 0 1-4.70352.37629h-63.77937l28.57873-56.42c-.06676.07045-.13126.14316-.19979.21126l-28.56709 56.39685v.18814H243.316a39.60816 39.60816 0 0 0 4.70352-.37628 19.24954 19.24954 0 0 0 12.79347-7.5256l59.82851-73.93891 20.31907-25.21065a26.42206 26.42206 0 0 0 3.7628-8.09l.00008-.00067a5.29569 5.29569 0 0 1 .14618-.8768 1.70441 1.70441 0 0 1-.1278.43827zm.90688-7.66952a24.86724 24.86724 0 0 1-.73708 6.60356c0 .14634-.00729.29277-.01835.43912-.00159.02069-.0005.04138-.00259.06207.0041-.04138.02077-.08285.02077-.12423l.00008-.00067a25.54405 25.54405 0 0 0 .73717-6.97985z\" class=\"cls-1\"/><path d=\"M115.38108 338.55389a10.72708 10.72708 0 0 1-2.44579-.18814 15.93175 15.93175 0 0 1-3.19837-.75258 28.27655 28.27655 0 0 1-11.85283-6.96108L71.1682 297.91575l-10.47015-12.977 10.282 12.78887 26.71589 33.11254a29.8032 29.8032 0 0 0 12.041 6.96116l.00193.00051a19.70422 19.70422 0 0 0 3.19644.75207c.75249 0 1.69321.18814 2.44579.18814h63.96751l-.09382-.18814z\" class=\"cls-1\"/><path d=\"M52.87071 74.57478q-1.12257.64534-2.24541 1.37288c-.31371.23455-.616.4748-.905.71572-.11392.09114-.21687.19116-.3287.284l52.42906 105.67982-37.80173-77.1784h76.19663l.10162.18814h.08653l30.4786 56.25377-10.53582 19.37844-.37629.18814-30.85489-57.19449H95.4383l53.0554 112.50754 7.5256-14.29862a9.43241 9.43241 0 0 1 17.44212 1.836c.01734.05671.02916.11459.04541.17155.04825.17952.09842.35828.136.54056a9.317 9.317 0 0 1-.87915 6.48268l-13.7342 25.77509-10.48715 19.4761 30.80622 61.80026 28.56713-56.39696a9.50054 9.50054 0 0 1-10.06018 2.183 9.23964 9.23964 0 0 1-5.33715-12.95188l6.773-13.54613a9.348 9.348 0 0 1 5.83716-4.40547 9.36462 9.36462 0 0 1 11.46049 11.76803l12.7933-25.41136a9.61839 9.61839 0 0 1-2.6682 1.79834 9.43238 9.43238 0 0 1-12.74781-12.53488l13.54613-26.904a9.12 9.12 0 0 1 6.48913-5.03114 9.25737 9.25737 0 0 1 11.0551 11.65187l12.52793-24.64738a9.52394 9.52394 0 0 1-7.76887 2.94229 8.77683 8.77683 0 0 1-4.61824-1.66012 9.26028 9.26028 0 0 1-3.19837-11.85284l13.358-26.904a9.18734 9.18734 0 0 1 2.36185-3.05445 9.54993 9.54993 0 0 1 10.43162-1.2727 9.1824 9.1824 0 0 1 4.13909 12.60533l-3.85931 7.815 16.76712-33.09294a9.52346 9.52346 0 0 1-5.00593 2.88926c-.07254.01617-.14491.02253-.21746.03694a9.03611 9.03611 0 0 1-6.74379-.97758 9.56425 9.56425 0 0 1-4.51529-6.20867h-11.2884a9.407 9.407 0 1 1 0-18.81392h39.34207L310.482 79.48546a19.91834 19.91834 0 0 0-2.25765-1.88136c-1.317-.94072-2.63394-1.88144-3.951-2.634L188.37926 19.46888q-.5643-.28221-1.12878-.56443c-.75257-.18814-1.50515-.56443-2.44587-.75258-.12808-.032-.25625-.057-.38442-.08812-.628-.0924-1.29682-.14408-1.87323-.28816a22.76746 22.76746 0 0 0-3.57465-.18815 45.53642 45.53642 0 0 0-7.14932.37629c-.75257.18814-1.50515.18814-2.44579.37629L53.67121 74.21759c-.27543.10328-.53763.23103-.8005.35719zM203.61861 136.68l16.55639-31.60752h6.02045a8.73982 8.73982 0 0 1 4.32723 1.12886 9.53885 9.53885 0 0 1 3.951 12.79347L220.175 145.52256a9.24039 9.24039 0 0 1-10.00079 4.89116 9.28868 9.28868 0 0 1-6.5556-13.73372zm-29.16168 54.7487l14.11056-26.52774a9.59585 9.59585 0 0 1 16.93251 9.0307l-14.11048 26.52766a9.53881 9.53881 0 0 1-12.79355 3.951 9.3451 9.3451 0 0 1-4.38311-4.85314 9.95367 9.95367 0 0 1 .24407-8.12853z\" class=\"cls-2\"/><path d=\"M187.25048 18.90445a9.31469 9.31469 0 0 0-2.44587-.75258 12.16962 12.16962 0 0 1 2.44587.75258z\" class=\"cls-2\"/><path d=\"M187.25048 18.90445a12.16962 12.16962 0 0 0-2.44587-.75258c.94072.18813 1.6933.56444 2.44587.75258zM50.6253 75.94766q1.1227-.7254 2.24541-1.37288a13.59871 13.59871 0 0 0-2.24541 1.37288z\" class=\"cls-2\"/><path d=\"M353.00158 212.31215l-.18815-.75257L323.84 87.1992v-.56443a31.87872 31.87872 0 0 0-15.8038-18.43771h-.37628l-115.89406-55.5012a27.43753 27.43753 0 0 0-12.79355-2.63394 45.80265 45.80265 0 0 0-11.2884.94064l-.75258.18815-.75249.37628L50.2847 67.44456a28.234 28.234 0 0 0-15.61555 19.19021L6.26 211.55958v.37629a31.70153 31.70153 0 0 0 5.456 23.89372l.18814.18815.18815.18814 79.95943 99.33779.18806.37629.37629.18814a34.82016 34.82016 0 0 0 22.38866 10.15953H243.128c9.97139 0 18.24956-3.95094 23.51743-10.91211l80.14757-99.14964.18814-.18814.18814-.18815a32.69776 32.69776 0 0 0 5.8323-23.51744zm-8.08995 10.15954c0 .04188-.01684.08368-.02094.12557-.00419.04172-.01441.08352-.02086.12515a5.30229 5.30229 0 0 0-.14635.87814v-.00067l-.00008.00067a26.42206 26.42206 0 0 1-3.7628 8.09l-20.31907 25.21065-59.82853 73.93895a19.24954 19.24954 0 0 1-12.79347 7.5256 39.60816 39.60816 0 0 1-4.70352.37628h-127.935a10.72708 10.72708 0 0 1-2.44579-.18814 19.67872 19.67872 0 0 1-3.19837-.75249l.00193.00042-.00193-.00051a29.8032 29.8032 0 0 1-12.041-6.96116l-26.7158-33.11254-10.282-12.78887-42.96155-53.24815a26.87508 26.87508 0 0 1-4.32714-11.10026 11.57224 11.57224 0 0 1-.18815-2.44587 21.45879 21.45879 0 0 1 .37621-4.89158L42.0066 88.32807a20.05066 20.05066 0 0 1 7.14931-11.47655l.04306.08645c.11334-.08905.21763-.188.33315-.27459a17.2548 17.2548 0 0 1 3.95094-2.44579L169.3772 18.34a9.44272 9.44272 0 0 1 2.25765-.37629 47.95077 47.95077 0 0 1 7.33746-.37629 22.76746 22.76746 0 0 1 3.57465.18815 8.01732 8.01732 0 0 1 1.87323.28816c.192.02823.3964.04406.57256.08812a12.1699 12.1699 0 0 1 2.44588.75258q.56444.28221 1.12878.56443l115.89414 55.5012a20.6252 20.6252 0 0 1 3.95094 2.634 19.93238 19.93238 0 0 1 2.25765 1.88136c.02052.02052.03937.04372.05989.06433l.12834-.25248a23.64466 23.64466 0 0 1 5.07972 7.14931c.18815.75258.56444 1.50507.75258 2.25765l28.78539 124.17227c.10421.8856.15506 1.75462.17265 2.61534a25.54405 25.54405 0 0 1-.73717 6.97985l-.00008.00067z\" class=\"cls-3\"/><path d=\"M49.344 76.85152c-.04473.0377-.08519.07941-.12967.11736l.04582.092c.0444-.03736.0867-.0764.13144-.11351zM115.38108 338.742c-.75258 0-1.6933-.18814-2.44579-.18814a10.72742 10.72742 0 0 0 2.44579.18814zm-5.64416-.9406a19.67872 19.67872 0 0 0 3.19837.75249 19.70422 19.70422 0 0 1-3.19644-.75207zm235.15377-115.20414c.0041-.04189.02094-.08369.02094-.12557l-.00017.00067c0 .04138-.01667.08285-.02077.12423zm-.16721 1.00262v.00067a5.30229 5.30229 0 0 1 .14635-.87814l-.00017.00067a5.29569 5.29569 0 0 0-.14618.8768z\" class=\"cls-3\"/><path d=\"M115.38108 338.742a10.72742 10.72742 0 0 1-2.44579-.18814 10.72708 10.72708 0 0 0 2.44579.18814zM49.344 76.85152l.18815-.18814c-.11552.08661-.21981.18554-.33315.27459l.01533.03091c.04445-.03795.08491-.07966.12967-.11736zm133.203-59.07593c.57641.14408 1.24522.19576 1.87323.28816a8.01732 8.01732 0 0 0-1.87323-.28816z\" class=\"cls-3\"/><path d=\"M49.39156 76.94735c.11183-.09281.21478-.19283.3287-.284.28909-.24092.59133-.48117.905-.71572-.36439.23547-.72879.45545-1.09318.71572l-.18815.18814zm4.27965-2.72976L169.3772 18.34 53.48306 74.21759c-.20414.1166-.4082.23983-.61235.35719.26287-.12616.52507-.25391.8005-.35719z\" class=\"cls-3\"/><path d=\"M52.87071 74.57478c.20415-.11736.40821-.24059.61235-.35719a17.2548 17.2548 0 0 0-3.95094 2.44579c.36439-.26027.72879-.48025 1.09318-.71572a13.59871 13.59871 0 0 1 2.24541-1.37288z\" class=\"cls-3\"/><path d=\"M179.255 338.554l.094.188v-.188h-.094zm-81.37091-7.90191a28.27655 28.27655 0 0 0 11.85283 6.96108 15.93175 15.93175 0 0 0 3.19837.75258 10.72708 10.72708 0 0 0 2.44579.18814h63.87369L148.4937 276.8441l-.18814.37629-.02664-.0542-.16151.24226-28.90618-59.3558-10.03874-20.41541-5.88156-12.00824L49.26012 77.06086a21.01233 21.01233 0 0 0-7.06546 11.26721L13.59742 213.25288a21.45879 21.45879 0 0 0-.37621 4.89158 9.15983 9.15983 0 0 0 .18815 2.25773 25.00431 25.00431 0 0 0 4.32714 11.10017l53.4317 66.22525-29.61294-36.59907 19.14279 23.8102 10.47015 12.977z\" class=\"cls-4\"/><path d=\"M71.168 297.728l-53.431-66.226 23.818 29.627 29.613 36.599zM42.19466 88.32807a21.01233 21.01233 0 0 1 7.06546-11.26721l-.04582-.092a20.62117 20.62117 0 0 0-7.01964 11.35921zM17.737 231.691l42.961 53.248-19.143-23.81-23.818-29.438z\" class=\"cls-4\"/><path d=\"M49.199 76.938l-.04306-.08645a20.05066 20.05066 0 0 0-7.14934 11.47652L13.59742 213.25288 42.19466 88.32807A19.29686 19.29686 0 0 1 49.199 76.938zM13.40936 220.40219a9.15983 9.15983 0 0 1-.18815-2.25773 11.57224 11.57224 0 0 0 .18815 2.44587 25.15547 25.15547 0 0 0 4.32714 10.912 25.00431 25.00431 0 0 1-4.32714-11.10014z\" class=\"cls-4\"/><path d=\"M17.7365 231.50236a25.15547 25.15547 0 0 1-4.32714-10.912 26.87508 26.87508 0 0 0 4.32714 11.10026l23.81876 29.438zM42.19466 88.32807a20.62117 20.62117 0 0 1 7.01964-11.35919l-.0153-.03088a19.29686 19.29686 0 0 0-7.00434 11.39007z\" class=\"cls-4\"/><path d=\"M287.529 125.2034l-.94072 2.634-.18814.18815a9.499 9.499 0 0 1-1.19756 1.62092L268.33384 162.94l-3.56979 7.22889-5.64416 11.85275a9.87788 9.87788 0 0 1-1.52082 2.08835L244.93846 209.156a9.70415 9.70415 0 0 1-.49348 1.2747l-3.38652 6.585-9.78324 20.31907a9.87179 9.87179 0 0 1-1.71944 2.299L216.582 265.226c-.05545.17282-.10379.3463-.1698.51786-.18814.18814-.18814.18814-.18814.37629l-6.58488 13.73419a9.87935 9.87935 0 0 1-1.52367 2.09145l-28.57873 56.42h63.77937a39.608 39.608 0 0 0 4.70352-.37629 19.68752 19.68752 0 0 0 12.79347-7.5256l59.82847-73.9389 20.31907-25.21073a24.52526 24.52526 0 0 0 3.7628-8.09c.01048-.02094.00905-.04188.01835-.06282a1.85575 1.85575 0 0 1 .15145-.62684c.01106-.14635.01835-.29278.01835-.43912a24.86724 24.86724 0 0 0 .73708-6.60356c-.02446-.75375-.08067-1.50222-.17273-2.239l-.05529-.2374L316.50244 88.8925a10.60693 10.60693 0 0 0-.75257-2.25773 23.56266 23.56266 0 0 0-5.01984-7.085L288.01761 124.236zm28.97354-36.12284c-.18815-.75249-.56444-1.50507-.75258-2.25765a10.60819 10.60819 0 0 1 .75257 2.25765z\" class=\"cls-5\"/><path d=\"M344.86966 222.72308c.00729-.04214.01692-.08436.021-.12649.00209-.02069.001-.04138.00259-.06207a1.85575 1.85575 0 0 0-.15145.62684 1.70441 1.70441 0 0 0 .12786-.43828zM315.75 86.82291c.18814.75258.56443 1.50516.75258 2.25765a10.60819 10.60819 0 0 0-.75258-2.25765zm-86.19422 152.81086c-.05595.05563-.11635.1053-.17348.15942l-12.7933 25.41136-.007.02144zM257.59907 184.11l-.01449.01608-12.52793 24.64731c-.03493.12808-.07773.25524-.11819.38266zm27.60393-54.464l-.006.006-16.767 33.093-.096.195 16.869-33.294z\" class=\"cls-5\"/><path d=\"M316.50244 88.8925l28.91825 124.123.05529.2374c.09206.73674.14827 1.48521.17273 2.239-.01759-.86072-.06844-1.72974-.17265-2.61534L316.69067 88.70427c-.18814-.75258-.56443-1.50507-.75258-2.25765a23.64466 23.64466 0 0 0-5.07972-7.14931l-.12834.25248a23.56266 23.56266 0 0 1 5.01984 7.085 10.60693 10.60693 0 0 1 .75257 2.25771zm28.36722 133.83058l.00017-.00067c.00645-.04163.01667-.08343.02086-.12515v-.00067c-.00411.04213-.01369.08435-.02103.12649z\" class=\"cls-5\"/><path d=\"M188.75555 165.089l-14.11048 26.5278a9.34134 9.34134 0 1 0 16.55636 8.65438l14.11048-26.52766a9.43369 9.43369 0 0 0-1.61322-11.07278 9.5211 9.5211 0 0 0-14.94314 2.41831z\" class=\"cls-3\"/><path d=\"M174.64507 191.6168l14.11048-26.5278a9.5211 9.5211 0 0 1 14.94314-2.41831 9.511 9.511 0 0 0-15.1312 2.23017l-14.11056 26.52774a9.95367 9.95367 0 0 0-.244 8.12848 9.3729 9.3729 0 0 1 .43214-7.94028zm41.76625 73.94577l-.1873.36942c.18471-.18471.18722-.19124.1873-.36942zM209.451 279.478l6.773-13.54605a9.59732 9.59732 0 0 0-4.13909-11.47654 9.29443 9.29443 0 0 0-6.95639-1.019 9.49468 9.49468 0 0 0-5.649 4.40547l-6.773 13.54613a9.40792 9.40792 0 0 0 4.32723 12.60533c.27149.13034.546.24351.82177.34655a9.5626 9.5626 0 0 0 10.65477-3.35678l-.59459 1.17385c.06853-.0681.133-.14081.19979-.21126l.583-1.15082c.18807-.3762.56436-.94064.75251-1.31688z\" class=\"cls-3\"/><path d=\"M212.08493 254.45545A9.59732 9.59732 0 0 1 216.224 265.932l.1873-.36942.00084-.00687.17684-.35115a9.36462 9.36462 0 0 0-11.46046-11.76806 9.29443 9.29443 0 0 1 6.95641 1.01895zM197.8555 284.34a9.50054 9.50054 0 0 0 10.06018-2.18293l.59459-1.17385A9.5626 9.5626 0 0 1 197.8555 284.34z\" class=\"cls-3\"/><path d=\"M216.224 266.12014c0-.18815 0-.18815.18814-.37629.066-.17156.11435-.345.1698-.51786l-.17064.33658c-.00008.17818-.00259.18471-.1873.36942l-6.773 13.54601c-.18814.3762-.56443.94064-.75257 1.31692l-.583 1.15082a9.87935 9.87935 0 0 0 1.52367-2.09145z\" class=\"cls-3\"/><path d=\"M216.41132 265.56257l.17064-.33658.007-.02144-.17684.35115zM148.4937 276.656l10.53582-19.56658 13.7342-25.77508a9.43315 9.43315 0 0 0 .87915-6.29454c-.03762-.18228-.08779-.361-.136-.54056-.01625-.057-.02807-.11484-.04541-.17155a9.48444 9.48444 0 0 0-17.44212-1.64782l-7.5256 14.29863L95.4383 124.26277h33.677l30.85489 57.19449.37629-.18814 10.34768-19.37844-30.377-56.25377H64.207l45.1536 92.18848 9.8506 20.22726 29.06769 59.11354.21478-.32209.04867-.09047z\" class=\"cls-3\"/><path d=\"M101.821 182.627l1.47 3.002 6.07 12.196-7.54-15.198zm70.94272 48.6873l-13.7342 25.77508L148.4937 276.656l.04867.09767 10.48715-19.4761 13.7342-25.77509a9.317 9.317 0 0 0 .87915-6.48268 9.43315 9.43315 0 0 1-.87915 6.2945z\" class=\"cls-3\"/><path fill=\"none\" stroke=\"#fff\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"0\" d=\"M173.46142 224.30765c.01734.05671.02916.11459.04541.17155\"/><path d=\"M64.207 105.637h76.11l-.101-.188H64.019l37.802 77.178 7.54 15.198-45.154-92.188zm39.084 79.992l5.881 12.008 10.039 20.416-9.85-20.228-6.07-12.196zm45.015 91.591l.188-.376-.215.322.027.054zm137.70644-149.17684l.19954-.39388 1.50515-2.82216.30048-.59116 8.91834-17.65833h-.16729l-9.4279 18.62577 9.407-18.62577h-39.13303a9.407 9.407 0 0 0 0 18.81392h11.2884a9.46073 9.46073 0 0 0 11.07094 7.18625 9.65911 9.65911 0 0 0 6.03837-4.53464z\" class=\"cls-3\"/><path d=\"M296.769 106.578h-.021l-9.407 18.625 9.428-18.625zm-16.79493 25.9998c.07255-.01441.14492-.02077.21746-.03694a9.52346 9.52346 0 0 0 5.00593-2.88926l.815-1.60844a9.65911 9.65911 0 0 1-6.03839 4.53464zm6.23793-4.92852l-.19954.39388.0114-.01759-.82127 1.62092a9.499 9.499 0 0 0 1.19756-1.62092l.18814-.18815.94072-2.634.48862-.96744-.30048.59116z\" class=\"cls-3\"/><path d=\"M286.02384 128.02557l-.0114.01759-.815 1.60844.00511-.00511zM258.17 182.97423l.19727-.38819a4.11213 4.11213 0 0 0 .56443-.94072l5.6442-11.28832 7.5256-15.23935a9.4245 9.4245 0 0 0-14.38256-11.52077 8.93718 8.93718 0 0 0-2.17371 2.86631v.18814l-13.358 26.904a9.408 9.408 0 0 0 4.32723 12.60533 9.61806 9.61806 0 0 0 3.30123.90763 9.47039 9.47039 0 0 0 8.35431-4.09406z\" class=\"cls-3\"/><path d=\"M267.96241 142.51234a9.18239 9.18239 0 0 1 4.13909 12.60532l-7.5256 15.23934.18815-.37629 3.66629-7.23618 3.85931-7.815a9.1824 9.1824 0 0 0-4.13909-12.60533 9.54993 9.54993 0 0 0-10.43162 1.2727 9.21467 9.21467 0 0 1 10.24347-1.08456zm-18.1467 44.55599a9.52394 9.52394 0 0 0 7.76887-2.94229l.58546-1.15181a9.47039 9.47039 0 0 1-8.35433 4.0941z\" class=\"cls-3\"/><path d=\"M258.93174 181.64532a4.11213 4.11213 0 0 1-.56443.94072l-.19727.38819.00913-.0119-.5801 1.14763a9.87788 9.87788 0 0 0 1.52082-2.08835l5.64416-11.85275 3.56979-7.22889-3.75794 7.417z\" class=\"cls-3\"/><path d=\"M264.576 170.357l3.758-7.417.096-.195-3.666 7.236-.188.376zm-6.39683 12.60533l-.00913.0119-.58546 1.15181.01449-.01608zm-28.47436 56.19011l.44154-.877c.18815-.37629.56444-.94072.75258-1.317l10.15953-20.31907 3.80151-7.47911a9.49138 9.49138 0 0 0-10.85842-12.0388 9.3473 9.3473 0 0 0-6.301 4.843l-13.358 26.90394a9.4298 9.4298 0 0 0 12.37152 12.72311 9.11894 9.11894 0 0 0 2.99074-2.43907z\" class=\"cls-3\"/><path d=\"M226.7141 241.59153a9.61839 9.61839 0 0 0 2.6682-1.79834l.32251-.64075a9.11894 9.11894 0 0 1-2.99071 2.43909zm13.40364-43.578a9.24534 9.24534 0 0 1 4.74226 11.14675l.19668-.38693a9.25737 9.25737 0 0 0-11.0551-11.65187 9.302 9.302 0 0 1 6.11616.89205z\" class=\"cls-3\"/><path d=\"M244.25683 210.61894l-3.38651 6.58488 3.82906-7.57494c.06015-.15556.10924-.31212.16059-.4686l-3.80151 7.47911-10.15953 20.31907c-.18814.37629-.56443.94072-.75258 1.317l-.44154.877c.08377-.10505.17373-.20306.2534-.31254l-.40243.79387a9.87179 9.87179 0 0 0 1.71944-2.299l9.78324-20.31907 3.38652-6.585a9.70415 9.70415 0 0 0 .49348-1.2747l-.23908.47287a9.76715 9.76715 0 0 1-.44255.99005z\" class=\"cls-3\"/><path d=\"M244.69938 209.62888l.23908-.47287c.04046-.12742.08326-.25458.11819-.38266l-.19668.38693c-.05135.15648-.10044.31304-.16059.4686zm-14.74117 29.21102c-.07967.10948-.16963.20749-.2534.31254l-.32251.64075c.05713-.05412.11753-.10379.17348-.15942zm14.29862-28.22096a9.76715 9.76715 0 0 0 .44255-.99006l-3.82906 7.57494zm-36.68728-61.14552a9.22039 9.22039 0 0 0 2.60462.9403 9.57056 9.57056 0 0 0 10.00079-4.89116l14.11048-26.52775a9.42567 9.42567 0 0 0-3.95094-12.79347 8.73961 8.73961 0 0 0-4.32724-1.12886H220.175L203.61861 136.68a9.76617 9.76617 0 0 0 3.95094 12.79342z\" class=\"cls-3\"/><path d=\"M210.17417 150.41372a9.24039 9.24039 0 0 0 10.00079-4.89116 9.57056 9.57056 0 0 1-10.00079 4.89116z\" class=\"cls-3\"/></svg>", "svgComplete": "", "svgWhite": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"6.10 10.85 345.80 334.30\"><defs><style>.cls-1{fill:#fff}</style></defs><path d=\"M115.09287 338.742l-1.852-.07941a14.868 14.868 0 0 0 1.852.07941z\" class=\"cls-1\"/><path d=\"M345.18784 212.87648L316.40244 88.7043c-.18817-.75263-.56445-1.50513-.75256-2.25769a23.64542 23.64542 0 0 0-5.07978-7.14929l-.12829.2525c-.02051-.02063-.03937-.04382-.05988-.06439a19.93834 19.93834 0 0 0-2.25769-1.88135 20.626 20.626 0 0 0-3.95092-2.634L188.27915 19.46889q-.56424-.28227-1.12878-.56446a12.17166 12.17166 0 0 0-2.44587-.75256c-.17614-.04407-.38055-.05988-.57257-.08814-.628-.0924-1.29681-.144-1.87323-.28814a22.76456 22.76456 0 0 0-3.57464-.18817 47.94466 47.94466 0 0 0-7.33747.37634 9.44163 9.44163 0 0 0-2.25759.37624L53.383 74.2176c-.27545.10328-.5376.231-.80047.35718a13.604 13.604 0 0 0-2.24543 1.37287c-.31372.23455-.616.47479-.905.71576-.114.09112-.21692.1911-.32873.28393-.04474.03711-.087.07617-.13141.11353l-.04584-.092-.01532-.031-.04309-.08642a20.05086 20.05086 0 0 0-7.14929 11.47656L13.30918 213.25288a21.45849 21.45849 0 0 0-.37622 4.8916 11.57114 11.57114 0 0 0 .18817 2.44587 26.87493 26.87493 0 0 0 4.32715 11.10022l42.96155 53.24816 10.282 12.78888 26.71586 33.11249a29.80244 29.80244 0 0 0 12.041 6.96118l.00189.00049a19.69944 19.69944 0 0 0 3.19647.75208c.7525 0 1.69318.18817 2.4458.18817h127.93496a39.614 39.614 0 0 0 4.70349-.37628 19.24937 19.24937 0 0 0 12.79346-7.52564l59.82855-73.9389 20.319-25.21063a26.421 26.421 0 0 0 3.76282-8.09l.00012-.00067a3.98126 3.98126 0 0 1 .12451-.77362c.007-.0343.01569-.06861.02161-.10291l.00006-.00024.00012-.00067c.00647-.04163.01666-.08344.02087-.12519v-.00067c.00409-.04138.02076-.08282.02076-.1242l.00012-.00068a25.54436 25.54436 0 0 0 .73712-6.97985c-.01755-.86075-.06842-1.72977-.17258-2.61539zm-87.877-28.76654l-.01447.01611a9.5482 9.5482 0 0 1-11.07013 2.03467 9.408 9.408 0 0 1-4.32721-12.60535l13.358-26.90405v-.18811a8.937 8.937 0 0 1 2.17371-2.86633 9.55005 9.55005 0 0 1 10.43164-1.27271 9.18249 9.18249 0 0 1 4.1391 12.60535l-3.85931 7.815-.0965.19544-3.56982 7.22888-5.64417 11.85278a9.87529 9.87529 0 0 1-1.52085 2.08832zm-13.15412 26.3208l-3.38647 6.585-9.78326 20.319a9.87121 9.87121 0 0 1-1.71943 2.29907c-.056.05561-.11633.10529-.17346.15937a9.61743 9.61743 0 0 1-2.66821 1.79834 9.42977 9.42977 0 0 1-12.37152-12.72309l13.358-26.90393a9.56395 9.56395 0 0 1 12.60535-4.1391 9.45293 9.45293 0 0 1 4.75073 10.94794c-.03491.12811-.0777.25525-.11817.38269a9.69652 9.69652 0 0 1-.49357 1.27471zM207.62748 282.157a9.50065 9.50065 0 0 1-10.06024 2.183c-.27576-.10309-.55024-.21625-.82172-.34656a9.408 9.408 0 0 1-4.32727-12.60534l6.773-13.54615a9.49475 9.49475 0 0 1 5.649-4.40545 9.36464 9.36464 0 0 1 11.46045 11.76806l-.007.02143c-.05548.17285-.10382.34631-.1698.51788-.18817.18811-.18817.18811-.18817.37628l-6.58484 13.73419a9.87977 9.87977 0 0 1-1.52368 2.09143c-.06674.07042-.13121.14317-.19973.21123zm2.25852-131.74329a9.21794 9.21794 0 0 1-2.60461-.94031 9.76615 9.76615 0 0 1-3.951-12.79345l16.5564-31.60749h5.83221a8.73886 8.73886 0 0 1 4.32721 1.12891 9.4256 9.4256 0 0 1 3.951 12.79346l-14.11047 26.52771a9.24039 9.24039 0 0 1-10.00074 4.89117zm77.3548-25.21033l-.94074 2.634-.18811.18817a9.4997 9.4997 0 0 1-1.19757 1.62091l-.00512.00513a9.52354 9.52354 0 0 1-5.00592 2.88922c-.07251.01617-.1449.02252-.21747.03692a9.46066 9.46066 0 0 1-11.07093-7.18622h-11.28843a9.407 9.407 0 1 1 0-18.8139h39.32123l-8.91833 17.65839zm-98.96149 39.69751a9.46795 9.46795 0 1 1 16.74439 8.84265l-14.11048 26.52765a9.3656 9.3656 0 0 1-16.98852-.714 9.95378 9.95378 0 0 1 .244-8.12848zm-86.74688 17.72626l-37.8018-77.17841h76.19665l.10163.18818 30.377 56.25378-10.34771 19.37842-.37628.18817-30.85486-57.19452h-33.677l53.05542 112.69568 7.52557-14.29865a9.48448 9.48448 0 0 1 17.44214 1.64783c.01734.0567.02918.11463.04541.17157.04822.17951.09839.35828.136.54059a9.317 9.317 0 0 1-.87915 6.48267l-13.73419 25.77508-10.48718 19.47608-.04865.09045-.18817.37628-.02661-.0542-29.06765-59.11352-10.03876-20.41541-5.88152-12.00822z\" class=\"cls-1\"/></svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/kubevirt/ssp-operator/main/config/crd/bases"}, "configuration": null, "component": {"version": "ssp.kubevirt.io/v1beta2", "kind": "SSP", "schema": "{\n \"description\": \"SSP is the Schema for the ssps API\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"SSPSpec defines the desired state of SSP\",\n   \"properties\": {\n    \"commonInstancetypes\": {\n     \"description\": \"CommonInstancetypes is ignored.\\nDeprecated: This field is ignored.\",\n     \"properties\": {\n      \"url\": {\n       \"description\": \"URL of a remote Kustomize target from which to generate and deploy resources.\\n\\nThe following caveats apply to the provided URL:\\n\\n* Only 'https://' and 'git://' URLs are supported.\\n\\n* The URL must include '?ref=$ref' or '?version=$ref' pinning it to a specific\\n  reference. It is recommended that the reference be a specific commit or tag\\n  to ensure the generated contents does not change over time. As such it is\\n  recommended not to use branches as the ref for the time being.\\n\\n* Only VirtualMachineClusterPreference and VirtualMachineClusterInstancetype\\n  resources generated from the URL are deployed by the operand.\\n\\nSee the following Kustomize documentation for more details:\\n\\nremote targets\\nhttps://github.com/kubernetes-sigs/kustomize/blob/master/examples/remoteBuild.md\",\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"commonTemplates\": {\n     \"description\": \"CommonTemplates is the configuration of the common templates operand\",\n     \"properties\": {\n      \"dataImportCronTemplates\": {\n       \"description\": \"DataImportCronTemplates defines a list of DataImportCrons managed by the SSP\\nOperator. This is intended for images used by CommonTemplates.\",\n       \"items\": {\n        \"description\": \"DataImportCronTemplate defines the template type for DataImportCrons.\\nIt requires metadata.name to be specified while leaving namespace as optional.\",\n        \"properties\": {\n         \"metadata\": {\n          \"properties\": {\n           \"annotations\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"object\"\n           },\n           \"finalizers\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"labels\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"object\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"namespace\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"spec\": {\n          \"description\": \"DataImportCronSpec defines specification for DataImportCron\",\n          \"properties\": {\n           \"garbageCollect\": {\n            \"description\": \"GarbageCollect specifies whether old PVCs should be cleaned up after a new PVC is imported.\\nOptions are currently \\\"Outdated\\\" and \\\"Never\\\", defaults to \\\"Outdated\\\".\",\n            \"type\": \"string\"\n           },\n           \"importsToKeep\": {\n            \"description\": \"Number of import PVCs to keep when garbage collecting. Default is 3.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"managedDataSource\": {\n            \"description\": \"ManagedDataSource specifies the name of the corresponding DataSource this cron will manage.\\nDataSource has to be in the same namespace.\",\n            \"type\": \"string\"\n           },\n           \"retentionPolicy\": {\n            \"description\": \"RetentionPolicy specifies whether the created DataVolumes and DataSources are retained when their DataImportCron is deleted. Default is RatainAll.\",\n            \"type\": \"string\"\n           },\n           \"schedule\": {\n            \"description\": \"Schedule specifies in cron format when and how often to look for new imports\",\n            \"type\": \"string\"\n           },\n           \"template\": {\n            \"description\": \"Template specifies template for the DVs to be created\",\n            \"properties\": {\n             \"apiVersion\": {\n              \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n              \"type\": \"string\"\n             },\n             \"kind\": {\n              \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n              \"type\": \"string\"\n             },\n             \"metadata\": {\n              \"properties\": {\n               \"annotations\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"object\"\n               },\n               \"finalizers\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               },\n               \"labels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"object\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"spec\": {\n              \"description\": \"DataVolumeSpec defines the DataVolume type specification\",\n              \"properties\": {\n               \"checkpoints\": {\n                \"description\": \"Checkpoints is a list of DataVolumeCheckpoints, representing stages in a multistage import.\",\n                \"items\": {\n                 \"description\": \"DataVolumeCheckpoint defines a stage in a warm migration.\",\n                 \"properties\": {\n                  \"current\": {\n                   \"description\": \"Current is the identifier of the snapshot created for this checkpoint.\",\n                   \"type\": \"string\"\n                  },\n                  \"previous\": {\n                   \"description\": \"Previous is the identifier of the snapshot from the previous checkpoint.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"current\",\n                  \"previous\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"contentType\": {\n                \"description\": \"DataVolumeContentType options: \\\"kubevirt\\\", \\\"archive\\\"\",\n                \"enum\": [\n                 \"kubevirt\",\n                 \"archive\"\n                ],\n                \"type\": \"string\"\n               },\n               \"finalCheckpoint\": {\n                \"description\": \"FinalCheckpoint indicates whether the current DataVolumeCheckpoint is the final checkpoint.\",\n                \"type\": \"boolean\"\n               },\n               \"preallocation\": {\n                \"description\": \"Preallocation controls whether storage for DataVolumes should be allocated in advance.\",\n                \"type\": \"boolean\"\n               },\n               \"priorityClassName\": {\n                \"description\": \"PriorityClassName for Importer, Cloner and Uploader pod\",\n                \"type\": \"string\"\n               },\n               \"pvc\": {\n                \"description\": \"PVC is the PVC specification\",\n                \"properties\": {\n                 \"accessModes\": {\n                  \"description\": \"accessModes contains the desired access modes the volume should have.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"dataSource\": {\n                  \"description\": \"dataSource field can be used to specify either:\\n* An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)\\n* An existing PVC (PersistentVolumeClaim)\\nIf the provisioner or an external controller can support the specified data source,\\nit will create a new volume based on the contents of the specified data source.\\nWhen the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef,\\nand dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified.\\nIf the namespace is specified, then dataSourceRef will not be copied to dataSource.\",\n                  \"properties\": {\n                   \"apiGroup\": {\n                    \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n                    \"type\": \"string\"\n                   },\n                   \"kind\": {\n                    \"description\": \"Kind is the type of resource being referenced\",\n                    \"type\": \"string\"\n                   },\n                   \"name\": {\n                    \"description\": \"Name is the name of resource being referenced\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"kind\",\n                   \"name\"\n                  ],\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"dataSourceRef\": {\n                  \"description\": \"dataSourceRef specifies the object from which to populate the volume with data, if a non-empty\\nvolume is desired. This may be any object from a non-empty API group (non\\ncore object) or a PersistentVolumeClaim object.\\nWhen this field is specified, volume binding will only succeed if the type of\\nthe specified object matches some installed volume populator or dynamic\\nprovisioner.\\nThis field will replace the functionality of the dataSource field and as such\\nif both fields are non-empty, they must have the same value. For backwards\\ncompatibility, when namespace isn't specified in dataSourceRef,\\nboth fields (dataSource and dataSourceRef) will be set to the same\\nvalue automatically if one of them is empty and the other is non-empty.\\nWhen namespace is specified in dataSourceRef,\\ndataSource isn't set to the same value and must be empty.\\nThere are three important differences between dataSource and dataSourceRef:\\n* While dataSource only allows two specific types of objects, dataSourceRef\\n  allows any non-core object, as well as PersistentVolumeClaim objects.\\n* While dataSource ignores disallowed values (dropping them), dataSourceRef\\n  preserves all values, and generates an error if a disallowed value is\\n  specified.\\n* While dataSource only allows local objects, dataSourceRef allows objects\\n  in any namespaces.\\n(Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\\n(Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\n                  \"properties\": {\n                   \"apiGroup\": {\n                    \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n                    \"type\": \"string\"\n                   },\n                   \"kind\": {\n                    \"description\": \"Kind is the type of resource being referenced\",\n                    \"type\": \"string\"\n                   },\n                   \"name\": {\n                    \"description\": \"Name is the name of resource being referenced\",\n                    \"type\": \"string\"\n                   },\n                   \"namespace\": {\n                    \"description\": \"Namespace is the namespace of resource being referenced\\nNote that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.\\n(Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"kind\",\n                   \"name\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"resources\": {\n                  \"description\": \"resources represents the minimum resources the volume should have.\\nIf RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements\\nthat are lower than previous value but must still be higher than capacity recorded in the\\nstatus field of the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n                  \"properties\": {\n                   \"limits\": {\n                    \"additionalProperties\": {\n                     \"anyOf\": [\n                      {\n                       \"type\": \"integer\"\n                      },\n                      {\n                       \"type\": \"string\"\n                      }\n                     ],\n                     \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                     \"x-kubernetes-int-or-string\": true\n                    },\n                    \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                    \"type\": \"object\"\n                   },\n                   \"requests\": {\n                    \"additionalProperties\": {\n                     \"anyOf\": [\n                      {\n                       \"type\": \"integer\"\n                      },\n                      {\n                       \"type\": \"string\"\n                      }\n                     ],\n                     \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                     \"x-kubernetes-int-or-string\": true\n                    },\n                    \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value. Requests cannot exceed Limits.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"selector\": {\n                  \"description\": \"selector is a label query over volumes to consider for binding.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"storageClassName\": {\n                  \"description\": \"storageClassName is the name of the StorageClass required by the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n                  \"type\": \"string\"\n                 },\n                 \"volumeAttributesClassName\": {\n                  \"description\": \"volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim.\\nIf specified, the CSI driver will create or update the volume with the attributes defined\\nin the corresponding VolumeAttributesClass. This has a different purpose than storageClassName,\\nit can be changed after the claim is created. An empty string value means that no VolumeAttributesClass\\nwill be applied to the claim but it's not allowed to reset this field to empty string once it is set.\\nIf unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass\\nwill be set by the persistentvolume controller if it exists.\\nIf the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be\\nset to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource\\nexists.\\nMore info: https://kubernetes.io/docs/concepts/storage/volume-attributes-classes/\\n(Beta) Using this field requires the VolumeAttributesClass feature gate to be enabled (off by default).\",\n                  \"type\": \"string\"\n                 },\n                 \"volumeMode\": {\n                  \"description\": \"volumeMode defines what type of volume is required by the claim.\\nValue of Filesystem is implied when not included in claim spec.\",\n                  \"type\": \"string\"\n                 },\n                 \"volumeName\": {\n                  \"description\": \"volumeName is the binding reference to the PersistentVolume backing this claim.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"source\": {\n                \"description\": \"Source is the src of the data for the requested DataVolume\",\n                \"properties\": {\n                 \"blank\": {\n                  \"description\": \"DataVolumeBlankImage provides the parameters to create a new raw blank image for the PVC\",\n                  \"type\": \"object\"\n                 },\n                 \"gcs\": {\n                  \"description\": \"DataVolumeSourceGCS provides the parameters to create a Data Volume from an GCS source\",\n                  \"properties\": {\n                   \"secretRef\": {\n                    \"description\": \"SecretRef provides the secret reference needed to access the GCS source\",\n                    \"type\": \"string\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the url of the GCS source\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"url\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"http\": {\n                  \"description\": \"DataVolumeSourceHTTP can be either an http or https endpoint, with an optional basic auth user name and password, and an optional configmap containing additional CAs\",\n                  \"properties\": {\n                   \"certConfigMap\": {\n                    \"description\": \"CertConfigMap is a configmap reference, containing a Certificate Authority(CA) public key, and a base64 encoded pem certificate\",\n                    \"type\": \"string\"\n                   },\n                   \"extraHeaders\": {\n                    \"description\": \"ExtraHeaders is a list of strings containing extra headers to include with HTTP transfer requests\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"secretExtraHeaders\": {\n                    \"description\": \"SecretExtraHeaders is a list of Secret references, each containing an extra HTTP header that may include sensitive information\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"secretRef\": {\n                    \"description\": \"SecretRef A Secret reference, the secret should contain accessKeyId (user name) base64 encoded, and secretKey (password) also base64 encoded\",\n                    \"type\": \"string\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the URL of the http(s) endpoint\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"url\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"imageio\": {\n                  \"description\": \"DataVolumeSourceImageIO provides the parameters to create a Data Volume from an imageio source\",\n                  \"properties\": {\n                   \"certConfigMap\": {\n                    \"description\": \"CertConfigMap provides a reference to the CA cert\",\n                    \"type\": \"string\"\n                   },\n                   \"diskId\": {\n                    \"description\": \"DiskID provides id of a disk to be imported\",\n                    \"type\": \"string\"\n                   },\n                   \"secretRef\": {\n                    \"description\": \"SecretRef provides the secret reference needed to access the ovirt-engine\",\n                    \"type\": \"string\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the URL of the ovirt-engine\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"diskId\",\n                   \"url\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"pvc\": {\n                  \"description\": \"DataVolumeSourcePVC provides the parameters to create a Data Volume from an existing PVC\",\n                  \"properties\": {\n                   \"name\": {\n                    \"description\": \"The name of the source PVC\",\n                    \"type\": \"string\"\n                   },\n                   \"namespace\": {\n                    \"description\": \"The namespace of the source PVC\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"name\",\n                   \"namespace\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"registry\": {\n                  \"description\": \"DataVolumeSourceRegistry provides the parameters to create a Data Volume from an registry source\",\n                  \"properties\": {\n                   \"certConfigMap\": {\n                    \"description\": \"CertConfigMap provides a reference to the Registry certs\",\n                    \"type\": \"string\"\n                   },\n                   \"imageStream\": {\n                    \"description\": \"ImageStream is the name of image stream for import\",\n                    \"type\": \"string\"\n                   },\n                   \"platform\": {\n                    \"description\": \"Platform describes the minimum runtime requirements of the image\",\n                    \"properties\": {\n                     \"architecture\": {\n                      \"description\": \"Architecture specifies the image target CPU architecture\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"pullMethod\": {\n                    \"description\": \"PullMethod can be either \\\"pod\\\" (default import), or \\\"node\\\" (node docker cache based import)\",\n                    \"type\": \"string\"\n                   },\n                   \"secretRef\": {\n                    \"description\": \"SecretRef provides the secret reference needed to access the Registry source\",\n                    \"type\": \"string\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the url of the registry source (starting with the scheme: docker, oci-archive)\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"s3\": {\n                  \"description\": \"DataVolumeSourceS3 provides the parameters to create a Data Volume from an S3 source\",\n                  \"properties\": {\n                   \"certConfigMap\": {\n                    \"description\": \"CertConfigMap is a configmap reference, containing a Certificate Authority(CA) public key, and a base64 encoded pem certificate\",\n                    \"type\": \"string\"\n                   },\n                   \"secretRef\": {\n                    \"description\": \"SecretRef provides the secret reference needed to access the S3 source\",\n                    \"type\": \"string\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the url of the S3 source\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"url\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"snapshot\": {\n                  \"description\": \"DataVolumeSourceSnapshot provides the parameters to create a Data Volume from an existing VolumeSnapshot\",\n                  \"properties\": {\n                   \"name\": {\n                    \"description\": \"The name of the source VolumeSnapshot\",\n                    \"type\": \"string\"\n                   },\n                   \"namespace\": {\n                    \"description\": \"The namespace of the source VolumeSnapshot\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"name\",\n                   \"namespace\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"upload\": {\n                  \"description\": \"DataVolumeSourceUpload provides the parameters to create a Data Volume by uploading the source\",\n                  \"type\": \"object\"\n                 },\n                 \"vddk\": {\n                  \"description\": \"DataVolumeSourceVDDK provides the parameters to create a Data Volume from a Vmware source\",\n                  \"properties\": {\n                   \"backingFile\": {\n                    \"description\": \"BackingFile is the path to the virtual hard disk to migrate from vCenter/ESXi\",\n                    \"type\": \"string\"\n                   },\n                   \"extraArgs\": {\n                    \"description\": \"ExtraArgs is a reference to a ConfigMap containing extra arguments to pass directly to the VDDK library\",\n                    \"type\": \"string\"\n                   },\n                   \"initImageURL\": {\n                    \"description\": \"InitImageURL is an optional URL to an image containing an extracted VDDK library, overrides v2v-vmware config map\",\n                    \"type\": \"string\"\n                   },\n                   \"secretRef\": {\n                    \"description\": \"SecretRef provides a reference to a secret containing the username and password needed to access the vCenter or ESXi host\",\n                    \"type\": \"string\"\n                   },\n                   \"thumbprint\": {\n                    \"description\": \"Thumbprint is the certificate thumbprint of the vCenter or ESXi host\",\n                    \"type\": \"string\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the URL of the vCenter or ESXi host with the VM to migrate\",\n                    \"type\": \"string\"\n                   },\n                   \"uuid\": {\n                    \"description\": \"UUID is the UUID of the virtual machine that the backing file is attached to in vCenter/ESXi\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"sourceRef\": {\n                \"description\": \"SourceRef is an indirect reference to the source of data for the requested DataVolume\",\n                \"properties\": {\n                 \"kind\": {\n                  \"description\": \"The kind of the source reference, currently only \\\"DataSource\\\" is supported\",\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"description\": \"The name of the source reference\",\n                  \"type\": \"string\"\n                 },\n                 \"namespace\": {\n                  \"description\": \"The namespace of the source reference, defaults to the DataVolume namespace\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"kind\",\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"storage\": {\n                \"description\": \"Storage is the requested storage specification\",\n                \"properties\": {\n                 \"accessModes\": {\n                  \"description\": \"AccessModes contains the desired access modes the volume should have.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"dataSource\": {\n                  \"description\": \"This field can be used to specify either: * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot) * An existing PVC (PersistentVolumeClaim) * An existing custom resource that implements data population (Alpha) In order to use custom resource types that implement data population, the AnyVolumeDataSource feature gate must be enabled. If the provisioner or an external controller can support the specified data source, it will create a new volume based on the contents of the specified data source.\\nIf the AnyVolumeDataSource feature gate is enabled, this field will always have the same contents as the DataSourceRef field.\",\n                  \"properties\": {\n                   \"apiGroup\": {\n                    \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n                    \"type\": \"string\"\n                   },\n                   \"kind\": {\n                    \"description\": \"Kind is the type of resource being referenced\",\n                    \"type\": \"string\"\n                   },\n                   \"name\": {\n                    \"description\": \"Name is the name of resource being referenced\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"kind\",\n                   \"name\"\n                  ],\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"dataSourceRef\": {\n                  \"description\": \"Specifies the object from which to populate the volume with data, if a non-empty volume is desired. This may be any local object from a non-empty API group (non core object) or a PersistentVolumeClaim object. When this field is specified, volume binding will only succeed if the type of the specified object matches some installed volume populator or dynamic provisioner.\\nThis field will replace the functionality of the DataSource field and as such if both fields are non-empty, they must have the same value. For backwards compatibility, both fields (DataSource and DataSourceRef) will be set to the same value automatically if one of them is empty and the other is non-empty.\\nThere are two important differences between DataSource and DataSourceRef:\\n* While DataSource only allows two specific types of objects, DataSourceRef allows any non-core object, as well as PersistentVolumeClaim objects.\\n* While DataSource ignores disallowed values (dropping them), DataSourceRef preserves all values, and generates an error if a disallowed value is specified.\\n(Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\",\n                  \"properties\": {\n                   \"apiGroup\": {\n                    \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n                    \"type\": \"string\"\n                   },\n                   \"kind\": {\n                    \"description\": \"Kind is the type of resource being referenced\",\n                    \"type\": \"string\"\n                   },\n                   \"name\": {\n                    \"description\": \"Name is the name of resource being referenced\",\n                    \"type\": \"string\"\n                   },\n                   \"namespace\": {\n                    \"description\": \"Namespace is the namespace of resource being referenced\\nNote that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.\\n(Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"kind\",\n                   \"name\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"resources\": {\n                  \"description\": \"Resources represents the minimum resources the volume should have.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n                  \"properties\": {\n                   \"limits\": {\n                    \"additionalProperties\": {\n                     \"anyOf\": [\n                      {\n                       \"type\": \"integer\"\n                      },\n                      {\n                       \"type\": \"string\"\n                      }\n                     ],\n                     \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                     \"x-kubernetes-int-or-string\": true\n                    },\n                    \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                    \"type\": \"object\"\n                   },\n                   \"requests\": {\n                    \"additionalProperties\": {\n                     \"anyOf\": [\n                      {\n                       \"type\": \"integer\"\n                      },\n                      {\n                       \"type\": \"string\"\n                      }\n                     ],\n                     \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                     \"x-kubernetes-int-or-string\": true\n                    },\n                    \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value. Requests cannot exceed Limits.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"selector\": {\n                  \"description\": \"A label query over volumes to consider for binding.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"storageClassName\": {\n                  \"description\": \"Name of the StorageClass required by the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n                  \"type\": \"string\"\n                 },\n                 \"volumeMode\": {\n                  \"description\": \"volumeMode defines what type of volume is required by the claim.\\nValue of Filesystem is implied when not included in claim spec.\",\n                  \"type\": \"string\"\n                 },\n                 \"volumeName\": {\n                  \"description\": \"VolumeName is the binding reference to the PersistentVolume backing this claim.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"status\": {\n              \"description\": \"DataVolumeStatus contains the current status of the DataVolume\",\n              \"properties\": {\n               \"claimName\": {\n                \"description\": \"ClaimName is the name of the underlying PVC used by the DataVolume.\",\n                \"type\": \"string\"\n               },\n               \"conditions\": {\n                \"items\": {\n                 \"description\": \"DataVolumeCondition represents the state of a data volume condition.\",\n                 \"properties\": {\n                  \"lastHeartbeatTime\": {\n                   \"format\": \"date-time\",\n                   \"type\": \"string\"\n                  },\n                  \"lastTransitionTime\": {\n                   \"format\": \"date-time\",\n                   \"type\": \"string\"\n                  },\n                  \"message\": {\n                   \"type\": \"string\"\n                  },\n                  \"reason\": {\n                   \"type\": \"string\"\n                  },\n                  \"status\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": {\n                   \"description\": \"DataVolumeConditionType is the string representation of known condition types\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"status\",\n                  \"type\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"phase\": {\n                \"description\": \"Phase is the current phase of the data volume\",\n                \"type\": \"string\"\n               },\n               \"progress\": {\n                \"description\": \"DataVolumeProgress is the current progress of the DataVolume transfer operation. Value between 0 and 100 inclusive, N/A if not available\",\n                \"type\": \"string\"\n               },\n               \"restartCount\": {\n                \"description\": \"RestartCount is the number of times the pod populating the DataVolume has restarted\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"spec\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"managedDataSource\",\n           \"schedule\",\n           \"template\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"spec\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"namespace\": {\n       \"description\": \"Namespace is the k8s namespace where CommonTemplates should be installed\",\n       \"maxLength\": 63,\n       \"pattern\": \"^[a-z0-9]([-a-z0-9]*[a-z0-9])?$\",\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"namespace\"\n     ],\n     \"type\": \"object\"\n    },\n    \"featureGates\": {\n     \"description\": \"FeatureGates for SSP\",\n     \"properties\": {\n      \"deployCommonInstancetypes\": {\n       \"description\": \"Deprecated: This field is ignored.\",\n       \"type\": \"boolean\"\n      },\n      \"deployTektonTaskResources\": {\n       \"description\": \"Deprecated: This field is ignored.\",\n       \"type\": \"boolean\"\n      },\n      \"deployVmConsoleProxy\": {\n       \"description\": \"Deprecated: This field is ignored.\",\n       \"type\": \"boolean\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"tektonPipelines\": {\n     \"description\": \"TektonPipelines is the configuration of the tekton-pipelines operand\\nDeprecated: This field is ignored.\",\n     \"properties\": {\n      \"namespace\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"tektonTasks\": {\n     \"description\": \"TektonTasks is the configuration of the tekton-tasks operand\\nDeprecated: This field is ignored.\",\n     \"properties\": {\n      \"namespace\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"templateValidator\": {\n     \"description\": \"TemplateValidator is configuration of the template validator operand\",\n     \"properties\": {\n      \"placement\": {\n       \"description\": \"Placement describes the node scheduling configuration\",\n       \"properties\": {\n        \"affinity\": {\n         \"description\": \"affinity enables pod affinity/anti-affinity placement expanding the types of constraints\\nthat can be expressed with nodeSelector.\\naffinity is going to be applied to the relevant kind of pods in parallel with nodeSelector\\nSee https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#affinity-and-anti-affinity\",\n         \"properties\": {\n          \"nodeAffinity\": {\n           \"description\": \"Describes node affinity scheduling rules for the pod.\",\n           \"properties\": {\n            \"preferredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy\\nthe affinity expressions specified by this field, but it may choose\\na node that violates one or more of the expressions. The node that is\\nmost preferred is the one with the greatest sum of weights, i.e.\\nfor each node that meets all of the scheduling requirements (resource\\nrequest, requiredDuringScheduling affinity expressions, etc.),\\ncompute a sum by iterating through the elements of this field and adding\\n\\\"weight\\\" to the sum if the node matches the corresponding matchExpressions; the\\nnode(s) with the highest sum are the most preferred.\",\n             \"items\": {\n              \"description\": \"An empty preferred scheduling term matches all objects with implicit weight 0\\n(i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).\",\n              \"properties\": {\n               \"preference\": {\n                \"description\": \"A node selector term, associated with the corresponding weight.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"A list of node selector requirements by node's labels.\",\n                  \"items\": {\n                   \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"The label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchFields\": {\n                  \"description\": \"A list of node selector requirements by node's fields.\",\n                  \"items\": {\n                   \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"The label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"weight\": {\n                \"description\": \"Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"preference\",\n               \"weight\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"requiredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"If the affinity requirements specified by this field are not met at\\nscheduling time, the pod will not be scheduled onto the node.\\nIf the affinity requirements specified by this field cease to be met\\nat some point during pod execution (e.g. due to an update), the system\\nmay or may not try to eventually evict the pod from its node.\",\n             \"properties\": {\n              \"nodeSelectorTerms\": {\n               \"description\": \"Required. A list of node selector terms. The terms are ORed.\",\n               \"items\": {\n                \"description\": \"A null or empty node selector term matches no objects. The requirements of\\nthem are ANDed.\\nThe TopologySelectorTerm type implements a subset of the NodeSelectorTerm.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"A list of node selector requirements by node's labels.\",\n                  \"items\": {\n                   \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"The label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchFields\": {\n                  \"description\": \"A list of node selector requirements by node's fields.\",\n                  \"items\": {\n                   \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"The label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              }\n             },\n             \"required\": [\n              \"nodeSelectorTerms\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"podAffinity\": {\n           \"description\": \"Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).\",\n           \"properties\": {\n            \"preferredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy\\nthe affinity expressions specified by this field, but it may choose\\na node that violates one or more of the expressions. The node that is\\nmost preferred is the one with the greatest sum of weights, i.e.\\nfor each node that meets all of the scheduling requirements (resource\\nrequest, requiredDuringScheduling affinity expressions, etc.),\\ncompute a sum by iterating through the elements of this field and adding\\n\\\"weight\\\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the\\nnode(s) with the highest sum are the most preferred.\",\n             \"items\": {\n              \"description\": \"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\n              \"properties\": {\n               \"podAffinityTerm\": {\n                \"description\": \"Required. A pod affinity term, associated with the corresponding weight.\",\n                \"properties\": {\n                 \"labelSelector\": {\n                  \"description\": \"A label query over a set of resources, in this case pods.\\nIf it's null, this PodAffinityTerm matches with no Pods.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"matchLabelKeys\": {\n                  \"description\": \"MatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both matchLabelKeys and labelSelector.\\nAlso, matchLabelKeys cannot be set when labelSelector isn't set.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"mismatchLabelKeys\": {\n                  \"description\": \"MismatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both mismatchLabelKeys and labelSelector.\\nAlso, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"namespaceSelector\": {\n                  \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"namespaces\": {\n                  \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"topologyKey\": {\n                  \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"topologyKey\"\n                ],\n                \"type\": \"object\"\n               },\n               \"weight\": {\n                \"description\": \"weight associated with matching the corresponding podAffinityTerm,\\nin the range 1-100.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"podAffinityTerm\",\n               \"weight\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"requiredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"If the affinity requirements specified by this field are not met at\\nscheduling time, the pod will not be scheduled onto the node.\\nIf the affinity requirements specified by this field cease to be met\\nat some point during pod execution (e.g. due to a pod label update), the\\nsystem may or may not try to eventually evict the pod from its node.\\nWhen there are multiple elements, the lists of nodes corresponding to each\\npodAffinityTerm are intersected, i.e. all terms must be satisfied.\",\n             \"items\": {\n              \"description\": \"Defines a set of pods (namely those matching the labelSelector\\nrelative to the given namespace(s)) that this pod should be\\nco-located (affinity) or not co-located (anti-affinity) with,\\nwhere co-located is defined as running on a node whose value of\\nthe label with key \\u003ctopologyKey\\u003e matches that of any node on which\\na pod of the set of pods is running\",\n              \"properties\": {\n               \"labelSelector\": {\n                \"description\": \"A label query over a set of resources, in this case pods.\\nIf it's null, this PodAffinityTerm matches with no Pods.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                  \"items\": {\n                   \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"key is the label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"matchLabelKeys\": {\n                \"description\": \"MatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both matchLabelKeys and labelSelector.\\nAlso, matchLabelKeys cannot be set when labelSelector isn't set.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"mismatchLabelKeys\": {\n                \"description\": \"MismatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both mismatchLabelKeys and labelSelector.\\nAlso, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"namespaceSelector\": {\n                \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                  \"items\": {\n                   \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"key is the label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaces\": {\n                \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"topologyKey\": {\n                \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"topologyKey\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"podAntiAffinity\": {\n           \"description\": \"Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).\",\n           \"properties\": {\n            \"preferredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy\\nthe anti-affinity expressions specified by this field, but it may choose\\na node that violates one or more of the expressions. The node that is\\nmost preferred is the one with the greatest sum of weights, i.e.\\nfor each node that meets all of the scheduling requirements (resource\\nrequest, requiredDuringScheduling anti-affinity expressions, etc.),\\ncompute a sum by iterating through the elements of this field and adding\\n\\\"weight\\\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the\\nnode(s) with the highest sum are the most preferred.\",\n             \"items\": {\n              \"description\": \"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\n              \"properties\": {\n               \"podAffinityTerm\": {\n                \"description\": \"Required. A pod affinity term, associated with the corresponding weight.\",\n                \"properties\": {\n                 \"labelSelector\": {\n                  \"description\": \"A label query over a set of resources, in this case pods.\\nIf it's null, this PodAffinityTerm matches with no Pods.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"matchLabelKeys\": {\n                  \"description\": \"MatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both matchLabelKeys and labelSelector.\\nAlso, matchLabelKeys cannot be set when labelSelector isn't set.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"mismatchLabelKeys\": {\n                  \"description\": \"MismatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both mismatchLabelKeys and labelSelector.\\nAlso, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"namespaceSelector\": {\n                  \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"namespaces\": {\n                  \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"topologyKey\": {\n                  \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"topologyKey\"\n                ],\n                \"type\": \"object\"\n               },\n               \"weight\": {\n                \"description\": \"weight associated with matching the corresponding podAffinityTerm,\\nin the range 1-100.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"podAffinityTerm\",\n               \"weight\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"requiredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"If the anti-affinity requirements specified by this field are not met at\\nscheduling time, the pod will not be scheduled onto the node.\\nIf the anti-affinity requirements specified by this field cease to be met\\nat some point during pod execution (e.g. due to a pod label update), the\\nsystem may or may not try to eventually evict the pod from its node.\\nWhen there are multiple elements, the lists of nodes corresponding to each\\npodAffinityTerm are intersected, i.e. all terms must be satisfied.\",\n             \"items\": {\n              \"description\": \"Defines a set of pods (namely those matching the labelSelector\\nrelative to the given namespace(s)) that this pod should be\\nco-located (affinity) or not co-located (anti-affinity) with,\\nwhere co-located is defined as running on a node whose value of\\nthe label with key \\u003ctopologyKey\\u003e matches that of any node on which\\na pod of the set of pods is running\",\n              \"properties\": {\n               \"labelSelector\": {\n                \"description\": \"A label query over a set of resources, in this case pods.\\nIf it's null, this PodAffinityTerm matches with no Pods.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                  \"items\": {\n                   \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"key is the label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"matchLabelKeys\": {\n                \"description\": \"MatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both matchLabelKeys and labelSelector.\\nAlso, matchLabelKeys cannot be set when labelSelector isn't set.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"mismatchLabelKeys\": {\n                \"description\": \"MismatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both mismatchLabelKeys and labelSelector.\\nAlso, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"namespaceSelector\": {\n                \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                  \"items\": {\n                   \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"key is the label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaces\": {\n                \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"topologyKey\": {\n                \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"topologyKey\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"nodeSelector\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"description\": \"nodeSelector is the node selector applied to the relevant kind of pods\\nIt specifies a map of key-value pairs: for the pod to be eligible to run on a node,\\nthe node must have each of the indicated key-value pairs as labels\\n(it can have additional labels as well).\\nSee https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#nodeselector\",\n         \"type\": \"object\"\n        },\n        \"tolerations\": {\n         \"description\": \"tolerations is a list of tolerations applied to the relevant kind of pods\\nSee https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/ for more info.\\nThese are additional tolerations other than default ones.\",\n         \"items\": {\n          \"description\": \"The pod this Toleration is attached to tolerates any taint that matches\\nthe triple \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\n          \"properties\": {\n           \"effect\": {\n            \"description\": \"Effect indicates the taint effect to match. Empty means match all taint effects.\\nWhen specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.\",\n            \"type\": \"string\"\n           },\n           \"key\": {\n            \"description\": \"Key is the taint key that the toleration applies to. Empty means match all taint keys.\\nIf the key is empty, operator must be Exists; this combination means to match all values and all keys.\",\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"description\": \"Operator represents a key's relationship to the value.\\nValid operators are Exists and Equal. Defaults to Equal.\\nExists is equivalent to wildcard for value, so that a pod can\\ntolerate all taints of a particular category.\",\n            \"type\": \"string\"\n           },\n           \"tolerationSeconds\": {\n            \"description\": \"TolerationSeconds represents the period of time the toleration (which must be\\nof effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,\\nit is not set, which means tolerate the taint forever (do not evict). Zero and\\nnegative values will be treated as 0 (evict immediately) by the system.\",\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"value\": {\n            \"description\": \"Value is the taint value the toleration matches to.\\nIf the operator is Exists, the value should be empty, otherwise just a regular string.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"replicas\": {\n       \"default\": 2,\n       \"description\": \"Replicas is the number of replicas of the template validator pod\",\n       \"format\": \"int32\",\n       \"minimum\": 0,\n       \"type\": \"integer\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"tlsSecurityProfile\": {\n     \"description\": \"TLSSecurityProfile is a configuration for the TLS.\",\n     \"properties\": {\n      \"custom\": {\n       \"description\": \"custom is a user-defined TLS security profile. Be extremely careful using a custom\\nprofile as invalid configurations can be catastrophic. An example custom profile\\nlooks like this:\\n\\n  ciphers:\\n\\n    - ECDHE-ECDSA-CHACHA20-POLY1305\\n\\n    - ECDHE-RSA-CHACHA20-POLY1305\\n\\n    - ECDHE-RSA-AES128-GCM-SHA256\\n\\n    - ECDHE-ECDSA-AES128-GCM-SHA256\\n\\n  minTLSVersion: VersionTLS11\",\n       \"nullable\": true,\n       \"properties\": {\n        \"ciphers\": {\n         \"description\": \"ciphers is used to specify the cipher algorithms that are negotiated\\nduring the TLS handshake.  Operators may remove entries their operands\\ndo not support.  For example, to use DES-CBC3-SHA  (yaml):\\n\\n  ciphers:\\n    - DES-CBC3-SHA\",\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"minTLSVersion\": {\n         \"description\": \"minTLSVersion is used to specify the minimal version of the TLS protocol\\nthat is negotiated during the TLS handshake. For example, to use TLS\\nversions 1.1, 1.2 and 1.3 (yaml):\\n\\n  minTLSVersion: VersionTLS11\\n\\nNOTE: currently the highest minTLSVersion allowed is VersionTLS12\",\n         \"enum\": [\n          \"VersionTLS10\",\n          \"VersionTLS11\",\n          \"VersionTLS12\",\n          \"VersionTLS13\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"intermediate\": {\n       \"description\": \"intermediate is a TLS security profile based on:\\n\\nhttps://wiki.mozilla.org/Security/Server_Side_TLS#Intermediate_compatibility_.28recommended.29\\n\\nand looks like this (yaml):\\n\\n  ciphers:\\n\\n    - TLS_AES_128_GCM_SHA256\\n\\n    - TLS_AES_256_GCM_SHA384\\n\\n    - TLS_CHACHA20_POLY1305_SHA256\\n\\n    - ECDHE-ECDSA-AES128-GCM-SHA256\\n\\n    - ECDHE-RSA-AES128-GCM-SHA256\\n\\n    - ECDHE-ECDSA-AES256-GCM-SHA384\\n\\n    - ECDHE-RSA-AES256-GCM-SHA384\\n\\n    - ECDHE-ECDSA-CHACHA20-POLY1305\\n\\n    - ECDHE-RSA-CHACHA20-POLY1305\\n\\n    - DHE-RSA-AES128-GCM-SHA256\\n\\n    - DHE-RSA-AES256-GCM-SHA384\\n\\n  minTLSVersion: VersionTLS12\",\n       \"nullable\": true,\n       \"type\": \"object\"\n      },\n      \"modern\": {\n       \"description\": \"modern is a TLS security profile based on:\\n\\nhttps://wiki.mozilla.org/Security/Server_Side_TLS#Modern_compatibility\\n\\nand looks like this (yaml):\\n\\n  ciphers:\\n\\n    - TLS_AES_128_GCM_SHA256\\n\\n    - TLS_AES_256_GCM_SHA384\\n\\n    - TLS_CHACHA20_POLY1305_SHA256\\n\\n  minTLSVersion: VersionTLS13\",\n       \"nullable\": true,\n       \"type\": \"object\"\n      },\n      \"old\": {\n       \"description\": \"old is a TLS security profile based on:\\n\\nhttps://wiki.mozilla.org/Security/Server_Side_TLS#Old_backward_compatibility\\n\\nand looks like this (yaml):\\n\\n  ciphers:\\n\\n    - TLS_AES_128_GCM_SHA256\\n\\n    - TLS_AES_256_GCM_SHA384\\n\\n    - TLS_CHACHA20_POLY1305_SHA256\\n\\n    - ECDHE-ECDSA-AES128-GCM-SHA256\\n\\n    - ECDHE-RSA-AES128-GCM-SHA256\\n\\n    - ECDHE-ECDSA-AES256-GCM-SHA384\\n\\n    - ECDHE-RSA-AES256-GCM-SHA384\\n\\n    - ECDHE-ECDSA-CHACHA20-POLY1305\\n\\n    - ECDHE-RSA-CHACHA20-POLY1305\\n\\n    - DHE-RSA-AES128-GCM-SHA256\\n\\n    - DHE-RSA-AES256-GCM-SHA384\\n\\n    - DHE-RSA-CHACHA20-POLY1305\\n\\n    - ECDHE-ECDSA-AES128-SHA256\\n\\n    - ECDHE-RSA-AES128-SHA256\\n\\n    - ECDHE-ECDSA-AES128-SHA\\n\\n    - ECDHE-RSA-AES128-SHA\\n\\n    - ECDHE-ECDSA-AES256-SHA384\\n\\n    - ECDHE-RSA-AES256-SHA384\\n\\n    - ECDHE-ECDSA-AES256-SHA\\n\\n    - ECDHE-RSA-AES256-SHA\\n\\n    - DHE-RSA-AES128-SHA256\\n\\n    - DHE-RSA-AES256-SHA256\\n\\n    - AES128-GCM-SHA256\\n\\n    - AES256-GCM-SHA384\\n\\n    - AES128-SHA256\\n\\n    - AES256-SHA256\\n\\n    - AES128-SHA\\n\\n    - AES256-SHA\\n\\n    - DES-CBC3-SHA\\n\\n  minTLSVersion: VersionTLS10\",\n       \"nullable\": true,\n       \"type\": \"object\"\n      },\n      \"type\": {\n       \"description\": \"type is one of Old, Intermediate, Modern or Custom. Custom provides\\nthe ability to specify individual TLS security profile parameters.\\nOld, Intermediate and Modern are TLS security profiles based on:\\n\\nhttps://wiki.mozilla.org/Security/Server_Side_TLS#Recommended_configurations\\n\\nThe profiles are intent based, so they may change over time as new ciphers are developed and existing ciphers\\nare found to be insecure.  Depending on precisely which ciphers are available to a process, the list may be\\nreduced.\\n\\nNote that the Modern profile is currently not supported because it is not\\nyet well adopted by common software libraries.\",\n       \"enum\": [\n        \"Old\",\n        \"Intermediate\",\n        \"Modern\",\n        \"Custom\"\n       ],\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"tokenGenerationService\": {\n     \"description\": \"TokenGenerationService configures the service for generating tokens to access VNC for a VM.\",\n     \"properties\": {\n      \"enabled\": {\n       \"type\": \"boolean\"\n      }\n     },\n     \"type\": \"object\"\n    }\n   },\n   \"required\": [\n    \"commonTemplates\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"title\": \"SSP\",\n \"type\": \"object\"\n}"}}