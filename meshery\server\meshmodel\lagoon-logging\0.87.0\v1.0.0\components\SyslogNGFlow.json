{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Syslog NG Flow", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "lagoon-logging", "displayName": "Lagoon Logging", "status": "ignored", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Uncategorized"}, "subCategory": "Uncategorized", "metadata": {"isAnnotation": false, "primaryColor": "#00B39F", "secondaryColor": "#00D3A9", "shape": "circle", "source_uri": "https://github.com/uselagoon/lagoon-charts/releases/download/lagoon-logging-0.87.0/lagoon-logging-0.87.0.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 32 32\" fill=\"none\">\n<g xmlns=\"http://www.w3.org/2000/svg\" clip-path=\"url(#clip0_36_80)\">\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.4632 7.69351V15.2015L22.9702 11.4346L16.4632 7.69351Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.4632 16.7705V24.3157L23.0307 20.5607L16.4632 16.7705Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.5274 15.1502V7.75632L9.10194 11.4416L15.5274 15.1502Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M2.63699 24.2878C3.89756 26.3157 5.60178 28.031 7.62134 29.3047V21.4033L2.63699 24.2878Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.5274 24.2785V16.8264L9.08579 20.556L15.5274 24.2785Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.55965 28.8344L15.0829 25.1049L8.55965 21.3335V28.8344Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4753 28.8742V21.3848L16.9615 25.1096L23.4753 28.8742Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M29.852 23.4194C30.9655 21.341 31.5949 19.0378 31.6935 16.6819L24.9119 20.5651L29.852 23.4194Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M24.4136 19.7691L30.96 16.0256L24.4136 12.2634V19.7691Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4755 10.6455V3.20041L16.9919 6.91827L23.4755 10.6455Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4754 19.7364V12.2239L16.9779 15.986L23.4754 19.7364Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.55965 12.2099V19.7784L15.1061 15.9882L8.55965 12.2099Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.5274 0.285706C13.1176 0.353534 10.756 0.977397 8.6271 2.10855L15.5274 6.06621V0.285706Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.55965 3.1492V10.6734L15.1107 6.91597L8.55965 3.1492Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.62134 2.69299C5.60228 3.96735 3.89818 5.6826 2.63699 7.7099L7.62134 10.5873V2.69299Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4335 2.14811C21.2869 0.992986 18.9001 0.355226 16.4632 0.285706V6.14069L23.4335 2.14811Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M0.285713 16.5517C0.367085 18.9754 1.01023 21.3471 2.16447 23.4799L7.21396 20.5559L0.285713 16.5517Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M2.16447 8.51996C1.01384 10.6433 0.370833 13.0043 0.285713 15.4178L7.22097 11.4393L2.16447 8.51996Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.61544 29.8822C10.7469 31.0189 13.1128 31.6461 15.5274 31.7143V25.9291L8.61544 29.8822Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M29.3675 7.73539C28.1143 5.71396 26.4208 4.00147 24.4136 2.72543V10.5987L29.3675 7.73539Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M24.4136 29.2791C26.4312 27.994 28.1314 26.2684 29.3863 24.2321L24.4136 21.3591V29.2791Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M31.7143 15.3738C31.6251 12.9835 30.9879 10.6458 29.8518 8.54102L24.8441 11.4325L31.7143 15.3738Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.4632 31.7143C18.8725 31.6467 21.2333 31.0229 23.3613 29.8914L16.4632 25.8942V31.7143Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.62141 19.711V12.2892L1.17738 15.9838L7.62141 19.711Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n</g>\n<defs xmlns=\"http://www.w3.org/2000/svg\">\n<clipPath xmlns=\"http://www.w3.org/2000/svg\" id=\"clip0_36_80\">\n<rect xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" fill=\"white\"></rect>\n</clipPath>\n</defs>\n</svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 32 32\" fill=\"none\"><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.405 8.732v6.57l5.694-3.297-5.694-3.273Zm0 7.942v6.602l5.747-3.285-5.747-3.317Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.586 15.256v-6.47l-5.622 3.225 5.622 3.245ZM4.307 23.252a13.809 13.809 0 0 0 4.362 4.39v-6.914l-4.362 2.524Zm11.279-.008v-6.52L9.95 19.985l5.636 3.258Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"m9.49 27.23 5.707-3.263-5.707-3.3v6.563Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M22.54 27.265v-6.553l-5.699 3.259 5.7 3.294Zm5.58-4.773a13.697 13.697 0 0 0 1.612-5.895l-5.934 3.397 4.323 2.498Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"m23.362 19.298 5.728-3.276-5.728-3.291v6.567Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M22.541 11.315V4.8l-5.673 3.253 5.673 3.262Zm0 7.955v-6.574l-5.685 3.292 5.685 3.281Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.49 12.684v6.622l5.728-3.316-5.728-3.306Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.586 2.25a13.69 13.69 0 0 0-6.037 1.595l6.037 3.463V2.25Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.49 4.756v6.583l5.732-3.288L9.49 4.756Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.669 4.356a13.83 13.83 0 0 0-4.362 4.39l4.362 2.518V4.356Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M22.504 3.88a13.695 13.695 0 0 0-6.099-1.63v5.123l6.1-3.493ZM2.25 16.483c.071 2.12.634 4.196 1.644 6.062l4.418-2.559-6.062-3.503Zm1.644-7.028a13.68 13.68 0 0 0-1.644 6.036l6.068-3.482-4.424-2.554Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.539 28.147a13.673 13.673 0 0 0 6.047 1.603v-5.062L9.54 28.147Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M27.697 8.768a13.83 13.83 0 0 0-4.335-4.383v6.889l4.335-2.506ZM23.362 27.62a13.851 13.851 0 0 0 4.351-4.417l-4.351-2.514v6.93Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M29.75 15.452a13.659 13.659 0 0 0-1.63-5.979l-4.381 2.53 6.011 3.45Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.405 29.75a13.673 13.673 0 0 0 6.036-1.595l-6.036-3.498v5.093Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.669 19.247v-6.494L3.03 15.986l5.639 3.261Z\" fill=\"#fff\" fill-opacity=\".8\"></path></svg>"}, "model": {"version": "0.87.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#00B39F", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg width=\"18\" height=\"18\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_36_80)\">\n<path d=\"M16.4632 7.69351V15.2015L22.9702 11.4346L16.4632 7.69351Z\" fill=\"white\"/>\n<path d=\"M16.4632 16.7705V24.3157L23.0307 20.5607L16.4632 16.7705Z\" fill=\"white\"/>\n<path d=\"M15.5274 15.1502V7.75632L9.10194 11.4416L15.5274 15.1502Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M2.63699 24.2878C3.89756 26.3157 5.60178 28.031 7.62134 29.3047V21.4033L2.63699 24.2878Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M15.5274 24.2785V16.8264L9.08579 20.556L15.5274 24.2785Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M8.55965 28.8344L15.0829 25.1049L8.55965 21.3335V28.8344Z\" fill=\"white\"/>\n<path d=\"M23.4753 28.8742V21.3848L16.9615 25.1096L23.4753 28.8742Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M29.852 23.4194C30.9655 21.341 31.5949 19.0378 31.6935 16.6819L24.9119 20.5651L29.852 23.4194Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M24.4136 19.7691L30.96 16.0256L24.4136 12.2634V19.7691Z\" fill=\"white\"/>\n<path d=\"M23.4755 10.6455V3.20041L16.9919 6.91827L23.4755 10.6455Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M23.4754 19.7364V12.2239L16.9779 15.986L23.4754 19.7364Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M8.55965 12.2099V19.7784L15.1061 15.9882L8.55965 12.2099Z\" fill=\"white\"/>\n<path d=\"M15.5274 0.285706C13.1176 0.353534 10.756 0.977397 8.6271 2.10855L15.5274 6.06621V0.285706Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M8.55965 3.1492V10.6734L15.1107 6.91597L8.55965 3.1492Z\" fill=\"white\"/>\n<path d=\"M7.62134 2.69299C5.60228 3.96735 3.89818 5.6826 2.63699 7.7099L7.62134 10.5873V2.69299Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M23.4335 2.14811C21.2869 0.992986 18.9001 0.355226 16.4632 0.285706V6.14069L23.4335 2.14811Z\" fill=\"white\"/>\n<path d=\"M0.285713 16.5517C0.367085 18.9754 1.01023 21.3471 2.16447 23.4799L7.21396 20.5559L0.285713 16.5517Z\" fill=\"white\"/>\n<path d=\"M2.16447 8.51996C1.01384 10.6433 0.370833 13.0043 0.285713 15.4178L7.22097 11.4393L2.16447 8.51996Z\" fill=\"white\"/>\n<path d=\"M8.61544 29.8822C10.7469 31.0189 13.1128 31.6461 15.5274 31.7143V25.9291L8.61544 29.8822Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M29.3675 7.73539C28.1143 5.71396 26.4208 4.00147 24.4136 2.72543V10.5987L29.3675 7.73539Z\" fill=\"white\"/>\n<path d=\"M24.4136 29.2791C26.4312 27.994 28.1314 26.2684 29.3863 24.2321L24.4136 21.3591V29.2791Z\" fill=\"white\"/>\n<path d=\"M31.7143 15.3738C31.6251 12.9835 30.9879 10.6458 29.8518 8.54102L24.8441 11.4325L31.7143 15.3738Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M16.4632 31.7143C18.8725 31.6467 21.2333 31.0229 23.3613 29.8914L16.4632 25.8942V31.7143Z\" fill=\"white\"/>\n<path d=\"M7.62141 19.711V12.2892L1.17738 15.9838L7.62141 19.711Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_36_80\">\n<rect width=\"32\" height=\"32\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>", "svgComplete": "", "svgWhite": "<svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M16.405 8.732v6.57l5.694-3.297-5.694-3.273Zm0 7.942v6.602l5.747-3.285-5.747-3.317Z\" fill=\"#fff\"/><path d=\"M15.586 15.256v-6.47l-5.622 3.225 5.622 3.245ZM4.307 23.252a13.809 13.809 0 0 0 4.362 4.39v-6.914l-4.362 2.524Zm11.279-.008v-6.52L9.95 19.985l5.636 3.258Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"m9.49 27.23 5.707-3.263-5.707-3.3v6.563Z\" fill=\"#fff\"/><path d=\"M22.54 27.265v-6.553l-5.699 3.259 5.7 3.294Zm5.58-4.773a13.697 13.697 0 0 0 1.612-5.895l-5.934 3.397 4.323 2.498Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"m23.362 19.298 5.728-3.276-5.728-3.291v6.567Z\" fill=\"#fff\"/><path d=\"M22.541 11.315V4.8l-5.673 3.253 5.673 3.262Zm0 7.955v-6.574l-5.685 3.292 5.685 3.281Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M9.49 12.684v6.622l5.728-3.316-5.728-3.306Z\" fill=\"#fff\"/><path d=\"M15.586 2.25a13.69 13.69 0 0 0-6.037 1.595l6.037 3.463V2.25Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M9.49 4.756v6.583l5.732-3.288L9.49 4.756Z\" fill=\"#fff\"/><path d=\"M8.669 4.356a13.83 13.83 0 0 0-4.362 4.39l4.362 2.518V4.356Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M22.504 3.88a13.695 13.695 0 0 0-6.099-1.63v5.123l6.1-3.493ZM2.25 16.483c.071 2.12.634 4.196 1.644 6.062l4.418-2.559-6.062-3.503Zm1.644-7.028a13.68 13.68 0 0 0-1.644 6.036l6.068-3.482-4.424-2.554Z\" fill=\"#fff\"/><path d=\"M9.539 28.147a13.673 13.673 0 0 0 6.047 1.603v-5.062L9.54 28.147Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M27.697 8.768a13.83 13.83 0 0 0-4.335-4.383v6.889l4.335-2.506ZM23.362 27.62a13.851 13.851 0 0 0 4.351-4.417l-4.351-2.514v6.93Z\" fill=\"#fff\"/><path d=\"M29.75 15.452a13.659 13.659 0 0 0-1.63-5.979l-4.381 2.53 6.011 3.45Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M16.405 29.75a13.673 13.673 0 0 0 6.036-1.595l-6.036-3.498v5.093Z\" fill=\"#fff\"/><path d=\"M8.669 19.247v-6.494L3.03 15.986l5.639 3.261Z\" fill=\"#fff\" fill-opacity=\".8\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/uselagoon/lagoon-charts/releases/download/lagoon-logging-0.87.0/lagoon-logging-0.87.0.tgz"}, "configuration": null, "component": {"version": "logging.banzaicloud.io/v1beta1", "kind": "SyslogNGFlow", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"properties\": {\n    \"filters\": {\n     \"items\": {\n      \"properties\": {\n       \"id\": {\n        \"type\": \"string\"\n       },\n       \"match\": {\n        \"properties\": {\n         \"and\": {\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"not\": {\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"or\": {\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"regexp\": {\n          \"properties\": {\n           \"flags\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"pattern\": {\n            \"type\": \"string\"\n           },\n           \"template\": {\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"type\": \"string\"\n           },\n           \"value\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"pattern\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"parser\": {\n        \"properties\": {\n         \"metrics-probe\": {\n          \"properties\": {\n           \"key\": {\n            \"type\": \"string\"\n           },\n           \"labels\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"object\"\n           },\n           \"level\": {\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"regexp\": {\n          \"properties\": {\n           \"flags\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"patterns\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"prefix\": {\n            \"type\": \"string\"\n           },\n           \"template\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"patterns\"\n          ],\n          \"type\": \"object\"\n         },\n         \"syslog-parser\": {\n          \"properties\": {\n           \"flags\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"rewrite\": {\n        \"items\": {\n         \"properties\": {\n          \"group_unset\": {\n           \"properties\": {\n            \"condition\": {\n             \"properties\": {\n              \"and\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"not\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"or\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"regexp\": {\n               \"properties\": {\n                \"flags\": {\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"pattern\": {\n                 \"type\": \"string\"\n                },\n                \"template\": {\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"pattern\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"pattern\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"pattern\"\n           ],\n           \"type\": \"object\"\n          },\n          \"rename\": {\n           \"properties\": {\n            \"condition\": {\n             \"properties\": {\n              \"and\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"not\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"or\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"regexp\": {\n               \"properties\": {\n                \"flags\": {\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"pattern\": {\n                 \"type\": \"string\"\n                },\n                \"template\": {\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"pattern\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"newName\": {\n             \"type\": \"string\"\n            },\n            \"oldName\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"newName\",\n            \"oldName\"\n           ],\n           \"type\": \"object\"\n          },\n          \"set\": {\n           \"properties\": {\n            \"condition\": {\n             \"properties\": {\n              \"and\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"not\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"or\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"regexp\": {\n               \"properties\": {\n                \"flags\": {\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"pattern\": {\n                 \"type\": \"string\"\n                },\n                \"template\": {\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"pattern\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"field\": {\n             \"type\": \"string\"\n            },\n            \"value\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"field\",\n            \"value\"\n           ],\n           \"type\": \"object\"\n          },\n          \"subst\": {\n           \"properties\": {\n            \"condition\": {\n             \"properties\": {\n              \"and\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"not\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"or\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"regexp\": {\n               \"properties\": {\n                \"flags\": {\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"pattern\": {\n                 \"type\": \"string\"\n                },\n                \"template\": {\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"pattern\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"field\": {\n             \"type\": \"string\"\n            },\n            \"flags\": {\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"pattern\": {\n             \"type\": \"string\"\n            },\n            \"replace\": {\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"field\",\n            \"pattern\",\n            \"replace\"\n           ],\n           \"type\": \"object\"\n          },\n          \"unset\": {\n           \"properties\": {\n            \"condition\": {\n             \"properties\": {\n              \"and\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"not\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"or\": {\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"regexp\": {\n               \"properties\": {\n                \"flags\": {\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"pattern\": {\n                 \"type\": \"string\"\n                },\n                \"template\": {\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"pattern\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"field\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"field\"\n           ],\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"globalOutputRefs\": {\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"localOutputRefs\": {\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"loggingRef\": {\n     \"type\": \"string\"\n    },\n    \"match\": {\n     \"properties\": {\n      \"and\": {\n       \"format\": \"textarea\",\n       \"type\": \"string\"\n      },\n      \"not\": {\n       \"format\": \"textarea\",\n       \"type\": \"string\"\n      },\n      \"or\": {\n       \"format\": \"textarea\",\n       \"type\": \"string\"\n      },\n      \"regexp\": {\n       \"properties\": {\n        \"flags\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"pattern\": {\n         \"type\": \"string\"\n        },\n        \"template\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"pattern\"\n       ],\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"outputMetrics\": {\n     \"items\": {\n      \"properties\": {\n       \"key\": {\n        \"type\": \"string\"\n       },\n       \"labels\": {\n        \"additionalProperties\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"object\"\n       },\n       \"level\": {\n        \"type\": \"integer\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Syslog NG Flow\",\n \"type\": \"object\"\n}"}}