{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Integration", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "loki-simple-scalable", "displayName": "Loki Simple Scalable", "description": "Loki is a horizontally scalable, highly available, multi-tenant log aggregation system inspired by Prometheus. It is designed to be very cost effective and easy to operate. It does not index the contents of the logs, but rather a set of labels for each log stream.", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Observability and Analysis"}, "subCategory": "Logging", "metadata": {"isAnnotation": false, "primaryColor": "#F15B2B", "secondaryColor": "#FAED1E", "shape": "circle", "source_uri": "https://github.com/grafana/helm-charts/releases/download/loki-simple-scalable-1.8.11/loki-simple-scalable-1.8.11.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 48 56\" fill=\"none\">\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.0478 54.9248L11.3838 50.4663L6.92529 51.1304L7.68418 55.5889L12.0478 54.9248Z\" fill=\"url(#paint0_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M46.957 42.4032L46.1981 38.0396L26.7515 41.0751L27.3206 45.4388L46.957 42.4032Z\" fill=\"url(#paint1_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M20.395 46.5772L24.8535 45.8183L24.1895 41.4546L19.731 42.1186L20.395 46.5772Z\" fill=\"url(#paint2_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M19.0674 53.7865L18.3085 49.4229L13.9448 50.0869L14.514 54.5454L19.0674 53.7865Z\" fill=\"url(#paint3_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M5.88135 44.2055L6.54539 48.6641L11.0039 48L10.3399 43.5415L5.88135 44.2055Z\" fill=\"url(#paint4_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M27.6997 47.9051L28.4586 52.4585L48.0001 49.4229L47.3361 44.9644L27.6997 47.9051Z\" fill=\"url(#paint5_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.5333 53.407L25.8969 52.8378L25.2329 48.2844L20.7744 49.0433L21.5333 53.407Z\" fill=\"url(#paint6_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.8062 43.1621L13.565 47.6205L17.9287 46.9566L17.2646 42.498L12.8062 43.1621Z\" fill=\"url(#paint7_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.39921 41.4546L1.99207 5.97632L0 6.26089L5.50197 41.7392L7.39921 41.4546Z\" fill=\"url(#paint8_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.96032 41.0751L4.07888 2.94067L2.18164 3.32014L8.06308 41.3597L9.96032 41.0751Z\" fill=\"url(#paint9_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M14.3245 40.4111L8.15847 0L6.26123 0.379412L12.4272 40.6008L14.3245 40.4111Z\" fill=\"url(#paint10_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.8852 40.0315L11.1935 3.2251L9.39111 3.50967L15.0828 40.2212L16.8852 40.0315Z\" fill=\"url(#paint11_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.2491 39.2728L16.2215 6.64038L14.3242 6.92495L19.3519 39.6523L21.2491 39.2728Z\" fill=\"url(#paint12_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.8104 38.8935L18.593 5.02783L16.6958 5.31241L22.0081 39.1781L23.8104 38.8935Z\" fill=\"url(#paint13_linear_17931_893)\"></path>\n<defs xmlns=\"http://www.w3.org/2000/svg\">\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint0_linear_17931_893\" x1=\"11.6469\" y1=\"66.8772\" x2=\"1.23198\" y2=\"-0.802501\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint1_linear_17931_893\" x1=\"39.9916\" y1=\"62.5154\" x2=\"29.5768\" y2=\"-5.1639\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint2_linear_17931_893\" x1=\"25.5063\" y1=\"64.7445\" x2=\"15.0913\" y2=\"-2.93516\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint3_linear_17931_893\" x1=\"18.5788\" y1=\"65.8105\" x2=\"8.1638\" y2=\"-1.86922\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint4_linear_17931_893\" x1=\"11.6394\" y1=\"66.8784\" x2=\"1.22448\" y2=\"-0.80128\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint5_linear_17931_893\" x1=\"39.9982\" y1=\"62.5143\" x2=\"29.5833\" y2=\"-5.16528\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint6_linear_17931_893\" x1=\"25.506\" y1=\"64.7443\" x2=\"15.091\" y2=\"-2.93537\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint7_linear_17931_893\" x1=\"18.5788\" y1=\"65.8103\" x2=\"8.16407\" y2=\"-1.86867\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint8_linear_17931_893\" x1=\"10.1623\" y1=\"65.7597\" x2=\"0.284696\" y2=\"1.57166\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint9_linear_17931_893\" x1=\"13.0129\" y1=\"67.1431\" x2=\"2.40785\" y2=\"-1.77243\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint10_linear_17931_893\" x1=\"17.6338\" y1=\"68.0331\" x2=\"6.38943\" y2=\"-5.0367\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint11_linear_17931_893\" x1=\"19.8305\" y1=\"65.208\" x2=\"9.57925\" y2=\"-1.40832\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint12_linear_17931_893\" x1=\"23.7353\" y1=\"61.7393\" x2=\"14.6289\" y2=\"2.56246\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint13_linear_17931_893\" x1=\"26.4465\" y1=\"62.1967\" x2=\"16.9911\" y2=\"0.751851\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n</defs>\n</svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 48 56\" fill=\"none\">\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.0478 54.9248L11.3838 50.4663L6.92529 51.1304L7.68418 55.5889L12.0478 54.9248Z\" fill=\"url(#paint0_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M46.957 42.4032L46.1981 38.0396L26.7515 41.0751L27.3206 45.4388L46.957 42.4032Z\" fill=\"url(#paint1_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M20.395 46.5772L24.8535 45.8183L24.1895 41.4546L19.731 42.1186L20.395 46.5772Z\" fill=\"url(#paint2_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M19.0674 53.7865L18.3085 49.4229L13.9448 50.0869L14.514 54.5454L19.0674 53.7865Z\" fill=\"url(#paint3_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M5.88135 44.2055L6.54539 48.6641L11.0039 48L10.3399 43.5415L5.88135 44.2055Z\" fill=\"url(#paint4_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M27.6997 47.9051L28.4586 52.4585L48.0001 49.4229L47.3361 44.9644L27.6997 47.9051Z\" fill=\"url(#paint5_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.5333 53.407L25.8969 52.8378L25.2329 48.2844L20.7744 49.0433L21.5333 53.407Z\" fill=\"url(#paint6_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.8062 43.1621L13.565 47.6205L17.9287 46.9566L17.2646 42.498L12.8062 43.1621Z\" fill=\"url(#paint7_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.39921 41.4546L1.99207 5.97632L0 6.26089L5.50197 41.7392L7.39921 41.4546Z\" fill=\"url(#paint8_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.96032 41.0751L4.07888 2.94067L2.18164 3.32014L8.06308 41.3597L9.96032 41.0751Z\" fill=\"url(#paint9_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M14.3245 40.4111L8.15847 0L6.26123 0.379412L12.4272 40.6008L14.3245 40.4111Z\" fill=\"url(#paint10_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.8852 40.0315L11.1935 3.2251L9.39111 3.50967L15.0828 40.2212L16.8852 40.0315Z\" fill=\"url(#paint11_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.2491 39.2728L16.2215 6.64038L14.3242 6.92495L19.3519 39.6523L21.2491 39.2728Z\" fill=\"url(#paint12_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.8104 38.8935L18.593 5.02783L16.6958 5.31241L22.0081 39.1781L23.8104 38.8935Z\" fill=\"url(#paint13_linear_17931_893)\"></path>\n<defs xmlns=\"http://www.w3.org/2000/svg\">\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint0_linear_17931_893\" x1=\"11.6469\" y1=\"66.8772\" x2=\"1.23198\" y2=\"-0.802501\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint1_linear_17931_893\" x1=\"39.9916\" y1=\"62.5154\" x2=\"29.5768\" y2=\"-5.1639\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint2_linear_17931_893\" x1=\"25.5063\" y1=\"64.7445\" x2=\"15.0913\" y2=\"-2.93516\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint3_linear_17931_893\" x1=\"18.5788\" y1=\"65.8105\" x2=\"8.1638\" y2=\"-1.86922\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint4_linear_17931_893\" x1=\"11.6394\" y1=\"66.8784\" x2=\"1.22448\" y2=\"-0.80128\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint5_linear_17931_893\" x1=\"39.9982\" y1=\"62.5143\" x2=\"29.5833\" y2=\"-5.16528\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint6_linear_17931_893\" x1=\"25.506\" y1=\"64.7443\" x2=\"15.091\" y2=\"-2.93537\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint7_linear_17931_893\" x1=\"18.5788\" y1=\"65.8103\" x2=\"8.16407\" y2=\"-1.86867\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint8_linear_17931_893\" x1=\"10.1623\" y1=\"65.7597\" x2=\"0.284696\" y2=\"1.57166\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint9_linear_17931_893\" x1=\"13.0129\" y1=\"67.1431\" x2=\"2.40785\" y2=\"-1.77243\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint10_linear_17931_893\" x1=\"17.6338\" y1=\"68.0331\" x2=\"6.38943\" y2=\"-5.0367\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint11_linear_17931_893\" x1=\"19.8305\" y1=\"65.208\" x2=\"9.57925\" y2=\"-1.40832\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint12_linear_17931_893\" x1=\"23.7353\" y1=\"61.7393\" x2=\"14.6289\" y2=\"2.56246\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint13_linear_17931_893\" x1=\"26.4465\" y1=\"62.1967\" x2=\"16.9911\" y2=\"0.751851\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n</defs>\n</svg>"}, "model": {"version": "1.8.11"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#F15B2A", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\"\n         viewBox=\"0 0 103.7 112.7\" style=\"enable-background:new 0 0 103.7 112.7;\" xml:space=\"preserve\">\n<style type=\"text/css\">\n        .st0{fill:url(#SVGID_1_);}\n</style>\n<linearGradient id=\"SVGID_1_\" gradientUnits=\"userSpaceOnUse\" x1=\"51.85\" y1=\"1069.5107\" x2=\"51.85\" y2=\"966.6585\" gradientTransform=\"matrix(1 0 0 1 0 -931.4)\">\n        <stop  offset=\"0\" style=\"stop-color:#FCEE1F\"/>\n        <stop  offset=\"1\" style=\"stop-color:#F15B2A\"/>\n</linearGradient>\n<path class=\"st0\" d=\"M103.5,49.9c-0.2-1.9-0.5-4.1-1.1-6.5c-0.6-2.4-1.6-5-2.9-7.8c-1.4-2.7-3.1-5.6-5.4-8.3\n        c-0.9-1.1-1.9-2.1-2.9-3.2c1.6-6.3-1.9-11.8-1.9-11.8c-6.1-0.4-9.9,1.9-11.3,2.9c-0.2-0.1-0.5-0.2-0.7-0.3c-1-0.4-2.1-0.8-3.2-1.2\n        c-1.1-0.3-2.2-0.7-3.3-0.9c-1.1-0.3-2.3-0.5-3.5-0.7c-0.2,0-0.4-0.1-0.6-0.1C64.1,3.6,56.5,0,56.5,0c-8.7,5.6-10.4,13.1-10.4,13.1\n        s0,0.2-0.1,0.4c-0.5,0.1-0.9,0.3-1.4,0.4c-0.6,0.2-1.3,0.4-1.9,0.7c-0.6,0.3-1.3,0.5-1.9,0.8c-1.3,0.6-2.5,1.2-3.8,1.9\n        c-1.2,0.7-2.4,1.4-3.5,2.2c-0.2-0.1-0.3-0.2-0.3-0.2c-11.7-4.5-22.1,0.9-22.1,0.9c-0.9,12.5,4.7,20.3,5.8,21.7\n        c-0.3,0.8-0.5,1.5-0.8,2.3c-0.9,2.8-1.5,5.7-1.9,8.7c-0.1,0.4-0.1,0.9-0.2,1.3C3.2,59.5,0,70.5,0,70.5c9,10.4,19.6,11,19.6,11l0,0\n        c1.3,2.4,2.9,4.7,4.6,6.8c0.7,0.9,1.5,1.7,2.3,2.6c-3.3,9.4,0.5,17.3,0.5,17.3c10.1,0.4,16.7-4.4,18.1-5.5c1,0.3,2,0.6,3,0.9\n        c3.1,0.8,6.3,1.3,9.4,1.4c0.8,0,1.6,0,2.4,0h0.4h0.3h0.5h0.5l0,0c4.7,6.8,13.1,7.7,13.1,7.7c5.9-6.3,6.3-12.4,6.3-13.8l0,0\n        c0,0,0,0,0-0.1s0-0.2,0-0.2l0,0c0-0.1,0-0.2,0-0.3c1.2-0.9,2.4-1.8,3.6-2.8c2.4-2.1,4.4-4.6,6.2-7.2c0.2-0.2,0.3-0.5,0.5-0.7\n        c6.7,0.4,11.4-4.2,11.4-4.2c-1.1-7-5.1-10.4-5.9-11l0,0c0,0,0,0-0.1-0.1l-0.1-0.1l0,0l-0.1-0.1c0-0.4,0.1-0.8,0.1-1.3\n        c0.1-0.8,0.1-1.5,0.1-2.3v-0.6v-0.3v-0.1c0-0.2,0-0.1,0-0.2v-0.5v-0.6c0-0.2,0-0.4,0-0.6s0-0.4-0.1-0.6l-0.1-0.6l-0.1-0.6\n        c-0.1-0.8-0.3-1.5-0.4-2.3c-0.7-3-1.9-5.9-3.4-8.4c-1.6-2.6-3.5-4.8-5.7-6.8c-2.2-1.9-4.6-3.5-7.2-4.6c-2.6-1.2-5.2-1.9-7.9-2.2\n        c-1.3-0.2-2.7-0.2-4-0.2h-0.5h-0.1H67h-0.2h-0.5c-0.2,0-0.4,0-0.5,0c-0.7,0.1-1.4,0.2-2,0.3c-2.7,0.5-5.2,1.5-7.4,2.8\n        c-2.2,1.3-4.1,3-5.7,4.9s-2.8,3.9-3.6,6.1c-0.8,2.1-1.3,4.4-1.4,6.5c0,0.5,0,1.1,0,1.6c0,0.1,0,0.3,0,0.4v0.4c0,0.3,0,0.5,0.1,0.8\n        c0.1,1.1,0.3,2.1,0.6,3.1c0.6,2,1.5,3.8,2.7,5.4s2.5,2.8,4,3.8s3,1.7,4.6,2.2s3.1,0.7,4.5,0.6c0.2,0,0.4,0,0.5,0s0.2,0,0.3,0\n        s0.2,0,0.3,0c0.2,0,0.3,0,0.5,0h0.1H64c0.1,0,0.2,0,0.3,0c0.2,0,0.4-0.1,0.5-0.1c0.2,0,0.3-0.1,0.5-0.1c0.3-0.1,0.7-0.2,1-0.3\n        c0.6-0.2,1.2-0.5,1.8-0.7c0.6-0.3,1.1-0.6,1.5-0.9c0.1-0.1,0.3-0.2,0.4-0.3c0.5-0.4,0.6-1.1,0.2-1.6c-0.4-0.4-1-0.5-1.5-0.3\n        c-0.1,0.1-0.2,0.1-0.4,0.2c-0.4,0.2-0.9,0.4-1.3,0.5c-0.5,0.1-1,0.3-1.5,0.4c-0.3,0-0.5,0.1-0.8,0.1c-0.1,0-0.3,0-0.4,0\n        c-0.1,0-0.3,0-0.4,0s-0.3,0-0.4,0c-0.2,0-0.3,0-0.5,0c0,0-0.1,0,0,0h-0.1h-0.1c-0.1,0-0.1,0-0.2,0s-0.3,0-0.4-0.1\n        c-1.1-0.2-2.3-0.5-3.4-1s-2.2-1.2-3.1-2.1c-1-0.9-1.8-1.9-2.5-3.1s-1.1-2.5-1.3-3.8c-0.1-0.7-0.2-1.4-0.1-2.1c0-0.2,0-0.4,0-0.6\n        c0,0.1,0,0,0,0v-0.1v-0.1c0-0.1,0-0.2,0-0.3c0-0.4,0.1-0.7,0.2-1.1c0.5-3,2-5.9,4.3-8.1c0.6-0.6,1.2-1.1,1.9-1.5\n        c0.7-0.5,1.4-0.9,2.1-1.2s1.5-0.6,2.3-0.8s1.6-0.4,2.4-0.4c0.4,0,0.8-0.1,1.2-0.1c0.1,0,0.2,0,0.3,0h0.3H67c0.1,0,0,0,0,0h0.1h0.3\n        c0.9,0.1,1.8,0.2,2.6,0.4c1.7,0.4,3.4,1,5,1.9c3.2,1.8,5.9,4.5,7.5,7.8c0.8,1.6,1.4,3.4,1.7,5.3c0.1,0.5,0.1,0.9,0.2,1.4v0.3V66\n        c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3v0.3v0.3c0,0.2,0,0.6,0,0.8c0,0.5-0.1,1-0.1,1.5c-0.1,0.5-0.1,1-0.2,1.5\n        c-0.1,0.5-0.2,1-0.3,1.5c-0.2,1-0.6,1.9-0.9,2.9c-0.7,1.9-1.7,3.7-2.9,5.3c-2.4,3.3-5.7,6-9.4,7.7c-1.9,0.8-3.8,1.5-5.8,1.8\n        c-1,0.2-2,0.3-3,0.3h-0.2h-0.2h-0.3h-0.5h-0.3c0.1,0,0,0,0,0h-0.1c-0.5,0-1.1,0-1.6-0.1c-2.2-0.2-4.3-0.6-6.4-1.2s-4.1-1.4-6-2.4\n        c-3.8-2-7.2-4.9-9.9-8.2c-1.3-1.7-2.5-3.5-3.5-5.4s-1.7-3.9-2.3-5.9s-0.9-4.1-1-6.2v-0.4v-0.1v-0.1v-0.2V60v-0.1v-0.1v-0.2v-0.5V59\n        l0,0v-0.2c0-0.3,0-0.5,0-0.8c0-1,0.1-2.1,0.3-3.2c0.1-1.1,0.3-2.1,0.5-3.2c0.2-1.1,0.5-2.1,0.8-3.2c0.6-2.1,1.3-4.1,2.2-6\n        c1.8-3.8,4.1-7.2,6.8-9.9c0.7-0.7,1.4-1.3,2.2-1.9c0.3-0.3,1-0.9,1.8-1.4s1.6-1,2.5-1.4c0.4-0.2,0.8-0.4,1.3-0.6\n        c0.2-0.1,0.4-0.2,0.7-0.3c0.2-0.1,0.4-0.2,0.7-0.3c0.9-0.4,1.8-0.7,2.7-1c0.2-0.1,0.5-0.1,0.7-0.2s0.5-0.1,0.7-0.2\n        c0.5-0.1,0.9-0.2,1.4-0.4c0.2-0.1,0.5-0.1,0.7-0.2c0.2,0,0.5-0.1,0.7-0.1s0.5-0.1,0.7-0.1l0.4-0.1l0.4-0.1c0.2,0,0.5-0.1,0.7-0.1\n        c0.3,0,0.5-0.1,0.8-0.1c0.2,0,0.6-0.1,0.8-0.1c0.2,0,0.3,0,0.5-0.1h0.3H61h0.2c0.3,0,0.5,0,0.8-0.1h0.4c0,0,0.1,0,0,0h0.1h0.2\n        c0.2,0,0.5,0,0.7,0c0.9,0,1.8,0,2.7,0c1.8,0.1,3.6,0.3,5.3,0.6c3.4,0.6,6.7,1.7,9.6,3.2c2.9,1.4,5.6,3.2,7.8,5.1\n        c0.1,0.1,0.3,0.2,0.4,0.4c0.1,0.1,0.3,0.2,0.4,0.4c0.3,0.2,0.5,0.5,0.8,0.7s0.5,0.5,0.8,0.7c0.2,0.3,0.5,0.5,0.7,0.8\n        c1,1,1.9,2.1,2.7,3.1c1.6,2.1,2.9,4.2,3.9,6.2c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.2,0.2,0.5,0.4,0.7\n        c0.1,0.2,0.2,0.5,0.3,0.7c0.1,0.2,0.2,0.5,0.3,0.7c0.4,0.9,0.7,1.8,1,2.7c0.5,1.4,0.8,2.6,1.1,3.6c0.1,0.4,0.5,0.7,0.9,0.7\n        c0.5,0,0.8-0.4,0.8-0.9C103.6,52.7,103.6,51.4,103.5,49.9z\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\"\n         viewBox=\"0 0 103.7 112.7\" style=\"enable-background:new 0 0 103.7 112.7;\" xml:space=\"preserve\" width='103.7' height='112.7'>\n<style type=\"text/css\">\n        .st0{fill:#FFFFFF;}\n</style>\n<path class=\"st0\" d=\"M103.5,49.9c-0.2-1.9-0.5-4.1-1.1-6.5c-0.6-2.4-1.6-5-2.9-7.8c-1.4-2.7-3.1-5.6-5.4-8.3\n        c-0.9-1.1-1.9-2.1-2.9-3.2c1.6-6.3-1.9-11.8-1.9-11.8c-6.1-0.4-9.9,1.9-11.3,2.9c-0.2-0.1-0.5-0.2-0.7-0.3c-1-0.4-2.1-0.8-3.2-1.2\n        c-1.1-0.3-2.2-0.7-3.3-0.9c-1.1-0.3-2.3-0.5-3.5-0.7c-0.2,0-0.4-0.1-0.6-0.1C64.1,3.6,56.5,0,56.5,0c-8.7,5.6-10.4,13.1-10.4,13.1\n        s0,0.2-0.1,0.4c-0.5,0.1-0.9,0.3-1.4,0.4c-0.6,0.2-1.3,0.4-1.9,0.7c-0.6,0.3-1.3,0.5-1.9,0.8c-1.3,0.6-2.5,1.2-3.8,1.9\n        c-1.2,0.7-2.4,1.4-3.5,2.2c-0.2-0.1-0.3-0.2-0.3-0.2c-11.7-4.5-22.1,0.9-22.1,0.9c-0.9,12.5,4.7,20.3,5.8,21.7\n        c-0.3,0.8-0.5,1.5-0.8,2.3c-0.9,2.8-1.5,5.7-1.9,8.7c-0.1,0.4-0.1,0.9-0.2,1.3C3.2,59.5,0,70.5,0,70.5c9,10.4,19.6,11,19.6,11l0,0\n        c1.3,2.4,2.9,4.7,4.6,6.8c0.7,0.9,1.5,1.7,2.3,2.6c-3.3,9.4,0.5,17.3,0.5,17.3c10.1,0.4,16.7-4.4,18.1-5.5c1,0.3,2,0.6,3,0.9\n        c3.1,0.8,6.3,1.3,9.4,1.4c0.8,0,1.6,0,2.4,0h0.4h0.3h0.5h0.5l0,0c4.7,6.8,13.1,7.7,13.1,7.7c5.9-6.3,6.3-12.4,6.3-13.8l0,0\n        c0,0,0,0,0-0.1s0-0.2,0-0.2l0,0c0-0.1,0-0.2,0-0.3c1.2-0.9,2.4-1.8,3.6-2.8c2.4-2.1,4.4-4.6,6.2-7.2c0.2-0.2,0.3-0.5,0.5-0.7\n        c6.7,0.4,11.4-4.2,11.4-4.2c-1.1-7-5.1-10.4-5.9-11l0,0c0,0,0,0-0.1-0.1l-0.1-0.1l0,0l-0.1-0.1c0-0.4,0.1-0.8,0.1-1.3\n        c0.1-0.8,0.1-1.5,0.1-2.3v-0.6v-0.3v-0.1c0-0.2,0-0.1,0-0.2v-0.5v-0.6c0-0.2,0-0.4,0-0.6s0-0.4-0.1-0.6l-0.1-0.6l-0.1-0.6\n        c-0.1-0.8-0.3-1.5-0.4-2.3c-0.7-3-1.9-5.9-3.4-8.4c-1.6-2.6-3.5-4.8-5.7-6.8c-2.2-1.9-4.6-3.5-7.2-4.6c-2.6-1.2-5.2-1.9-7.9-2.2\n        c-1.3-0.2-2.7-0.2-4-0.2h-0.5h-0.1H67h-0.2h-0.5c-0.2,0-0.4,0-0.5,0c-0.7,0.1-1.4,0.2-2,0.3c-2.7,0.5-5.2,1.5-7.4,2.8\n        c-2.2,1.3-4.1,3-5.7,4.9s-2.8,3.9-3.6,6.1c-0.8,2.1-1.3,4.4-1.4,6.5c0,0.5,0,1.1,0,1.6c0,0.1,0,0.3,0,0.4v0.4c0,0.3,0,0.5,0.1,0.8\n        c0.1,1.1,0.3,2.1,0.6,3.1c0.6,2,1.5,3.8,2.7,5.4s2.5,2.8,4,3.8s3,1.7,4.6,2.2s3.1,0.7,4.5,0.6c0.2,0,0.4,0,0.5,0s0.2,0,0.3,0\n        s0.2,0,0.3,0c0.2,0,0.3,0,0.5,0h0.1H64c0.1,0,0.2,0,0.3,0c0.2,0,0.4-0.1,0.5-0.1c0.2,0,0.3-0.1,0.5-0.1c0.3-0.1,0.7-0.2,1-0.3\n        c0.6-0.2,1.2-0.5,1.8-0.7c0.6-0.3,1.1-0.6,1.5-0.9c0.1-0.1,0.3-0.2,0.4-0.3c0.5-0.4,0.6-1.1,0.2-1.6c-0.4-0.4-1-0.5-1.5-0.3\n        c-0.1,0.1-0.2,0.1-0.4,0.2c-0.4,0.2-0.9,0.4-1.3,0.5c-0.5,0.1-1,0.3-1.5,0.4c-0.3,0-0.5,0.1-0.8,0.1c-0.1,0-0.3,0-0.4,0\n        c-0.1,0-0.3,0-0.4,0s-0.3,0-0.4,0c-0.2,0-0.3,0-0.5,0c0,0-0.1,0,0,0h-0.1h-0.1c-0.1,0-0.1,0-0.2,0s-0.3,0-0.4-0.1\n        c-1.1-0.2-2.3-0.5-3.4-1s-2.2-1.2-3.1-2.1c-1-0.9-1.8-1.9-2.5-3.1s-1.1-2.5-1.3-3.8c-0.1-0.7-0.2-1.4-0.1-2.1c0-0.2,0-0.4,0-0.6\n        c0,0.1,0,0,0,0v-0.1v-0.1c0-0.1,0-0.2,0-0.3c0-0.4,0.1-0.7,0.2-1.1c0.5-3,2-5.9,4.3-8.1c0.6-0.6,1.2-1.1,1.9-1.5\n        c0.7-0.5,1.4-0.9,2.1-1.2s1.5-0.6,2.3-0.8s1.6-0.4,2.4-0.4c0.4,0,0.8-0.1,1.2-0.1c0.1,0,0.2,0,0.3,0h0.3H67c0.1,0,0,0,0,0h0.1h0.3\n        c0.9,0.1,1.8,0.2,2.6,0.4c1.7,0.4,3.4,1,5,1.9c3.2,1.8,5.9,4.5,7.5,7.8c0.8,1.6,1.4,3.4,1.7,5.3c0.1,0.5,0.1,0.9,0.2,1.4v0.3V66\n        c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3v0.3v0.3c0,0.2,0,0.6,0,0.8c0,0.5-0.1,1-0.1,1.5c-0.1,0.5-0.1,1-0.2,1.5\n        c-0.1,0.5-0.2,1-0.3,1.5c-0.2,1-0.6,1.9-0.9,2.9c-0.7,1.9-1.7,3.7-2.9,5.3c-2.4,3.3-5.7,6-9.4,7.7c-1.9,0.8-3.8,1.5-5.8,1.8\n        c-1,0.2-2,0.3-3,0.3h-0.2h-0.2h-0.3h-0.5h-0.3c0.1,0,0,0,0,0h-0.1c-0.5,0-1.1,0-1.6-0.1c-2.2-0.2-4.3-0.6-6.4-1.2s-4.1-1.4-6-2.4\n        c-3.8-2-7.2-4.9-9.9-8.2c-1.3-1.7-2.5-3.5-3.5-5.4s-1.7-3.9-2.3-5.9s-0.9-4.1-1-6.2v-0.4v-0.1v-0.1v-0.2V60v-0.1v-0.1v-0.2v-0.5V59\n        l0,0v-0.2c0-0.3,0-0.5,0-0.8c0-1,0.1-2.1,0.3-3.2c0.1-1.1,0.3-2.1,0.5-3.2c0.2-1.1,0.5-2.1,0.8-3.2c0.6-2.1,1.3-4.1,2.2-6\n        c1.8-3.8,4.1-7.2,6.8-9.9c0.7-0.7,1.4-1.3,2.2-1.9c0.3-0.3,1-0.9,1.8-1.4s1.6-1,2.5-1.4c0.4-0.2,0.8-0.4,1.3-0.6\n        c0.2-0.1,0.4-0.2,0.7-0.3c0.2-0.1,0.4-0.2,0.7-0.3c0.9-0.4,1.8-0.7,2.7-1c0.2-0.1,0.5-0.1,0.7-0.2s0.5-0.1,0.7-0.2\n        c0.5-0.1,0.9-0.2,1.4-0.4c0.2-0.1,0.5-0.1,0.7-0.2c0.2,0,0.5-0.1,0.7-0.1s0.5-0.1,0.7-0.1l0.4-0.1l0.4-0.1c0.2,0,0.5-0.1,0.7-0.1\n        c0.3,0,0.5-0.1,0.8-0.1c0.2,0,0.6-0.1,0.8-0.1c0.2,0,0.3,0,0.5-0.1h0.3H61h0.2c0.3,0,0.5,0,0.8-0.1h0.4c0,0,0.1,0,0,0h0.1h0.2\n        c0.2,0,0.5,0,0.7,0c0.9,0,1.8,0,2.7,0c1.8,0.1,3.6,0.3,5.3,0.6c3.4,0.6,6.7,1.7,9.6,3.2c2.9,1.4,5.6,3.2,7.8,5.1\n        c0.1,0.1,0.3,0.2,0.4,0.4c0.1,0.1,0.3,0.2,0.4,0.4c0.3,0.2,0.5,0.5,0.8,0.7s0.5,0.5,0.8,0.7c0.2,0.3,0.5,0.5,0.7,0.8\n        c1,1,1.9,2.1,2.7,3.1c1.6,2.1,2.9,4.2,3.9,6.2c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.2,0.2,0.5,0.4,0.7\n        c0.1,0.2,0.2,0.5,0.3,0.7c0.1,0.2,0.2,0.5,0.3,0.7c0.4,0.9,0.7,1.8,1,2.7c0.5,1.4,0.8,2.6,1.1,3.6c0.1,0.4,0.5,0.7,0.9,0.7\n        c0.5,0,0.8-0.4,0.8-0.9C103.6,52.7,103.6,51.4,103.5,49.9z\"/>\n</svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/grafana/helm-charts/releases/download/loki-simple-scalable-1.8.11/loki-simple-scalable-1.8.11.tgz"}, "configuration": null, "component": {"version": "monitoring.grafana.com/v1alpha1", "kind": "Integration", "schema": "{\n \"description\": \"Integration runs a single Grafana Agent integration. Integrations that generate telemetry must be configured to send that telemetry somewhere; such as autoscrape for exporter-based integrations. \\n Integrations have access to the LogsInstances and MetricsInstances in the same GrafanaAgent resource set, referenced by the \\u003cnamespace\\u003e/\\u003cname\\u003e of the *Instance resource. \\n For example, if there is a default/production MetricsInstance, you can configure a supported integration's autoscrape block with: \\n autoscrape: enable: true metrics_instance: default/production \\n There is currently no way for telemetry created by an Operator-managed integration to be collected from outside of the integration itself.\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Specifies the desired behavior of the Integration.\",\n   \"properties\": {\n    \"config\": {\n     \"description\": \"The configuration for the named integration. Note that integrations are deployed with the integrations-next feature flag, which has different common settings: \\n https://grafana.com/docs/agent/latest/configuration/integrations/integrations-next/\",\n     \"format\": \"textarea\",\n     \"type\": \"string\"\n    },\n    \"configMaps\": {\n     \"description\": \"An extra list of keys from ConfigMaps in the same namespace as the Integration which will be mounted into the Grafana Agent pod running this integration. \\n ConfigMaps will be mounted at /etc/grafana-agent/integrations/configMaps/\\u003cconfigmap_namespace\\u003e/\\u003cconfigmap_name\\u003e/\\u003ckey\\u003e.\",\n     \"items\": {\n      \"description\": \"Selects a key from a ConfigMap.\",\n      \"properties\": {\n       \"key\": {\n        \"description\": \"The key to select.\",\n        \"type\": \"string\"\n       },\n       \"name\": {\n        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n        \"type\": \"string\"\n       },\n       \"optional\": {\n        \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n        \"type\": \"boolean\"\n       }\n      },\n      \"required\": [\n       \"key\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"name\": {\n     \"description\": \"Name of the integration to run (e.g., \\\"node_exporter\\\", \\\"mysqld_exporter\\\").\",\n     \"type\": \"string\"\n    },\n    \"secrets\": {\n     \"description\": \"An extra list of keys from Secrets in the same namespace as the Integration which will be mounted into the Grafana Agent pod running this integration. \\n Secrets will be mounted at /etc/grafana-agent/integrations/secrets/\\u003csecret_namespace\\u003e/\\u003csecret_name\\u003e/\\u003ckey\\u003e.\",\n     \"items\": {\n      \"description\": \"SecretKeySelector selects a key of a Secret.\",\n      \"properties\": {\n       \"key\": {\n        \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n        \"type\": \"string\"\n       },\n       \"name\": {\n        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n        \"type\": \"string\"\n       },\n       \"optional\": {\n        \"description\": \"Specify whether the Secret or its key must be defined\",\n        \"type\": \"boolean\"\n       }\n      },\n      \"required\": [\n       \"key\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"type\": {\n     \"description\": \"Type informs Grafana Agent Operator how to manage the integration being configured.\",\n     \"properties\": {\n      \"allNodes\": {\n       \"description\": \"When true, the configured integration should be run on every Node in the cluster. This is required for integrations that generate Node-specific metrics like node_exporter, otherwise it must be false to avoid generating duplicate metrics.\",\n       \"type\": \"boolean\"\n      },\n      \"unique\": {\n       \"description\": \"Whether this integration can only be defined once for a Grafana Agent process, such as statsd_exporter. It is invalid for a GrafanaAgent to discover multiple unique Integrations with the same integration name (i.e., a single GrafanaAgent cannot deploy two statsd_exporters).\",\n       \"type\": \"boolean\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"volumeMounts\": {\n     \"description\": \"An extra list of VolumeMounts to be associated with the Grafana Agent pods running this integration. VolumeMount names will be mutated to be unique across all used IntegrationSpecs. \\n Mount paths should include the namespace/name of the Integration CR to avoid potentially colliding with other resources.\",\n     \"items\": {\n      \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n      \"properties\": {\n       \"mountPath\": {\n        \"description\": \"Path within the container at which the volume should be mounted.  Must not contain ':'.\",\n        \"type\": \"string\"\n       },\n       \"mountPropagation\": {\n        \"description\": \"mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10.\",\n        \"type\": \"string\"\n       },\n       \"name\": {\n        \"description\": \"This must match the Name of a Volume.\",\n        \"type\": \"string\"\n       },\n       \"readOnly\": {\n        \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.\",\n        \"type\": \"boolean\"\n       },\n       \"subPath\": {\n        \"description\": \"Path within the volume from which the container's volume should be mounted. Defaults to \\\"\\\" (volume's root).\",\n        \"type\": \"string\"\n       },\n       \"subPathExpr\": {\n        \"description\": \"Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \\\"\\\" (volume's root). SubPathExpr and SubPath are mutually exclusive.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"mountPath\",\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"volumes\": {\n     \"description\": \"An extra list of Volumes to be associated with the Grafana Agent pods running this integration. Volume names will be mutated to be unique across all Integrations. Note that the specified volumes should be able to tolerate existing on multiple pods at once when type is daemonset. \\n Don't use volumes for loading secrets/configMaps from the same namespace as the Integration; use the secrets and configMaps fields instead.\",\n     \"items\": {\n      \"description\": \"Volume represents a named volume in a pod that may be accessed by any container in the pod.\",\n      \"properties\": {\n       \"awsElasticBlockStore\": {\n        \"description\": \"AWSElasticBlockStore represents an AWS Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"partition\": {\n          \"description\": \"The partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \\\"1\\\". Similarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty).\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"readOnly\": {\n          \"description\": \"Specify \\\"true\\\" to force and set the ReadOnly property in VolumeMounts to \\\"true\\\". If omitted, the default is \\\"false\\\". More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n          \"type\": \"boolean\"\n         },\n         \"volumeID\": {\n          \"description\": \"Unique ID of the persistent disk resource in AWS (Amazon EBS volume). More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"azureDisk\": {\n        \"description\": \"AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.\",\n        \"properties\": {\n         \"cachingMode\": {\n          \"description\": \"Host Caching mode: None, Read Only, Read Write.\",\n          \"type\": \"string\"\n         },\n         \"diskName\": {\n          \"description\": \"The Name of the data disk in the blob storage\",\n          \"type\": \"string\"\n         },\n         \"diskURI\": {\n          \"description\": \"The URI the data disk in the blob storage\",\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"kind\": {\n          \"description\": \"Expected values Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"diskName\",\n         \"diskURI\"\n        ],\n        \"type\": \"object\"\n       },\n       \"azureFile\": {\n        \"description\": \"AzureFile represents an Azure File Service mount on the host and bind mount to the pod.\",\n        \"properties\": {\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretName\": {\n          \"description\": \"the name of secret that contains Azure Storage Account Name and Key\",\n          \"type\": \"string\"\n         },\n         \"shareName\": {\n          \"description\": \"Share Name\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"secretName\",\n         \"shareName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"cephfs\": {\n        \"description\": \"CephFS represents a Ceph FS mount on the host that shares a pod's lifetime\",\n        \"properties\": {\n         \"monitors\": {\n          \"description\": \"Required: Monitors is a collection of Ceph monitors More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"path\": {\n          \"description\": \"Optional: Used as the mounted root, rather than the full Ceph tree, default is /\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"type\": \"boolean\"\n         },\n         \"secretFile\": {\n          \"description\": \"Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"secretRef\": {\n          \"description\": \"Optional: SecretRef is reference to the authentication secret for User, default is empty. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"user\": {\n          \"description\": \"Optional: User is the rados user name, default is admin More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"monitors\"\n        ],\n        \"type\": \"object\"\n       },\n       \"cinder\": {\n        \"description\": \"Cinder represents a cinder volume attached and mounted on kubelets host machine. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"Optional: points to a secret object containing parameters used to connect to OpenStack.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"volumeID\": {\n          \"description\": \"volume id used to identify the volume in cinder. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"configMap\": {\n        \"description\": \"ConfigMap represents a configMap that should populate this volume\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"description\": \"If unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n          \"items\": {\n           \"description\": \"Maps a string key to a path within a volume.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to project.\",\n             \"type\": \"string\"\n            },\n            \"mode\": {\n             \"description\": \"Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"name\": {\n          \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n          \"type\": \"string\"\n         },\n         \"optional\": {\n          \"description\": \"Specify whether the ConfigMap or its keys must be defined\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"csi\": {\n        \"description\": \"CSI (Container Storage Interface) represents ephemeral storage that is handled by certain external CSI drivers (Beta feature).\",\n        \"properties\": {\n         \"driver\": {\n          \"description\": \"Driver is the name of the CSI driver that handles this volume. Consult with your admin for the correct name as registered in the cluster.\",\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". If not provided, the empty value is passed to the associated CSI driver which will determine the default filesystem to apply.\",\n          \"type\": \"string\"\n         },\n         \"nodePublishSecretRef\": {\n          \"description\": \"NodePublishSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI NodePublishVolume and NodeUnpublishVolume calls. This field is optional, and  may be empty if no secret is required. If the secret object contains more than one secret, all secret references are passed.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"readOnly\": {\n          \"description\": \"Specifies a read-only configuration for the volume. Defaults to false (read/write).\",\n          \"type\": \"boolean\"\n         },\n         \"volumeAttributes\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"VolumeAttributes stores driver-specific properties that are passed to the CSI driver. Consult your driver's documentation for supported values.\",\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"driver\"\n        ],\n        \"type\": \"object\"\n       },\n       \"downwardAPI\": {\n        \"description\": \"DownwardAPI represents downward API about the pod that should populate this volume\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"Optional: mode bits to use on created files by default. Must be a Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"description\": \"Items is a list of downward API volume file\",\n          \"items\": {\n           \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n           \"properties\": {\n            \"fieldRef\": {\n             \"description\": \"Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.\",\n             \"properties\": {\n              \"apiVersion\": {\n               \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n               \"type\": \"string\"\n              },\n              \"fieldPath\": {\n               \"description\": \"Path of the field to select in the specified API version.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"fieldPath\"\n             ],\n             \"type\": \"object\"\n            },\n            \"mode\": {\n             \"description\": \"Optional: mode bits used to set permissions on this file, must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n             \"type\": \"string\"\n            },\n            \"resourceFieldRef\": {\n             \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n             \"properties\": {\n              \"containerName\": {\n               \"description\": \"Container name: required for volumes, optional for env vars\",\n               \"type\": \"string\"\n              },\n              \"divisor\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"resource\": {\n               \"description\": \"Required: resource to select\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"resource\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"emptyDir\": {\n        \"description\": \"EmptyDir represents a temporary directory that shares a pod's lifetime. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n        \"properties\": {\n         \"medium\": {\n          \"description\": \"What type of storage medium should back this directory. The default is \\\"\\\" which means to use the node's default medium. Must be an empty string (default) or Memory. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n          \"type\": \"string\"\n         },\n         \"sizeLimit\": {\n          \"anyOf\": [\n           {\n            \"type\": \"integer\"\n           },\n           {\n            \"type\": \"string\"\n           }\n          ],\n          \"description\": \"Total amount of local storage required for this EmptyDir volume. The size limit is also applicable for memory medium. The maximum usage on memory medium EmptyDir would be the minimum value between the SizeLimit specified here and the sum of memory limits of all containers in a pod. The default is nil which means that the limit is undefined. More info: http://kubernetes.io/docs/user-guide/volumes#emptydir\",\n          \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n          \"x-kubernetes-int-or-string\": true\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"ephemeral\": {\n        \"description\": \"Ephemeral represents a volume that is handled by a cluster storage driver. The volume's lifecycle is tied to the pod that defines it - it will be created before the pod starts, and deleted when the pod is removed. \\n Use this if: a) the volume is only needed while the pod runs, b) features of normal volumes like restoring from snapshot or capacity tracking are needed, c) the storage driver is specified through a storage class, and d) the storage driver supports dynamic volume provisioning through a PersistentVolumeClaim (see EphemeralVolumeSource for more information on the connection between this volume type and PersistentVolumeClaim). \\n Use PersistentVolumeClaim or one of the vendor-specific APIs for volumes that persist for longer than the lifecycle of an individual pod. \\n Use CSI for light-weight local ephemeral volumes if the CSI driver is meant to be used that way - see the documentation of the driver for more information. \\n A pod can use both types of ephemeral volumes and persistent volumes at the same time.\",\n        \"properties\": {\n         \"volumeClaimTemplate\": {\n          \"description\": \"Will be used to create a stand-alone PVC to provision the volume. The pod in which this EphemeralVolumeSource is embedded will be the owner of the PVC, i.e. the PVC will be deleted together with the pod.  The name of the PVC will be `\\u003cpod name\\u003e-\\u003cvolume name\\u003e` where `\\u003cvolume name\\u003e` is the name from the `PodSpec.Volumes` array entry. Pod validation will reject the pod if the concatenated name is not valid for a PVC (for example, too long). \\n An existing PVC with that name that is not owned by the pod will *not* be used for the pod to avoid using an unrelated volume by mistake. Starting the pod is then blocked until the unrelated PVC is removed. If such a pre-created PVC is meant to be used by the pod, the PVC has to updated with an owner reference to the pod once the pod exists. Normally this should not be necessary, but it may be useful when manually reconstructing a broken cluster. \\n This field is read-only and no changes will be made by Kubernetes to the PVC after it has been created. \\n Required, must not be nil.\",\n          \"properties\": {\n           \"metadata\": {\n            \"description\": \"May contain labels and annotations that will be copied into the PVC when creating it. No other fields are allowed and will be rejected during validation.\",\n            \"type\": \"object\"\n           },\n           \"spec\": {\n            \"description\": \"The specification for the PersistentVolumeClaim. The entire content is copied unchanged into the PVC that gets created from this template. The same fields as in a PersistentVolumeClaim are also valid here.\",\n            \"properties\": {\n             \"accessModes\": {\n              \"description\": \"AccessModes contains the desired access modes the volume should have. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"dataSource\": {\n              \"description\": \"This field can be used to specify either: * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot) * An existing PVC (PersistentVolumeClaim) If the provisioner or an external controller can support the specified data source, it will create a new volume based on the contents of the specified data source. If the AnyVolumeDataSource feature gate is enabled, this field will always have the same contents as the DataSourceRef field.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind is the type of resource being referenced\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name is the name of resource being referenced\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"dataSourceRef\": {\n              \"description\": \"Specifies the object from which to populate the volume with data, if a non-empty volume is desired. This may be any local object from a non-empty API group (non core object) or a PersistentVolumeClaim object. When this field is specified, volume binding will only succeed if the type of the specified object matches some installed volume populator or dynamic provisioner. This field will replace the functionality of the DataSource field and as such if both fields are non-empty, they must have the same value. For backwards compatibility, both fields (DataSource and DataSourceRef) will be set to the same value automatically if one of them is empty and the other is non-empty. There are two important differences between DataSource and DataSourceRef: * While DataSource only allows two specific types of objects, DataSourceRef allows any non-core object, as well as PersistentVolumeClaim objects. * While DataSource ignores disallowed values (dropping them), DataSourceRef preserves all values, and generates an error if a disallowed value is specified. (Alpha) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind is the type of resource being referenced\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name is the name of resource being referenced\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"resources\": {\n              \"description\": \"Resources represents the minimum resources the volume should have. If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements that are lower than previous value but must still be higher than capacity recorded in the status field of the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n              \"properties\": {\n               \"limits\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"description\": \"Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                \"type\": \"object\"\n               },\n               \"requests\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"description\": \"Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"selector\": {\n              \"description\": \"A label query over volumes to consider for binding.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"storageClassName\": {\n              \"description\": \"Name of the StorageClass required by the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n              \"type\": \"string\"\n             },\n             \"volumeMode\": {\n              \"description\": \"volumeMode defines what type of volume is required by the claim. Value of Filesystem is implied when not included in claim spec.\",\n              \"type\": \"string\"\n             },\n             \"volumeName\": {\n              \"description\": \"VolumeName is the binding reference to the PersistentVolume backing this claim.\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"spec\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"fc\": {\n        \"description\": \"FC represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"lun\": {\n          \"description\": \"Optional: FC target lun number\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"readOnly\": {\n          \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"targetWWNs\": {\n          \"description\": \"Optional: FC target worldwide names (WWNs)\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"wwids\": {\n          \"description\": \"Optional: FC volume world wide identifiers (wwids) Either wwids or combination of targetWWNs and lun must be set, but not both simultaneously.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"flexVolume\": {\n        \"description\": \"FlexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin.\",\n        \"properties\": {\n         \"driver\": {\n          \"description\": \"Driver is the name of the driver to use for this volume.\",\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". The default filesystem depends on FlexVolume script.\",\n          \"type\": \"string\"\n         },\n         \"options\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Optional: Extra command options if any.\",\n          \"type\": \"object\"\n         },\n         \"readOnly\": {\n          \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"Optional: SecretRef is reference to the secret object containing sensitive information to pass to the plugin scripts. This may be empty if no secret object is specified. If the secret object contains more than one secret, all secrets are passed to the plugin scripts.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"driver\"\n        ],\n        \"type\": \"object\"\n       },\n       \"flocker\": {\n        \"description\": \"Flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running\",\n        \"properties\": {\n         \"datasetName\": {\n          \"description\": \"Name of the dataset stored as metadata -\\u003e name on the dataset for Flocker should be considered as deprecated\",\n          \"type\": \"string\"\n         },\n         \"datasetUUID\": {\n          \"description\": \"UUID of the dataset. This is unique identifier of a Flocker dataset\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"gcePersistentDisk\": {\n        \"description\": \"GCEPersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"partition\": {\n          \"description\": \"The partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \\\"1\\\". Similarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty). More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"pdName\": {\n          \"description\": \"Unique name of the PD resource in GCE. Used to identify the disk in GCE. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"pdName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"gitRepo\": {\n        \"description\": \"GitRepo represents a git repository at a particular revision. DEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir into the Pod's container.\",\n        \"properties\": {\n         \"directory\": {\n          \"description\": \"Target directory name. Must not contain or start with '..'.  If '.' is supplied, the volume directory will be the git repository.  Otherwise, if specified, the volume will contain the git repository in the subdirectory with the given name.\",\n          \"type\": \"string\"\n         },\n         \"repository\": {\n          \"description\": \"Repository URL\",\n          \"type\": \"string\"\n         },\n         \"revision\": {\n          \"description\": \"Commit hash for the specified revision.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"repository\"\n        ],\n        \"type\": \"object\"\n       },\n       \"glusterfs\": {\n        \"description\": \"Glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/glusterfs/README.md\",\n        \"properties\": {\n         \"endpoints\": {\n          \"description\": \"EndpointsName is the endpoint name that details Glusterfs topology. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n          \"type\": \"string\"\n         },\n         \"path\": {\n          \"description\": \"Path is the Glusterfs volume path. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the Glusterfs volume to be mounted with read-only permissions. Defaults to false. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"endpoints\",\n         \"path\"\n        ],\n        \"type\": \"object\"\n       },\n       \"hostPath\": {\n        \"description\": \"HostPath represents a pre-existing file or directory on the host machine that is directly exposed to the container. This is generally used for system agents or other privileged things that are allowed to see the host machine. Most containers will NOT need this. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath --- TODO(jonesdl) We need to restrict who can use host directory mounts and who can/can not mount host directories as read/write.\",\n        \"properties\": {\n         \"path\": {\n          \"description\": \"Path of the directory on the host. If the path is a symlink, it will follow the link to the real path. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n          \"type\": \"string\"\n         },\n         \"type\": {\n          \"description\": \"Type for HostPath Volume Defaults to \\\"\\\" More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"path\"\n        ],\n        \"type\": \"object\"\n       },\n       \"iscsi\": {\n        \"description\": \"ISCSI represents an ISCSI Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://examples.k8s.io/volumes/iscsi/README.md\",\n        \"properties\": {\n         \"chapAuthDiscovery\": {\n          \"description\": \"whether support iSCSI Discovery CHAP authentication\",\n          \"type\": \"boolean\"\n         },\n         \"chapAuthSession\": {\n          \"description\": \"whether support iSCSI Session CHAP authentication\",\n          \"type\": \"boolean\"\n         },\n         \"fsType\": {\n          \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"initiatorName\": {\n          \"description\": \"Custom iSCSI Initiator Name. If initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface \\u003ctarget portal\\u003e:\\u003cvolume name\\u003e will be created for the connection.\",\n          \"type\": \"string\"\n         },\n         \"iqn\": {\n          \"description\": \"Target iSCSI Qualified Name.\",\n          \"type\": \"string\"\n         },\n         \"iscsiInterface\": {\n          \"description\": \"iSCSI Interface Name that uses an iSCSI transport. Defaults to 'default' (tcp).\",\n          \"type\": \"string\"\n         },\n         \"lun\": {\n          \"description\": \"iSCSI Target Lun number.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"portals\": {\n          \"description\": \"iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"CHAP Secret for iSCSI target and initiator authentication\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"targetPortal\": {\n          \"description\": \"iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"iqn\",\n         \"lun\",\n         \"targetPortal\"\n        ],\n        \"type\": \"object\"\n       },\n       \"name\": {\n        \"description\": \"Volume's name. Must be a DNS_LABEL and unique within the pod. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n        \"type\": \"string\"\n       },\n       \"nfs\": {\n        \"description\": \"NFS represents an NFS mount on the host that shares a pod's lifetime More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n        \"properties\": {\n         \"path\": {\n          \"description\": \"Path that is exported by the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the NFS export to be mounted with read-only permissions. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n          \"type\": \"boolean\"\n         },\n         \"server\": {\n          \"description\": \"Server is the hostname or IP address of the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"path\",\n         \"server\"\n        ],\n        \"type\": \"object\"\n       },\n       \"persistentVolumeClaim\": {\n        \"description\": \"PersistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n        \"properties\": {\n         \"claimName\": {\n          \"description\": \"ClaimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Will force the ReadOnly setting in VolumeMounts. Default false.\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"claimName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"photonPersistentDisk\": {\n        \"description\": \"PhotonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"pdID\": {\n          \"description\": \"ID that identifies Photon Controller persistent disk\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"pdID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"portworxVolume\": {\n        \"description\": \"PortworxVolume represents a portworx volume attached and mounted on kubelets host machine\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"FSType represents the filesystem type to mount Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"volumeID\": {\n          \"description\": \"VolumeID uniquely identifies a Portworx volume\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"projected\": {\n        \"description\": \"Items for all in one resources secrets, configmaps, and downward API\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"Mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"sources\": {\n          \"description\": \"list of volume projections\",\n          \"items\": {\n           \"description\": \"Projection that may be projected along with other supported volume types\",\n           \"properties\": {\n            \"configMap\": {\n             \"description\": \"information about the configMap data to project\",\n             \"properties\": {\n              \"items\": {\n               \"description\": \"If unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n               \"items\": {\n                \"description\": \"Maps a string key to a path within a volume.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"The key to project.\",\n                  \"type\": \"string\"\n                 },\n                 \"mode\": {\n                  \"description\": \"Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the ConfigMap or its keys must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"downwardAPI\": {\n             \"description\": \"information about the downwardAPI data to project\",\n             \"properties\": {\n              \"items\": {\n               \"description\": \"Items is a list of DownwardAPIVolume file\",\n               \"items\": {\n                \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n                \"properties\": {\n                 \"fieldRef\": {\n                  \"description\": \"Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.\",\n                  \"properties\": {\n                   \"apiVersion\": {\n                    \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                    \"type\": \"string\"\n                   },\n                   \"fieldPath\": {\n                    \"description\": \"Path of the field to select in the specified API version.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"fieldPath\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"mode\": {\n                  \"description\": \"Optional: mode bits used to set permissions on this file, must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n                  \"type\": \"string\"\n                 },\n                 \"resourceFieldRef\": {\n                  \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n                  \"properties\": {\n                   \"containerName\": {\n                    \"description\": \"Container name: required for volumes, optional for env vars\",\n                    \"type\": \"string\"\n                   },\n                   \"divisor\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                    \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"resource\": {\n                    \"description\": \"Required: resource to select\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"resource\"\n                  ],\n                  \"type\": \"object\"\n                 }\n                },\n                \"required\": [\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"secret\": {\n             \"description\": \"information about the secret data to project\",\n             \"properties\": {\n              \"items\": {\n               \"description\": \"If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n               \"items\": {\n                \"description\": \"Maps a string key to a path within a volume.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"The key to project.\",\n                  \"type\": \"string\"\n                 },\n                 \"mode\": {\n                  \"description\": \"Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"serviceAccountToken\": {\n             \"description\": \"information about the serviceAccountToken data to project\",\n             \"properties\": {\n              \"audience\": {\n               \"description\": \"Audience is the intended audience of the token. A recipient of a token must identify itself with an identifier specified in the audience of the token, and otherwise should reject the token. The audience defaults to the identifier of the apiserver.\",\n               \"type\": \"string\"\n              },\n              \"expirationSeconds\": {\n               \"description\": \"ExpirationSeconds is the requested duration of validity of the service account token. As the token approaches expiration, the kubelet volume plugin will proactively rotate the service account token. The kubelet will start trying to rotate the token if the token is older than 80 percent of its time to live or if the token is older than 24 hours.Defaults to 1 hour and must be at least 10 minutes.\",\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              },\n              \"path\": {\n               \"description\": \"Path is the path relative to the mount point of the file to project the token into.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"quobyte\": {\n        \"description\": \"Quobyte represents a Quobyte mount on the host that shares a pod's lifetime\",\n        \"properties\": {\n         \"group\": {\n          \"description\": \"Group to map volume access to Default is no group\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the Quobyte volume to be mounted with read-only permissions. Defaults to false.\",\n          \"type\": \"boolean\"\n         },\n         \"registry\": {\n          \"description\": \"Registry represents a single or multiple Quobyte Registry services specified as a string as host:port pair (multiple entries are separated with commas) which acts as the central registry for volumes\",\n          \"type\": \"string\"\n         },\n         \"tenant\": {\n          \"description\": \"Tenant owning the given Quobyte volume in the Backend Used with dynamically provisioned Quobyte volumes, value is set by the plugin\",\n          \"type\": \"string\"\n         },\n         \"user\": {\n          \"description\": \"User to map volume access to Defaults to serivceaccount user\",\n          \"type\": \"string\"\n         },\n         \"volume\": {\n          \"description\": \"Volume is a string that references an already created Quobyte volume by name.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"registry\",\n         \"volume\"\n        ],\n        \"type\": \"object\"\n       },\n       \"rbd\": {\n        \"description\": \"RBD represents a Rados Block Device mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/rbd/README.md\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#rbd TODO: how do we prevent errors in the filesystem from compromising the machine\",\n          \"type\": \"string\"\n         },\n         \"image\": {\n          \"description\": \"The rados image name. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"keyring\": {\n          \"description\": \"Keyring is the path to key ring for RBDUser. Default is /etc/ceph/keyring. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"monitors\": {\n          \"description\": \"A collection of Ceph monitors. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"pool\": {\n          \"description\": \"The rados pool name. Default is rbd. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"SecretRef is name of the authentication secret for RBDUser. If provided overrides keyring. Default is nil. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"user\": {\n          \"description\": \"The rados user name. Default is admin. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"image\",\n         \"monitors\"\n        ],\n        \"type\": \"object\"\n       },\n       \"scaleIO\": {\n        \"description\": \"ScaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Default is \\\"xfs\\\".\",\n          \"type\": \"string\"\n         },\n         \"gateway\": {\n          \"description\": \"The host address of the ScaleIO API Gateway.\",\n          \"type\": \"string\"\n         },\n         \"protectionDomain\": {\n          \"description\": \"The name of the ScaleIO Protection Domain for the configured storage.\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"SecretRef references to the secret for ScaleIO user and other sensitive information. If this is not provided, Login operation will fail.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"sslEnabled\": {\n          \"description\": \"Flag to enable/disable SSL communication with Gateway, default false\",\n          \"type\": \"boolean\"\n         },\n         \"storageMode\": {\n          \"description\": \"Indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned. Default is ThinProvisioned.\",\n          \"type\": \"string\"\n         },\n         \"storagePool\": {\n          \"description\": \"The ScaleIO Storage Pool associated with the protection domain.\",\n          \"type\": \"string\"\n         },\n         \"system\": {\n          \"description\": \"The name of the storage system as configured in ScaleIO.\",\n          \"type\": \"string\"\n         },\n         \"volumeName\": {\n          \"description\": \"The name of a volume already created in the ScaleIO system that is associated with this volume source.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"gateway\",\n         \"secretRef\",\n         \"system\"\n        ],\n        \"type\": \"object\"\n       },\n       \"secret\": {\n        \"description\": \"Secret represents a secret that should populate this volume. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"description\": \"If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n          \"items\": {\n           \"description\": \"Maps a string key to a path within a volume.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to project.\",\n             \"type\": \"string\"\n            },\n            \"mode\": {\n             \"description\": \"Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"optional\": {\n          \"description\": \"Specify whether the Secret or its keys must be defined\",\n          \"type\": \"boolean\"\n         },\n         \"secretName\": {\n          \"description\": \"Name of the secret in the pod's namespace to use. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"storageos\": {\n        \"description\": \"StorageOS represents a StorageOS volume attached and mounted on Kubernetes nodes.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"SecretRef specifies the secret to use for obtaining the StorageOS API credentials.  If not specified, default values will be attempted.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"volumeName\": {\n          \"description\": \"VolumeName is the human-readable name of the StorageOS volume.  Volume names are only unique within a namespace.\",\n          \"type\": \"string\"\n         },\n         \"volumeNamespace\": {\n          \"description\": \"VolumeNamespace specifies the scope of the volume within StorageOS.  If no namespace is specified then the Pod's namespace will be used.  This allows the Kubernetes name scoping to be mirrored within StorageOS for tighter integration. Set VolumeName to any name to override the default behaviour. Set to \\\"default\\\" if you are not using namespaces within StorageOS. Namespaces that do not pre-exist within StorageOS will be created.\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"vsphereVolume\": {\n        \"description\": \"VsphereVolume represents a vSphere volume attached and mounted on kubelets host machine\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"storagePolicyID\": {\n          \"description\": \"Storage Policy Based Management (SPBM) profile ID associated with the StoragePolicyName.\",\n          \"type\": \"string\"\n         },\n         \"storagePolicyName\": {\n          \"description\": \"Storage Policy Based Management (SPBM) profile name.\",\n          \"type\": \"string\"\n         },\n         \"volumePath\": {\n          \"description\": \"Path that identifies vSphere volume vmdk\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumePath\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    }\n   },\n   \"required\": [\n    \"config\",\n    \"name\",\n    \"type\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Integration\",\n \"type\": \"object\"\n}"}}