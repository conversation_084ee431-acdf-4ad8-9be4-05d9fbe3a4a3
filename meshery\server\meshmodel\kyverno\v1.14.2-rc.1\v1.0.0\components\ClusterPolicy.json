{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Cluster Policy", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kyverno", "displayName": "Kyverno", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Security & Compliance"}, "subCategory": "Container Security", "metadata": {"isAnnotation": false, "primaryColor": "#e77e5b", "secondaryColor": "#e77e5b", "shape": "shield", "source_uri": "git://github.com/kyverno/kyverno/main/config/crds/kyverno", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 500 500\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#e87e5b;}.cls-1,.cls-2{fill-rule:evenodd;}.cls-2{fill:#3784c5;}</style></defs><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1\" data-name=\"Layer 1\"><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M85.978,159.12977,62.8,260.6758H86.6494l19.44481-85.18748c-3.67742-3.73171-7.44723-7.31243-11.34072-10.64083a10.63854,10.63854,0,0,1-2.57145-3.25561c-2.00621-.77007-4.09061-1.59534-6.204-2.46211M231.42381,67.34456,112.1119,124.80222c1.52162,2.13,2.97738,4.24159,4.34786,6.26969a10.64019,10.64019,0,0,1,3.75052,1.7744c3.83767,2.83657,7.86273,5.51845,12.00391,8.089l99.36786-47.85077c.55723-4.79743.95806-9.57158,1.12509-14.29529a10.66422,10.66422,0,0,1,.94635-4.03924C232.908,72.38443,232.15146,69.8955,231.42381,67.34456Zm161.27254,56.72785L274.88369,67.33715c-.72856,2.55341-1.48428,5.04528-2.23045,7.41284a10.67016,10.67016,0,0,1,.9465,4.03924c.16673,4.72171.56787,9.49415,1.12556,14.2902l98.02314,47.20224c4.15445-2.62574,8.1903-5.36392,12.0309-8.25678a10.68789,10.68789,0,0,1,3.7368-1.80756C389.8331,128.22825,391.23488,126.15989,392.69635,124.07241ZM443.51958,260.6758,420.01438,157.685c-2.37065,1.007-4.71138,1.95716-6.9554,2.8412a10.6927,10.6927,0,0,1-2.5423,3.27658c-3.65167,3.17757-7.19121,6.57908-10.6453,10.12063l19.802,86.75243Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-2\" d=\"M310.80628,197.98424l2.82932,3.494c1.31341-11.17046,10.57992-21.88857,28.16752-31.49762,18.333-10.01513,35.45434-18.90162,49.41063-29.40875l1.85044,2.28243q16.36194-25.5555,25.41716-32.17314c8.11195-4.9367,15.60838-3.49975,21.43667,3.00345,5.15077,7.05348,4.99824,14.68455-1.51791,21.59368q-8.35657,7.48094-36.75573,18.1712l1.84812,2.28289c-13.17744,11.47105-25.43089,26.36813-39.04109,42.21865-13.05143,15.20616-25.46389,22.04018-36.66458,21.00423l2.83148,3.4945-11.00713,8.91256L309.8843,207.878l-7.47391-3.09521ZM175.161,222.7922l2.79832-3.517c-11.18851,1.1345-23.66-5.59048-36.846-20.68251-13.74561-15.73207-26.12754-30.52212-39.40815-41.87534l1.83008-2.30047Q75.044,143.97428,66.62113,136.56738c-6.57383-6.85144-6.79345-14.48174-1.70561-21.57949,5.77123-6.55409,13.25378-8.05643,21.41092-3.19176q9.1105,6.54145,25.69447,31.95182l1.83069-2.30032c14.04929,10.38421,31.24622,19.12264,49.66588,28.978,17.67181,9.45251,27.031,20.09165,28.44142,31.24977l2.79956-3.51918,8.461,6.73068-7.22282,2.99111-9.80909,23.68672Zm62.25216-45.00356H241.91c-7.85409-8.05057-10.35459-21.99822-6.753-41.712,3.75484-20.55125,7.62165-39.44856,8.24072-56.90815h2.94052q-9.56509-28.79985-9.01095-40.00039c1.26961-9.41025,7.10423-14.334,15.82538-14.7703,8.72255.43631,14.55748,5.36005,15.82755,14.7703q.55059,11.20047-9.01173,40.00039h2.94052c.61938,17.45959,4.48666,36.3569,8.24074,56.90815,3.6017,19.71379,1.101,33.66144-6.75289,41.712h4.49668v13.11374l-15.954-6.60715-15.52634,6.42963Z\"></path><polygon xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" points=\"252.939 193.893 278.016 204.281 303.097 214.664 313.479 239.747 322.151 260.676 276.724 260.676 274.707 255.806 270.974 246.787 261.956 243.055 252.939 239.32 243.923 243.055 234.905 246.787 231.172 255.806 229.155 260.676 183.729 260.676 192.4 239.747 202.783 214.664 227.864 204.281 252.939 193.893\"></polygon><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-2\" d=\"M68.2137,349.624a8.91449,8.91449,0,1,1-17.829,0V286.58912a8.91593,8.91593,0,0,1,8.91487-8.9141h384.218a8.91517,8.91517,0,0,1,8.91456,8.9141V349.624a8.91433,8.91433,0,1,1-17.82866,0v-54.12H260.32175v54.12a8.91433,8.91433,0,1,1-17.82866,0v-54.12H68.2137Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M241.91505,443.20813a1.18142,1.18142,0,0,0,.95652.48921h8.5364l8.53608-.00124a1.1742,1.1742,0,0,0,.92336-.44741l5.323-6.67131,5.322-6.67578a1.188,1.188,0,0,0,.22672-.99354l-3.7951-16.63169a1.16455,1.16455,0,0,0-.64144-.81571l-15.381-7.40682a1.17893,1.17893,0,0,0-1.02671,0l-15.381,7.40682a1.187,1.187,0,0,0-.63789.80337l-3.79,16.60146a1.18422,1.18422,0,0,0,.21808,1.03735ZM229.851,467.85723a2.68563,2.68563,0,0,0,2.174,1.10751H251.41l19.385-.00354a2.6692,2.6692,0,0,0,2.09736-1.01282l12.08719-15.15357,12.085-15.159a2.708,2.708,0,0,0,.51744-2.25806l-8.62075-37.76932a2.65084,2.65084,0,0,0-1.45654-1.85182l-34.93073-16.82278a2.67955,2.67955,0,0,0-2.32855,0l-34.9309,16.82278a2.6774,2.6774,0,0,0-1.44836,1.82375l-8.607,37.70054a2.68116,2.68116,0,0,0,.494,2.35614Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M48.64133,443.20813a1.17824,1.17824,0,0,0,.95653.48921h8.53639l8.53608-.00124a1.17715,1.17715,0,0,0,.92337-.44741l5.32335-6.67131,5.31965-6.67578a1.18728,1.18728,0,0,0,.22841-.99354l-3.7968-16.63169a1.16679,1.16679,0,0,0-.6402-.81571l-15.38228-7.40682a1.17622,1.17622,0,0,0-1.02547,0l-15.38028,7.40682a1.18185,1.18185,0,0,0-.63835.80337l-3.78955,16.60146a1.17623,1.17623,0,0,0,.21824,1.03735Zm-12.06375,24.6491a2.685,2.685,0,0,0,2.17308,1.10751H58.13425l19.38652-.00354a2.67635,2.67635,0,0,0,2.09812-1.01282l12.08627-15.15357,12.08365-15.159a2.69274,2.69274,0,0,0,.51743-2.25806l-8.61936-37.76932a2.64767,2.64767,0,0,0-1.45592-1.85182L59.30037,378.93387a2.6829,2.6829,0,0,0-2.33008,0L22.03877,395.75665a2.686,2.686,0,0,0-1.44866,1.82375l-8.60456,37.70054a2.67125,2.67125,0,0,0,.49414,2.35614Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M432.85945,443.20813a1.178,1.178,0,0,0,.95652.48921h8.5361l8.53622-.00124a1.17706,1.17706,0,0,0,.92321-.44741l5.32337-6.67131,5.31979-6.67578a1.18136,1.18136,0,0,0,.2281-.99354l-3.7951-16.63169a1.16472,1.16472,0,0,0-.64159-.81571l-15.381-7.40682a1.17853,1.17853,0,0,0-1.02653,0l-15.38107,7.40682a1.18977,1.18977,0,0,0-.638.80337l-3.79,16.60146a1.18425,1.18425,0,0,0,.21822,1.03735Zm-12.06422,24.6491a2.68578,2.68578,0,0,0,2.1734,1.10751h19.38344l19.38635-.00354a2.67442,2.67442,0,0,0,2.09828-1.01282l12.08642-15.15357,12.08349-15.159a2.69186,2.69186,0,0,0,.51667-2.25806l-8.61844-37.76932a2.6516,2.6516,0,0,0-1.45592-1.85182l-34.93136-16.82278a2.6829,2.6829,0,0,0-2.33008,0l-34.9309,16.82278a2.68376,2.68376,0,0,0-1.44851,1.82375l-8.60487,37.70054a2.67227,2.67227,0,0,0,.4943,2.35614Z\"></path></g></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 500 500\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff;fill-rule:evenodd;}</style></defs><g xmlns=\"http://www.w3.org/2000/svg\" id=\"Layer_1\" data-name=\"Layer 1\"><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M85.978,159.12977,62.8,260.6758H86.6494l19.44481-85.18748c-3.67742-3.73171-7.44723-7.31243-11.34072-10.64083a10.63854,10.63854,0,0,1-2.57145-3.25561c-2.00621-.77007-4.09061-1.59534-6.204-2.46211M231.42381,67.34456,112.1119,124.80222c1.52162,2.13,2.97738,4.24159,4.34786,6.26969a10.64019,10.64019,0,0,1,3.75052,1.7744c3.83767,2.83657,7.86273,5.51845,12.00391,8.089l99.36786-47.85077c.55723-4.79743.95806-9.57158,1.12509-14.29529a10.66422,10.66422,0,0,1,.94635-4.03924C232.908,72.38443,232.15146,69.8955,231.42381,67.34456Zm161.27254,56.72785L274.88369,67.33715c-.72856,2.55341-1.48428,5.04528-2.23045,7.41284a10.67016,10.67016,0,0,1,.9465,4.03924c.16673,4.72171.56787,9.49415,1.12556,14.2902l98.02314,47.20224c4.15445-2.62574,8.1903-5.36392,12.0309-8.25678a10.68789,10.68789,0,0,1,3.7368-1.80756C389.8331,128.22825,391.23488,126.15989,392.69635,124.07241ZM443.51958,260.6758,420.01438,157.685c-2.37065,1.007-4.71138,1.95716-6.9554,2.8412a10.6927,10.6927,0,0,1-2.5423,3.27658c-3.65167,3.17757-7.19121,6.57908-10.6453,10.12063l19.802,86.75243Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M310.80628,197.98424l2.82932,3.494c1.31341-11.17046,10.57992-21.88857,28.16752-31.49762,18.333-10.01513,35.45434-18.90162,49.41063-29.40875l1.85044,2.28243q16.36194-25.5555,25.41716-32.17314c8.11195-4.9367,15.60838-3.49975,21.43667,3.00345,5.15077,7.05348,4.99824,14.68455-1.51791,21.59368q-8.35657,7.48094-36.75573,18.1712l1.84812,2.28289c-13.17744,11.47105-25.43089,26.36813-39.04109,42.21865-13.05143,15.20616-25.46389,22.04018-36.66458,21.00423l2.83148,3.4945-11.00713,8.91256L309.8843,207.878l-7.47391-3.09521ZM175.161,222.7922l2.79832-3.517c-11.18851,1.1345-23.66-5.59048-36.846-20.68251-13.74561-15.73207-26.12754-30.52212-39.40815-41.87534l1.83008-2.30047Q75.044,143.97428,66.62113,136.56738c-6.57383-6.85144-6.79345-14.48174-1.70561-21.57949,5.77123-6.55409,13.25378-8.05643,21.41092-3.19176q9.1105,6.54145,25.69447,31.95182l1.83069-2.30032c14.04929,10.38421,31.24622,19.12264,49.66588,28.978,17.67181,9.45251,27.031,20.09165,28.44142,31.24977l2.79956-3.51918,8.461,6.73068-7.22282,2.99111-9.80909,23.68672Zm62.25216-45.00356H241.91c-7.85409-8.05057-10.35459-21.99822-6.753-41.712,3.75484-20.55125,7.62165-39.44856,8.24072-56.90815h2.94052q-9.56509-28.79985-9.01095-40.00039c1.26961-9.41025,7.10423-14.334,15.82538-14.7703,8.72255.43631,14.55748,5.36005,15.82755,14.7703q.55059,11.20047-9.01173,40.00039h2.94052c.61938,17.45959,4.48666,36.3569,8.24074,56.90815,3.6017,19.71379,1.101,33.66144-6.75289,41.712h4.49668v13.11374l-15.954-6.60715-15.52634,6.42963Z\"></path><polygon xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" points=\"252.939 193.893 278.016 204.281 303.097 214.664 313.479 239.747 322.151 260.676 276.724 260.676 274.707 255.806 270.974 246.787 261.956 243.055 252.939 239.32 243.923 243.055 234.905 246.787 231.172 255.806 229.155 260.676 183.729 260.676 192.4 239.747 202.783 214.664 227.864 204.281 252.939 193.893\"></polygon><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M68.2137,349.624a8.91449,8.91449,0,1,1-17.829,0V286.58912a8.91593,8.91593,0,0,1,8.91487-8.9141h384.218a8.91517,8.91517,0,0,1,8.91456,8.9141V349.624a8.91433,8.91433,0,1,1-17.82866,0v-54.12H260.32175v54.12a8.91433,8.91433,0,1,1-17.82866,0v-54.12H68.2137Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M241.91505,443.20813a1.18142,1.18142,0,0,0,.95652.48921h8.5364l8.53608-.00124a1.1742,1.1742,0,0,0,.92336-.44741l5.323-6.67131,5.322-6.67578a1.188,1.188,0,0,0,.22672-.99354l-3.7951-16.63169a1.16455,1.16455,0,0,0-.64144-.81571l-15.381-7.40682a1.17893,1.17893,0,0,0-1.02671,0l-15.381,7.40682a1.187,1.187,0,0,0-.63789.80337l-3.79,16.60146a1.18422,1.18422,0,0,0,.21808,1.03735ZM229.851,467.85723a2.68563,2.68563,0,0,0,2.174,1.10751H251.41l19.385-.00354a2.6692,2.6692,0,0,0,2.09736-1.01282l12.08719-15.15357,12.085-15.159a2.708,2.708,0,0,0,.51744-2.25806l-8.62075-37.76932a2.65084,2.65084,0,0,0-1.45654-1.85182l-34.93073-16.82278a2.67955,2.67955,0,0,0-2.32855,0l-34.9309,16.82278a2.6774,2.6774,0,0,0-1.44836,1.82375l-8.607,37.70054a2.68116,2.68116,0,0,0,.494,2.35614Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M48.64133,443.20813a1.17824,1.17824,0,0,0,.95653.48921h8.53639l8.53608-.00124a1.17715,1.17715,0,0,0,.92337-.44741l5.32335-6.67131,5.31965-6.67578a1.18728,1.18728,0,0,0,.22841-.99354l-3.7968-16.63169a1.16679,1.16679,0,0,0-.6402-.81571l-15.38228-7.40682a1.17622,1.17622,0,0,0-1.02547,0l-15.38028,7.40682a1.18185,1.18185,0,0,0-.63835.80337l-3.78955,16.60146a1.17623,1.17623,0,0,0,.21824,1.03735Zm-12.06375,24.6491a2.685,2.685,0,0,0,2.17308,1.10751H58.13425l19.38652-.00354a2.67635,2.67635,0,0,0,2.09812-1.01282l12.08627-15.15357,12.08365-15.159a2.69274,2.69274,0,0,0,.51743-2.25806l-8.61936-37.76932a2.64767,2.64767,0,0,0-1.45592-1.85182L59.30037,378.93387a2.6829,2.6829,0,0,0-2.33008,0L22.03877,395.75665a2.686,2.686,0,0,0-1.44866,1.82375l-8.60456,37.70054a2.67125,2.67125,0,0,0,.49414,2.35614Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M432.85945,443.20813a1.178,1.178,0,0,0,.95652.48921h8.5361l8.53622-.00124a1.17706,1.17706,0,0,0,.92321-.44741l5.32337-6.67131,5.31979-6.67578a1.18136,1.18136,0,0,0,.2281-.99354l-3.7951-16.63169a1.16472,1.16472,0,0,0-.64159-.81571l-15.381-7.40682a1.17853,1.17853,0,0,0-1.02653,0l-15.38107,7.40682a1.18977,1.18977,0,0,0-.638.80337l-3.79,16.60146a1.18425,1.18425,0,0,0,.21822,1.03735Zm-12.06422,24.6491a2.68578,2.68578,0,0,0,2.1734,1.10751h19.38344l19.38635-.00354a2.67442,2.67442,0,0,0,2.09828-1.01282l12.08642-15.15357,12.08349-15.159a2.69186,2.69186,0,0,0,.51667-2.25806l-8.61844-37.76932a2.6516,2.6516,0,0,0-1.45592-1.85182l-34.93136-16.82278a2.6829,2.6829,0,0,0-2.33008,0l-34.9309,16.82278a2.68376,2.68376,0,0,0-1.44851,1.82375l-8.60487,37.70054a2.67227,2.67227,0,0,0,.4943,2.35614Z\"></path></g></svg>"}, "model": {"version": "v1.14.2-rc.1"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#e77e5b", "secondaryColor": "#e77e5b", "shape": "shield", "svgColor": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 500 500\"><defs><style>.cls-1{fill:#e87e5b;}.cls-1,.cls-2{fill-rule:evenodd;}.cls-2{fill:#3784c5;}</style></defs><g id=\"Layer_1\" data-name=\"Layer 1\"><path class=\"cls-1\" d=\"M85.978,159.12977,62.8,260.6758H86.6494l19.44481-85.18748c-3.67742-3.73171-7.44723-7.31243-11.34072-10.64083a10.63854,10.63854,0,0,1-2.57145-3.25561c-2.00621-.77007-4.09061-1.59534-6.204-2.46211M231.42381,67.34456,112.1119,124.80222c1.52162,2.13,2.97738,4.24159,4.34786,6.26969a10.64019,10.64019,0,0,1,3.75052,1.7744c3.83767,2.83657,7.86273,5.51845,12.00391,8.089l99.36786-47.85077c.55723-4.79743.95806-9.57158,1.12509-14.29529a10.66422,10.66422,0,0,1,.94635-4.03924C232.908,72.38443,232.15146,69.8955,231.42381,67.34456Zm161.27254,56.72785L274.88369,67.33715c-.72856,2.55341-1.48428,5.04528-2.23045,7.41284a10.67016,10.67016,0,0,1,.9465,4.03924c.16673,4.72171.56787,9.49415,1.12556,14.2902l98.02314,47.20224c4.15445-2.62574,8.1903-5.36392,12.0309-8.25678a10.68789,10.68789,0,0,1,3.7368-1.80756C389.8331,128.22825,391.23488,126.15989,392.69635,124.07241ZM443.51958,260.6758,420.01438,157.685c-2.37065,1.007-4.71138,1.95716-6.9554,2.8412a10.6927,10.6927,0,0,1-2.5423,3.27658c-3.65167,3.17757-7.19121,6.57908-10.6453,10.12063l19.802,86.75243Z\"/><path class=\"cls-2\" d=\"M310.80628,197.98424l2.82932,3.494c1.31341-11.17046,10.57992-21.88857,28.16752-31.49762,18.333-10.01513,35.45434-18.90162,49.41063-29.40875l1.85044,2.28243q16.36194-25.5555,25.41716-32.17314c8.11195-4.9367,15.60838-3.49975,21.43667,3.00345,5.15077,7.05348,4.99824,14.68455-1.51791,21.59368q-8.35657,7.48094-36.75573,18.1712l1.84812,2.28289c-13.17744,11.47105-25.43089,26.36813-39.04109,42.21865-13.05143,15.20616-25.46389,22.04018-36.66458,21.00423l2.83148,3.4945-11.00713,8.91256L309.8843,207.878l-7.47391-3.09521ZM175.161,222.7922l2.79832-3.517c-11.18851,1.1345-23.66-5.59048-36.846-20.68251-13.74561-15.73207-26.12754-30.52212-39.40815-41.87534l1.83008-2.30047Q75.044,143.97428,66.62113,136.56738c-6.57383-6.85144-6.79345-14.48174-1.70561-21.57949,5.77123-6.55409,13.25378-8.05643,21.41092-3.19176q9.1105,6.54145,25.69447,31.95182l1.83069-2.30032c14.04929,10.38421,31.24622,19.12264,49.66588,28.978,17.67181,9.45251,27.031,20.09165,28.44142,31.24977l2.79956-3.51918,8.461,6.73068-7.22282,2.99111-9.80909,23.68672Zm62.25216-45.00356H241.91c-7.85409-8.05057-10.35459-21.99822-6.753-41.712,3.75484-20.55125,7.62165-39.44856,8.24072-56.90815h2.94052q-9.56509-28.79985-9.01095-40.00039c1.26961-9.41025,7.10423-14.334,15.82538-14.7703,8.72255.43631,14.55748,5.36005,15.82755,14.7703q.55059,11.20047-9.01173,40.00039h2.94052c.61938,17.45959,4.48666,36.3569,8.24074,56.90815,3.6017,19.71379,1.101,33.66144-6.75289,41.712h4.49668v13.11374l-15.954-6.60715-15.52634,6.42963Z\"/><polygon class=\"cls-1\" points=\"252.939 193.893 278.016 204.281 303.097 214.664 313.479 239.747 322.151 260.676 276.724 260.676 274.707 255.806 270.974 246.787 261.956 243.055 252.939 239.32 243.923 243.055 234.905 246.787 231.172 255.806 229.155 260.676 183.729 260.676 192.4 239.747 202.783 214.664 227.864 204.281 252.939 193.893\"/><path class=\"cls-2\" d=\"M68.2137,349.624a8.91449,8.91449,0,1,1-17.829,0V286.58912a8.91593,8.91593,0,0,1,8.91487-8.9141h384.218a8.91517,8.91517,0,0,1,8.91456,8.9141V349.624a8.91433,8.91433,0,1,1-17.82866,0v-54.12H260.32175v54.12a8.91433,8.91433,0,1,1-17.82866,0v-54.12H68.2137Z\"/><path class=\"cls-1\" d=\"M241.91505,443.20813a1.18142,1.18142,0,0,0,.95652.48921h8.5364l8.53608-.00124a1.1742,1.1742,0,0,0,.92336-.44741l5.323-6.67131,5.322-6.67578a1.188,1.188,0,0,0,.22672-.99354l-3.7951-16.63169a1.16455,1.16455,0,0,0-.64144-.81571l-15.381-7.40682a1.17893,1.17893,0,0,0-1.02671,0l-15.381,7.40682a1.187,1.187,0,0,0-.63789.80337l-3.79,16.60146a1.18422,1.18422,0,0,0,.21808,1.03735ZM229.851,467.85723a2.68563,2.68563,0,0,0,2.174,1.10751H251.41l19.385-.00354a2.6692,2.6692,0,0,0,2.09736-1.01282l12.08719-15.15357,12.085-15.159a2.708,2.708,0,0,0,.51744-2.25806l-8.62075-37.76932a2.65084,2.65084,0,0,0-1.45654-1.85182l-34.93073-16.82278a2.67955,2.67955,0,0,0-2.32855,0l-34.9309,16.82278a2.6774,2.6774,0,0,0-1.44836,1.82375l-8.607,37.70054a2.68116,2.68116,0,0,0,.494,2.35614Z\"/><path class=\"cls-1\" d=\"M48.64133,443.20813a1.17824,1.17824,0,0,0,.95653.48921h8.53639l8.53608-.00124a1.17715,1.17715,0,0,0,.92337-.44741l5.32335-6.67131,5.31965-6.67578a1.18728,1.18728,0,0,0,.22841-.99354l-3.7968-16.63169a1.16679,1.16679,0,0,0-.6402-.81571l-15.38228-7.40682a1.17622,1.17622,0,0,0-1.02547,0l-15.38028,7.40682a1.18185,1.18185,0,0,0-.63835.80337l-3.78955,16.60146a1.17623,1.17623,0,0,0,.21824,1.03735Zm-12.06375,24.6491a2.685,2.685,0,0,0,2.17308,1.10751H58.13425l19.38652-.00354a2.67635,2.67635,0,0,0,2.09812-1.01282l12.08627-15.15357,12.08365-15.159a2.69274,2.69274,0,0,0,.51743-2.25806l-8.61936-37.76932a2.64767,2.64767,0,0,0-1.45592-1.85182L59.30037,378.93387a2.6829,2.6829,0,0,0-2.33008,0L22.03877,395.75665a2.686,2.686,0,0,0-1.44866,1.82375l-8.60456,37.70054a2.67125,2.67125,0,0,0,.49414,2.35614Z\"/><path class=\"cls-1\" d=\"M432.85945,443.20813a1.178,1.178,0,0,0,.95652.48921h8.5361l8.53622-.00124a1.17706,1.17706,0,0,0,.92321-.44741l5.32337-6.67131,5.31979-6.67578a1.18136,1.18136,0,0,0,.2281-.99354l-3.7951-16.63169a1.16472,1.16472,0,0,0-.64159-.81571l-15.381-7.40682a1.17853,1.17853,0,0,0-1.02653,0l-15.38107,7.40682a1.18977,1.18977,0,0,0-.638.80337l-3.79,16.60146a1.18425,1.18425,0,0,0,.21822,1.03735Zm-12.06422,24.6491a2.68578,2.68578,0,0,0,2.1734,1.10751h19.38344l19.38635-.00354a2.67442,2.67442,0,0,0,2.09828-1.01282l12.08642-15.15357,12.08349-15.159a2.69186,2.69186,0,0,0,.51667-2.25806l-8.61844-37.76932a2.6516,2.6516,0,0,0-1.45592-1.85182l-34.93136-16.82278a2.6829,2.6829,0,0,0-2.33008,0l-34.9309,16.82278a2.68376,2.68376,0,0,0-1.44851,1.82375l-8.60487,37.70054a2.67227,2.67227,0,0,0,.4943,2.35614Z\"/></g></svg>", "svgComplete": "", "svgWhite": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 500 500\"><defs><style>.cls-1{fill:#fff;fill-rule:evenodd;}</style></defs><g id=\"Layer_1\" data-name=\"Layer 1\"><path class=\"cls-1\" d=\"M85.978,159.12977,62.8,260.6758H86.6494l19.44481-85.18748c-3.67742-3.73171-7.44723-7.31243-11.34072-10.64083a10.63854,10.63854,0,0,1-2.57145-3.25561c-2.00621-.77007-4.09061-1.59534-6.204-2.46211M231.42381,67.34456,112.1119,124.80222c1.52162,2.13,2.97738,4.24159,4.34786,6.26969a10.64019,10.64019,0,0,1,3.75052,1.7744c3.83767,2.83657,7.86273,5.51845,12.00391,8.089l99.36786-47.85077c.55723-4.79743.95806-9.57158,1.12509-14.29529a10.66422,10.66422,0,0,1,.94635-4.03924C232.908,72.38443,232.15146,69.8955,231.42381,67.34456Zm161.27254,56.72785L274.88369,67.33715c-.72856,2.55341-1.48428,5.04528-2.23045,7.41284a10.67016,10.67016,0,0,1,.9465,4.03924c.16673,4.72171.56787,9.49415,1.12556,14.2902l98.02314,47.20224c4.15445-2.62574,8.1903-5.36392,12.0309-8.25678a10.68789,10.68789,0,0,1,3.7368-1.80756C389.8331,128.22825,391.23488,126.15989,392.69635,124.07241ZM443.51958,260.6758,420.01438,157.685c-2.37065,1.007-4.71138,1.95716-6.9554,2.8412a10.6927,10.6927,0,0,1-2.5423,3.27658c-3.65167,3.17757-7.19121,6.57908-10.6453,10.12063l19.802,86.75243Z\"/><path class=\"cls-1\" d=\"M310.80628,197.98424l2.82932,3.494c1.31341-11.17046,10.57992-21.88857,28.16752-31.49762,18.333-10.01513,35.45434-18.90162,49.41063-29.40875l1.85044,2.28243q16.36194-25.5555,25.41716-32.17314c8.11195-4.9367,15.60838-3.49975,21.43667,3.00345,5.15077,7.05348,4.99824,14.68455-1.51791,21.59368q-8.35657,7.48094-36.75573,18.1712l1.84812,2.28289c-13.17744,11.47105-25.43089,26.36813-39.04109,42.21865-13.05143,15.20616-25.46389,22.04018-36.66458,21.00423l2.83148,3.4945-11.00713,8.91256L309.8843,207.878l-7.47391-3.09521ZM175.161,222.7922l2.79832-3.517c-11.18851,1.1345-23.66-5.59048-36.846-20.68251-13.74561-15.73207-26.12754-30.52212-39.40815-41.87534l1.83008-2.30047Q75.044,143.97428,66.62113,136.56738c-6.57383-6.85144-6.79345-14.48174-1.70561-21.57949,5.77123-6.55409,13.25378-8.05643,21.41092-3.19176q9.1105,6.54145,25.69447,31.95182l1.83069-2.30032c14.04929,10.38421,31.24622,19.12264,49.66588,28.978,17.67181,9.45251,27.031,20.09165,28.44142,31.24977l2.79956-3.51918,8.461,6.73068-7.22282,2.99111-9.80909,23.68672Zm62.25216-45.00356H241.91c-7.85409-8.05057-10.35459-21.99822-6.753-41.712,3.75484-20.55125,7.62165-39.44856,8.24072-56.90815h2.94052q-9.56509-28.79985-9.01095-40.00039c1.26961-9.41025,7.10423-14.334,15.82538-14.7703,8.72255.43631,14.55748,5.36005,15.82755,14.7703q.55059,11.20047-9.01173,40.00039h2.94052c.61938,17.45959,4.48666,36.3569,8.24074,56.90815,3.6017,19.71379,1.101,33.66144-6.75289,41.712h4.49668v13.11374l-15.954-6.60715-15.52634,6.42963Z\"/><polygon class=\"cls-1\" points=\"252.939 193.893 278.016 204.281 303.097 214.664 313.479 239.747 322.151 260.676 276.724 260.676 274.707 255.806 270.974 246.787 261.956 243.055 252.939 239.32 243.923 243.055 234.905 246.787 231.172 255.806 229.155 260.676 183.729 260.676 192.4 239.747 202.783 214.664 227.864 204.281 252.939 193.893\"/><path class=\"cls-1\" d=\"M68.2137,349.624a8.91449,8.91449,0,1,1-17.829,0V286.58912a8.91593,8.91593,0,0,1,8.91487-8.9141h384.218a8.91517,8.91517,0,0,1,8.91456,8.9141V349.624a8.91433,8.91433,0,1,1-17.82866,0v-54.12H260.32175v54.12a8.91433,8.91433,0,1,1-17.82866,0v-54.12H68.2137Z\"/><path class=\"cls-1\" d=\"M241.91505,443.20813a1.18142,1.18142,0,0,0,.95652.48921h8.5364l8.53608-.00124a1.1742,1.1742,0,0,0,.92336-.44741l5.323-6.67131,5.322-6.67578a1.188,1.188,0,0,0,.22672-.99354l-3.7951-16.63169a1.16455,1.16455,0,0,0-.64144-.81571l-15.381-7.40682a1.17893,1.17893,0,0,0-1.02671,0l-15.381,7.40682a1.187,1.187,0,0,0-.63789.80337l-3.79,16.60146a1.18422,1.18422,0,0,0,.21808,1.03735ZM229.851,467.85723a2.68563,2.68563,0,0,0,2.174,1.10751H251.41l19.385-.00354a2.6692,2.6692,0,0,0,2.09736-1.01282l12.08719-15.15357,12.085-15.159a2.708,2.708,0,0,0,.51744-2.25806l-8.62075-37.76932a2.65084,2.65084,0,0,0-1.45654-1.85182l-34.93073-16.82278a2.67955,2.67955,0,0,0-2.32855,0l-34.9309,16.82278a2.6774,2.6774,0,0,0-1.44836,1.82375l-8.607,37.70054a2.68116,2.68116,0,0,0,.494,2.35614Z\"/><path class=\"cls-1\" d=\"M48.64133,443.20813a1.17824,1.17824,0,0,0,.95653.48921h8.53639l8.53608-.00124a1.17715,1.17715,0,0,0,.92337-.44741l5.32335-6.67131,5.31965-6.67578a1.18728,1.18728,0,0,0,.22841-.99354l-3.7968-16.63169a1.16679,1.16679,0,0,0-.6402-.81571l-15.38228-7.40682a1.17622,1.17622,0,0,0-1.02547,0l-15.38028,7.40682a1.18185,1.18185,0,0,0-.63835.80337l-3.78955,16.60146a1.17623,1.17623,0,0,0,.21824,1.03735Zm-12.06375,24.6491a2.685,2.685,0,0,0,2.17308,1.10751H58.13425l19.38652-.00354a2.67635,2.67635,0,0,0,2.09812-1.01282l12.08627-15.15357,12.08365-15.159a2.69274,2.69274,0,0,0,.51743-2.25806l-8.61936-37.76932a2.64767,2.64767,0,0,0-1.45592-1.85182L59.30037,378.93387a2.6829,2.6829,0,0,0-2.33008,0L22.03877,395.75665a2.686,2.686,0,0,0-1.44866,1.82375l-8.60456,37.70054a2.67125,2.67125,0,0,0,.49414,2.35614Z\"/><path class=\"cls-1\" d=\"M432.85945,443.20813a1.178,1.178,0,0,0,.95652.48921h8.5361l8.53622-.00124a1.17706,1.17706,0,0,0,.92321-.44741l5.32337-6.67131,5.31979-6.67578a1.18136,1.18136,0,0,0,.2281-.99354l-3.7951-16.63169a1.16472,1.16472,0,0,0-.64159-.81571l-15.381-7.40682a1.17853,1.17853,0,0,0-1.02653,0l-15.38107,7.40682a1.18977,1.18977,0,0,0-.638.80337l-3.79,16.60146a1.18425,1.18425,0,0,0,.21822,1.03735Zm-12.06422,24.6491a2.68578,2.68578,0,0,0,2.1734,1.10751h19.38344l19.38635-.00354a2.67442,2.67442,0,0,0,2.09828-1.01282l12.08642-15.15357,12.08349-15.159a2.69186,2.69186,0,0,0,.51667-2.25806l-8.61844-37.76932a2.6516,2.6516,0,0,0-1.45592-1.85182l-34.93136-16.82278a2.6829,2.6829,0,0,0-2.33008,0l-34.9309,16.82278a2.68376,2.68376,0,0,0-1.44851,1.82375l-8.60487,37.70054a2.67227,2.67227,0,0,0,.4943,2.35614Z\"/></g></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": false, "published": false, "source_uri": "git://github.com/kyverno/kyverno/main/config/crds/kyverno"}, "configuration": null, "component": {"version": "kyverno.io/v1", "kind": "ClusterPolicy", "schema": "{\n \"description\": \"ClusterPolicy declares validation, mutation, and generation behaviors for matching resources.\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Spec declares policy behaviors.\",\n   \"properties\": {\n    \"admission\": {\n     \"default\": true,\n     \"description\": \"Admission controls if rules are applied during admission.\\nOptional. Default value is \\\"true\\\".\",\n     \"type\": \"boolean\"\n    },\n    \"applyRules\": {\n     \"description\": \"ApplyRules controls how rules in a policy are applied. Rule are processed in\\nthe order of declaration. When set to `One` processing stops after a rule has\\nbeen applied i.e. the rule matches and results in a pass, fail, or error. When\\nset to `All` all rules in the policy are processed. The default is `All`.\",\n     \"enum\": [\n      \"All\",\n      \"One\"\n     ],\n     \"type\": \"string\"\n    },\n    \"background\": {\n     \"default\": true,\n     \"description\": \"Background controls if rules are applied to existing resources during a background scan.\\nOptional. Default value is \\\"true\\\". The value must be set to \\\"false\\\" if the policy rule\\nuses variables that are only available in the admission review request (e.g. user name).\",\n     \"type\": \"boolean\"\n    },\n    \"emitWarning\": {\n     \"default\": false,\n     \"description\": \"EmitWarning enables API response warnings for mutate policy rules or validate policy rules with validationFailureAction set to Audit.\\nEnabling this option will extend admission request processing times. The default value is \\\"false\\\".\",\n     \"type\": \"boolean\"\n    },\n    \"failurePolicy\": {\n     \"description\": \"Deprecated, use failurePolicy under the webhookConfiguration instead.\",\n     \"enum\": [\n      \"Ignore\",\n      \"Fail\"\n     ],\n     \"type\": \"string\"\n    },\n    \"generateExisting\": {\n     \"description\": \"Deprecated, use generateExisting under the generate rule instead\",\n     \"type\": \"boolean\"\n    },\n    \"generateExistingOnPolicyUpdate\": {\n     \"description\": \"Deprecated, use generateExisting instead\",\n     \"type\": \"boolean\"\n    },\n    \"mutateExistingOnPolicyUpdate\": {\n     \"description\": \"Deprecated, use mutateExistingOnPolicyUpdate under the mutate rule instead\",\n     \"type\": \"boolean\"\n    },\n    \"rules\": {\n     \"description\": \"Rules is a list of Rule instances. A Policy contains multiple rules and\\neach rule can validate, mutate, or generate resources.\",\n     \"items\": {\n      \"description\": \"Rule defines a validation, mutation, or generation control for matching resources.\\nEach rules contains a match declaration to select resources, and an optional exclude\\ndeclaration to specify which resources to exclude.\",\n      \"properties\": {\n       \"celPreconditions\": {\n        \"description\": \"CELPreconditions are used to determine if a policy rule should be applied by evaluating a\\nset of CEL conditions. It can only be used with the validate.cel subrule\",\n        \"items\": {\n         \"description\": \"MatchCondition represents a condition which must by fulfilled for a request to be sent to a webhook.\",\n         \"properties\": {\n          \"expression\": {\n           \"description\": \"Expression represents the expression which will be evaluated by CEL. Must evaluate to bool.\\nCEL expressions have access to the contents of the AdmissionRequest and Authorizer, organized into CEL variables:\\n\\n'object' - The object from the incoming request. The value is null for DELETE requests.\\n'oldObject' - The existing object. The value is null for CREATE requests.\\n'request' - Attributes of the admission request(/pkg/apis/admission/types.go#AdmissionRequest).\\n'authorizer' - A CEL Authorizer. May be used to perform authorization checks for the principal (user or service account) of the request.\\n  See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz\\n'authorizer.requestResource' - A CEL ResourceCheck constructed from the 'authorizer' and configured with the\\n  request resource.\\nDocumentation on CEL: https://kubernetes.io/docs/reference/using-api/cel/\\n\\nRequired.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"Name is an identifier for this match condition, used for strategic merging of MatchConditions,\\nas well as providing an identifier for logging purposes. A good name should be descriptive of\\nthe associated expression.\\nName must be a qualified name consisting of alphanumeric characters, '-', '_' or '.', and\\nmust start and end with an alphanumeric character (e.g. 'MyName',  or 'my.name',  or\\n'123-abc', regex used for validation is '([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9]') with an\\noptional DNS subdomain prefix and '/' (e.g. 'example.com/MyName')\\n\\nRequired.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"expression\",\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"context\": {\n        \"description\": \"Context defines variables and data sources that can be used during rule execution.\",\n        \"items\": {\n         \"description\": \"ContextEntry adds variables and data sources to a rule Context. Either a\\nConfigMap reference or a APILookup must be provided.\",\n         \"oneOf\": [\n          {\n           \"required\": [\n            \"configMap\"\n           ]\n          },\n          {\n           \"required\": [\n            \"apiCall\"\n           ]\n          },\n          {\n           \"required\": [\n            \"imageRegistry\"\n           ]\n          },\n          {\n           \"required\": [\n            \"variable\"\n           ]\n          },\n          {\n           \"required\": [\n            \"globalReference\"\n           ]\n          }\n         ],\n         \"properties\": {\n          \"apiCall\": {\n           \"description\": \"APICall is an HTTP request to the Kubernetes API server, or other JSON web service.\\nThe data returned is stored in the context with the name for the context entry.\",\n           \"properties\": {\n            \"data\": {\n             \"description\": \"The data object specifies the POST data sent to the server.\\nOnly applicable when the method field is set to POST.\",\n             \"items\": {\n              \"description\": \"RequestData contains the HTTP POST data\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"Key is a unique identifier for the data value\",\n                \"type\": \"string\"\n               },\n               \"value\": {\n                \"description\": \"Value is the data value\",\n                \"format\": \"textarea\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"key\",\n               \"value\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"default\": {\n             \"description\": \"Default is an optional arbitrary JSON object that the context\\nvalue is set to, if the apiCall returns error.\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"jmesPath\": {\n             \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n             \"type\": \"string\"\n            },\n            \"method\": {\n             \"default\": \"GET\",\n             \"description\": \"Method is the HTTP request type (GET or POST). Defaults to GET.\",\n             \"enum\": [\n              \"GET\",\n              \"POST\"\n             ],\n             \"type\": \"string\"\n            },\n            \"service\": {\n             \"description\": \"Service is an API call to a JSON web service.\\nThis is used for non-Kubernetes API server calls.\\nIt's mutually exclusive with the URLPath field.\",\n             \"properties\": {\n              \"caBundle\": {\n               \"description\": \"CABundle is a PEM encoded CA bundle which will be used to validate\\nthe server certificate.\",\n               \"type\": \"string\"\n              },\n              \"headers\": {\n               \"description\": \"Headers is a list of optional HTTP headers to be included in the request.\",\n               \"items\": {\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"Key is the header key\",\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is the header value\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"value\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"url\": {\n               \"description\": \"URL is the JSON web service URL. A typical form is\\n`https://{service}.{namespace}:{port}/{path}`.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"url\"\n             ],\n             \"type\": \"object\"\n            },\n            \"urlPath\": {\n             \"description\": \"URLPath is the URL path to be used in the HTTP GET or POST request to the\\nKubernetes API server (e.g. \\\"/api/v1/namespaces\\\" or  \\\"/apis/apps/v1/deployments\\\").\\nThe format required is the same format used by the `kubectl get --raw` command.\\nSee https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls\\nfor details.\\nIt's mutually exclusive with the Service field.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"configMap\": {\n           \"description\": \"ConfigMap is the ConfigMap reference.\",\n           \"properties\": {\n            \"name\": {\n             \"description\": \"Name is the ConfigMap name.\",\n             \"type\": \"string\"\n            },\n            \"namespace\": {\n             \"description\": \"Namespace is the ConfigMap namespace.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"globalReference\": {\n           \"description\": \"GlobalContextEntryReference is a reference to a cached global context entry.\",\n           \"properties\": {\n            \"jmesPath\": {\n             \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name of the global context entry\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"imageRegistry\": {\n           \"description\": \"ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image\\ndetails.\",\n           \"properties\": {\n            \"imageRegistryCredentials\": {\n             \"description\": \"ImageRegistryCredentials provides credentials that will be used for authentication with registry\",\n             \"properties\": {\n              \"allowInsecureRegistry\": {\n               \"description\": \"AllowInsecureRegistry allows insecure access to a registry.\",\n               \"type\": \"boolean\"\n              },\n              \"providers\": {\n               \"description\": \"Providers specifies a list of OCI Registry names, whose authentication providers are provided.\\nIt can be of one of these values: default,google,azure,amazon,github.\",\n               \"items\": {\n                \"description\": \"ImageRegistryCredentialsProvidersType provides the list of credential providers required.\",\n                \"enum\": [\n                 \"default\",\n                 \"amazon\",\n                 \"azure\",\n                 \"google\",\n                 \"github\"\n                ],\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"secrets\": {\n               \"description\": \"Secrets specifies a list of secrets that are provided for credentials.\\nSecrets must live in the Kyverno namespace.\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"jmesPath\": {\n             \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the ImageData struct returned as a result of processing\\nthe image reference.\",\n             \"type\": \"string\"\n            },\n            \"reference\": {\n             \"description\": \"Reference is image reference to a container image in the registry.\\nExample: ghcr.io/kyverno/kyverno:latest\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"reference\"\n           ],\n           \"type\": \"object\"\n          },\n          \"name\": {\n           \"description\": \"Name is the variable name.\",\n           \"type\": \"string\"\n          },\n          \"variable\": {\n           \"description\": \"Variable defines an arbitrary JMESPath context variable that can be defined inline.\",\n           \"properties\": {\n            \"default\": {\n             \"description\": \"Default is an optional arbitrary JSON object that the variable may take if the JMESPath\\nexpression evaluates to nil\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"jmesPath\": {\n             \"description\": \"JMESPath is an optional JMESPath Expression that can be used to\\ntransform the variable.\",\n             \"type\": \"string\"\n            },\n            \"value\": {\n             \"description\": \"Value is any arbitrary JSON object representable in YAML or JSON form.\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"exclude\": {\n        \"description\": \"ExcludeResources defines when this policy rule should not be applied. The exclude\\ncriteria can include resource information (e.g. kind, name, namespace, labels)\\nand admission review request information like the name or role.\",\n        \"not\": {\n         \"required\": [\n          \"any\",\n          \"all\"\n         ]\n        },\n        \"properties\": {\n         \"all\": {\n          \"description\": \"All allows specifying resources which will be ANDed\",\n          \"items\": {\n           \"description\": \"ResourceFilter allow users to \\\"AND\\\" or \\\"OR\\\" between resources\",\n           \"properties\": {\n            \"clusterRoles\": {\n             \"description\": \"ClusterRoles is the list of cluster-wide role names for the user.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"resources\": {\n             \"description\": \"ResourceDescription contains information about the resource being created or modified.\",\n             \"not\": {\n              \"required\": [\n               \"name\",\n               \"names\"\n              ]\n             },\n             \"properties\": {\n              \"annotations\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"description\": \"Annotations is a  map of annotations (key-value pairs of type string). Annotation keys\\nand values support the wildcard characters \\\"*\\\" (matches zero or many characters) and\\n\\\"?\\\" (matches at least one character).\",\n               \"type\": \"object\"\n              },\n              \"kinds\": {\n               \"description\": \"Kinds is a list of resource kinds.\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of the resource. The name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\\nNOTE: \\\"Name\\\" is being deprecated in favor of \\\"Names\\\".\",\n               \"type\": \"string\"\n              },\n              \"names\": {\n               \"description\": \"Names are the names of the resources. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"namespaceSelector\": {\n               \"description\": \"NamespaceSelector is a label selector for the resource namespace. Label keys and values\\nin `matchLabels` support the wildcard characters `*` (matches zero or many characters)\\nand `?` (matches one character).Wildcards allows writing label selectors like\\n[\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that using [\\\"*\\\" : \\\"*\\\"] matches any key and value but\\ndoes not match an empty label set.\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"namespaces\": {\n               \"description\": \"Namespaces is a list of namespaces names. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"operations\": {\n               \"description\": \"Operations can contain values [\\\"CREATE, \\\"UPDATE\\\", \\\"CONNECT\\\", \\\"DELETE\\\"], which are used to match a specific action.\",\n               \"items\": {\n                \"description\": \"AdmissionOperation can have one of the values CREATE, UPDATE, CONNECT, DELETE, which are used to match a specific action.\",\n                \"enum\": [\n                 \"CREATE\",\n                 \"CONNECT\",\n                 \"UPDATE\",\n                 \"DELETE\"\n                ],\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"selector\": {\n               \"description\": \"Selector is a label selector. Label keys and values in `matchLabels` support the wildcard\\ncharacters `*` (matches zero or many characters) and `?` (matches one character).\\nWildcards allows writing label selectors like [\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that\\nusing [\\\"*\\\" : \\\"*\\\"] matches any key and value but does not match an empty label set.\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"roles\": {\n             \"description\": \"Roles is the list of namespaced role names for the user.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"subjects\": {\n             \"description\": \"Subjects is the list of subject names like users, user groups, and service accounts.\",\n             \"items\": {\n              \"description\": \"Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,\\nor a value for non-objects such as user and group names.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup holds the API group of the referenced subject.\\nDefaults to \\\"\\\" for ServiceAccount subjects.\\nDefaults to \\\"rbac.authorization.k8s.io\\\" for User and Group subjects.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind of object being referenced. Values defined by this API group are \\\"User\\\", \\\"Group\\\", and \\\"ServiceAccount\\\".\\nIf the Authorizer does not recognized the kind value, the Authorizer should report an error.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the object being referenced.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace of the referenced object.  If the object kind is non-namespace, such as \\\"User\\\" or \\\"Group\\\", and this value is not empty\\nthe Authorizer should report an error.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"any\": {\n          \"description\": \"Any allows specifying resources which will be ORed\",\n          \"items\": {\n           \"description\": \"ResourceFilter allow users to \\\"AND\\\" or \\\"OR\\\" between resources\",\n           \"properties\": {\n            \"clusterRoles\": {\n             \"description\": \"ClusterRoles is the list of cluster-wide role names for the user.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"resources\": {\n             \"description\": \"ResourceDescription contains information about the resource being created or modified.\",\n             \"not\": {\n              \"required\": [\n               \"name\",\n               \"names\"\n              ]\n             },\n             \"properties\": {\n              \"annotations\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"description\": \"Annotations is a  map of annotations (key-value pairs of type string). Annotation keys\\nand values support the wildcard characters \\\"*\\\" (matches zero or many characters) and\\n\\\"?\\\" (matches at least one character).\",\n               \"type\": \"object\"\n              },\n              \"kinds\": {\n               \"description\": \"Kinds is a list of resource kinds.\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of the resource. The name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\\nNOTE: \\\"Name\\\" is being deprecated in favor of \\\"Names\\\".\",\n               \"type\": \"string\"\n              },\n              \"names\": {\n               \"description\": \"Names are the names of the resources. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"namespaceSelector\": {\n               \"description\": \"NamespaceSelector is a label selector for the resource namespace. Label keys and values\\nin `matchLabels` support the wildcard characters `*` (matches zero or many characters)\\nand `?` (matches one character).Wildcards allows writing label selectors like\\n[\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that using [\\\"*\\\" : \\\"*\\\"] matches any key and value but\\ndoes not match an empty label set.\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"namespaces\": {\n               \"description\": \"Namespaces is a list of namespaces names. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"operations\": {\n               \"description\": \"Operations can contain values [\\\"CREATE, \\\"UPDATE\\\", \\\"CONNECT\\\", \\\"DELETE\\\"], which are used to match a specific action.\",\n               \"items\": {\n                \"description\": \"AdmissionOperation can have one of the values CREATE, UPDATE, CONNECT, DELETE, which are used to match a specific action.\",\n                \"enum\": [\n                 \"CREATE\",\n                 \"CONNECT\",\n                 \"UPDATE\",\n                 \"DELETE\"\n                ],\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"selector\": {\n               \"description\": \"Selector is a label selector. Label keys and values in `matchLabels` support the wildcard\\ncharacters `*` (matches zero or many characters) and `?` (matches one character).\\nWildcards allows writing label selectors like [\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that\\nusing [\\\"*\\\" : \\\"*\\\"] matches any key and value but does not match an empty label set.\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"roles\": {\n             \"description\": \"Roles is the list of namespaced role names for the user.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"subjects\": {\n             \"description\": \"Subjects is the list of subject names like users, user groups, and service accounts.\",\n             \"items\": {\n              \"description\": \"Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,\\nor a value for non-objects such as user and group names.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup holds the API group of the referenced subject.\\nDefaults to \\\"\\\" for ServiceAccount subjects.\\nDefaults to \\\"rbac.authorization.k8s.io\\\" for User and Group subjects.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind of object being referenced. Values defined by this API group are \\\"User\\\", \\\"Group\\\", and \\\"ServiceAccount\\\".\\nIf the Authorizer does not recognized the kind value, the Authorizer should report an error.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the object being referenced.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace of the referenced object.  If the object kind is non-namespace, such as \\\"User\\\" or \\\"Group\\\", and this value is not empty\\nthe Authorizer should report an error.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"clusterRoles\": {\n          \"description\": \"ClusterRoles is the list of cluster-wide role names for the user.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"resources\": {\n          \"description\": \"ResourceDescription contains information about the resource being created or modified.\\nRequires at least one tag to be specified when under MatchResources.\\nSpecifying ResourceDescription directly under match is being deprecated.\\nPlease specify under \\\"any\\\" or \\\"all\\\" instead.\",\n          \"not\": {\n           \"required\": [\n            \"name\",\n            \"names\"\n           ]\n          },\n          \"properties\": {\n           \"annotations\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"description\": \"Annotations is a  map of annotations (key-value pairs of type string). Annotation keys\\nand values support the wildcard characters \\\"*\\\" (matches zero or many characters) and\\n\\\"?\\\" (matches at least one character).\",\n            \"type\": \"object\"\n           },\n           \"kinds\": {\n            \"description\": \"Kinds is a list of resource kinds.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"name\": {\n            \"description\": \"Name is the name of the resource. The name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\\nNOTE: \\\"Name\\\" is being deprecated in favor of \\\"Names\\\".\",\n            \"type\": \"string\"\n           },\n           \"names\": {\n            \"description\": \"Names are the names of the resources. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"namespaceSelector\": {\n            \"description\": \"NamespaceSelector is a label selector for the resource namespace. Label keys and values\\nin `matchLabels` support the wildcard characters `*` (matches zero or many characters)\\nand `?` (matches one character).Wildcards allows writing label selectors like\\n[\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that using [\\\"*\\\" : \\\"*\\\"] matches any key and value but\\ndoes not match an empty label set.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"namespaces\": {\n            \"description\": \"Namespaces is a list of namespaces names. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"operations\": {\n            \"description\": \"Operations can contain values [\\\"CREATE, \\\"UPDATE\\\", \\\"CONNECT\\\", \\\"DELETE\\\"], which are used to match a specific action.\",\n            \"items\": {\n             \"description\": \"AdmissionOperation can have one of the values CREATE, UPDATE, CONNECT, DELETE, which are used to match a specific action.\",\n             \"enum\": [\n              \"CREATE\",\n              \"CONNECT\",\n              \"UPDATE\",\n              \"DELETE\"\n             ],\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"selector\": {\n            \"description\": \"Selector is a label selector. Label keys and values in `matchLabels` support the wildcard\\ncharacters `*` (matches zero or many characters) and `?` (matches one character).\\nWildcards allows writing label selectors like [\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that\\nusing [\\\"*\\\" : \\\"*\\\"] matches any key and value but does not match an empty label set.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"roles\": {\n          \"description\": \"Roles is the list of namespaced role names for the user.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"subjects\": {\n          \"description\": \"Subjects is the list of subject names like users, user groups, and service accounts.\",\n          \"items\": {\n           \"description\": \"Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,\\nor a value for non-objects such as user and group names.\",\n           \"properties\": {\n            \"apiGroup\": {\n             \"description\": \"APIGroup holds the API group of the referenced subject.\\nDefaults to \\\"\\\" for ServiceAccount subjects.\\nDefaults to \\\"rbac.authorization.k8s.io\\\" for User and Group subjects.\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind of object being referenced. Values defined by this API group are \\\"User\\\", \\\"Group\\\", and \\\"ServiceAccount\\\".\\nIf the Authorizer does not recognized the kind value, the Authorizer should report an error.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name of the object being referenced.\",\n             \"type\": \"string\"\n            },\n            \"namespace\": {\n             \"description\": \"Namespace of the referenced object.  If the object kind is non-namespace, such as \\\"User\\\" or \\\"Group\\\", and this value is not empty\\nthe Authorizer should report an error.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"kind\",\n            \"name\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"generate\": {\n        \"description\": \"Generation is used to create new resources.\",\n        \"properties\": {\n         \"apiVersion\": {\n          \"description\": \"APIVersion specifies resource apiVersion.\",\n          \"type\": \"string\"\n         },\n         \"clone\": {\n          \"description\": \"Clone specifies the source resource used to populate each generated resource.\\nAt most one of Data or Clone can be specified. If neither are provided, the generated\\nresource will be created with default data only.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name specifies name of the resource.\",\n            \"type\": \"string\"\n           },\n           \"namespace\": {\n            \"description\": \"Namespace specifies source resource namespace.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"cloneList\": {\n          \"description\": \"CloneList specifies the list of source resource used to populate each generated resource.\",\n          \"properties\": {\n           \"kinds\": {\n            \"description\": \"Kinds is a list of resource kinds.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"namespace\": {\n            \"description\": \"Namespace specifies source resource namespace.\",\n            \"type\": \"string\"\n           },\n           \"selector\": {\n            \"description\": \"Selector is a label selector. Label keys and values in `matchLabels`.\\nwildcard characters are not supported.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"data\": {\n          \"description\": \"Data provides the resource declaration used to populate each generated resource.\\nAt most one of Data or Clone must be specified. If neither are provided, the generated\\nresource will be created with default data only.\",\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"foreach\": {\n          \"description\": \"ForEach applies generate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.\",\n          \"items\": {\n           \"properties\": {\n            \"apiVersion\": {\n             \"description\": \"APIVersion specifies resource apiVersion.\",\n             \"type\": \"string\"\n            },\n            \"clone\": {\n             \"description\": \"Clone specifies the source resource used to populate each generated resource.\\nAt most one of Data or Clone can be specified. If neither are provided, the generated\\nresource will be created with default data only.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name specifies name of the resource.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"Namespace specifies source resource namespace.\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"cloneList\": {\n             \"description\": \"CloneList specifies the list of source resource used to populate each generated resource.\",\n             \"properties\": {\n              \"kinds\": {\n               \"description\": \"Kinds is a list of resource kinds.\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"namespace\": {\n               \"description\": \"Namespace specifies source resource namespace.\",\n               \"type\": \"string\"\n              },\n              \"selector\": {\n               \"description\": \"Selector is a label selector. Label keys and values in `matchLabels`.\\nwildcard characters are not supported.\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"context\": {\n             \"description\": \"Context defines variables and data sources that can be used during rule execution.\",\n             \"items\": {\n              \"description\": \"ContextEntry adds variables and data sources to a rule Context. Either a\\nConfigMap reference or a APILookup must be provided.\",\n              \"oneOf\": [\n               {\n                \"required\": [\n                 \"configMap\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"apiCall\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"imageRegistry\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"variable\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"globalReference\"\n                ]\n               }\n              ],\n              \"properties\": {\n               \"apiCall\": {\n                \"description\": \"APICall is an HTTP request to the Kubernetes API server, or other JSON web service.\\nThe data returned is stored in the context with the name for the context entry.\",\n                \"properties\": {\n                 \"data\": {\n                  \"description\": \"The data object specifies the POST data sent to the server.\\nOnly applicable when the method field is set to POST.\",\n                  \"items\": {\n                   \"description\": \"RequestData contains the HTTP POST data\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"Key is a unique identifier for the data value\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"Value is the data value\",\n                     \"format\": \"textarea\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"default\": {\n                  \"description\": \"Default is an optional arbitrary JSON object that the context\\nvalue is set to, if the apiCall returns error.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n                  \"type\": \"string\"\n                 },\n                 \"method\": {\n                  \"default\": \"GET\",\n                  \"description\": \"Method is the HTTP request type (GET or POST). Defaults to GET.\",\n                  \"enum\": [\n                   \"GET\",\n                   \"POST\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"service\": {\n                  \"description\": \"Service is an API call to a JSON web service.\\nThis is used for non-Kubernetes API server calls.\\nIt's mutually exclusive with the URLPath field.\",\n                  \"properties\": {\n                   \"caBundle\": {\n                    \"description\": \"CABundle is a PEM encoded CA bundle which will be used to validate\\nthe server certificate.\",\n                    \"type\": \"string\"\n                   },\n                   \"headers\": {\n                    \"description\": \"Headers is a list of optional HTTP headers to be included in the request.\",\n                    \"items\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"Key is the header key\",\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"description\": \"Value is the header value\",\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the JSON web service URL. A typical form is\\n`https://{service}.{namespace}:{port}/{path}`.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"url\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"urlPath\": {\n                  \"description\": \"URLPath is the URL path to be used in the HTTP GET or POST request to the\\nKubernetes API server (e.g. \\\"/api/v1/namespaces\\\" or  \\\"/apis/apps/v1/deployments\\\").\\nThe format required is the same format used by the `kubectl get --raw` command.\\nSee https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls\\nfor details.\\nIt's mutually exclusive with the Service field.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"configMap\": {\n                \"description\": \"ConfigMap is the ConfigMap reference.\",\n                \"properties\": {\n                 \"name\": {\n                  \"description\": \"Name is the ConfigMap name.\",\n                  \"type\": \"string\"\n                 },\n                 \"namespace\": {\n                  \"description\": \"Namespace is the ConfigMap namespace.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"globalReference\": {\n                \"description\": \"GlobalContextEntryReference is a reference to a cached global context entry.\",\n                \"properties\": {\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"description\": \"Name of the global context entry\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"imageRegistry\": {\n                \"description\": \"ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image\\ndetails.\",\n                \"properties\": {\n                 \"imageRegistryCredentials\": {\n                  \"description\": \"ImageRegistryCredentials provides credentials that will be used for authentication with registry\",\n                  \"properties\": {\n                   \"allowInsecureRegistry\": {\n                    \"description\": \"AllowInsecureRegistry allows insecure access to a registry.\",\n                    \"type\": \"boolean\"\n                   },\n                   \"providers\": {\n                    \"description\": \"Providers specifies a list of OCI Registry names, whose authentication providers are provided.\\nIt can be of one of these values: default,google,azure,amazon,github.\",\n                    \"items\": {\n                     \"description\": \"ImageRegistryCredentialsProvidersType provides the list of credential providers required.\",\n                     \"enum\": [\n                      \"default\",\n                      \"amazon\",\n                      \"azure\",\n                      \"google\",\n                      \"github\"\n                     ],\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"secrets\": {\n                    \"description\": \"Secrets specifies a list of secrets that are provided for credentials.\\nSecrets must live in the Kyverno namespace.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the ImageData struct returned as a result of processing\\nthe image reference.\",\n                  \"type\": \"string\"\n                 },\n                 \"reference\": {\n                  \"description\": \"Reference is image reference to a container image in the registry.\\nExample: ghcr.io/kyverno/kyverno:latest\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"reference\"\n                ],\n                \"type\": \"object\"\n               },\n               \"name\": {\n                \"description\": \"Name is the variable name.\",\n                \"type\": \"string\"\n               },\n               \"variable\": {\n                \"description\": \"Variable defines an arbitrary JMESPath context variable that can be defined inline.\",\n                \"properties\": {\n                 \"default\": {\n                  \"description\": \"Default is an optional arbitrary JSON object that the variable may take if the JMESPath\\nexpression evaluates to nil\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JMESPath Expression that can be used to\\ntransform the variable.\",\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is any arbitrary JSON object representable in YAML or JSON form.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"data\": {\n             \"description\": \"Data provides the resource declaration used to populate each generated resource.\\nAt most one of Data or Clone must be specified. If neither are provided, the generated\\nresource will be created with default data only.\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind specifies resource kind.\",\n             \"type\": \"string\"\n            },\n            \"list\": {\n             \"description\": \"List specifies a JMESPath expression that results in one or more elements\\nto which the validation logic is applied.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name specifies the resource name.\",\n             \"type\": \"string\"\n            },\n            \"namespace\": {\n             \"description\": \"Namespace specifies resource namespace.\",\n             \"type\": \"string\"\n            },\n            \"preconditions\": {\n             \"description\": \"AnyAllConditions are used to determine if a policy rule should be applied by evaluating a\\nset of conditions. The declaration can contain nested `any` or `all` statements.\\nSee: https://kyverno.io/docs/writing-policies/preconditions/\",\n             \"format\": \"textarea\",\n             \"properties\": {\n              \"all\": {\n               \"description\": \"AllConditions enable variable-based conditional rule execution. This is useful for\\nfiner control of when an rule is applied. A condition can reference object data\\nusing JMESPath notation.\\nHere, all of the conditions need to pass\",\n               \"items\": {\n                \"description\": \"Condition defines variable-based conditional criteria for rule execution.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"Key is the context entry (using JMESPath) for conditional rule evaluation.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"message\": {\n                  \"description\": \"Message is an optional display message\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"Operator is the conditional operation to perform. Valid operators are:\\nEquals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,\\nGreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,\\nDurationLessThanOrEquals, DurationLessThan\",\n                  \"enum\": [\n                   \"Equals\",\n                   \"NotEquals\",\n                   \"In\",\n                   \"AnyIn\",\n                   \"AllIn\",\n                   \"NotIn\",\n                   \"AnyNotIn\",\n                   \"AllNotIn\",\n                   \"GreaterThanOrEquals\",\n                   \"GreaterThan\",\n                   \"LessThanOrEquals\",\n                   \"LessThan\",\n                   \"DurationGreaterThanOrEquals\",\n                   \"DurationGreaterThan\",\n                   \"DurationLessThanOrEquals\",\n                   \"DurationLessThan\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is the conditional value, or set of values. The values can be fixed set\\nor can be variables declared using JMESPath.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"any\": {\n               \"description\": \"AnyConditions enable variable-based conditional rule execution. This is useful for\\nfiner control of when an rule is applied. A condition can reference object data\\nusing JMESPath notation.\\nHere, at least one of the conditions need to pass\",\n               \"items\": {\n                \"description\": \"Condition defines variable-based conditional criteria for rule execution.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"Key is the context entry (using JMESPath) for conditional rule evaluation.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"message\": {\n                  \"description\": \"Message is an optional display message\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"Operator is the conditional operation to perform. Valid operators are:\\nEquals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,\\nGreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,\\nDurationLessThanOrEquals, DurationLessThan\",\n                  \"enum\": [\n                   \"Equals\",\n                   \"NotEquals\",\n                   \"In\",\n                   \"AnyIn\",\n                   \"AllIn\",\n                   \"NotIn\",\n                   \"AnyNotIn\",\n                   \"AllNotIn\",\n                   \"GreaterThanOrEquals\",\n                   \"GreaterThan\",\n                   \"LessThanOrEquals\",\n                   \"LessThan\",\n                   \"DurationGreaterThanOrEquals\",\n                   \"DurationGreaterThan\",\n                   \"DurationLessThanOrEquals\",\n                   \"DurationLessThan\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is the conditional value, or set of values. The values can be fixed set\\nor can be variables declared using JMESPath.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"type\": \"string\"\n            },\n            \"uid\": {\n             \"description\": \"UID specifies the resource uid.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"generateExisting\": {\n          \"description\": \"GenerateExisting controls whether to trigger the rule in existing resources\\nIf is set to \\\"true\\\" the rule will be triggered and applied to existing matched resources.\",\n          \"type\": \"boolean\"\n         },\n         \"kind\": {\n          \"description\": \"Kind specifies resource kind.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name specifies the resource name.\",\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"description\": \"Namespace specifies resource namespace.\",\n          \"type\": \"string\"\n         },\n         \"orphanDownstreamOnPolicyDelete\": {\n          \"description\": \"OrphanDownstreamOnPolicyDelete controls whether generated resources should be deleted when the rule that generated\\nthem is deleted with synchronization enabled. This option is only applicable to generate rules of the data type.\\nSee https://kyverno.io/docs/writing-policies/generate/#data-examples.\\nDefaults to \\\"false\\\" if not specified.\",\n          \"type\": \"boolean\"\n         },\n         \"synchronize\": {\n          \"description\": \"Synchronize controls if generated resources should be kept in-sync with their source resource.\\nIf Synchronize is set to \\\"true\\\" changes to generated resources will be overwritten with resource\\ndata from Data or the resource specified in the Clone declaration.\\nOptional. Defaults to \\\"false\\\" if not specified.\",\n          \"type\": \"boolean\"\n         },\n         \"uid\": {\n          \"description\": \"UID specifies the resource uid.\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"imageExtractors\": {\n        \"additionalProperties\": {\n         \"items\": {\n          \"properties\": {\n           \"jmesPath\": {\n            \"description\": \"JMESPath is an optional JMESPath expression to apply to the image value.\\nThis is useful when the extracted image begins with a prefix like 'docker://'.\\nThe 'trim_prefix' function may be used to trim the prefix: trim_prefix(@, 'docker://').\\nNote - Image digest mutation may not be used when applying a JMESPAth to an image.\",\n            \"type\": \"string\"\n           },\n           \"key\": {\n            \"description\": \"Key is an optional name of the field within 'path' that will be used to uniquely identify an image.\\nNote - this field MUST be unique.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"description\": \"Name is the entry the image will be available under 'images.\\u003cname\\u003e' in the context.\\nIf this field is not defined, image entries will appear under 'images.custom'.\",\n            \"type\": \"string\"\n           },\n           \"path\": {\n            \"description\": \"Path is the path to the object containing the image field in a custom resource.\\nIt should be slash-separated. Each slash-separated key must be a valid YAML key or a wildcard '*'.\\nWildcard keys are expanded in case of arrays or objects.\",\n            \"type\": \"string\"\n           },\n           \"value\": {\n            \"description\": \"Value is an optional name of the field within 'path' that points to the image URI.\\nThis is useful when a custom 'key' is also defined.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"path\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"description\": \"ImageExtractors defines a mapping from kinds to ImageExtractorConfigs.\\nThis config is only valid for verifyImages rules.\",\n        \"type\": \"object\"\n       },\n       \"match\": {\n        \"description\": \"MatchResources defines when this policy rule should be applied. The match\\ncriteria can include resource information (e.g. kind, name, namespace, labels)\\nand admission review request information like the user name or role.\\nAt least one kind is required.\",\n        \"not\": {\n         \"required\": [\n          \"any\",\n          \"all\"\n         ]\n        },\n        \"properties\": {\n         \"all\": {\n          \"description\": \"All allows specifying resources which will be ANDed\",\n          \"items\": {\n           \"description\": \"ResourceFilter allow users to \\\"AND\\\" or \\\"OR\\\" between resources\",\n           \"properties\": {\n            \"clusterRoles\": {\n             \"description\": \"ClusterRoles is the list of cluster-wide role names for the user.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"resources\": {\n             \"description\": \"ResourceDescription contains information about the resource being created or modified.\",\n             \"not\": {\n              \"required\": [\n               \"name\",\n               \"names\"\n              ]\n             },\n             \"properties\": {\n              \"annotations\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"description\": \"Annotations is a  map of annotations (key-value pairs of type string). Annotation keys\\nand values support the wildcard characters \\\"*\\\" (matches zero or many characters) and\\n\\\"?\\\" (matches at least one character).\",\n               \"type\": \"object\"\n              },\n              \"kinds\": {\n               \"description\": \"Kinds is a list of resource kinds.\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of the resource. The name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\\nNOTE: \\\"Name\\\" is being deprecated in favor of \\\"Names\\\".\",\n               \"type\": \"string\"\n              },\n              \"names\": {\n               \"description\": \"Names are the names of the resources. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"namespaceSelector\": {\n               \"description\": \"NamespaceSelector is a label selector for the resource namespace. Label keys and values\\nin `matchLabels` support the wildcard characters `*` (matches zero or many characters)\\nand `?` (matches one character).Wildcards allows writing label selectors like\\n[\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that using [\\\"*\\\" : \\\"*\\\"] matches any key and value but\\ndoes not match an empty label set.\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"namespaces\": {\n               \"description\": \"Namespaces is a list of namespaces names. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"operations\": {\n               \"description\": \"Operations can contain values [\\\"CREATE, \\\"UPDATE\\\", \\\"CONNECT\\\", \\\"DELETE\\\"], which are used to match a specific action.\",\n               \"items\": {\n                \"description\": \"AdmissionOperation can have one of the values CREATE, UPDATE, CONNECT, DELETE, which are used to match a specific action.\",\n                \"enum\": [\n                 \"CREATE\",\n                 \"CONNECT\",\n                 \"UPDATE\",\n                 \"DELETE\"\n                ],\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"selector\": {\n               \"description\": \"Selector is a label selector. Label keys and values in `matchLabels` support the wildcard\\ncharacters `*` (matches zero or many characters) and `?` (matches one character).\\nWildcards allows writing label selectors like [\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that\\nusing [\\\"*\\\" : \\\"*\\\"] matches any key and value but does not match an empty label set.\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"roles\": {\n             \"description\": \"Roles is the list of namespaced role names for the user.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"subjects\": {\n             \"description\": \"Subjects is the list of subject names like users, user groups, and service accounts.\",\n             \"items\": {\n              \"description\": \"Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,\\nor a value for non-objects such as user and group names.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup holds the API group of the referenced subject.\\nDefaults to \\\"\\\" for ServiceAccount subjects.\\nDefaults to \\\"rbac.authorization.k8s.io\\\" for User and Group subjects.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind of object being referenced. Values defined by this API group are \\\"User\\\", \\\"Group\\\", and \\\"ServiceAccount\\\".\\nIf the Authorizer does not recognized the kind value, the Authorizer should report an error.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the object being referenced.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace of the referenced object.  If the object kind is non-namespace, such as \\\"User\\\" or \\\"Group\\\", and this value is not empty\\nthe Authorizer should report an error.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"any\": {\n          \"description\": \"Any allows specifying resources which will be ORed\",\n          \"items\": {\n           \"description\": \"ResourceFilter allow users to \\\"AND\\\" or \\\"OR\\\" between resources\",\n           \"properties\": {\n            \"clusterRoles\": {\n             \"description\": \"ClusterRoles is the list of cluster-wide role names for the user.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"resources\": {\n             \"description\": \"ResourceDescription contains information about the resource being created or modified.\",\n             \"not\": {\n              \"required\": [\n               \"name\",\n               \"names\"\n              ]\n             },\n             \"properties\": {\n              \"annotations\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"description\": \"Annotations is a  map of annotations (key-value pairs of type string). Annotation keys\\nand values support the wildcard characters \\\"*\\\" (matches zero or many characters) and\\n\\\"?\\\" (matches at least one character).\",\n               \"type\": \"object\"\n              },\n              \"kinds\": {\n               \"description\": \"Kinds is a list of resource kinds.\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of the resource. The name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\\nNOTE: \\\"Name\\\" is being deprecated in favor of \\\"Names\\\".\",\n               \"type\": \"string\"\n              },\n              \"names\": {\n               \"description\": \"Names are the names of the resources. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"namespaceSelector\": {\n               \"description\": \"NamespaceSelector is a label selector for the resource namespace. Label keys and values\\nin `matchLabels` support the wildcard characters `*` (matches zero or many characters)\\nand `?` (matches one character).Wildcards allows writing label selectors like\\n[\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that using [\\\"*\\\" : \\\"*\\\"] matches any key and value but\\ndoes not match an empty label set.\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"namespaces\": {\n               \"description\": \"Namespaces is a list of namespaces names. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"operations\": {\n               \"description\": \"Operations can contain values [\\\"CREATE, \\\"UPDATE\\\", \\\"CONNECT\\\", \\\"DELETE\\\"], which are used to match a specific action.\",\n               \"items\": {\n                \"description\": \"AdmissionOperation can have one of the values CREATE, UPDATE, CONNECT, DELETE, which are used to match a specific action.\",\n                \"enum\": [\n                 \"CREATE\",\n                 \"CONNECT\",\n                 \"UPDATE\",\n                 \"DELETE\"\n                ],\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"selector\": {\n               \"description\": \"Selector is a label selector. Label keys and values in `matchLabels` support the wildcard\\ncharacters `*` (matches zero or many characters) and `?` (matches one character).\\nWildcards allows writing label selectors like [\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that\\nusing [\\\"*\\\" : \\\"*\\\"] matches any key and value but does not match an empty label set.\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"roles\": {\n             \"description\": \"Roles is the list of namespaced role names for the user.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"subjects\": {\n             \"description\": \"Subjects is the list of subject names like users, user groups, and service accounts.\",\n             \"items\": {\n              \"description\": \"Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,\\nor a value for non-objects such as user and group names.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup holds the API group of the referenced subject.\\nDefaults to \\\"\\\" for ServiceAccount subjects.\\nDefaults to \\\"rbac.authorization.k8s.io\\\" for User and Group subjects.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind of object being referenced. Values defined by this API group are \\\"User\\\", \\\"Group\\\", and \\\"ServiceAccount\\\".\\nIf the Authorizer does not recognized the kind value, the Authorizer should report an error.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name of the object being referenced.\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace of the referenced object.  If the object kind is non-namespace, such as \\\"User\\\" or \\\"Group\\\", and this value is not empty\\nthe Authorizer should report an error.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"clusterRoles\": {\n          \"description\": \"ClusterRoles is the list of cluster-wide role names for the user.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"resources\": {\n          \"description\": \"ResourceDescription contains information about the resource being created or modified.\\nRequires at least one tag to be specified when under MatchResources.\\nSpecifying ResourceDescription directly under match is being deprecated.\\nPlease specify under \\\"any\\\" or \\\"all\\\" instead.\",\n          \"not\": {\n           \"required\": [\n            \"name\",\n            \"names\"\n           ]\n          },\n          \"properties\": {\n           \"annotations\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"description\": \"Annotations is a  map of annotations (key-value pairs of type string). Annotation keys\\nand values support the wildcard characters \\\"*\\\" (matches zero or many characters) and\\n\\\"?\\\" (matches at least one character).\",\n            \"type\": \"object\"\n           },\n           \"kinds\": {\n            \"description\": \"Kinds is a list of resource kinds.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"name\": {\n            \"description\": \"Name is the name of the resource. The name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\\nNOTE: \\\"Name\\\" is being deprecated in favor of \\\"Names\\\".\",\n            \"type\": \"string\"\n           },\n           \"names\": {\n            \"description\": \"Names are the names of the resources. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"namespaceSelector\": {\n            \"description\": \"NamespaceSelector is a label selector for the resource namespace. Label keys and values\\nin `matchLabels` support the wildcard characters `*` (matches zero or many characters)\\nand `?` (matches one character).Wildcards allows writing label selectors like\\n[\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that using [\\\"*\\\" : \\\"*\\\"] matches any key and value but\\ndoes not match an empty label set.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"namespaces\": {\n            \"description\": \"Namespaces is a list of namespaces names. Each name supports wildcard characters\\n\\\"*\\\" (matches zero or many characters) and \\\"?\\\" (at least one character).\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"operations\": {\n            \"description\": \"Operations can contain values [\\\"CREATE, \\\"UPDATE\\\", \\\"CONNECT\\\", \\\"DELETE\\\"], which are used to match a specific action.\",\n            \"items\": {\n             \"description\": \"AdmissionOperation can have one of the values CREATE, UPDATE, CONNECT, DELETE, which are used to match a specific action.\",\n             \"enum\": [\n              \"CREATE\",\n              \"CONNECT\",\n              \"UPDATE\",\n              \"DELETE\"\n             ],\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"selector\": {\n            \"description\": \"Selector is a label selector. Label keys and values in `matchLabels` support the wildcard\\ncharacters `*` (matches zero or many characters) and `?` (matches one character).\\nWildcards allows writing label selectors like [\\\"storage.k8s.io/*\\\": \\\"*\\\"]. Note that\\nusing [\\\"*\\\" : \\\"*\\\"] matches any key and value but does not match an empty label set.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"roles\": {\n          \"description\": \"Roles is the list of namespaced role names for the user.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"subjects\": {\n          \"description\": \"Subjects is the list of subject names like users, user groups, and service accounts.\",\n          \"items\": {\n           \"description\": \"Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,\\nor a value for non-objects such as user and group names.\",\n           \"properties\": {\n            \"apiGroup\": {\n             \"description\": \"APIGroup holds the API group of the referenced subject.\\nDefaults to \\\"\\\" for ServiceAccount subjects.\\nDefaults to \\\"rbac.authorization.k8s.io\\\" for User and Group subjects.\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind of object being referenced. Values defined by this API group are \\\"User\\\", \\\"Group\\\", and \\\"ServiceAccount\\\".\\nIf the Authorizer does not recognized the kind value, the Authorizer should report an error.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name of the object being referenced.\",\n             \"type\": \"string\"\n            },\n            \"namespace\": {\n             \"description\": \"Namespace of the referenced object.  If the object kind is non-namespace, such as \\\"User\\\" or \\\"Group\\\", and this value is not empty\\nthe Authorizer should report an error.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"kind\",\n            \"name\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"mutate\": {\n        \"description\": \"Mutation is used to modify matching resources.\",\n        \"properties\": {\n         \"foreach\": {\n          \"description\": \"ForEach applies mutation rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.\",\n          \"items\": {\n           \"description\": \"ForEachMutation applies mutation rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.\",\n           \"properties\": {\n            \"context\": {\n             \"description\": \"Context defines variables and data sources that can be used during rule execution.\",\n             \"items\": {\n              \"description\": \"ContextEntry adds variables and data sources to a rule Context. Either a\\nConfigMap reference or a APILookup must be provided.\",\n              \"oneOf\": [\n               {\n                \"required\": [\n                 \"configMap\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"apiCall\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"imageRegistry\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"variable\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"globalReference\"\n                ]\n               }\n              ],\n              \"properties\": {\n               \"apiCall\": {\n                \"description\": \"APICall is an HTTP request to the Kubernetes API server, or other JSON web service.\\nThe data returned is stored in the context with the name for the context entry.\",\n                \"properties\": {\n                 \"data\": {\n                  \"description\": \"The data object specifies the POST data sent to the server.\\nOnly applicable when the method field is set to POST.\",\n                  \"items\": {\n                   \"description\": \"RequestData contains the HTTP POST data\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"Key is a unique identifier for the data value\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"Value is the data value\",\n                     \"format\": \"textarea\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"default\": {\n                  \"description\": \"Default is an optional arbitrary JSON object that the context\\nvalue is set to, if the apiCall returns error.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n                  \"type\": \"string\"\n                 },\n                 \"method\": {\n                  \"default\": \"GET\",\n                  \"description\": \"Method is the HTTP request type (GET or POST). Defaults to GET.\",\n                  \"enum\": [\n                   \"GET\",\n                   \"POST\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"service\": {\n                  \"description\": \"Service is an API call to a JSON web service.\\nThis is used for non-Kubernetes API server calls.\\nIt's mutually exclusive with the URLPath field.\",\n                  \"properties\": {\n                   \"caBundle\": {\n                    \"description\": \"CABundle is a PEM encoded CA bundle which will be used to validate\\nthe server certificate.\",\n                    \"type\": \"string\"\n                   },\n                   \"headers\": {\n                    \"description\": \"Headers is a list of optional HTTP headers to be included in the request.\",\n                    \"items\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"Key is the header key\",\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"description\": \"Value is the header value\",\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the JSON web service URL. A typical form is\\n`https://{service}.{namespace}:{port}/{path}`.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"url\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"urlPath\": {\n                  \"description\": \"URLPath is the URL path to be used in the HTTP GET or POST request to the\\nKubernetes API server (e.g. \\\"/api/v1/namespaces\\\" or  \\\"/apis/apps/v1/deployments\\\").\\nThe format required is the same format used by the `kubectl get --raw` command.\\nSee https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls\\nfor details.\\nIt's mutually exclusive with the Service field.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"configMap\": {\n                \"description\": \"ConfigMap is the ConfigMap reference.\",\n                \"properties\": {\n                 \"name\": {\n                  \"description\": \"Name is the ConfigMap name.\",\n                  \"type\": \"string\"\n                 },\n                 \"namespace\": {\n                  \"description\": \"Namespace is the ConfigMap namespace.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"globalReference\": {\n                \"description\": \"GlobalContextEntryReference is a reference to a cached global context entry.\",\n                \"properties\": {\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"description\": \"Name of the global context entry\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"imageRegistry\": {\n                \"description\": \"ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image\\ndetails.\",\n                \"properties\": {\n                 \"imageRegistryCredentials\": {\n                  \"description\": \"ImageRegistryCredentials provides credentials that will be used for authentication with registry\",\n                  \"properties\": {\n                   \"allowInsecureRegistry\": {\n                    \"description\": \"AllowInsecureRegistry allows insecure access to a registry.\",\n                    \"type\": \"boolean\"\n                   },\n                   \"providers\": {\n                    \"description\": \"Providers specifies a list of OCI Registry names, whose authentication providers are provided.\\nIt can be of one of these values: default,google,azure,amazon,github.\",\n                    \"items\": {\n                     \"description\": \"ImageRegistryCredentialsProvidersType provides the list of credential providers required.\",\n                     \"enum\": [\n                      \"default\",\n                      \"amazon\",\n                      \"azure\",\n                      \"google\",\n                      \"github\"\n                     ],\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"secrets\": {\n                    \"description\": \"Secrets specifies a list of secrets that are provided for credentials.\\nSecrets must live in the Kyverno namespace.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the ImageData struct returned as a result of processing\\nthe image reference.\",\n                  \"type\": \"string\"\n                 },\n                 \"reference\": {\n                  \"description\": \"Reference is image reference to a container image in the registry.\\nExample: ghcr.io/kyverno/kyverno:latest\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"reference\"\n                ],\n                \"type\": \"object\"\n               },\n               \"name\": {\n                \"description\": \"Name is the variable name.\",\n                \"type\": \"string\"\n               },\n               \"variable\": {\n                \"description\": \"Variable defines an arbitrary JMESPath context variable that can be defined inline.\",\n                \"properties\": {\n                 \"default\": {\n                  \"description\": \"Default is an optional arbitrary JSON object that the variable may take if the JMESPath\\nexpression evaluates to nil\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JMESPath Expression that can be used to\\ntransform the variable.\",\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is any arbitrary JSON object representable in YAML or JSON form.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"foreach\": {\n             \"description\": \"Foreach declares a nested foreach iterator\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"list\": {\n             \"description\": \"List specifies a JMESPath expression that results in one or more elements\\nto which the validation logic is applied.\",\n             \"type\": \"string\"\n            },\n            \"order\": {\n             \"description\": \"Order defines the iteration order on the list.\\nCan be Ascending to iterate from first to last element or Descending to iterate in from last to first element.\",\n             \"enum\": [\n              \"Ascending\",\n              \"Descending\"\n             ],\n             \"type\": \"string\"\n            },\n            \"patchStrategicMerge\": {\n             \"description\": \"PatchStrategicMerge is a strategic merge patch used to modify resources.\\nSee https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/\\nand https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesstrategicmerge/.\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"patchesJson6902\": {\n             \"description\": \"PatchesJSON6902 is a list of RFC 6902 JSON Patch declarations used to modify resources.\\nSee https://tools.ietf.org/html/rfc6902 and https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesjson6902/.\",\n             \"type\": \"string\"\n            },\n            \"preconditions\": {\n             \"description\": \"AnyAllConditions are used to determine if a policy rule should be applied by evaluating a\\nset of conditions. The declaration can contain nested `any` or `all` statements.\\nSee: https://kyverno.io/docs/writing-policies/preconditions/\",\n             \"format\": \"textarea\",\n             \"properties\": {\n              \"all\": {\n               \"description\": \"AllConditions enable variable-based conditional rule execution. This is useful for\\nfiner control of when an rule is applied. A condition can reference object data\\nusing JMESPath notation.\\nHere, all of the conditions need to pass\",\n               \"items\": {\n                \"description\": \"Condition defines variable-based conditional criteria for rule execution.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"Key is the context entry (using JMESPath) for conditional rule evaluation.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"message\": {\n                  \"description\": \"Message is an optional display message\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"Operator is the conditional operation to perform. Valid operators are:\\nEquals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,\\nGreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,\\nDurationLessThanOrEquals, DurationLessThan\",\n                  \"enum\": [\n                   \"Equals\",\n                   \"NotEquals\",\n                   \"In\",\n                   \"AnyIn\",\n                   \"AllIn\",\n                   \"NotIn\",\n                   \"AnyNotIn\",\n                   \"AllNotIn\",\n                   \"GreaterThanOrEquals\",\n                   \"GreaterThan\",\n                   \"LessThanOrEquals\",\n                   \"LessThan\",\n                   \"DurationGreaterThanOrEquals\",\n                   \"DurationGreaterThan\",\n                   \"DurationLessThanOrEquals\",\n                   \"DurationLessThan\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is the conditional value, or set of values. The values can be fixed set\\nor can be variables declared using JMESPath.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"any\": {\n               \"description\": \"AnyConditions enable variable-based conditional rule execution. This is useful for\\nfiner control of when an rule is applied. A condition can reference object data\\nusing JMESPath notation.\\nHere, at least one of the conditions need to pass\",\n               \"items\": {\n                \"description\": \"Condition defines variable-based conditional criteria for rule execution.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"Key is the context entry (using JMESPath) for conditional rule evaluation.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"message\": {\n                  \"description\": \"Message is an optional display message\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"Operator is the conditional operation to perform. Valid operators are:\\nEquals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,\\nGreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,\\nDurationLessThanOrEquals, DurationLessThan\",\n                  \"enum\": [\n                   \"Equals\",\n                   \"NotEquals\",\n                   \"In\",\n                   \"AnyIn\",\n                   \"AllIn\",\n                   \"NotIn\",\n                   \"AnyNotIn\",\n                   \"AllNotIn\",\n                   \"GreaterThanOrEquals\",\n                   \"GreaterThan\",\n                   \"LessThanOrEquals\",\n                   \"LessThan\",\n                   \"DurationGreaterThanOrEquals\",\n                   \"DurationGreaterThan\",\n                   \"DurationLessThanOrEquals\",\n                   \"DurationLessThan\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is the conditional value, or set of values. The values can be fixed set\\nor can be variables declared using JMESPath.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"mutateExistingOnPolicyUpdate\": {\n          \"description\": \"MutateExistingOnPolicyUpdate controls if the mutateExisting rule will be applied on policy events.\",\n          \"type\": \"boolean\"\n         },\n         \"patchStrategicMerge\": {\n          \"description\": \"PatchStrategicMerge is a strategic merge patch used to modify resources.\\nSee https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/\\nand https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesstrategicmerge/.\",\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"patchesJson6902\": {\n          \"description\": \"PatchesJSON6902 is a list of RFC 6902 JSON Patch declarations used to modify resources.\\nSee https://tools.ietf.org/html/rfc6902 and https://kubectl.docs.kubernetes.io/references/kustomize/kustomization/patchesjson6902/.\",\n          \"type\": \"string\"\n         },\n         \"targets\": {\n          \"description\": \"Targets defines the target resources to be mutated.\",\n          \"items\": {\n           \"description\": \"TargetResourceSpec defines targets for mutating existing resources.\",\n           \"properties\": {\n            \"apiVersion\": {\n             \"description\": \"APIVersion specifies resource apiVersion.\",\n             \"type\": \"string\"\n            },\n            \"context\": {\n             \"description\": \"Context defines variables and data sources that can be used during rule execution.\",\n             \"items\": {\n              \"description\": \"ContextEntry adds variables and data sources to a rule Context. Either a\\nConfigMap reference or a APILookup must be provided.\",\n              \"oneOf\": [\n               {\n                \"required\": [\n                 \"configMap\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"apiCall\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"imageRegistry\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"variable\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"globalReference\"\n                ]\n               }\n              ],\n              \"properties\": {\n               \"apiCall\": {\n                \"description\": \"APICall is an HTTP request to the Kubernetes API server, or other JSON web service.\\nThe data returned is stored in the context with the name for the context entry.\",\n                \"properties\": {\n                 \"data\": {\n                  \"description\": \"The data object specifies the POST data sent to the server.\\nOnly applicable when the method field is set to POST.\",\n                  \"items\": {\n                   \"description\": \"RequestData contains the HTTP POST data\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"Key is a unique identifier for the data value\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"Value is the data value\",\n                     \"format\": \"textarea\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"default\": {\n                  \"description\": \"Default is an optional arbitrary JSON object that the context\\nvalue is set to, if the apiCall returns error.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n                  \"type\": \"string\"\n                 },\n                 \"method\": {\n                  \"default\": \"GET\",\n                  \"description\": \"Method is the HTTP request type (GET or POST). Defaults to GET.\",\n                  \"enum\": [\n                   \"GET\",\n                   \"POST\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"service\": {\n                  \"description\": \"Service is an API call to a JSON web service.\\nThis is used for non-Kubernetes API server calls.\\nIt's mutually exclusive with the URLPath field.\",\n                  \"properties\": {\n                   \"caBundle\": {\n                    \"description\": \"CABundle is a PEM encoded CA bundle which will be used to validate\\nthe server certificate.\",\n                    \"type\": \"string\"\n                   },\n                   \"headers\": {\n                    \"description\": \"Headers is a list of optional HTTP headers to be included in the request.\",\n                    \"items\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"Key is the header key\",\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"description\": \"Value is the header value\",\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the JSON web service URL. A typical form is\\n`https://{service}.{namespace}:{port}/{path}`.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"url\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"urlPath\": {\n                  \"description\": \"URLPath is the URL path to be used in the HTTP GET or POST request to the\\nKubernetes API server (e.g. \\\"/api/v1/namespaces\\\" or  \\\"/apis/apps/v1/deployments\\\").\\nThe format required is the same format used by the `kubectl get --raw` command.\\nSee https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls\\nfor details.\\nIt's mutually exclusive with the Service field.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"configMap\": {\n                \"description\": \"ConfigMap is the ConfigMap reference.\",\n                \"properties\": {\n                 \"name\": {\n                  \"description\": \"Name is the ConfigMap name.\",\n                  \"type\": \"string\"\n                 },\n                 \"namespace\": {\n                  \"description\": \"Namespace is the ConfigMap namespace.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"globalReference\": {\n                \"description\": \"GlobalContextEntryReference is a reference to a cached global context entry.\",\n                \"properties\": {\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"description\": \"Name of the global context entry\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"imageRegistry\": {\n                \"description\": \"ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image\\ndetails.\",\n                \"properties\": {\n                 \"imageRegistryCredentials\": {\n                  \"description\": \"ImageRegistryCredentials provides credentials that will be used for authentication with registry\",\n                  \"properties\": {\n                   \"allowInsecureRegistry\": {\n                    \"description\": \"AllowInsecureRegistry allows insecure access to a registry.\",\n                    \"type\": \"boolean\"\n                   },\n                   \"providers\": {\n                    \"description\": \"Providers specifies a list of OCI Registry names, whose authentication providers are provided.\\nIt can be of one of these values: default,google,azure,amazon,github.\",\n                    \"items\": {\n                     \"description\": \"ImageRegistryCredentialsProvidersType provides the list of credential providers required.\",\n                     \"enum\": [\n                      \"default\",\n                      \"amazon\",\n                      \"azure\",\n                      \"google\",\n                      \"github\"\n                     ],\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"secrets\": {\n                    \"description\": \"Secrets specifies a list of secrets that are provided for credentials.\\nSecrets must live in the Kyverno namespace.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the ImageData struct returned as a result of processing\\nthe image reference.\",\n                  \"type\": \"string\"\n                 },\n                 \"reference\": {\n                  \"description\": \"Reference is image reference to a container image in the registry.\\nExample: ghcr.io/kyverno/kyverno:latest\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"reference\"\n                ],\n                \"type\": \"object\"\n               },\n               \"name\": {\n                \"description\": \"Name is the variable name.\",\n                \"type\": \"string\"\n               },\n               \"variable\": {\n                \"description\": \"Variable defines an arbitrary JMESPath context variable that can be defined inline.\",\n                \"properties\": {\n                 \"default\": {\n                  \"description\": \"Default is an optional arbitrary JSON object that the variable may take if the JMESPath\\nexpression evaluates to nil\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JMESPath Expression that can be used to\\ntransform the variable.\",\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is any arbitrary JSON object representable in YAML or JSON form.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"kind\": {\n             \"description\": \"Kind specifies resource kind.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name specifies the resource name.\",\n             \"type\": \"string\"\n            },\n            \"namespace\": {\n             \"description\": \"Namespace specifies resource namespace.\",\n             \"type\": \"string\"\n            },\n            \"preconditions\": {\n             \"description\": \"Preconditions are used to determine if a policy rule should be applied by evaluating a\\nset of conditions. The declaration can contain nested `any` or `all` statements. A direct list\\nof conditions (without `any` or `all` statements is supported for backwards compatibility but\\nwill be deprecated in the next major release.\\nSee: https://kyverno.io/docs/writing-policies/preconditions/\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"selector\": {\n             \"description\": \"Selector allows you to select target resources with their labels.\",\n             \"properties\": {\n              \"matchExpressions\": {\n               \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n               \"items\": {\n                \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"key is the label key that the selector applies to.\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                  \"type\": \"string\"\n                 },\n                 \"values\": {\n                  \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"operator\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"matchLabels\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"uid\": {\n             \"description\": \"UID specifies the resource uid.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"name\": {\n        \"description\": \"Name is a label to identify the rule, It must be unique within the policy.\",\n        \"maxLength\": 63,\n        \"type\": \"string\"\n       },\n       \"preconditions\": {\n        \"description\": \"Preconditions are used to determine if a policy rule should be applied by evaluating a\\nset of conditions. The declaration can contain nested `any` or `all` statements. A direct list\\nof conditions (without `any` or `all` statements is supported for backwards compatibility but\\nwill be deprecated in the next major release.\\nSee: https://kyverno.io/docs/writing-policies/preconditions/\",\n        \"format\": \"textarea\",\n        \"type\": \"string\"\n       },\n       \"reportProperties\": {\n        \"additionalProperties\": {\n         \"type\": \"string\"\n        },\n        \"description\": \"ReportProperties are the additional properties from the rule that will be added to the policy report result\",\n        \"type\": \"object\"\n       },\n       \"skipBackgroundRequests\": {\n        \"default\": true,\n        \"description\": \"SkipBackgroundRequests bypasses admission requests that are sent by the background controller.\\nThe default value is set to \\\"true\\\", it must be set to \\\"false\\\" to apply\\ngenerate and mutateExisting rules to those requests.\",\n        \"type\": \"boolean\"\n       },\n       \"validate\": {\n        \"description\": \"Validation is used to validate matching resources.\",\n        \"properties\": {\n         \"allowExistingViolations\": {\n          \"default\": true,\n          \"description\": \"AllowExistingViolations allows prexisting violating resources to continue violating a policy.\",\n          \"type\": \"boolean\"\n         },\n         \"anyPattern\": {\n          \"description\": \"AnyPattern specifies list of validation patterns. At least one of the patterns\\nmust be satisfied for the validation rule to succeed.\",\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"assert\": {\n          \"description\": \"Assert defines a kyverno-json assertion tree.\",\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"cel\": {\n          \"description\": \"CEL allows validation checks using the Common Expression Language (https://kubernetes.io/docs/reference/using-api/cel/).\",\n          \"properties\": {\n           \"auditAnnotations\": {\n            \"description\": \"AuditAnnotations contains CEL expressions which are used to produce audit annotations for the audit event of the API request.\",\n            \"items\": {\n             \"description\": \"AuditAnnotation describes how to produce an audit annotation for an API request.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"key specifies the audit annotation key. The audit annotation keys of\\na ValidatingAdmissionPolicy must be unique. The key must be a qualified\\nname ([A-Za-z0-9][-A-Za-z0-9_.]*) no more than 63 bytes in length.\\n\\nThe key is combined with the resource name of the\\nValidatingAdmissionPolicy to construct an audit annotation key:\\n\\\"{ValidatingAdmissionPolicy name}/{key}\\\".\\n\\nIf an admission webhook uses the same resource name as this ValidatingAdmissionPolicy\\nand the same audit annotation key, the annotation key will be identical.\\nIn this case, the first annotation written with the key will be included\\nin the audit event and all subsequent annotations with the same key\\nwill be discarded.\\n\\nRequired.\",\n               \"type\": \"string\"\n              },\n              \"valueExpression\": {\n               \"description\": \"valueExpression represents the expression which is evaluated by CEL to\\nproduce an audit annotation value. The expression must evaluate to either\\na string or null value. If the expression evaluates to a string, the\\naudit annotation is included with the string value. If the expression\\nevaluates to null or empty string the audit annotation will be omitted.\\nThe valueExpression may be no longer than 5kb in length.\\nIf the result of the valueExpression is more than 10kb in length, it\\nwill be truncated to 10kb.\\n\\nIf multiple ValidatingAdmissionPolicyBinding resources match an\\nAPI request, then the valueExpression will be evaluated for\\neach binding. All unique values produced by the valueExpressions\\nwill be joined together in a comma-separated list.\\n\\nRequired.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"key\",\n              \"valueExpression\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"expressions\": {\n            \"description\": \"Expressions is a list of CELExpression types.\",\n            \"items\": {\n             \"description\": \"Validation specifies the CEL expression which is used to apply the validation.\",\n             \"properties\": {\n              \"expression\": {\n               \"description\": \"Expression represents the expression which will be evaluated by CEL.\\nref: https://github.com/google/cel-spec\\nCEL expressions have access to the contents of the API request/response, organized into CEL variables as well as some other useful variables:\\n\\n- 'object' - The object from the incoming request. The value is null for DELETE requests.\\n- 'oldObject' - The existing object. The value is null for CREATE requests.\\n- 'request' - Attributes of the API request([ref](/pkg/apis/admission/types.go#AdmissionRequest)).\\n- 'params' - Parameter resource referred to by the policy binding being evaluated. Only populated if the policy has a ParamKind.\\n- 'namespaceObject' - The namespace object that the incoming object belongs to. The value is null for cluster-scoped resources.\\n- 'variables' - Map of composited variables, from its name to its lazily evaluated value.\\n  For example, a variable named 'foo' can be accessed as 'variables.foo'.\\n- 'authorizer' - A CEL Authorizer. May be used to perform authorization checks for the principal (user or service account) of the request.\\n  See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz\\n- 'authorizer.requestResource' - A CEL ResourceCheck constructed from the 'authorizer' and configured with the\\n  request resource.\\n\\nThe `apiVersion`, `kind`, `metadata.name` and `metadata.generateName` are always accessible from the root of the\\nobject. No other metadata properties are accessible.\\n\\nOnly property names of the form `[a-zA-Z_.-/][a-zA-Z0-9_.-/]*` are accessible.\\nAccessible property names are escaped according to the following rules when accessed in the expression:\\n- '__' escapes to '__underscores__'\\n- '.' escapes to '__dot__'\\n- '-' escapes to '__dash__'\\n- '/' escapes to '__slash__'\\n- Property names that exactly match a CEL RESERVED keyword escape to '__{keyword}__'. The keywords are:\\n\\t  \\\"true\\\", \\\"false\\\", \\\"null\\\", \\\"in\\\", \\\"as\\\", \\\"break\\\", \\\"const\\\", \\\"continue\\\", \\\"else\\\", \\\"for\\\", \\\"function\\\", \\\"if\\\",\\n\\t  \\\"import\\\", \\\"let\\\", \\\"loop\\\", \\\"package\\\", \\\"namespace\\\", \\\"return\\\".\\nExamples:\\n  - Expression accessing a property named \\\"namespace\\\": {\\\"Expression\\\": \\\"object.__namespace__ \\u003e 0\\\"}\\n  - Expression accessing a property named \\\"x-prop\\\": {\\\"Expression\\\": \\\"object.x__dash__prop \\u003e 0\\\"}\\n  - Expression accessing a property named \\\"redact__d\\\": {\\\"Expression\\\": \\\"object.redact__underscores__d \\u003e 0\\\"}\\n\\nEquality on arrays with list type of 'set' or 'map' ignores element order, i.e. [1, 2] == [2, 1].\\nConcatenation on arrays with x-kubernetes-list-type use the semantics of the list type:\\n  - 'set': `X + Y` performs a union where the array positions of all elements in `X` are preserved and\\n    non-intersecting elements in `Y` are appended, retaining their partial order.\\n  - 'map': `X + Y` performs a merge where the array positions of all keys in `X` are preserved but the values\\n    are overwritten by values in `Y` when the key sets of `X` and `Y` intersect. Elements in `Y` with\\n    non-intersecting keys are appended, retaining their partial order.\\nRequired.\",\n               \"type\": \"string\"\n              },\n              \"message\": {\n               \"description\": \"Message represents the message displayed when validation fails. The message is required if the Expression contains\\nline breaks. The message must not contain line breaks.\\nIf unset, the message is \\\"failed rule: {Rule}\\\".\\ne.g. \\\"must be a URL with the host matching spec.host\\\"\\nIf the Expression contains line breaks. Message is required.\\nThe message must not contain line breaks.\\nIf unset, the message is \\\"failed Expression: {Expression}\\\".\",\n               \"type\": \"string\"\n              },\n              \"messageExpression\": {\n               \"description\": \"messageExpression declares a CEL expression that evaluates to the validation failure message that is returned when this rule fails.\\nSince messageExpression is used as a failure message, it must evaluate to a string.\\nIf both message and messageExpression are present on a validation, then messageExpression will be used if validation fails.\\nIf messageExpression results in a runtime error, the runtime error is logged, and the validation failure message is produced\\nas if the messageExpression field were unset. If messageExpression evaluates to an empty string, a string with only spaces, or a string\\nthat contains line breaks, then the validation failure message will also be produced as if the messageExpression field were unset, and\\nthe fact that messageExpression produced an empty string/string with only spaces/string with line breaks will be logged.\\nmessageExpression has access to all the same variables as the `expression` except for 'authorizer' and 'authorizer.requestResource'.\\nExample:\\n\\\"object.x must be less than max (\\\"+string(params.max)+\\\")\\\"\",\n               \"type\": \"string\"\n              },\n              \"reason\": {\n               \"description\": \"Reason represents a machine-readable description of why this validation failed.\\nIf this is the first validation in the list to fail, this reason, as well as the\\ncorresponding HTTP response code, are used in the\\nHTTP response to the client.\\nThe currently supported reasons are: \\\"Unauthorized\\\", \\\"Forbidden\\\", \\\"Invalid\\\", \\\"RequestEntityTooLarge\\\".\\nIf not set, StatusReasonInvalid is used in the response to the client.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"expression\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"generate\": {\n            \"default\": false,\n            \"description\": \"Generate specifies whether to generate a Kubernetes ValidatingAdmissionPolicy from the rule.\\nOptional. Defaults to \\\"false\\\" if not specified.\",\n            \"type\": \"boolean\"\n           },\n           \"paramKind\": {\n            \"description\": \"ParamKind is a tuple of Group Kind and Version.\",\n            \"properties\": {\n             \"apiVersion\": {\n              \"description\": \"APIVersion is the API group version the resources belong to.\\nIn format of \\\"group/version\\\".\\nRequired.\",\n              \"type\": \"string\"\n             },\n             \"kind\": {\n              \"description\": \"Kind is the API kind the resources belong to.\\nRequired.\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"paramRef\": {\n            \"description\": \"ParamRef references a parameter resource.\",\n            \"properties\": {\n             \"name\": {\n              \"description\": \"name is the name of the resource being referenced.\\n\\nOne of `name` or `selector` must be set, but `name` and `selector` are\\nmutually exclusive properties. If one is set, the other must be unset.\\n\\nA single parameter used for all admission requests can be configured\\nby setting the `name` field, leaving `selector` blank, and setting namespace\\nif `paramKind` is namespace-scoped.\",\n              \"type\": \"string\"\n             },\n             \"namespace\": {\n              \"description\": \"namespace is the namespace of the referenced resource. Allows limiting\\nthe search for params to a specific namespace. Applies to both `name` and\\n`selector` fields.\\n\\nA per-namespace parameter may be used by specifying a namespace-scoped\\n`paramKind` in the policy and leaving this field empty.\\n\\n- If `paramKind` is cluster-scoped, this field MUST be unset. Setting this\\nfield results in a configuration error.\\n\\n- If `paramKind` is namespace-scoped, the namespace of the object being\\nevaluated for admission will be used when this field is left unset. Take\\ncare that if this is left empty the binding must not match any cluster-scoped\\nresources, which will result in an error.\",\n              \"type\": \"string\"\n             },\n             \"parameterNotFoundAction\": {\n              \"description\": \"`parameterNotFoundAction` controls the behavior of the binding when the resource\\nexists, and name or selector is valid, but there are no parameters\\nmatched by the binding. If the value is set to `Allow`, then no\\nmatched parameters will be treated as successful validation by the binding.\\nIf set to `Deny`, then no matched parameters will be subject to the\\n`failurePolicy` of the policy.\\n\\nAllowed values are `Allow` or `Deny`\\n\\nRequired\",\n              \"type\": \"string\"\n             },\n             \"selector\": {\n              \"description\": \"selector can be used to match multiple param objects based on their labels.\\nSupply selector: {} to match all resources of the ParamKind.\\n\\nIf multiple params are found, they are all evaluated with the policy expressions\\nand the results are ANDed together.\\n\\nOne of `name` or `selector` must be set, but `name` and `selector` are\\nmutually exclusive properties. If one is set, the other must be unset.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"variables\": {\n            \"description\": \"Variables contain definitions of variables that can be used in composition of other expressions.\\nEach variable is defined as a named CEL expression.\\nThe variables defined here will be available under `variables` in other expressions of the policy.\",\n            \"items\": {\n             \"description\": \"Variable is the definition of a variable that is used for composition. A variable is defined as a named expression.\",\n             \"properties\": {\n              \"expression\": {\n               \"description\": \"Expression is the expression that will be evaluated as the value of the variable.\\nThe CEL expression has access to the same identifiers as the CEL expressions in Validation.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of the variable. The name must be a valid CEL identifier and unique among all variables.\\nThe variable can be accessed in other expressions through `variables`\\nFor example, if name is \\\"foo\\\", the variable will be available as `variables.foo`\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"expression\",\n              \"name\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"deny\": {\n          \"description\": \"Deny defines conditions used to pass or fail a validation rule.\",\n          \"properties\": {\n           \"conditions\": {\n            \"description\": \"Multiple conditions can be declared under an `any` or `all` statement. A direct list\\nof conditions (without `any` or `all` statements) is also supported for backwards compatibility\\nbut will be deprecated in the next major release.\\nSee: https://kyverno.io/docs/writing-policies/validate/#deny-rules\",\n            \"format\": \"textarea\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureAction\": {\n          \"description\": \"FailureAction defines if a validation policy rule violation should block\\nthe admission review request (Enforce), or allow (Audit) the admission review request\\nand report an error in a policy report. Optional.\\nAllowed values are Audit or Enforce.\",\n          \"enum\": [\n           \"Audit\",\n           \"Enforce\"\n          ],\n          \"type\": \"string\"\n         },\n         \"failureActionOverrides\": {\n          \"description\": \"FailureActionOverrides is a Cluster Policy attribute that specifies FailureAction\\nnamespace-wise. It overrides FailureAction for the specified namespaces.\",\n          \"items\": {\n           \"properties\": {\n            \"action\": {\n             \"description\": \"ValidationFailureAction defines the policy validation failure action\",\n             \"enum\": [\n              \"audit\",\n              \"enforce\",\n              \"Audit\",\n              \"Enforce\"\n             ],\n             \"type\": \"string\"\n            },\n            \"namespaceSelector\": {\n             \"description\": \"A label selector is a label query over a set of resources. The result of matchLabels and\\nmatchExpressions are ANDed. An empty label selector matches all objects. A null\\nlabel selector matches no objects.\",\n             \"properties\": {\n              \"matchExpressions\": {\n               \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n               \"items\": {\n                \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"key is the label key that the selector applies to.\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                  \"type\": \"string\"\n                 },\n                 \"values\": {\n                  \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"operator\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"matchLabels\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"namespaces\": {\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"foreach\": {\n          \"description\": \"ForEach applies validate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.\",\n          \"items\": {\n           \"description\": \"ForEachValidation applies validate rules to a list of sub-elements by creating a context for each entry in the list and looping over it to apply the specified logic.\",\n           \"properties\": {\n            \"anyPattern\": {\n             \"description\": \"AnyPattern specifies list of validation patterns. At least one of the patterns\\nmust be satisfied for the validation rule to succeed.\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"context\": {\n             \"description\": \"Context defines variables and data sources that can be used during rule execution.\",\n             \"items\": {\n              \"description\": \"ContextEntry adds variables and data sources to a rule Context. Either a\\nConfigMap reference or a APILookup must be provided.\",\n              \"oneOf\": [\n               {\n                \"required\": [\n                 \"configMap\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"apiCall\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"imageRegistry\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"variable\"\n                ]\n               },\n               {\n                \"required\": [\n                 \"globalReference\"\n                ]\n               }\n              ],\n              \"properties\": {\n               \"apiCall\": {\n                \"description\": \"APICall is an HTTP request to the Kubernetes API server, or other JSON web service.\\nThe data returned is stored in the context with the name for the context entry.\",\n                \"properties\": {\n                 \"data\": {\n                  \"description\": \"The data object specifies the POST data sent to the server.\\nOnly applicable when the method field is set to POST.\",\n                  \"items\": {\n                   \"description\": \"RequestData contains the HTTP POST data\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"Key is a unique identifier for the data value\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"Value is the data value\",\n                     \"format\": \"textarea\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"default\": {\n                  \"description\": \"Default is an optional arbitrary JSON object that the context\\nvalue is set to, if the apiCall returns error.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n                  \"type\": \"string\"\n                 },\n                 \"method\": {\n                  \"default\": \"GET\",\n                  \"description\": \"Method is the HTTP request type (GET or POST). Defaults to GET.\",\n                  \"enum\": [\n                   \"GET\",\n                   \"POST\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"service\": {\n                  \"description\": \"Service is an API call to a JSON web service.\\nThis is used for non-Kubernetes API server calls.\\nIt's mutually exclusive with the URLPath field.\",\n                  \"properties\": {\n                   \"caBundle\": {\n                    \"description\": \"CABundle is a PEM encoded CA bundle which will be used to validate\\nthe server certificate.\",\n                    \"type\": \"string\"\n                   },\n                   \"headers\": {\n                    \"description\": \"Headers is a list of optional HTTP headers to be included in the request.\",\n                    \"items\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"Key is the header key\",\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"description\": \"Value is the header value\",\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"url\": {\n                    \"description\": \"URL is the JSON web service URL. A typical form is\\n`https://{service}.{namespace}:{port}/{path}`.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"url\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"urlPath\": {\n                  \"description\": \"URLPath is the URL path to be used in the HTTP GET or POST request to the\\nKubernetes API server (e.g. \\\"/api/v1/namespaces\\\" or  \\\"/apis/apps/v1/deployments\\\").\\nThe format required is the same format used by the `kubectl get --raw` command.\\nSee https://kyverno.io/docs/writing-policies/external-data-sources/#variables-from-kubernetes-api-server-calls\\nfor details.\\nIt's mutually exclusive with the Service field.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"configMap\": {\n                \"description\": \"ConfigMap is the ConfigMap reference.\",\n                \"properties\": {\n                 \"name\": {\n                  \"description\": \"Name is the ConfigMap name.\",\n                  \"type\": \"string\"\n                 },\n                 \"namespace\": {\n                  \"description\": \"Namespace is the ConfigMap namespace.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"globalReference\": {\n                \"description\": \"GlobalContextEntryReference is a reference to a cached global context entry.\",\n                \"properties\": {\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the JSON response returned from the server. For example\\na JMESPath of \\\"items | length(@)\\\" applied to the API server response\\nfor the URLPath \\\"/apis/apps/v1/deployments\\\" will return the total count\\nof deployments across all namespaces.\",\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"description\": \"Name of the global context entry\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"imageRegistry\": {\n                \"description\": \"ImageRegistry defines requests to an OCI/Docker V2 registry to fetch image\\ndetails.\",\n                \"properties\": {\n                 \"imageRegistryCredentials\": {\n                  \"description\": \"ImageRegistryCredentials provides credentials that will be used for authentication with registry\",\n                  \"properties\": {\n                   \"allowInsecureRegistry\": {\n                    \"description\": \"AllowInsecureRegistry allows insecure access to a registry.\",\n                    \"type\": \"boolean\"\n                   },\n                   \"providers\": {\n                    \"description\": \"Providers specifies a list of OCI Registry names, whose authentication providers are provided.\\nIt can be of one of these values: default,google,azure,amazon,github.\",\n                    \"items\": {\n                     \"description\": \"ImageRegistryCredentialsProvidersType provides the list of credential providers required.\",\n                     \"enum\": [\n                      \"default\",\n                      \"amazon\",\n                      \"azure\",\n                      \"google\",\n                      \"github\"\n                     ],\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"secrets\": {\n                    \"description\": \"Secrets specifies a list of secrets that are provided for credentials.\\nSecrets must live in the Kyverno namespace.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JSON Match Expression that can be used to\\ntransform the ImageData struct returned as a result of processing\\nthe image reference.\",\n                  \"type\": \"string\"\n                 },\n                 \"reference\": {\n                  \"description\": \"Reference is image reference to a container image in the registry.\\nExample: ghcr.io/kyverno/kyverno:latest\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"reference\"\n                ],\n                \"type\": \"object\"\n               },\n               \"name\": {\n                \"description\": \"Name is the variable name.\",\n                \"type\": \"string\"\n               },\n               \"variable\": {\n                \"description\": \"Variable defines an arbitrary JMESPath context variable that can be defined inline.\",\n                \"properties\": {\n                 \"default\": {\n                  \"description\": \"Default is an optional arbitrary JSON object that the variable may take if the JMESPath\\nexpression evaluates to nil\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"jmesPath\": {\n                  \"description\": \"JMESPath is an optional JMESPath Expression that can be used to\\ntransform the variable.\",\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is any arbitrary JSON object representable in YAML or JSON form.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"deny\": {\n             \"description\": \"Deny defines conditions used to pass or fail a validation rule.\",\n             \"properties\": {\n              \"conditions\": {\n               \"description\": \"Multiple conditions can be declared under an `any` or `all` statement. A direct list\\nof conditions (without `any` or `all` statements) is also supported for backwards compatibility\\nbut will be deprecated in the next major release.\\nSee: https://kyverno.io/docs/writing-policies/validate/#deny-rules\",\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"elementScope\": {\n             \"description\": \"ElementScope specifies whether to use the current list element as the scope for validation. Defaults to \\\"true\\\" if not specified.\\nWhen set to \\\"false\\\", \\\"request.object\\\" is used as the validation scope within the foreach\\nblock to allow referencing other elements in the subtree.\",\n             \"type\": \"boolean\"\n            },\n            \"foreach\": {\n             \"description\": \"Foreach declares a nested foreach iterator\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"list\": {\n             \"description\": \"List specifies a JMESPath expression that results in one or more elements\\nto which the validation logic is applied.\",\n             \"type\": \"string\"\n            },\n            \"pattern\": {\n             \"description\": \"Pattern specifies an overlay-style pattern used to check resources.\",\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"preconditions\": {\n             \"description\": \"AnyAllConditions are used to determine if a policy rule should be applied by evaluating a\\nset of conditions. The declaration can contain nested `any` or `all` statements.\\nSee: https://kyverno.io/docs/writing-policies/preconditions/\",\n             \"format\": \"textarea\",\n             \"properties\": {\n              \"all\": {\n               \"description\": \"AllConditions enable variable-based conditional rule execution. This is useful for\\nfiner control of when an rule is applied. A condition can reference object data\\nusing JMESPath notation.\\nHere, all of the conditions need to pass\",\n               \"items\": {\n                \"description\": \"Condition defines variable-based conditional criteria for rule execution.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"Key is the context entry (using JMESPath) for conditional rule evaluation.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"message\": {\n                  \"description\": \"Message is an optional display message\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"Operator is the conditional operation to perform. Valid operators are:\\nEquals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,\\nGreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,\\nDurationLessThanOrEquals, DurationLessThan\",\n                  \"enum\": [\n                   \"Equals\",\n                   \"NotEquals\",\n                   \"In\",\n                   \"AnyIn\",\n                   \"AllIn\",\n                   \"NotIn\",\n                   \"AnyNotIn\",\n                   \"AllNotIn\",\n                   \"GreaterThanOrEquals\",\n                   \"GreaterThan\",\n                   \"LessThanOrEquals\",\n                   \"LessThan\",\n                   \"DurationGreaterThanOrEquals\",\n                   \"DurationGreaterThan\",\n                   \"DurationLessThanOrEquals\",\n                   \"DurationLessThan\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is the conditional value, or set of values. The values can be fixed set\\nor can be variables declared using JMESPath.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"any\": {\n               \"description\": \"AnyConditions enable variable-based conditional rule execution. This is useful for\\nfiner control of when an rule is applied. A condition can reference object data\\nusing JMESPath notation.\\nHere, at least one of the conditions need to pass\",\n               \"items\": {\n                \"description\": \"Condition defines variable-based conditional criteria for rule execution.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"Key is the context entry (using JMESPath) for conditional rule evaluation.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"message\": {\n                  \"description\": \"Message is an optional display message\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"Operator is the conditional operation to perform. Valid operators are:\\nEquals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,\\nGreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,\\nDurationLessThanOrEquals, DurationLessThan\",\n                  \"enum\": [\n                   \"Equals\",\n                   \"NotEquals\",\n                   \"In\",\n                   \"AnyIn\",\n                   \"AllIn\",\n                   \"NotIn\",\n                   \"AnyNotIn\",\n                   \"AllNotIn\",\n                   \"GreaterThanOrEquals\",\n                   \"GreaterThan\",\n                   \"LessThanOrEquals\",\n                   \"LessThan\",\n                   \"DurationGreaterThanOrEquals\",\n                   \"DurationGreaterThan\",\n                   \"DurationLessThanOrEquals\",\n                   \"DurationLessThan\"\n                  ],\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"description\": \"Value is the conditional value, or set of values. The values can be fixed set\\nor can be variables declared using JMESPath.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"manifests\": {\n          \"description\": \"Manifest specifies conditions for manifest verification\",\n          \"properties\": {\n           \"annotationDomain\": {\n            \"description\": \"AnnotationDomain is custom domain of annotation for message and signature. Default is \\\"cosign.sigstore.dev\\\".\",\n            \"type\": \"string\"\n           },\n           \"attestors\": {\n            \"description\": \"Attestors specified the required attestors (i.e. authorities)\",\n            \"items\": {\n             \"properties\": {\n              \"count\": {\n               \"description\": \"Count specifies the required number of entries that must match. If the count is null, all entries must match\\n(a logical AND). If the count is 1, at least one entry must match (a logical OR). If the count contains a\\nvalue N, then N must be less than or equal to the size of entries, and at least N entries must match.\",\n               \"minimum\": 1,\n               \"type\": \"integer\"\n              },\n              \"entries\": {\n               \"description\": \"Entries contains the available attestors. An attestor can be a static key,\\nattributes for keyless verification, or a nested attestor declaration.\",\n               \"items\": {\n                \"properties\": {\n                 \"annotations\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"description\": \"Annotations are used for image verification.\\nEvery specified key-value pair must exist and match in the verified payload.\\nThe payload may contain other key-value pairs.\",\n                  \"type\": \"object\"\n                 },\n                 \"attestor\": {\n                  \"description\": \"Attestor is a nested set of Attestor used to specify a more complex set of match authorities.\",\n                  \"format\": \"textarea\",\n                  \"type\": \"string\"\n                 },\n                 \"certificates\": {\n                  \"description\": \"Certificates specifies one or more certificates.\",\n                  \"properties\": {\n                   \"cert\": {\n                    \"description\": \"Cert is an optional PEM-encoded public certificate.\",\n                    \"type\": \"string\"\n                   },\n                   \"certChain\": {\n                    \"description\": \"CertChain is an optional PEM encoded set of certificates used to verify.\",\n                    \"type\": \"string\"\n                   },\n                   \"ctlog\": {\n                    \"description\": \"CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate\\nTimestamps (SCTs). If the value is unset, the default behavior by Cosign is used.\",\n                    \"properties\": {\n                     \"ignoreSCT\": {\n                      \"description\": \"IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate\\ntimestamp. Default is false. Set to true if this was opted out during signing.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"pubkey\": {\n                      \"description\": \"PubKey, if set, is used to validate SCTs against a custom source.\",\n                      \"type\": \"string\"\n                     },\n                     \"tsaCertChain\": {\n                      \"description\": \"TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must\\ncontain the root CA certificate. Optionally may contain intermediate CA certificates, and\\nmay contain the leaf TSA certificate if not present in the timestamurce.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"rekor\": {\n                    \"description\": \"Rekor provides configuration for the Rekor transparency log service. If an empty object\\nis provided the public instance of Rekor (https://rekor.sigstore.dev) is used.\",\n                    \"properties\": {\n                     \"ignoreTlog\": {\n                      \"description\": \"IgnoreTlog skips transparency log verification.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"pubkey\": {\n                      \"description\": \"RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.\\nIf set, this will be used to validate transparency log signatures from a custom Rekor.\",\n                      \"type\": \"string\"\n                     },\n                     \"url\": {\n                      \"description\": \"URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"keyless\": {\n                  \"description\": \"Keyless is a set of attribute used to verify a Sigstore keyless attestor.\\nSee https://github.com/sigstore/cosign/blob/main/KEYLESS.md.\",\n                  \"properties\": {\n                   \"additionalExtensions\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"AdditionalExtensions are certificate-extensions used for keyless signing.\",\n                    \"type\": \"object\"\n                   },\n                   \"ctlog\": {\n                    \"description\": \"CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate\\nTimestamps (SCTs). If the value is unset, the default behavior by Cosign is used.\",\n                    \"properties\": {\n                     \"ignoreSCT\": {\n                      \"description\": \"IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate\\ntimestamp. Default is false. Set to true if this was opted out during signing.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"pubkey\": {\n                      \"description\": \"PubKey, if set, is used to validate SCTs against a custom source.\",\n                      \"type\": \"string\"\n                     },\n                     \"tsaCertChain\": {\n                      \"description\": \"TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must\\ncontain the root CA certificate. Optionally may contain intermediate CA certificates, and\\nmay contain the leaf TSA certificate if not present in the timestamurce.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"issuer\": {\n                    \"description\": \"Issuer is the certificate issuer used for keyless signing.\",\n                    \"type\": \"string\"\n                   },\n                   \"issuerRegExp\": {\n                    \"description\": \"IssuerRegExp is the regular expression to match certificate issuer used for keyless signing.\",\n                    \"type\": \"string\"\n                   },\n                   \"rekor\": {\n                    \"description\": \"Rekor provides configuration for the Rekor transparency log service. If an empty object\\nis provided the public instance of Rekor (https://rekor.sigstore.dev) is used.\",\n                    \"properties\": {\n                     \"ignoreTlog\": {\n                      \"description\": \"IgnoreTlog skips transparency log verification.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"pubkey\": {\n                      \"description\": \"RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.\\nIf set, this will be used to validate transparency log signatures from a custom Rekor.\",\n                      \"type\": \"string\"\n                     },\n                     \"url\": {\n                      \"description\": \"URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"roots\": {\n                    \"description\": \"Roots is an optional set of PEM encoded trusted root certificates.\\nIf not provided, the system roots are used.\",\n                    \"type\": \"string\"\n                   },\n                   \"subject\": {\n                    \"description\": \"Subject is the verified identity used for keyless signing, for example the email address.\",\n                    \"type\": \"string\"\n                   },\n                   \"subjectRegExp\": {\n                    \"description\": \"SubjectRegExp is the regular expression to match identity used for keyless signing, for example the email address.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"keys\": {\n                  \"description\": \"Keys specifies one or more public keys.\",\n                  \"properties\": {\n                   \"ctlog\": {\n                    \"description\": \"CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate\\nTimestamps (SCTs). If the value is unset, the default behavior by Cosign is used.\",\n                    \"properties\": {\n                     \"ignoreSCT\": {\n                      \"description\": \"IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate\\ntimestamp. Default is false. Set to true if this was opted out during signing.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"pubkey\": {\n                      \"description\": \"PubKey, if set, is used to validate SCTs against a custom source.\",\n                      \"type\": \"string\"\n                     },\n                     \"tsaCertChain\": {\n                      \"description\": \"TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must\\ncontain the root CA certificate. Optionally may contain intermediate CA certificates, and\\nmay contain the leaf TSA certificate if not present in the timestamurce.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"kms\": {\n                    \"description\": \"KMS provides the URI to the public key stored in a Key Management System. See:\\nhttps://github.com/sigstore/cosign/blob/main/KMS.md\",\n                    \"type\": \"string\"\n                   },\n                   \"publicKeys\": {\n                    \"description\": \"Keys is a set of X.509 public keys used to verify image signatures. The keys can be directly\\nspecified or can be a variable reference to a key specified in a ConfigMap (see\\nhttps://kyverno.io/docs/writing-policies/variables/), or reference a standard Kubernetes Secret\\nelsewhere in the cluster by specifying it in the format \\\"k8s://\\u003cnamespace\\u003e/\\u003csecret_name\\u003e\\\".\\nThe named Secret must specify a key `cosign.pub` containing the public key used for\\nverification, (see https://github.com/sigstore/cosign/blob/main/KMS.md#kubernetes-secret).\\nWhen multiple keys are specified each key is processed as a separate staticKey entry\\n(.attestors[*].entries.keys) within the set of attestors and the count is applied across the keys.\",\n                    \"type\": \"string\"\n                   },\n                   \"rekor\": {\n                    \"description\": \"Rekor provides configuration for the Rekor transparency log service. If an empty object\\nis provided the public instance of Rekor (https://rekor.sigstore.dev) is used.\",\n                    \"properties\": {\n                     \"ignoreTlog\": {\n                      \"description\": \"IgnoreTlog skips transparency log verification.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"pubkey\": {\n                      \"description\": \"RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.\\nIf set, this will be used to validate transparency log signatures from a custom Rekor.\",\n                      \"type\": \"string\"\n                     },\n                     \"url\": {\n                      \"description\": \"URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"secret\": {\n                    \"description\": \"Reference to a Secret resource that contains a public key\",\n                    \"properties\": {\n                     \"name\": {\n                      \"description\": \"Name of the secret. The provided secret must contain a key named cosign.pub.\",\n                      \"type\": \"string\"\n                     },\n                     \"namespace\": {\n                      \"description\": \"Namespace name where the Secret exists.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"name\",\n                     \"namespace\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"signatureAlgorithm\": {\n                    \"default\": \"sha256\",\n                    \"description\": \"Deprecated. Use attestor.signatureAlgorithm instead.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"repository\": {\n                  \"description\": \"Repository is an optional alternate OCI repository to use for signatures and attestations that match this rule.\\nIf specified Repository will override other OCI image repository locations for this Attestor.\",\n                  \"type\": \"string\"\n                 },\n                 \"signatureAlgorithm\": {\n                  \"default\": \"sha256\",\n                  \"description\": \"Specify signature algorithm for public keys. Supported values are sha224, sha256, sha384 and sha512.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"dryRun\": {\n            \"description\": \"DryRun configuration\",\n            \"properties\": {\n             \"enable\": {\n              \"type\": \"boolean\"\n             },\n             \"namespace\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"ignoreFields\": {\n            \"description\": \"Fields which will be ignored while comparing manifests.\",\n            \"items\": {\n             \"properties\": {\n              \"fields\": {\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"objects\": {\n               \"items\": {\n                \"properties\": {\n                 \"group\": {\n                  \"type\": \"string\"\n                 },\n                 \"kind\": {\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"type\": \"string\"\n                 },\n                 \"namespace\": {\n                  \"type\": \"string\"\n                 },\n                 \"version\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"repository\": {\n            \"description\": \"Repository is an optional alternate OCI repository to use for resource bundle reference.\\nThe repository can be overridden per Attestor or Attestation.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"message\": {\n          \"description\": \"Message specifies a custom message to be displayed on failure.\",\n          \"type\": \"string\"\n         },\n         \"pattern\": {\n          \"description\": \"Pattern specifies an overlay-style pattern used to check resources.\",\n          \"format\": \"textarea\",\n          \"type\": \"string\"\n         },\n         \"podSecurity\": {\n          \"description\": \"PodSecurity applies exemptions for Kubernetes Pod Security admission\\nby specifying exclusions for Pod Security Standards controls.\",\n          \"properties\": {\n           \"exclude\": {\n            \"description\": \"Exclude specifies the Pod Security Standard controls to be excluded.\",\n            \"items\": {\n             \"description\": \"PodSecurityStandard specifies the Pod Security Standard controls to be excluded.\",\n             \"properties\": {\n              \"controlName\": {\n               \"description\": \"ControlName specifies the name of the Pod Security Standard control.\\nSee: https://kubernetes.io/docs/concepts/security/pod-security-standards/\",\n               \"enum\": [\n                \"HostProcess\",\n                \"Host Namespaces\",\n                \"Privileged Containers\",\n                \"Capabilities\",\n                \"HostPath Volumes\",\n                \"Host Ports\",\n                \"AppArmor\",\n                \"SELinux\",\n                \"/proc Mount Type\",\n                \"Seccomp\",\n                \"Sysctls\",\n                \"Volume Types\",\n                \"Privilege Escalation\",\n                \"Running as Non-root\",\n                \"Running as Non-root user\"\n               ],\n               \"type\": \"string\"\n              },\n              \"images\": {\n               \"description\": \"Images selects matching containers and applies the container level PSS.\\nEach image is the image name consisting of the registry address, repository, image, and tag.\\nEmpty list matches no containers, PSS checks are applied at the pod level only.\\nWildcards ('*' and '?') are allowed. See: https://kubernetes.io/docs/concepts/containers/images.\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              },\n              \"restrictedField\": {\n               \"description\": \"RestrictedField selects the field for the given Pod Security Standard control.\\nWhen not set, all restricted fields for the control are selected.\",\n               \"type\": \"string\"\n              },\n              \"values\": {\n               \"description\": \"Values defines the allowed values that can be excluded.\",\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"required\": [\n              \"controlName\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"level\": {\n            \"description\": \"Level defines the Pod Security Standard level to be applied to workloads.\\nAllowed values are privileged, baseline, and restricted.\",\n            \"enum\": [\n             \"privileged\",\n             \"baseline\",\n             \"restricted\"\n            ],\n            \"type\": \"string\"\n           },\n           \"version\": {\n            \"description\": \"Version defines the Pod Security Standard versions that Kubernetes supports.\\nAllowed values are v1.19, v1.20, v1.21, v1.22, v1.23, v1.24, v1.25, v1.26, v1.27, v1.28, v1.29, latest. Defaults to latest.\",\n            \"enum\": [\n             \"v1.19\",\n             \"v1.20\",\n             \"v1.21\",\n             \"v1.22\",\n             \"v1.23\",\n             \"v1.24\",\n             \"v1.25\",\n             \"v1.26\",\n             \"v1.27\",\n             \"v1.28\",\n             \"v1.29\",\n             \"latest\"\n            ],\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"verifyImages\": {\n        \"description\": \"VerifyImages is used to verify image signatures and mutate them to add a digest\",\n        \"items\": {\n         \"description\": \"ImageVerification validates that images that match the specified pattern\\nare signed with the supplied public key. Once the image is verified it is\\nmutated to include the SHA digest retrieved during the registration.\",\n         \"properties\": {\n          \"additionalExtensions\": {\n           \"additionalProperties\": {\n            \"type\": \"string\"\n           },\n           \"description\": \"Deprecated.\",\n           \"type\": \"object\"\n          },\n          \"annotations\": {\n           \"additionalProperties\": {\n            \"type\": \"string\"\n           },\n           \"description\": \"Deprecated. Use annotations per Attestor instead.\",\n           \"type\": \"object\"\n          },\n          \"attestations\": {\n           \"description\": \"Attestations are optional checks for signed in-toto Statements used to verify the image.\\nSee https://github.com/in-toto/attestation. Kyverno fetches signed attestations from the\\nOCI registry and decodes them into a list of Statement declarations.\",\n           \"items\": {\n            \"description\": \"Attestation are checks for signed in-toto Statements that are used to verify the image.\\nSee https://github.com/in-toto/attestation. Kyverno fetches signed attestations from the\\nOCI registry and decodes them into a list of Statements.\",\n            \"properties\": {\n             \"attestors\": {\n              \"description\": \"Attestors specify the required attestors (i.e. authorities).\",\n              \"items\": {\n               \"properties\": {\n                \"count\": {\n                 \"description\": \"Count specifies the required number of entries that must match. If the count is null, all entries must match\\n(a logical AND). If the count is 1, at least one entry must match (a logical OR). If the count contains a\\nvalue N, then N must be less than or equal to the size of entries, and at least N entries must match.\",\n                 \"minimum\": 1,\n                 \"type\": \"integer\"\n                },\n                \"entries\": {\n                 \"description\": \"Entries contains the available attestors. An attestor can be a static key,\\nattributes for keyless verification, or a nested attestor declaration.\",\n                 \"items\": {\n                  \"properties\": {\n                   \"annotations\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"Annotations are used for image verification.\\nEvery specified key-value pair must exist and match in the verified payload.\\nThe payload may contain other key-value pairs.\",\n                    \"type\": \"object\"\n                   },\n                   \"attestor\": {\n                    \"description\": \"Attestor is a nested set of Attestor used to specify a more complex set of match authorities.\",\n                    \"format\": \"textarea\",\n                    \"type\": \"string\"\n                   },\n                   \"certificates\": {\n                    \"description\": \"Certificates specifies one or more certificates.\",\n                    \"properties\": {\n                     \"cert\": {\n                      \"description\": \"Cert is an optional PEM-encoded public certificate.\",\n                      \"type\": \"string\"\n                     },\n                     \"certChain\": {\n                      \"description\": \"CertChain is an optional PEM encoded set of certificates used to verify.\",\n                      \"type\": \"string\"\n                     },\n                     \"ctlog\": {\n                      \"description\": \"CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate\\nTimestamps (SCTs). If the value is unset, the default behavior by Cosign is used.\",\n                      \"properties\": {\n                       \"ignoreSCT\": {\n                        \"description\": \"IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate\\ntimestamp. Default is false. Set to true if this was opted out during signing.\",\n                        \"type\": \"boolean\"\n                       },\n                       \"pubkey\": {\n                        \"description\": \"PubKey, if set, is used to validate SCTs against a custom source.\",\n                        \"type\": \"string\"\n                       },\n                       \"tsaCertChain\": {\n                        \"description\": \"TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must\\ncontain the root CA certificate. Optionally may contain intermediate CA certificates, and\\nmay contain the leaf TSA certificate if not present in the timestamurce.\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"rekor\": {\n                      \"description\": \"Rekor provides configuration for the Rekor transparency log service. If an empty object\\nis provided the public instance of Rekor (https://rekor.sigstore.dev) is used.\",\n                      \"properties\": {\n                       \"ignoreTlog\": {\n                        \"description\": \"IgnoreTlog skips transparency log verification.\",\n                        \"type\": \"boolean\"\n                       },\n                       \"pubkey\": {\n                        \"description\": \"RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.\\nIf set, this will be used to validate transparency log signatures from a custom Rekor.\",\n                        \"type\": \"string\"\n                       },\n                       \"url\": {\n                        \"description\": \"URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"keyless\": {\n                    \"description\": \"Keyless is a set of attribute used to verify a Sigstore keyless attestor.\\nSee https://github.com/sigstore/cosign/blob/main/KEYLESS.md.\",\n                    \"properties\": {\n                     \"additionalExtensions\": {\n                      \"additionalProperties\": {\n                       \"type\": \"string\"\n                      },\n                      \"description\": \"AdditionalExtensions are certificate-extensions used for keyless signing.\",\n                      \"type\": \"object\"\n                     },\n                     \"ctlog\": {\n                      \"description\": \"CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate\\nTimestamps (SCTs). If the value is unset, the default behavior by Cosign is used.\",\n                      \"properties\": {\n                       \"ignoreSCT\": {\n                        \"description\": \"IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate\\ntimestamp. Default is false. Set to true if this was opted out during signing.\",\n                        \"type\": \"boolean\"\n                       },\n                       \"pubkey\": {\n                        \"description\": \"PubKey, if set, is used to validate SCTs against a custom source.\",\n                        \"type\": \"string\"\n                       },\n                       \"tsaCertChain\": {\n                        \"description\": \"TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must\\ncontain the root CA certificate. Optionally may contain intermediate CA certificates, and\\nmay contain the leaf TSA certificate if not present in the timestamurce.\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"issuer\": {\n                      \"description\": \"Issuer is the certificate issuer used for keyless signing.\",\n                      \"type\": \"string\"\n                     },\n                     \"issuerRegExp\": {\n                      \"description\": \"IssuerRegExp is the regular expression to match certificate issuer used for keyless signing.\",\n                      \"type\": \"string\"\n                     },\n                     \"rekor\": {\n                      \"description\": \"Rekor provides configuration for the Rekor transparency log service. If an empty object\\nis provided the public instance of Rekor (https://rekor.sigstore.dev) is used.\",\n                      \"properties\": {\n                       \"ignoreTlog\": {\n                        \"description\": \"IgnoreTlog skips transparency log verification.\",\n                        \"type\": \"boolean\"\n                       },\n                       \"pubkey\": {\n                        \"description\": \"RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.\\nIf set, this will be used to validate transparency log signatures from a custom Rekor.\",\n                        \"type\": \"string\"\n                       },\n                       \"url\": {\n                        \"description\": \"URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"roots\": {\n                      \"description\": \"Roots is an optional set of PEM encoded trusted root certificates.\\nIf not provided, the system roots are used.\",\n                      \"type\": \"string\"\n                     },\n                     \"subject\": {\n                      \"description\": \"Subject is the verified identity used for keyless signing, for example the email address.\",\n                      \"type\": \"string\"\n                     },\n                     \"subjectRegExp\": {\n                      \"description\": \"SubjectRegExp is the regular expression to match identity used for keyless signing, for example the email address.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"keys\": {\n                    \"description\": \"Keys specifies one or more public keys.\",\n                    \"properties\": {\n                     \"ctlog\": {\n                      \"description\": \"CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate\\nTimestamps (SCTs). If the value is unset, the default behavior by Cosign is used.\",\n                      \"properties\": {\n                       \"ignoreSCT\": {\n                        \"description\": \"IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate\\ntimestamp. Default is false. Set to true if this was opted out during signing.\",\n                        \"type\": \"boolean\"\n                       },\n                       \"pubkey\": {\n                        \"description\": \"PubKey, if set, is used to validate SCTs against a custom source.\",\n                        \"type\": \"string\"\n                       },\n                       \"tsaCertChain\": {\n                        \"description\": \"TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must\\ncontain the root CA certificate. Optionally may contain intermediate CA certificates, and\\nmay contain the leaf TSA certificate if not present in the timestamurce.\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"kms\": {\n                      \"description\": \"KMS provides the URI to the public key stored in a Key Management System. See:\\nhttps://github.com/sigstore/cosign/blob/main/KMS.md\",\n                      \"type\": \"string\"\n                     },\n                     \"publicKeys\": {\n                      \"description\": \"Keys is a set of X.509 public keys used to verify image signatures. The keys can be directly\\nspecified or can be a variable reference to a key specified in a ConfigMap (see\\nhttps://kyverno.io/docs/writing-policies/variables/), or reference a standard Kubernetes Secret\\nelsewhere in the cluster by specifying it in the format \\\"k8s://\\u003cnamespace\\u003e/\\u003csecret_name\\u003e\\\".\\nThe named Secret must specify a key `cosign.pub` containing the public key used for\\nverification, (see https://github.com/sigstore/cosign/blob/main/KMS.md#kubernetes-secret).\\nWhen multiple keys are specified each key is processed as a separate staticKey entry\\n(.attestors[*].entries.keys) within the set of attestors and the count is applied across the keys.\",\n                      \"type\": \"string\"\n                     },\n                     \"rekor\": {\n                      \"description\": \"Rekor provides configuration for the Rekor transparency log service. If an empty object\\nis provided the public instance of Rekor (https://rekor.sigstore.dev) is used.\",\n                      \"properties\": {\n                       \"ignoreTlog\": {\n                        \"description\": \"IgnoreTlog skips transparency log verification.\",\n                        \"type\": \"boolean\"\n                       },\n                       \"pubkey\": {\n                        \"description\": \"RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.\\nIf set, this will be used to validate transparency log signatures from a custom Rekor.\",\n                        \"type\": \"string\"\n                       },\n                       \"url\": {\n                        \"description\": \"URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"secret\": {\n                      \"description\": \"Reference to a Secret resource that contains a public key\",\n                      \"properties\": {\n                       \"name\": {\n                        \"description\": \"Name of the secret. The provided secret must contain a key named cosign.pub.\",\n                        \"type\": \"string\"\n                       },\n                       \"namespace\": {\n                        \"description\": \"Namespace name where the Secret exists.\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"required\": [\n                       \"name\",\n                       \"namespace\"\n                      ],\n                      \"type\": \"object\"\n                     },\n                     \"signatureAlgorithm\": {\n                      \"default\": \"sha256\",\n                      \"description\": \"Deprecated. Use attestor.signatureAlgorithm instead.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"repository\": {\n                    \"description\": \"Repository is an optional alternate OCI repository to use for signatures and attestations that match this rule.\\nIf specified Repository will override other OCI image repository locations for this Attestor.\",\n                    \"type\": \"string\"\n                   },\n                   \"signatureAlgorithm\": {\n                    \"default\": \"sha256\",\n                    \"description\": \"Specify signature algorithm for public keys. Supported values are sha224, sha256, sha384 and sha512.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"conditions\": {\n              \"description\": \"Conditions are used to verify attributes within a Predicate. If no Conditions are specified\\nthe attestation check is satisfied as long there are predicates that match the predicate type.\",\n              \"items\": {\n               \"description\": \"AnyAllConditions consists of conditions wrapped denoting a logical criteria to be fulfilled.\\nAnyConditions get fulfilled when at least one of its sub-conditions passes.\\nAllConditions get fulfilled only when all of its sub-conditions pass.\",\n               \"properties\": {\n                \"all\": {\n                 \"description\": \"AllConditions enable variable-based conditional rule execution. This is useful for\\nfiner control of when an rule is applied. A condition can reference object data\\nusing JMESPath notation.\\nHere, all of the conditions need to pass\",\n                 \"items\": {\n                  \"description\": \"Condition defines variable-based conditional criteria for rule execution.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"Key is the context entry (using JMESPath) for conditional rule evaluation.\",\n                    \"format\": \"textarea\",\n                    \"type\": \"string\"\n                   },\n                   \"message\": {\n                    \"description\": \"Message is an optional display message\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"Operator is the conditional operation to perform. Valid operators are:\\nEquals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,\\nGreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,\\nDurationLessThanOrEquals, DurationLessThan\",\n                    \"enum\": [\n                     \"Equals\",\n                     \"NotEquals\",\n                     \"In\",\n                     \"AnyIn\",\n                     \"AllIn\",\n                     \"NotIn\",\n                     \"AnyNotIn\",\n                     \"AllNotIn\",\n                     \"GreaterThanOrEquals\",\n                     \"GreaterThan\",\n                     \"LessThanOrEquals\",\n                     \"LessThan\",\n                     \"DurationGreaterThanOrEquals\",\n                     \"DurationGreaterThan\",\n                     \"DurationLessThanOrEquals\",\n                     \"DurationLessThan\"\n                    ],\n                    \"type\": \"string\"\n                   },\n                   \"value\": {\n                    \"description\": \"Value is the conditional value, or set of values. The values can be fixed set\\nor can be variables declared using JMESPath.\",\n                    \"format\": \"textarea\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"any\": {\n                 \"description\": \"AnyConditions enable variable-based conditional rule execution. This is useful for\\nfiner control of when an rule is applied. A condition can reference object data\\nusing JMESPath notation.\\nHere, at least one of the conditions need to pass\",\n                 \"items\": {\n                  \"description\": \"Condition defines variable-based conditional criteria for rule execution.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"Key is the context entry (using JMESPath) for conditional rule evaluation.\",\n                    \"format\": \"textarea\",\n                    \"type\": \"string\"\n                   },\n                   \"message\": {\n                    \"description\": \"Message is an optional display message\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"Operator is the conditional operation to perform. Valid operators are:\\nEquals, NotEquals, In, AnyIn, AllIn, NotIn, AnyNotIn, AllNotIn, GreaterThanOrEquals,\\nGreaterThan, LessThanOrEquals, LessThan, DurationGreaterThanOrEquals, DurationGreaterThan,\\nDurationLessThanOrEquals, DurationLessThan\",\n                    \"enum\": [\n                     \"Equals\",\n                     \"NotEquals\",\n                     \"In\",\n                     \"AnyIn\",\n                     \"AllIn\",\n                     \"NotIn\",\n                     \"AnyNotIn\",\n                     \"AllNotIn\",\n                     \"GreaterThanOrEquals\",\n                     \"GreaterThan\",\n                     \"LessThanOrEquals\",\n                     \"LessThan\",\n                     \"DurationGreaterThanOrEquals\",\n                     \"DurationGreaterThan\",\n                     \"DurationLessThanOrEquals\",\n                     \"DurationLessThan\"\n                    ],\n                    \"type\": \"string\"\n                   },\n                   \"value\": {\n                    \"description\": \"Value is the conditional value, or set of values. The values can be fixed set\\nor can be variables declared using JMESPath.\",\n                    \"format\": \"textarea\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"name\": {\n              \"description\": \"Name is the variable name.\",\n              \"type\": \"string\"\n             },\n             \"predicateType\": {\n              \"description\": \"Deprecated in favour of 'Type', to be removed soon\",\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"description\": \"Type defines the type of attestation contained within the Statement.\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"type\": \"array\"\n          },\n          \"attestors\": {\n           \"description\": \"Attestors specified the required attestors (i.e. authorities)\",\n           \"items\": {\n            \"properties\": {\n             \"count\": {\n              \"description\": \"Count specifies the required number of entries that must match. If the count is null, all entries must match\\n(a logical AND). If the count is 1, at least one entry must match (a logical OR). If the count contains a\\nvalue N, then N must be less than or equal to the size of entries, and at least N entries must match.\",\n              \"minimum\": 1,\n              \"type\": \"integer\"\n             },\n             \"entries\": {\n              \"description\": \"Entries contains the available attestors. An attestor can be a static key,\\nattributes for keyless verification, or a nested attestor declaration.\",\n              \"items\": {\n               \"properties\": {\n                \"annotations\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"Annotations are used for image verification.\\nEvery specified key-value pair must exist and match in the verified payload.\\nThe payload may contain other key-value pairs.\",\n                 \"type\": \"object\"\n                },\n                \"attestor\": {\n                 \"description\": \"Attestor is a nested set of Attestor used to specify a more complex set of match authorities.\",\n                 \"format\": \"textarea\",\n                 \"type\": \"string\"\n                },\n                \"certificates\": {\n                 \"description\": \"Certificates specifies one or more certificates.\",\n                 \"properties\": {\n                  \"cert\": {\n                   \"description\": \"Cert is an optional PEM-encoded public certificate.\",\n                   \"type\": \"string\"\n                  },\n                  \"certChain\": {\n                   \"description\": \"CertChain is an optional PEM encoded set of certificates used to verify.\",\n                   \"type\": \"string\"\n                  },\n                  \"ctlog\": {\n                   \"description\": \"CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate\\nTimestamps (SCTs). If the value is unset, the default behavior by Cosign is used.\",\n                   \"properties\": {\n                    \"ignoreSCT\": {\n                     \"description\": \"IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate\\ntimestamp. Default is false. Set to true if this was opted out during signing.\",\n                     \"type\": \"boolean\"\n                    },\n                    \"pubkey\": {\n                     \"description\": \"PubKey, if set, is used to validate SCTs against a custom source.\",\n                     \"type\": \"string\"\n                    },\n                    \"tsaCertChain\": {\n                     \"description\": \"TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must\\ncontain the root CA certificate. Optionally may contain intermediate CA certificates, and\\nmay contain the leaf TSA certificate if not present in the timestamurce.\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  },\n                  \"rekor\": {\n                   \"description\": \"Rekor provides configuration for the Rekor transparency log service. If an empty object\\nis provided the public instance of Rekor (https://rekor.sigstore.dev) is used.\",\n                   \"properties\": {\n                    \"ignoreTlog\": {\n                     \"description\": \"IgnoreTlog skips transparency log verification.\",\n                     \"type\": \"boolean\"\n                    },\n                    \"pubkey\": {\n                     \"description\": \"RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.\\nIf set, this will be used to validate transparency log signatures from a custom Rekor.\",\n                     \"type\": \"string\"\n                    },\n                    \"url\": {\n                     \"description\": \"URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"keyless\": {\n                 \"description\": \"Keyless is a set of attribute used to verify a Sigstore keyless attestor.\\nSee https://github.com/sigstore/cosign/blob/main/KEYLESS.md.\",\n                 \"properties\": {\n                  \"additionalExtensions\": {\n                   \"additionalProperties\": {\n                    \"type\": \"string\"\n                   },\n                   \"description\": \"AdditionalExtensions are certificate-extensions used for keyless signing.\",\n                   \"type\": \"object\"\n                  },\n                  \"ctlog\": {\n                   \"description\": \"CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate\\nTimestamps (SCTs). If the value is unset, the default behavior by Cosign is used.\",\n                   \"properties\": {\n                    \"ignoreSCT\": {\n                     \"description\": \"IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate\\ntimestamp. Default is false. Set to true if this was opted out during signing.\",\n                     \"type\": \"boolean\"\n                    },\n                    \"pubkey\": {\n                     \"description\": \"PubKey, if set, is used to validate SCTs against a custom source.\",\n                     \"type\": \"string\"\n                    },\n                    \"tsaCertChain\": {\n                     \"description\": \"TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must\\ncontain the root CA certificate. Optionally may contain intermediate CA certificates, and\\nmay contain the leaf TSA certificate if not present in the timestamurce.\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  },\n                  \"issuer\": {\n                   \"description\": \"Issuer is the certificate issuer used for keyless signing.\",\n                   \"type\": \"string\"\n                  },\n                  \"issuerRegExp\": {\n                   \"description\": \"IssuerRegExp is the regular expression to match certificate issuer used for keyless signing.\",\n                   \"type\": \"string\"\n                  },\n                  \"rekor\": {\n                   \"description\": \"Rekor provides configuration for the Rekor transparency log service. If an empty object\\nis provided the public instance of Rekor (https://rekor.sigstore.dev) is used.\",\n                   \"properties\": {\n                    \"ignoreTlog\": {\n                     \"description\": \"IgnoreTlog skips transparency log verification.\",\n                     \"type\": \"boolean\"\n                    },\n                    \"pubkey\": {\n                     \"description\": \"RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.\\nIf set, this will be used to validate transparency log signatures from a custom Rekor.\",\n                     \"type\": \"string\"\n                    },\n                    \"url\": {\n                     \"description\": \"URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  },\n                  \"roots\": {\n                   \"description\": \"Roots is an optional set of PEM encoded trusted root certificates.\\nIf not provided, the system roots are used.\",\n                   \"type\": \"string\"\n                  },\n                  \"subject\": {\n                   \"description\": \"Subject is the verified identity used for keyless signing, for example the email address.\",\n                   \"type\": \"string\"\n                  },\n                  \"subjectRegExp\": {\n                   \"description\": \"SubjectRegExp is the regular expression to match identity used for keyless signing, for example the email address.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"keys\": {\n                 \"description\": \"Keys specifies one or more public keys.\",\n                 \"properties\": {\n                  \"ctlog\": {\n                   \"description\": \"CTLog (certificate timestamp log) provides a configuration for validation of Signed Certificate\\nTimestamps (SCTs). If the value is unset, the default behavior by Cosign is used.\",\n                   \"properties\": {\n                    \"ignoreSCT\": {\n                     \"description\": \"IgnoreSCT defines whether to use the Signed Certificate Timestamp (SCT) log to check for a certificate\\ntimestamp. Default is false. Set to true if this was opted out during signing.\",\n                     \"type\": \"boolean\"\n                    },\n                    \"pubkey\": {\n                     \"description\": \"PubKey, if set, is used to validate SCTs against a custom source.\",\n                     \"type\": \"string\"\n                    },\n                    \"tsaCertChain\": {\n                     \"description\": \"TSACertChain, if set, is the PEM-encoded certificate chain file for the RFC3161 timestamp authority. Must\\ncontain the root CA certificate. Optionally may contain intermediate CA certificates, and\\nmay contain the leaf TSA certificate if not present in the timestamurce.\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  },\n                  \"kms\": {\n                   \"description\": \"KMS provides the URI to the public key stored in a Key Management System. See:\\nhttps://github.com/sigstore/cosign/blob/main/KMS.md\",\n                   \"type\": \"string\"\n                  },\n                  \"publicKeys\": {\n                   \"description\": \"Keys is a set of X.509 public keys used to verify image signatures. The keys can be directly\\nspecified or can be a variable reference to a key specified in a ConfigMap (see\\nhttps://kyverno.io/docs/writing-policies/variables/), or reference a standard Kubernetes Secret\\nelsewhere in the cluster by specifying it in the format \\\"k8s://\\u003cnamespace\\u003e/\\u003csecret_name\\u003e\\\".\\nThe named Secret must specify a key `cosign.pub` containing the public key used for\\nverification, (see https://github.com/sigstore/cosign/blob/main/KMS.md#kubernetes-secret).\\nWhen multiple keys are specified each key is processed as a separate staticKey entry\\n(.attestors[*].entries.keys) within the set of attestors and the count is applied across the keys.\",\n                   \"type\": \"string\"\n                  },\n                  \"rekor\": {\n                   \"description\": \"Rekor provides configuration for the Rekor transparency log service. If an empty object\\nis provided the public instance of Rekor (https://rekor.sigstore.dev) is used.\",\n                   \"properties\": {\n                    \"ignoreTlog\": {\n                     \"description\": \"IgnoreTlog skips transparency log verification.\",\n                     \"type\": \"boolean\"\n                    },\n                    \"pubkey\": {\n                     \"description\": \"RekorPubKey is an optional PEM-encoded public key to use for a custom Rekor.\\nIf set, this will be used to validate transparency log signatures from a custom Rekor.\",\n                     \"type\": \"string\"\n                    },\n                    \"url\": {\n                     \"description\": \"URL is the address of the transparency log. Defaults to the public Rekor log instance https://rekor.sigstore.dev.\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  },\n                  \"secret\": {\n                   \"description\": \"Reference to a Secret resource that contains a public key\",\n                   \"properties\": {\n                    \"name\": {\n                     \"description\": \"Name of the secret. The provided secret must contain a key named cosign.pub.\",\n                     \"type\": \"string\"\n                    },\n                    \"namespace\": {\n                     \"description\": \"Namespace name where the Secret exists.\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\",\n                    \"namespace\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"signatureAlgorithm\": {\n                   \"default\": \"sha256\",\n                   \"description\": \"Deprecated. Use attestor.signatureAlgorithm instead.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"repository\": {\n                 \"description\": \"Repository is an optional alternate OCI repository to use for signatures and attestations that match this rule.\\nIf specified Repository will override other OCI image repository locations for this Attestor.\",\n                 \"type\": \"string\"\n                },\n                \"signatureAlgorithm\": {\n                 \"default\": \"sha256\",\n                 \"description\": \"Specify signature algorithm for public keys. Supported values are sha224, sha256, sha384 and sha512.\",\n                 \"type\": \"string\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"type\": \"array\"\n          },\n          \"cosignOCI11\": {\n           \"description\": \"CosignOCI11 enables the experimental OCI 1.1 behaviour in cosign image verification.\\nDefaults to false.\",\n           \"type\": \"boolean\"\n          },\n          \"failureAction\": {\n           \"description\": \"Allowed values are Audit or Enforce.\",\n           \"enum\": [\n            \"Audit\",\n            \"Enforce\"\n           ],\n           \"type\": \"string\"\n          },\n          \"image\": {\n           \"description\": \"Deprecated. Use ImageReferences instead.\",\n           \"type\": \"string\"\n          },\n          \"imageReferences\": {\n           \"description\": \"ImageReferences is a list of matching image reference patterns. At least one pattern in the\\nlist must match the image for the rule to apply. Each image reference consists of a registry\\naddress (defaults to docker.io), repository, image, and tag (defaults to latest).\\nWildcards ('*' and '?') are allowed. See: https://kubernetes.io/docs/concepts/containers/images.\",\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"imageRegistryCredentials\": {\n           \"description\": \"ImageRegistryCredentials provides credentials that will be used for authentication with registry.\",\n           \"properties\": {\n            \"allowInsecureRegistry\": {\n             \"description\": \"AllowInsecureRegistry allows insecure access to a registry.\",\n             \"type\": \"boolean\"\n            },\n            \"providers\": {\n             \"description\": \"Providers specifies a list of OCI Registry names, whose authentication providers are provided.\\nIt can be of one of these values: default,google,azure,amazon,github.\",\n             \"items\": {\n              \"description\": \"ImageRegistryCredentialsProvidersType provides the list of credential providers required.\",\n              \"enum\": [\n               \"default\",\n               \"amazon\",\n               \"azure\",\n               \"google\",\n               \"github\"\n              ],\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"secrets\": {\n             \"description\": \"Secrets specifies a list of secrets that are provided for credentials.\\nSecrets must live in the Kyverno namespace.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"issuer\": {\n           \"description\": \"Deprecated. Use KeylessAttestor instead.\",\n           \"type\": \"string\"\n          },\n          \"key\": {\n           \"description\": \"Deprecated. Use StaticKeyAttestor instead.\",\n           \"type\": \"string\"\n          },\n          \"mutateDigest\": {\n           \"default\": true,\n           \"description\": \"MutateDigest enables replacement of image tags with digests.\\nDefaults to true.\",\n           \"type\": \"boolean\"\n          },\n          \"repository\": {\n           \"description\": \"Repository is an optional alternate OCI repository to use for image signatures and attestations that match this rule.\\nIf specified Repository will override the default OCI image repository configured for the installation.\\nThe repository can also be overridden per Attestor or Attestation.\",\n           \"type\": \"string\"\n          },\n          \"required\": {\n           \"default\": true,\n           \"description\": \"Required validates that images are verified i.e. have matched passed a signature or attestation check.\",\n           \"type\": \"boolean\"\n          },\n          \"roots\": {\n           \"description\": \"Deprecated. Use KeylessAttestor instead.\",\n           \"type\": \"string\"\n          },\n          \"skipImageReferences\": {\n           \"description\": \"SkipImageReferences is a list of matching image reference patterns that should be skipped.\\nAt least one pattern in the list must match the image for the rule to be skipped. Each image reference\\nconsists of a registry address (defaults to docker.io), repository, image, and tag (defaults to latest).\\nWildcards ('*' and '?') are allowed. See: https://kubernetes.io/docs/concepts/containers/images.\",\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"subject\": {\n           \"description\": \"Deprecated. Use KeylessAttestor instead.\",\n           \"type\": \"string\"\n          },\n          \"type\": {\n           \"description\": \"Type specifies the method of signature validation. The allowed options\\nare Cosign, Sigstore Bundle and Notary. By default Cosign is used if a type is not specified.\",\n           \"enum\": [\n            \"Cosign\",\n            \"SigstoreBundle\",\n            \"Notary\"\n           ],\n           \"type\": \"string\"\n          },\n          \"useCache\": {\n           \"default\": true,\n           \"description\": \"UseCache enables caching of image verify responses for this rule.\",\n           \"type\": \"boolean\"\n          },\n          \"validate\": {\n           \"description\": \"Validation checks conditions across multiple image\\nverification attestations or context entries\",\n           \"properties\": {\n            \"deny\": {\n             \"description\": \"Deny defines conditions used to pass or fail a validation rule.\",\n             \"properties\": {\n              \"conditions\": {\n               \"description\": \"Multiple conditions can be declared under an `any` or `all` statement. A direct list\\nof conditions (without `any` or `all` statements) is also supported for backwards compatibility\\nbut will be deprecated in the next major release.\\nSee: https://kyverno.io/docs/writing-policies/validate/#deny-rules\",\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"message\": {\n             \"description\": \"Message specifies a custom message to be displayed on failure.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"verifyDigest\": {\n           \"default\": true,\n           \"description\": \"VerifyDigest validates that images have a digest.\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       }\n      },\n      \"required\": [\n       \"match\",\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"schemaValidation\": {\n     \"description\": \"Deprecated.\",\n     \"type\": \"boolean\"\n    },\n    \"useServerSideApply\": {\n     \"description\": \"UseServerSideApply controls whether to use server-side apply for generate rules\\nIf is set to \\\"true\\\" create \\u0026 update for generate rules will use apply instead of create/update.\\nDefaults to \\\"false\\\" if not specified.\",\n     \"type\": \"boolean\"\n    },\n    \"validationFailureAction\": {\n     \"default\": \"Audit\",\n     \"description\": \"Deprecated, use validationFailureAction under the validate rule instead.\",\n     \"enum\": [\n      \"audit\",\n      \"enforce\",\n      \"Audit\",\n      \"Enforce\"\n     ],\n     \"type\": \"string\"\n    },\n    \"validationFailureActionOverrides\": {\n     \"description\": \"Deprecated, use validationFailureActionOverrides under the validate rule instead.\",\n     \"items\": {\n      \"properties\": {\n       \"action\": {\n        \"description\": \"ValidationFailureAction defines the policy validation failure action\",\n        \"enum\": [\n         \"audit\",\n         \"enforce\",\n         \"Audit\",\n         \"Enforce\"\n        ],\n        \"type\": \"string\"\n       },\n       \"namespaceSelector\": {\n        \"description\": \"A label selector is a label query over a set of resources. The result of matchLabels and\\nmatchExpressions are ANDed. An empty label selector matches all objects. A null\\nlabel selector matches no objects.\",\n        \"properties\": {\n         \"matchExpressions\": {\n          \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n          \"items\": {\n           \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"key is the label key that the selector applies to.\",\n             \"type\": \"string\"\n            },\n            \"operator\": {\n             \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n             \"type\": \"string\"\n            },\n            \"values\": {\n             \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"operator\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"matchLabels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\",\n        \"x-kubernetes-map-type\": \"atomic\"\n       },\n       \"namespaces\": {\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"webhookConfiguration\": {\n     \"description\": \"WebhookConfiguration specifies the custom configuration for Kubernetes admission webhookconfiguration.\",\n     \"properties\": {\n      \"failurePolicy\": {\n       \"description\": \"FailurePolicy defines how unexpected policy errors and webhook response timeout errors are handled.\\nRules within the same policy share the same failure behavior.\\nThis field should not be accessed directly, instead `GetFailurePolicy()` should be used.\\nAllowed values are Ignore or Fail. Defaults to Fail.\",\n       \"enum\": [\n        \"Ignore\",\n        \"Fail\"\n       ],\n       \"type\": \"string\"\n      },\n      \"matchConditions\": {\n       \"description\": \"MatchCondition configures admission webhook matchConditions.\\nRequires Kubernetes 1.27 or later.\",\n       \"items\": {\n        \"description\": \"MatchCondition represents a condition which must by fulfilled for a request to be sent to a webhook.\",\n        \"properties\": {\n         \"expression\": {\n          \"description\": \"Expression represents the expression which will be evaluated by CEL. Must evaluate to bool.\\nCEL expressions have access to the contents of the AdmissionRequest and Authorizer, organized into CEL variables:\\n\\n'object' - The object from the incoming request. The value is null for DELETE requests.\\n'oldObject' - The existing object. The value is null for CREATE requests.\\n'request' - Attributes of the admission request(/pkg/apis/admission/types.go#AdmissionRequest).\\n'authorizer' - A CEL Authorizer. May be used to perform authorization checks for the principal (user or service account) of the request.\\n  See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz\\n'authorizer.requestResource' - A CEL ResourceCheck constructed from the 'authorizer' and configured with the\\n  request resource.\\nDocumentation on CEL: https://kubernetes.io/docs/reference/using-api/cel/\\n\\nRequired.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name is an identifier for this match condition, used for strategic merging of MatchConditions,\\nas well as providing an identifier for logging purposes. A good name should be descriptive of\\nthe associated expression.\\nName must be a qualified name consisting of alphanumeric characters, '-', '_' or '.', and\\nmust start and end with an alphanumeric character (e.g. 'MyName',  or 'my.name',  or\\n'123-abc', regex used for validation is '([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9]') with an\\noptional DNS subdomain prefix and '/' (e.g. 'example.com/MyName')\\n\\nRequired.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"expression\",\n         \"name\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"timeoutSeconds\": {\n       \"description\": \"TimeoutSeconds specifies the maximum time in seconds allowed to apply this policy.\\nAfter the configured time expires, the admission request may fail, or may simply ignore the policy results,\\nbased on the failure policy. The default timeout is 10s, the value must be between 1 and 30 seconds.\",\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"webhookTimeoutSeconds\": {\n     \"description\": \"Deprecated, use webhookTimeoutSeconds under webhookConfiguration instead.\",\n     \"format\": \"int32\",\n     \"type\": \"integer\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"required\": [\n  \"spec\"\n ],\n \"title\": \"Cluster Policy\",\n \"type\": \"object\"\n}"}}