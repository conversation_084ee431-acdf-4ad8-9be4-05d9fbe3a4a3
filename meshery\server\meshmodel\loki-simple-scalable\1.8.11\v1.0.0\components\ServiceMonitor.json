{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Service Monitor", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "loki-simple-scalable", "displayName": "Loki Simple Scalable", "description": "Loki is a horizontally scalable, highly available, multi-tenant log aggregation system inspired by Prometheus. It is designed to be very cost effective and easy to operate. It does not index the contents of the logs, but rather a set of labels for each log stream.", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Observability and Analysis"}, "subCategory": "Logging", "metadata": {"isAnnotation": false, "primaryColor": "#F15B2B", "secondaryColor": "#FAED1E", "shape": "circle", "source_uri": "https://github.com/grafana/helm-charts/releases/download/loki-simple-scalable-1.8.11/loki-simple-scalable-1.8.11.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 48 56\" fill=\"none\">\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.0478 54.9248L11.3838 50.4663L6.92529 51.1304L7.68418 55.5889L12.0478 54.9248Z\" fill=\"url(#paint0_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M46.957 42.4032L46.1981 38.0396L26.7515 41.0751L27.3206 45.4388L46.957 42.4032Z\" fill=\"url(#paint1_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M20.395 46.5772L24.8535 45.8183L24.1895 41.4546L19.731 42.1186L20.395 46.5772Z\" fill=\"url(#paint2_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M19.0674 53.7865L18.3085 49.4229L13.9448 50.0869L14.514 54.5454L19.0674 53.7865Z\" fill=\"url(#paint3_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M5.88135 44.2055L6.54539 48.6641L11.0039 48L10.3399 43.5415L5.88135 44.2055Z\" fill=\"url(#paint4_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M27.6997 47.9051L28.4586 52.4585L48.0001 49.4229L47.3361 44.9644L27.6997 47.9051Z\" fill=\"url(#paint5_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.5333 53.407L25.8969 52.8378L25.2329 48.2844L20.7744 49.0433L21.5333 53.407Z\" fill=\"url(#paint6_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.8062 43.1621L13.565 47.6205L17.9287 46.9566L17.2646 42.498L12.8062 43.1621Z\" fill=\"url(#paint7_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.39921 41.4546L1.99207 5.97632L0 6.26089L5.50197 41.7392L7.39921 41.4546Z\" fill=\"url(#paint8_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.96032 41.0751L4.07888 2.94067L2.18164 3.32014L8.06308 41.3597L9.96032 41.0751Z\" fill=\"url(#paint9_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M14.3245 40.4111L8.15847 0L6.26123 0.379412L12.4272 40.6008L14.3245 40.4111Z\" fill=\"url(#paint10_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.8852 40.0315L11.1935 3.2251L9.39111 3.50967L15.0828 40.2212L16.8852 40.0315Z\" fill=\"url(#paint11_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.2491 39.2728L16.2215 6.64038L14.3242 6.92495L19.3519 39.6523L21.2491 39.2728Z\" fill=\"url(#paint12_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.8104 38.8935L18.593 5.02783L16.6958 5.31241L22.0081 39.1781L23.8104 38.8935Z\" fill=\"url(#paint13_linear_17931_893)\"></path>\n<defs xmlns=\"http://www.w3.org/2000/svg\">\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint0_linear_17931_893\" x1=\"11.6469\" y1=\"66.8772\" x2=\"1.23198\" y2=\"-0.802501\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint1_linear_17931_893\" x1=\"39.9916\" y1=\"62.5154\" x2=\"29.5768\" y2=\"-5.1639\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint2_linear_17931_893\" x1=\"25.5063\" y1=\"64.7445\" x2=\"15.0913\" y2=\"-2.93516\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint3_linear_17931_893\" x1=\"18.5788\" y1=\"65.8105\" x2=\"8.1638\" y2=\"-1.86922\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint4_linear_17931_893\" x1=\"11.6394\" y1=\"66.8784\" x2=\"1.22448\" y2=\"-0.80128\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint5_linear_17931_893\" x1=\"39.9982\" y1=\"62.5143\" x2=\"29.5833\" y2=\"-5.16528\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint6_linear_17931_893\" x1=\"25.506\" y1=\"64.7443\" x2=\"15.091\" y2=\"-2.93537\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint7_linear_17931_893\" x1=\"18.5788\" y1=\"65.8103\" x2=\"8.16407\" y2=\"-1.86867\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint8_linear_17931_893\" x1=\"10.1623\" y1=\"65.7597\" x2=\"0.284696\" y2=\"1.57166\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint9_linear_17931_893\" x1=\"13.0129\" y1=\"67.1431\" x2=\"2.40785\" y2=\"-1.77243\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint10_linear_17931_893\" x1=\"17.6338\" y1=\"68.0331\" x2=\"6.38943\" y2=\"-5.0367\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint11_linear_17931_893\" x1=\"19.8305\" y1=\"65.208\" x2=\"9.57925\" y2=\"-1.40832\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint12_linear_17931_893\" x1=\"23.7353\" y1=\"61.7393\" x2=\"14.6289\" y2=\"2.56246\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint13_linear_17931_893\" x1=\"26.4465\" y1=\"62.1967\" x2=\"16.9911\" y2=\"0.751851\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FAED1E\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#F15B2B\"></stop>\n</linearGradient>\n</defs>\n</svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 48 56\" fill=\"none\">\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.0478 54.9248L11.3838 50.4663L6.92529 51.1304L7.68418 55.5889L12.0478 54.9248Z\" fill=\"url(#paint0_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M46.957 42.4032L46.1981 38.0396L26.7515 41.0751L27.3206 45.4388L46.957 42.4032Z\" fill=\"url(#paint1_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M20.395 46.5772L24.8535 45.8183L24.1895 41.4546L19.731 42.1186L20.395 46.5772Z\" fill=\"url(#paint2_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M19.0674 53.7865L18.3085 49.4229L13.9448 50.0869L14.514 54.5454L19.0674 53.7865Z\" fill=\"url(#paint3_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M5.88135 44.2055L6.54539 48.6641L11.0039 48L10.3399 43.5415L5.88135 44.2055Z\" fill=\"url(#paint4_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M27.6997 47.9051L28.4586 52.4585L48.0001 49.4229L47.3361 44.9644L27.6997 47.9051Z\" fill=\"url(#paint5_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.5333 53.407L25.8969 52.8378L25.2329 48.2844L20.7744 49.0433L21.5333 53.407Z\" fill=\"url(#paint6_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M12.8062 43.1621L13.565 47.6205L17.9287 46.9566L17.2646 42.498L12.8062 43.1621Z\" fill=\"url(#paint7_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.39921 41.4546L1.99207 5.97632L0 6.26089L5.50197 41.7392L7.39921 41.4546Z\" fill=\"url(#paint8_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.96032 41.0751L4.07888 2.94067L2.18164 3.32014L8.06308 41.3597L9.96032 41.0751Z\" fill=\"url(#paint9_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M14.3245 40.4111L8.15847 0L6.26123 0.379412L12.4272 40.6008L14.3245 40.4111Z\" fill=\"url(#paint10_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.8852 40.0315L11.1935 3.2251L9.39111 3.50967L15.0828 40.2212L16.8852 40.0315Z\" fill=\"url(#paint11_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M21.2491 39.2728L16.2215 6.64038L14.3242 6.92495L19.3519 39.6523L21.2491 39.2728Z\" fill=\"url(#paint12_linear_17931_893)\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.8104 38.8935L18.593 5.02783L16.6958 5.31241L22.0081 39.1781L23.8104 38.8935Z\" fill=\"url(#paint13_linear_17931_893)\"></path>\n<defs xmlns=\"http://www.w3.org/2000/svg\">\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint0_linear_17931_893\" x1=\"11.6469\" y1=\"66.8772\" x2=\"1.23198\" y2=\"-0.802501\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint1_linear_17931_893\" x1=\"39.9916\" y1=\"62.5154\" x2=\"29.5768\" y2=\"-5.1639\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint2_linear_17931_893\" x1=\"25.5063\" y1=\"64.7445\" x2=\"15.0913\" y2=\"-2.93516\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint3_linear_17931_893\" x1=\"18.5788\" y1=\"65.8105\" x2=\"8.1638\" y2=\"-1.86922\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint4_linear_17931_893\" x1=\"11.6394\" y1=\"66.8784\" x2=\"1.22448\" y2=\"-0.80128\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint5_linear_17931_893\" x1=\"39.9982\" y1=\"62.5143\" x2=\"29.5833\" y2=\"-5.16528\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint6_linear_17931_893\" x1=\"25.506\" y1=\"64.7443\" x2=\"15.091\" y2=\"-2.93537\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint7_linear_17931_893\" x1=\"18.5788\" y1=\"65.8103\" x2=\"8.16407\" y2=\"-1.86867\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint8_linear_17931_893\" x1=\"10.1623\" y1=\"65.7597\" x2=\"0.284696\" y2=\"1.57166\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint9_linear_17931_893\" x1=\"13.0129\" y1=\"67.1431\" x2=\"2.40785\" y2=\"-1.77243\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint10_linear_17931_893\" x1=\"17.6338\" y1=\"68.0331\" x2=\"6.38943\" y2=\"-5.0367\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint11_linear_17931_893\" x1=\"19.8305\" y1=\"65.208\" x2=\"9.57925\" y2=\"-1.40832\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint12_linear_17931_893\" x1=\"23.7353\" y1=\"61.7393\" x2=\"14.6289\" y2=\"2.56246\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n<linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"paint13_linear_17931_893\" x1=\"26.4465\" y1=\"62.1967\" x2=\"16.9911\" y2=\"0.751851\" gradientUnits=\"userSpaceOnUse\">\n<stop xmlns=\"http://www.w3.org/2000/svg\" stop-color=\"#FFFFFF\"></stop>\n<stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#FFFFFF\"></stop>\n</linearGradient>\n</defs>\n</svg>"}, "model": {"version": "1.8.11"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#F15B2A", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\"\n         viewBox=\"0 0 103.7 112.7\" style=\"enable-background:new 0 0 103.7 112.7;\" xml:space=\"preserve\">\n<style type=\"text/css\">\n        .st0{fill:url(#SVGID_1_);}\n</style>\n<linearGradient id=\"SVGID_1_\" gradientUnits=\"userSpaceOnUse\" x1=\"51.85\" y1=\"1069.5107\" x2=\"51.85\" y2=\"966.6585\" gradientTransform=\"matrix(1 0 0 1 0 -931.4)\">\n        <stop  offset=\"0\" style=\"stop-color:#FCEE1F\"/>\n        <stop  offset=\"1\" style=\"stop-color:#F15B2A\"/>\n</linearGradient>\n<path class=\"st0\" d=\"M103.5,49.9c-0.2-1.9-0.5-4.1-1.1-6.5c-0.6-2.4-1.6-5-2.9-7.8c-1.4-2.7-3.1-5.6-5.4-8.3\n        c-0.9-1.1-1.9-2.1-2.9-3.2c1.6-6.3-1.9-11.8-1.9-11.8c-6.1-0.4-9.9,1.9-11.3,2.9c-0.2-0.1-0.5-0.2-0.7-0.3c-1-0.4-2.1-0.8-3.2-1.2\n        c-1.1-0.3-2.2-0.7-3.3-0.9c-1.1-0.3-2.3-0.5-3.5-0.7c-0.2,0-0.4-0.1-0.6-0.1C64.1,3.6,56.5,0,56.5,0c-8.7,5.6-10.4,13.1-10.4,13.1\n        s0,0.2-0.1,0.4c-0.5,0.1-0.9,0.3-1.4,0.4c-0.6,0.2-1.3,0.4-1.9,0.7c-0.6,0.3-1.3,0.5-1.9,0.8c-1.3,0.6-2.5,1.2-3.8,1.9\n        c-1.2,0.7-2.4,1.4-3.5,2.2c-0.2-0.1-0.3-0.2-0.3-0.2c-11.7-4.5-22.1,0.9-22.1,0.9c-0.9,12.5,4.7,20.3,5.8,21.7\n        c-0.3,0.8-0.5,1.5-0.8,2.3c-0.9,2.8-1.5,5.7-1.9,8.7c-0.1,0.4-0.1,0.9-0.2,1.3C3.2,59.5,0,70.5,0,70.5c9,10.4,19.6,11,19.6,11l0,0\n        c1.3,2.4,2.9,4.7,4.6,6.8c0.7,0.9,1.5,1.7,2.3,2.6c-3.3,9.4,0.5,17.3,0.5,17.3c10.1,0.4,16.7-4.4,18.1-5.5c1,0.3,2,0.6,3,0.9\n        c3.1,0.8,6.3,1.3,9.4,1.4c0.8,0,1.6,0,2.4,0h0.4h0.3h0.5h0.5l0,0c4.7,6.8,13.1,7.7,13.1,7.7c5.9-6.3,6.3-12.4,6.3-13.8l0,0\n        c0,0,0,0,0-0.1s0-0.2,0-0.2l0,0c0-0.1,0-0.2,0-0.3c1.2-0.9,2.4-1.8,3.6-2.8c2.4-2.1,4.4-4.6,6.2-7.2c0.2-0.2,0.3-0.5,0.5-0.7\n        c6.7,0.4,11.4-4.2,11.4-4.2c-1.1-7-5.1-10.4-5.9-11l0,0c0,0,0,0-0.1-0.1l-0.1-0.1l0,0l-0.1-0.1c0-0.4,0.1-0.8,0.1-1.3\n        c0.1-0.8,0.1-1.5,0.1-2.3v-0.6v-0.3v-0.1c0-0.2,0-0.1,0-0.2v-0.5v-0.6c0-0.2,0-0.4,0-0.6s0-0.4-0.1-0.6l-0.1-0.6l-0.1-0.6\n        c-0.1-0.8-0.3-1.5-0.4-2.3c-0.7-3-1.9-5.9-3.4-8.4c-1.6-2.6-3.5-4.8-5.7-6.8c-2.2-1.9-4.6-3.5-7.2-4.6c-2.6-1.2-5.2-1.9-7.9-2.2\n        c-1.3-0.2-2.7-0.2-4-0.2h-0.5h-0.1H67h-0.2h-0.5c-0.2,0-0.4,0-0.5,0c-0.7,0.1-1.4,0.2-2,0.3c-2.7,0.5-5.2,1.5-7.4,2.8\n        c-2.2,1.3-4.1,3-5.7,4.9s-2.8,3.9-3.6,6.1c-0.8,2.1-1.3,4.4-1.4,6.5c0,0.5,0,1.1,0,1.6c0,0.1,0,0.3,0,0.4v0.4c0,0.3,0,0.5,0.1,0.8\n        c0.1,1.1,0.3,2.1,0.6,3.1c0.6,2,1.5,3.8,2.7,5.4s2.5,2.8,4,3.8s3,1.7,4.6,2.2s3.1,0.7,4.5,0.6c0.2,0,0.4,0,0.5,0s0.2,0,0.3,0\n        s0.2,0,0.3,0c0.2,0,0.3,0,0.5,0h0.1H64c0.1,0,0.2,0,0.3,0c0.2,0,0.4-0.1,0.5-0.1c0.2,0,0.3-0.1,0.5-0.1c0.3-0.1,0.7-0.2,1-0.3\n        c0.6-0.2,1.2-0.5,1.8-0.7c0.6-0.3,1.1-0.6,1.5-0.9c0.1-0.1,0.3-0.2,0.4-0.3c0.5-0.4,0.6-1.1,0.2-1.6c-0.4-0.4-1-0.5-1.5-0.3\n        c-0.1,0.1-0.2,0.1-0.4,0.2c-0.4,0.2-0.9,0.4-1.3,0.5c-0.5,0.1-1,0.3-1.5,0.4c-0.3,0-0.5,0.1-0.8,0.1c-0.1,0-0.3,0-0.4,0\n        c-0.1,0-0.3,0-0.4,0s-0.3,0-0.4,0c-0.2,0-0.3,0-0.5,0c0,0-0.1,0,0,0h-0.1h-0.1c-0.1,0-0.1,0-0.2,0s-0.3,0-0.4-0.1\n        c-1.1-0.2-2.3-0.5-3.4-1s-2.2-1.2-3.1-2.1c-1-0.9-1.8-1.9-2.5-3.1s-1.1-2.5-1.3-3.8c-0.1-0.7-0.2-1.4-0.1-2.1c0-0.2,0-0.4,0-0.6\n        c0,0.1,0,0,0,0v-0.1v-0.1c0-0.1,0-0.2,0-0.3c0-0.4,0.1-0.7,0.2-1.1c0.5-3,2-5.9,4.3-8.1c0.6-0.6,1.2-1.1,1.9-1.5\n        c0.7-0.5,1.4-0.9,2.1-1.2s1.5-0.6,2.3-0.8s1.6-0.4,2.4-0.4c0.4,0,0.8-0.1,1.2-0.1c0.1,0,0.2,0,0.3,0h0.3H67c0.1,0,0,0,0,0h0.1h0.3\n        c0.9,0.1,1.8,0.2,2.6,0.4c1.7,0.4,3.4,1,5,1.9c3.2,1.8,5.9,4.5,7.5,7.8c0.8,1.6,1.4,3.4,1.7,5.3c0.1,0.5,0.1,0.9,0.2,1.4v0.3V66\n        c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3v0.3v0.3c0,0.2,0,0.6,0,0.8c0,0.5-0.1,1-0.1,1.5c-0.1,0.5-0.1,1-0.2,1.5\n        c-0.1,0.5-0.2,1-0.3,1.5c-0.2,1-0.6,1.9-0.9,2.9c-0.7,1.9-1.7,3.7-2.9,5.3c-2.4,3.3-5.7,6-9.4,7.7c-1.9,0.8-3.8,1.5-5.8,1.8\n        c-1,0.2-2,0.3-3,0.3h-0.2h-0.2h-0.3h-0.5h-0.3c0.1,0,0,0,0,0h-0.1c-0.5,0-1.1,0-1.6-0.1c-2.2-0.2-4.3-0.6-6.4-1.2s-4.1-1.4-6-2.4\n        c-3.8-2-7.2-4.9-9.9-8.2c-1.3-1.7-2.5-3.5-3.5-5.4s-1.7-3.9-2.3-5.9s-0.9-4.1-1-6.2v-0.4v-0.1v-0.1v-0.2V60v-0.1v-0.1v-0.2v-0.5V59\n        l0,0v-0.2c0-0.3,0-0.5,0-0.8c0-1,0.1-2.1,0.3-3.2c0.1-1.1,0.3-2.1,0.5-3.2c0.2-1.1,0.5-2.1,0.8-3.2c0.6-2.1,1.3-4.1,2.2-6\n        c1.8-3.8,4.1-7.2,6.8-9.9c0.7-0.7,1.4-1.3,2.2-1.9c0.3-0.3,1-0.9,1.8-1.4s1.6-1,2.5-1.4c0.4-0.2,0.8-0.4,1.3-0.6\n        c0.2-0.1,0.4-0.2,0.7-0.3c0.2-0.1,0.4-0.2,0.7-0.3c0.9-0.4,1.8-0.7,2.7-1c0.2-0.1,0.5-0.1,0.7-0.2s0.5-0.1,0.7-0.2\n        c0.5-0.1,0.9-0.2,1.4-0.4c0.2-0.1,0.5-0.1,0.7-0.2c0.2,0,0.5-0.1,0.7-0.1s0.5-0.1,0.7-0.1l0.4-0.1l0.4-0.1c0.2,0,0.5-0.1,0.7-0.1\n        c0.3,0,0.5-0.1,0.8-0.1c0.2,0,0.6-0.1,0.8-0.1c0.2,0,0.3,0,0.5-0.1h0.3H61h0.2c0.3,0,0.5,0,0.8-0.1h0.4c0,0,0.1,0,0,0h0.1h0.2\n        c0.2,0,0.5,0,0.7,0c0.9,0,1.8,0,2.7,0c1.8,0.1,3.6,0.3,5.3,0.6c3.4,0.6,6.7,1.7,9.6,3.2c2.9,1.4,5.6,3.2,7.8,5.1\n        c0.1,0.1,0.3,0.2,0.4,0.4c0.1,0.1,0.3,0.2,0.4,0.4c0.3,0.2,0.5,0.5,0.8,0.7s0.5,0.5,0.8,0.7c0.2,0.3,0.5,0.5,0.7,0.8\n        c1,1,1.9,2.1,2.7,3.1c1.6,2.1,2.9,4.2,3.9,6.2c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.2,0.2,0.5,0.4,0.7\n        c0.1,0.2,0.2,0.5,0.3,0.7c0.1,0.2,0.2,0.5,0.3,0.7c0.4,0.9,0.7,1.8,1,2.7c0.5,1.4,0.8,2.6,1.1,3.6c0.1,0.4,0.5,0.7,0.9,0.7\n        c0.5,0,0.8-0.4,0.8-0.9C103.6,52.7,103.6,51.4,103.5,49.9z\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\"\n         viewBox=\"0 0 103.7 112.7\" style=\"enable-background:new 0 0 103.7 112.7;\" xml:space=\"preserve\" width='103.7' height='112.7'>\n<style type=\"text/css\">\n        .st0{fill:#FFFFFF;}\n</style>\n<path class=\"st0\" d=\"M103.5,49.9c-0.2-1.9-0.5-4.1-1.1-6.5c-0.6-2.4-1.6-5-2.9-7.8c-1.4-2.7-3.1-5.6-5.4-8.3\n        c-0.9-1.1-1.9-2.1-2.9-3.2c1.6-6.3-1.9-11.8-1.9-11.8c-6.1-0.4-9.9,1.9-11.3,2.9c-0.2-0.1-0.5-0.2-0.7-0.3c-1-0.4-2.1-0.8-3.2-1.2\n        c-1.1-0.3-2.2-0.7-3.3-0.9c-1.1-0.3-2.3-0.5-3.5-0.7c-0.2,0-0.4-0.1-0.6-0.1C64.1,3.6,56.5,0,56.5,0c-8.7,5.6-10.4,13.1-10.4,13.1\n        s0,0.2-0.1,0.4c-0.5,0.1-0.9,0.3-1.4,0.4c-0.6,0.2-1.3,0.4-1.9,0.7c-0.6,0.3-1.3,0.5-1.9,0.8c-1.3,0.6-2.5,1.2-3.8,1.9\n        c-1.2,0.7-2.4,1.4-3.5,2.2c-0.2-0.1-0.3-0.2-0.3-0.2c-11.7-4.5-22.1,0.9-22.1,0.9c-0.9,12.5,4.7,20.3,5.8,21.7\n        c-0.3,0.8-0.5,1.5-0.8,2.3c-0.9,2.8-1.5,5.7-1.9,8.7c-0.1,0.4-0.1,0.9-0.2,1.3C3.2,59.5,0,70.5,0,70.5c9,10.4,19.6,11,19.6,11l0,0\n        c1.3,2.4,2.9,4.7,4.6,6.8c0.7,0.9,1.5,1.7,2.3,2.6c-3.3,9.4,0.5,17.3,0.5,17.3c10.1,0.4,16.7-4.4,18.1-5.5c1,0.3,2,0.6,3,0.9\n        c3.1,0.8,6.3,1.3,9.4,1.4c0.8,0,1.6,0,2.4,0h0.4h0.3h0.5h0.5l0,0c4.7,6.8,13.1,7.7,13.1,7.7c5.9-6.3,6.3-12.4,6.3-13.8l0,0\n        c0,0,0,0,0-0.1s0-0.2,0-0.2l0,0c0-0.1,0-0.2,0-0.3c1.2-0.9,2.4-1.8,3.6-2.8c2.4-2.1,4.4-4.6,6.2-7.2c0.2-0.2,0.3-0.5,0.5-0.7\n        c6.7,0.4,11.4-4.2,11.4-4.2c-1.1-7-5.1-10.4-5.9-11l0,0c0,0,0,0-0.1-0.1l-0.1-0.1l0,0l-0.1-0.1c0-0.4,0.1-0.8,0.1-1.3\n        c0.1-0.8,0.1-1.5,0.1-2.3v-0.6v-0.3v-0.1c0-0.2,0-0.1,0-0.2v-0.5v-0.6c0-0.2,0-0.4,0-0.6s0-0.4-0.1-0.6l-0.1-0.6l-0.1-0.6\n        c-0.1-0.8-0.3-1.5-0.4-2.3c-0.7-3-1.9-5.9-3.4-8.4c-1.6-2.6-3.5-4.8-5.7-6.8c-2.2-1.9-4.6-3.5-7.2-4.6c-2.6-1.2-5.2-1.9-7.9-2.2\n        c-1.3-0.2-2.7-0.2-4-0.2h-0.5h-0.1H67h-0.2h-0.5c-0.2,0-0.4,0-0.5,0c-0.7,0.1-1.4,0.2-2,0.3c-2.7,0.5-5.2,1.5-7.4,2.8\n        c-2.2,1.3-4.1,3-5.7,4.9s-2.8,3.9-3.6,6.1c-0.8,2.1-1.3,4.4-1.4,6.5c0,0.5,0,1.1,0,1.6c0,0.1,0,0.3,0,0.4v0.4c0,0.3,0,0.5,0.1,0.8\n        c0.1,1.1,0.3,2.1,0.6,3.1c0.6,2,1.5,3.8,2.7,5.4s2.5,2.8,4,3.8s3,1.7,4.6,2.2s3.1,0.7,4.5,0.6c0.2,0,0.4,0,0.5,0s0.2,0,0.3,0\n        s0.2,0,0.3,0c0.2,0,0.3,0,0.5,0h0.1H64c0.1,0,0.2,0,0.3,0c0.2,0,0.4-0.1,0.5-0.1c0.2,0,0.3-0.1,0.5-0.1c0.3-0.1,0.7-0.2,1-0.3\n        c0.6-0.2,1.2-0.5,1.8-0.7c0.6-0.3,1.1-0.6,1.5-0.9c0.1-0.1,0.3-0.2,0.4-0.3c0.5-0.4,0.6-1.1,0.2-1.6c-0.4-0.4-1-0.5-1.5-0.3\n        c-0.1,0.1-0.2,0.1-0.4,0.2c-0.4,0.2-0.9,0.4-1.3,0.5c-0.5,0.1-1,0.3-1.5,0.4c-0.3,0-0.5,0.1-0.8,0.1c-0.1,0-0.3,0-0.4,0\n        c-0.1,0-0.3,0-0.4,0s-0.3,0-0.4,0c-0.2,0-0.3,0-0.5,0c0,0-0.1,0,0,0h-0.1h-0.1c-0.1,0-0.1,0-0.2,0s-0.3,0-0.4-0.1\n        c-1.1-0.2-2.3-0.5-3.4-1s-2.2-1.2-3.1-2.1c-1-0.9-1.8-1.9-2.5-3.1s-1.1-2.5-1.3-3.8c-0.1-0.7-0.2-1.4-0.1-2.1c0-0.2,0-0.4,0-0.6\n        c0,0.1,0,0,0,0v-0.1v-0.1c0-0.1,0-0.2,0-0.3c0-0.4,0.1-0.7,0.2-1.1c0.5-3,2-5.9,4.3-8.1c0.6-0.6,1.2-1.1,1.9-1.5\n        c0.7-0.5,1.4-0.9,2.1-1.2s1.5-0.6,2.3-0.8s1.6-0.4,2.4-0.4c0.4,0,0.8-0.1,1.2-0.1c0.1,0,0.2,0,0.3,0h0.3H67c0.1,0,0,0,0,0h0.1h0.3\n        c0.9,0.1,1.8,0.2,2.6,0.4c1.7,0.4,3.4,1,5,1.9c3.2,1.8,5.9,4.5,7.5,7.8c0.8,1.6,1.4,3.4,1.7,5.3c0.1,0.5,0.1,0.9,0.2,1.4v0.3V66\n        c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3v0.3v0.3c0,0.2,0,0.6,0,0.8c0,0.5-0.1,1-0.1,1.5c-0.1,0.5-0.1,1-0.2,1.5\n        c-0.1,0.5-0.2,1-0.3,1.5c-0.2,1-0.6,1.9-0.9,2.9c-0.7,1.9-1.7,3.7-2.9,5.3c-2.4,3.3-5.7,6-9.4,7.7c-1.9,0.8-3.8,1.5-5.8,1.8\n        c-1,0.2-2,0.3-3,0.3h-0.2h-0.2h-0.3h-0.5h-0.3c0.1,0,0,0,0,0h-0.1c-0.5,0-1.1,0-1.6-0.1c-2.2-0.2-4.3-0.6-6.4-1.2s-4.1-1.4-6-2.4\n        c-3.8-2-7.2-4.9-9.9-8.2c-1.3-1.7-2.5-3.5-3.5-5.4s-1.7-3.9-2.3-5.9s-0.9-4.1-1-6.2v-0.4v-0.1v-0.1v-0.2V60v-0.1v-0.1v-0.2v-0.5V59\n        l0,0v-0.2c0-0.3,0-0.5,0-0.8c0-1,0.1-2.1,0.3-3.2c0.1-1.1,0.3-2.1,0.5-3.2c0.2-1.1,0.5-2.1,0.8-3.2c0.6-2.1,1.3-4.1,2.2-6\n        c1.8-3.8,4.1-7.2,6.8-9.9c0.7-0.7,1.4-1.3,2.2-1.9c0.3-0.3,1-0.9,1.8-1.4s1.6-1,2.5-1.4c0.4-0.2,0.8-0.4,1.3-0.6\n        c0.2-0.1,0.4-0.2,0.7-0.3c0.2-0.1,0.4-0.2,0.7-0.3c0.9-0.4,1.8-0.7,2.7-1c0.2-0.1,0.5-0.1,0.7-0.2s0.5-0.1,0.7-0.2\n        c0.5-0.1,0.9-0.2,1.4-0.4c0.2-0.1,0.5-0.1,0.7-0.2c0.2,0,0.5-0.1,0.7-0.1s0.5-0.1,0.7-0.1l0.4-0.1l0.4-0.1c0.2,0,0.5-0.1,0.7-0.1\n        c0.3,0,0.5-0.1,0.8-0.1c0.2,0,0.6-0.1,0.8-0.1c0.2,0,0.3,0,0.5-0.1h0.3H61h0.2c0.3,0,0.5,0,0.8-0.1h0.4c0,0,0.1,0,0,0h0.1h0.2\n        c0.2,0,0.5,0,0.7,0c0.9,0,1.8,0,2.7,0c1.8,0.1,3.6,0.3,5.3,0.6c3.4,0.6,6.7,1.7,9.6,3.2c2.9,1.4,5.6,3.2,7.8,5.1\n        c0.1,0.1,0.3,0.2,0.4,0.4c0.1,0.1,0.3,0.2,0.4,0.4c0.3,0.2,0.5,0.5,0.8,0.7s0.5,0.5,0.8,0.7c0.2,0.3,0.5,0.5,0.7,0.8\n        c1,1,1.9,2.1,2.7,3.1c1.6,2.1,2.9,4.2,3.9,6.2c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.2,0.2,0.5,0.4,0.7\n        c0.1,0.2,0.2,0.5,0.3,0.7c0.1,0.2,0.2,0.5,0.3,0.7c0.4,0.9,0.7,1.8,1,2.7c0.5,1.4,0.8,2.6,1.1,3.6c0.1,0.4,0.5,0.7,0.9,0.7\n        c0.5,0,0.8-0.4,0.8-0.9C103.6,52.7,103.6,51.4,103.5,49.9z\"/>\n</svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/grafana/helm-charts/releases/download/loki-simple-scalable-1.8.11/loki-simple-scalable-1.8.11.tgz"}, "configuration": null, "component": {"version": "monitoring.coreos.com/v1", "kind": "ServiceMonitor", "schema": "{\n \"description\": \"ServiceMonitor defines monitoring for a set of services.\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Specification of desired Service selection for target discovery by Prometheus.\",\n   \"properties\": {\n    \"endpoints\": {\n     \"description\": \"A list of endpoints allowed as part of this ServiceMonitor.\",\n     \"items\": {\n      \"description\": \"Endpoint defines a scrapeable endpoint serving Prometheus metrics.\",\n      \"properties\": {\n       \"authorization\": {\n        \"description\": \"Authorization section for this endpoint\",\n        \"properties\": {\n         \"credentials\": {\n          \"description\": \"The secret's key that contains the credentials of the request\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": {\n          \"description\": \"Set the authentication type. Defaults to <PERSON><PERSON>, <PERSON> will cause an error\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"basicAuth\": {\n        \"description\": \"BasicAuth allow an endpoint to authenticate over basic authentication More info: https://prometheus.io/docs/operating/configuration/#endpoints\",\n        \"properties\": {\n         \"password\": {\n          \"description\": \"The secret in the service monitor namespace that contains the password for authentication.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\"\n         },\n         \"username\": {\n          \"description\": \"The secret in the service monitor namespace that contains the username for authentication.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"bearerTokenFile\": {\n        \"description\": \"File to read bearer token for scraping targets.\",\n        \"type\": \"string\"\n       },\n       \"bearerTokenSecret\": {\n        \"description\": \"Secret to mount to read bearer token for scraping targets. The secret needs to be in the same namespace as the service monitor and accessible by the Prometheus Operator.\",\n        \"properties\": {\n         \"key\": {\n          \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n          \"type\": \"string\"\n         },\n         \"optional\": {\n          \"description\": \"Specify whether the Secret or its key must be defined\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"key\"\n        ],\n        \"type\": \"object\"\n       },\n       \"followRedirects\": {\n        \"description\": \"FollowRedirects configures whether scrape requests follow HTTP 3xx redirects.\",\n        \"type\": \"boolean\"\n       },\n       \"honorLabels\": {\n        \"description\": \"HonorLabels chooses the metric's labels on collisions with target labels.\",\n        \"type\": \"boolean\"\n       },\n       \"honorTimestamps\": {\n        \"description\": \"HonorTimestamps controls whether Prometheus respects the timestamps present in scraped data.\",\n        \"type\": \"boolean\"\n       },\n       \"interval\": {\n        \"description\": \"Interval at which metrics should be scraped If not specified Prometheus' global scrape interval is used.\",\n        \"pattern\": \"^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n        \"type\": \"string\"\n       },\n       \"metricRelabelings\": {\n        \"description\": \"MetricRelabelConfigs to apply to samples before ingestion.\",\n        \"items\": {\n         \"description\": \"RelabelConfig allows dynamic rewriting of the label set, being applied to samples before ingestion. It defines `\\u003cmetric_relabel_configs\\u003e`-section of Prometheus configuration. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#metric_relabel_configs\",\n         \"properties\": {\n          \"action\": {\n           \"default\": \"replace\",\n           \"description\": \"Action to perform based on regex matching. Default is 'replace'\",\n           \"enum\": [\n            \"replace\",\n            \"keep\",\n            \"drop\",\n            \"hashmod\",\n            \"labelmap\",\n            \"labeldrop\",\n            \"labelkeep\"\n           ],\n           \"type\": \"string\"\n          },\n          \"modulus\": {\n           \"description\": \"Modulus to take of the hash of the source label values.\",\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"regex\": {\n           \"description\": \"Regular expression against which the extracted value is matched. Default is '(.*)'\",\n           \"type\": \"string\"\n          },\n          \"replacement\": {\n           \"description\": \"Replacement value against which a regex replace is performed if the regular expression matches. Regex capture groups are available. Default is '$1'\",\n           \"type\": \"string\"\n          },\n          \"separator\": {\n           \"description\": \"Separator placed between concatenated source label values. default is ';'.\",\n           \"type\": \"string\"\n          },\n          \"sourceLabels\": {\n           \"description\": \"The source labels select values from existing labels. Their content is concatenated using the configured separator and matched against the configured regular expression for the replace, keep, and drop actions.\",\n           \"items\": {\n            \"description\": \"LabelName is a valid Prometheus label name which may only contain ASCII letters, numbers, as well as underscores.\",\n            \"pattern\": \"^[a-zA-Z_][a-zA-Z0-9_]*$\",\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"targetLabel\": {\n           \"description\": \"Label to which the resulting value is written in a replace action. It is mandatory for replace actions. Regex capture groups are available.\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"oauth2\": {\n        \"description\": \"OAuth2 for the URL. Only valid in Prometheus versions 2.27.0 and newer.\",\n        \"properties\": {\n         \"clientId\": {\n          \"description\": \"The secret or configmap containing the OAuth2 client id\",\n          \"properties\": {\n           \"configMap\": {\n            \"description\": \"ConfigMap containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key to select.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           },\n           \"secret\": {\n            \"description\": \"Secret containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"clientSecret\": {\n          \"description\": \"The secret containing the OAuth2 client secret\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\"\n         },\n         \"endpointParams\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Parameters to append to the token URL\",\n          \"type\": \"object\"\n         },\n         \"scopes\": {\n          \"description\": \"OAuth2 scopes used for the token request\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"tokenUrl\": {\n          \"description\": \"The URL to fetch the token from\",\n          \"minLength\": 1,\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"clientId\",\n         \"clientSecret\",\n         \"tokenUrl\"\n        ],\n        \"type\": \"object\"\n       },\n       \"params\": {\n        \"additionalProperties\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"description\": \"Optional HTTP URL parameters\",\n        \"type\": \"object\"\n       },\n       \"path\": {\n        \"description\": \"HTTP path to scrape for metrics.\",\n        \"type\": \"string\"\n       },\n       \"port\": {\n        \"description\": \"Name of the service port this endpoint refers to. Mutually exclusive with targetPort.\",\n        \"type\": \"string\"\n       },\n       \"proxyUrl\": {\n        \"description\": \"ProxyURL eg http://proxyserver:2195 Directs scrapes to proxy through this endpoint.\",\n        \"type\": \"string\"\n       },\n       \"relabelings\": {\n        \"description\": \"RelabelConfigs to apply to samples before scraping. Prometheus Operator automatically adds relabelings for a few standard Kubernetes fields. The original scrape job's name is available via the `__tmp_prometheus_job_name` label. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config\",\n        \"items\": {\n         \"description\": \"RelabelConfig allows dynamic rewriting of the label set, being applied to samples before ingestion. It defines `\\u003cmetric_relabel_configs\\u003e`-section of Prometheus configuration. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#metric_relabel_configs\",\n         \"properties\": {\n          \"action\": {\n           \"default\": \"replace\",\n           \"description\": \"Action to perform based on regex matching. Default is 'replace'\",\n           \"enum\": [\n            \"replace\",\n            \"keep\",\n            \"drop\",\n            \"hashmod\",\n            \"labelmap\",\n            \"labeldrop\",\n            \"labelkeep\"\n           ],\n           \"type\": \"string\"\n          },\n          \"modulus\": {\n           \"description\": \"Modulus to take of the hash of the source label values.\",\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"regex\": {\n           \"description\": \"Regular expression against which the extracted value is matched. Default is '(.*)'\",\n           \"type\": \"string\"\n          },\n          \"replacement\": {\n           \"description\": \"Replacement value against which a regex replace is performed if the regular expression matches. Regex capture groups are available. Default is '$1'\",\n           \"type\": \"string\"\n          },\n          \"separator\": {\n           \"description\": \"Separator placed between concatenated source label values. default is ';'.\",\n           \"type\": \"string\"\n          },\n          \"sourceLabels\": {\n           \"description\": \"The source labels select values from existing labels. Their content is concatenated using the configured separator and matched against the configured regular expression for the replace, keep, and drop actions.\",\n           \"items\": {\n            \"description\": \"LabelName is a valid Prometheus label name which may only contain ASCII letters, numbers, as well as underscores.\",\n            \"pattern\": \"^[a-zA-Z_][a-zA-Z0-9_]*$\",\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"targetLabel\": {\n           \"description\": \"Label to which the resulting value is written in a replace action. It is mandatory for replace actions. Regex capture groups are available.\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"scheme\": {\n        \"description\": \"HTTP scheme to use for scraping.\",\n        \"type\": \"string\"\n       },\n       \"scrapeTimeout\": {\n        \"description\": \"Timeout after which the scrape is ended If not specified, the Prometheus global scrape timeout is used unless it is less than `Interval` in which the latter is used.\",\n        \"pattern\": \"^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n        \"type\": \"string\"\n       },\n       \"targetPort\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"description\": \"Name or number of the target port of the Pod behind the Service, the port must be specified with container port property. Mutually exclusive with port.\",\n        \"x-kubernetes-int-or-string\": true\n       },\n       \"tlsConfig\": {\n        \"description\": \"TLS configuration to use when scraping the endpoint\",\n        \"properties\": {\n         \"ca\": {\n          \"description\": \"Struct containing the CA cert to use for the targets.\",\n          \"properties\": {\n           \"configMap\": {\n            \"description\": \"ConfigMap containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key to select.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           },\n           \"secret\": {\n            \"description\": \"Secret containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"caFile\": {\n          \"description\": \"Path to the CA cert in the Prometheus container to use for the targets.\",\n          \"type\": \"string\"\n         },\n         \"cert\": {\n          \"description\": \"Struct containing the client cert file for the targets.\",\n          \"properties\": {\n           \"configMap\": {\n            \"description\": \"ConfigMap containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key to select.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           },\n           \"secret\": {\n            \"description\": \"Secret containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"certFile\": {\n          \"description\": \"Path to the client cert file in the Prometheus container for the targets.\",\n          \"type\": \"string\"\n         },\n         \"insecureSkipVerify\": {\n          \"description\": \"Disable target certificate validation.\",\n          \"type\": \"boolean\"\n         },\n         \"keyFile\": {\n          \"description\": \"Path to the client key file in the Prometheus container for the targets.\",\n          \"type\": \"string\"\n         },\n         \"keySecret\": {\n          \"description\": \"Secret containing the client key file for the targets.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\"\n         },\n         \"serverName\": {\n          \"description\": \"Used to verify the hostname for the targets.\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"jobLabel\": {\n     \"description\": \"Chooses the label of the Kubernetes `Endpoints`. Its value will be used for the `job`-label's value of the created metrics. \\n Default \\u0026 fallback value: the name of the respective Kubernetes `Endpoint`.\",\n     \"type\": \"string\"\n    },\n    \"labelLimit\": {\n     \"description\": \"Per-scrape limit on number of labels that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"labelNameLengthLimit\": {\n     \"description\": \"Per-scrape limit on length of labels name that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"labelValueLengthLimit\": {\n     \"description\": \"Per-scrape limit on length of labels value that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"namespaceSelector\": {\n     \"description\": \"Selector to select which namespaces the Kubernetes Endpoints objects are discovered from.\",\n     \"properties\": {\n      \"any\": {\n       \"description\": \"Boolean describing whether all namespaces are selected in contrast to a list restricting them.\",\n       \"type\": \"boolean\"\n      },\n      \"matchNames\": {\n       \"description\": \"List of namespace names to select from.\",\n       \"items\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"podTargetLabels\": {\n     \"description\": \"PodTargetLabels transfers labels on the Kubernetes `Pod` onto the created metrics.\",\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"sampleLimit\": {\n     \"description\": \"SampleLimit defines per-scrape limit on number of scraped samples that will be accepted.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"selector\": {\n     \"description\": \"Selector to select Endpoints objects.\",\n     \"properties\": {\n      \"matchExpressions\": {\n       \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n       \"items\": {\n        \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\n        \"properties\": {\n         \"key\": {\n          \"description\": \"key is the label key that the selector applies to.\",\n          \"type\": \"string\"\n         },\n         \"operator\": {\n          \"description\": \"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\n          \"type\": \"string\"\n         },\n         \"values\": {\n          \"description\": \"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"required\": [\n         \"key\",\n         \"operator\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"matchLabels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"targetLabels\": {\n     \"description\": \"TargetLabels transfers labels from the Kubernetes `Service` onto the created metrics.\",\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"targetLimit\": {\n     \"description\": \"TargetLimit defines a limit on the number of scraped targets that will be accepted.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    }\n   },\n   \"required\": [\n    \"endpoints\",\n    \"selector\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"required\": [\n  \"spec\"\n ],\n \"title\": \"Service Monitor\",\n \"type\": \"object\"\n}"}}