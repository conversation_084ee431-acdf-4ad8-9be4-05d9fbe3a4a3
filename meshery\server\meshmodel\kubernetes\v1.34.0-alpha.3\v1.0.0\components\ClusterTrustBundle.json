{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Cluster Trust Bundle", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubernetes", "displayName": "Kubernetes", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Orchestration & Management"}, "subCategory": "Scheduling & Orchestration", "metadata": {"isAnnotation": false, "primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-0.17 0.08 230.10 223.35\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}.cls-2{fill:#326ce5}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.466a3.59 3.59 0 0 0-.855-.065 3.685 3.685 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53 43.53 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.656 3.656 0 0 0-.733.065l-.037-.045-21.785 3.698a43.695 43.695 0 0 0 17.54 21.946l8.442-20.4-.066-.08a3.683 3.683 0 0 0 .207-2.29zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.2l-10.738-19.406zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.039a3.725 3.725 0 0 0 5.913 2.84l.065.027 18.036-12.788a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.921a3.72 3.72 0 0 0 5.904-2.85l.092-.043 1.253-22.14a44.682 44.682 0 0 0-4.501.776 43.467 43.467 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.729a3.72 3.72 0 0 0 1.462-6.396l.018-.088-16.574-14.824a43.454 43.454 0 0 0-6.168 27.51l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.934 1.514-6.581-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.167l-17.422-75.675a13.602 13.602 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0L38.76 48.437a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.506 13.506 0 0 0 .491 1.718 13.1 13.1 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.423 13.423 0 0 0 2.574 2.133 13.924 13.924 0 0 0 3.857 1.677 13.298 13.298 0 0 0 3.43.473h.759l77.504-.018a12.993 12.993 0 0 0 1.41-.083 13.47 13.47 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.941 13.941 0 0 0 1.479-.822 13.303 13.303 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67 13.67 0 0 0 .5-1.724 13.282 13.282 0 0 0-.004-5.81zm-73.147 29.432a14.516 14.516 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.372 39.372 0 0 0 3.742 6.7 35.06 35.06 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.275 35.275 0 0 1-1.225-3.868 39.305 39.305 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.15-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.73a3.429 3.429 0 0 0-1.819.887 29.517 29.517 0 0 0-3.268 7.582 34.9 34.9 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.177 35.177 0 0 1 2.262-3.373 41.228 41.228 0 0 0 3.82-6.866 4.188 4.188 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68 4.68 0 0 0-2.428-.855 39.524 39.524 0 0 0-7.356 2.165 35.589 35.589 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.605.605 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.388 39.388 0 0 0 7.564-1.242 5.835 5.835 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.366 39.366 0 0 0-6.277-4.41 35.25 35.25 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.253 35.253 0 0 1 2.78 2.95 39.383 39.383 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082A54.289 54.289 0 0 1 102.86 61.89a55.052 55.052 0 0 1 7.63-1.173l.1-1.784a4.6 4.6 0 0 0 1.37-2.184 39.476 39.476 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.08-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.86.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.533 39.533 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.69 4.69 0 0 0 2.56-.278 39.532 39.532 0 0 0 5.69-5.148 35.004 35.004 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.095 35.095 0 0 1-3.507 2.046 39.495 39.495 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.745 4.745 0 0 0 1.82 1.83 39.464 39.464 0 0 0 7.568 1.246 35.64 35.64 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.03-.348-.075-.785-.151-1.09-.231a35.14 35.14 0 0 1-3.785-1.462 39.477 39.477 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.92 54.92 0 0 1-24.328 30.605z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M225.407 135.107L206.4 52.547a14.838 14.838 0 0 0-7.958-9.9l-76.935-36.73a14.825 14.825 0 0 0-12.771 0L31.808 42.669a14.838 14.838 0 0 0-7.961 9.895L4.873 135.129a14.668 14.668 0 0 0 1.995 11.185c.261.4.538.788.838 1.162l53.246 66.205a14.98 14.98 0 0 0 11.499 5.487l85.387-.02a14.986 14.986 0 0 0 11.5-5.48l53.227-66.211a14.72 14.72 0 0 0 2.842-12.347zm-9.197 3.866a13.677 13.677 0 0 1-.498 1.723 13.184 13.184 0 0 1-2.11 3.786l-47.299 58.838-1.486 1.852a13.305 13.305 0 0 1-3.24 2.865 13.945 13.945 0 0 1-1.474.822q-.513.237-1.045.43a13.873 13.873 0 0 1-1.383.445 13.473 13.473 0 0 1-1.989.379 12.988 12.988 0 0 1-1.41.082l-77.504.018h-.76a13.298 13.298 0 0 1-3.429-.472 13.925 13.925 0 0 1-3.855-1.679 13.424 13.424 0 0 1-2.576-2.132c-.233-.246-.468-.487-.68-.75l-48.805-60.679q-.408-.514-.765-1.066a13.102 13.102 0 0 1-1.343-2.726 13.505 13.505 0 0 1-.491-1.719 13.315 13.315 0 0 1 .004-5.809l17.394-75.675a13.598 13.598 0 0 1 7.295-9.07l70.508-33.685a13.589 13.589 0 0 1 11.705 0l70.519 33.67a13.602 13.602 0 0 1 7.293 9.073l17.422 75.674a13.282 13.282 0 0 1 .002 5.807z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M185.814 127.106c-.36-.083-.874-.225-1.227-.29a35.642 35.642 0 0 0-4.046-.326 39.464 39.464 0 0 1-7.57-1.242 4.745 4.745 0 0 1-1.82-1.832l-1.704-.496a54.837 54.837 0 0 0-8.79-37.983c.436-.396 1.262-1.127 1.495-1.342a3.338 3.338 0 0 1 .792-2.39 39.495 39.495 0 0 1 6.277-4.41 35.095 35.095 0 0 0 3.507-2.046c.272-.202.644-.522.929-.75a4.242 4.242 0 1 0-5.256-6.6c-.288.227-.68.525-.936.747a35.004 35.004 0 0 0-2.789 2.95 39.533 39.533 0 0 1-5.69 5.148 4.69 4.69 0 0 1-2.56.278l-1.606 1.147a55.309 55.309 0 0 0-35.032-16.927c-.039-.561-.087-1.577-.102-1.884a3.344 3.344 0 0 1-1.377-2.11 39.533 39.533 0 0 1 .478-7.656 35.112 35.112 0 0 0 .575-4.012c.013-.34-.007-.834-.007-1.201a4.242 4.242 0 1 0-8.441.004c0 .04.009.078.01.118-.004.35-.021.774-.01 1.08a35.476 35.476 0 0 0 .576 4.015 39.475 39.475 0 0 1 .47 7.654 4.601 4.601 0 0 1-1.37 2.182l-.1 1.786a55.052 55.052 0 0 0-7.63 1.173 54.289 54.289 0 0 0-27.574 15.754q-.77-.531-1.526-1.082a3.333 3.333 0 0 1-2.506-.243 39.383 39.383 0 0 1-5.69-5.141 35.255 35.255 0 0 0-2.777-2.95c-.257-.22-.65-.52-.938-.75a5.03 5.03 0 0 0-2.942-1.11 4.066 4.066 0 0 0-3.363 1.49 4.473 4.473 0 0 0 1.038 6.222l.065.046c.276.226.616.515.872.702a35.256 35.256 0 0 0 3.499 2.048 39.367 39.367 0 0 1 6.276 4.412 4.69 4.69 0 0 1 .849 2.434l1.351 1.208a54.613 54.613 0 0 0-8.611 38.073l-1.767.514a5.835 5.835 0 0 1-1.814 1.827 39.39 39.39 0 0 1-7.565 1.247 35.266 35.266 0 0 0-4.049.326c-.324.06-.774.174-1.127.262l-.037.008-.06.018a4.236 4.236 0 1 0 1.875 8.224l.063-.01c.028-.006.052-.02.08-.025.348-.08.786-.163 1.092-.246a35.59 35.59 0 0 0 3.786-1.451 39.527 39.527 0 0 1 7.358-2.165 4.68 4.68 0 0 1 2.426.857l1.84-.315a54.922 54.922 0 0 0 24.34 30.387l-.769 1.84a4.188 4.188 0 0 1 .377 2.387 41.228 41.228 0 0 1-3.82 6.864 35.183 35.183 0 0 0-2.263 3.372c-.168.318-.381.805-.542 1.138a4.238 4.238 0 1 0 7.621 3.616l.007-.008v-.01c.16-.33.387-.763.522-1.072a34.903 34.903 0 0 0 1.218-3.868 29.517 29.517 0 0 1 3.268-7.582 3.43 3.43 0 0 1 1.819-.888l.957-1.73a54.617 54.617 0 0 0 38.946.099c.268.478.768 1.392.9 1.623a3.335 3.335 0 0 1 2.155 1.31 39.306 39.306 0 0 1 2.898 7.096 35.275 35.275 0 0 0 1.225 3.868c.137.312.36.75.522 1.082a4.24 4.24 0 1 0 7.63-3.607c-.161-.333-.383-.82-.55-1.136a35.06 35.06 0 0 0-2.263-3.364 39.372 39.372 0 0 1-3.742-6.7 3.314 3.314 0 0 1 .324-2.49 14.519 14.519 0 0 1-.703-1.703 54.92 54.92 0 0 0 24.328-30.605c.546.087 1.497.253 1.806.316a3.337 3.337 0 0 1 2.36-.877 39.476 39.476 0 0 1 7.36 2.165 35.135 35.135 0 0 0 3.788 1.462c.305.08.74.156 1.09.233.029.008.055.02.083.028l.06.009a4.236 4.236 0 1 0 1.878-8.224zm-40.1-42.987l-18.037 12.787-.063-.03a3.723 3.723 0 0 1-5.913-2.838l-.02-.01-1.253-22.103a43.85 43.85 0 0 1 25.285 12.194zm-33.978 24.228h6.788l4.22 5.276-1.513 6.58-6.096 2.934-6.114-2.94-1.516-6.583zm-6.386-35.648a44.672 44.672 0 0 1 4.503-.774l-1.255 22.137-.092.044a3.72 3.72 0 0 1-5.904 2.852l-.035.02-18.154-12.872a43.467 43.467 0 0 1 20.937-11.407zm-27.52 19.68l16.574 14.824-.018.09a3.72 3.72 0 0 1-1.462 6.395l-.017.072-21.245 6.13a43.454 43.454 0 0 1 6.168-27.51zm22.191 39.38l-8.441 20.397a43.696 43.696 0 0 1-17.536-21.948l21.783-3.7.037.049a3.655 3.655 0 0 1 .73-.065 3.72 3.72 0 0 1 3.364 5.185zm24.916 26.23a43.637 43.637 0 0 1-23.815-1.223l10.713-19.372h.018a3.725 3.725 0 0 1 6.557-.006h.08l10.74 19.404q-2.091.698-4.293 1.199zm13.841-5.751l-8.528-20.605.026-.037a3.725 3.725 0 0 1 1.803-4.823 3.685 3.685 0 0 1 1.425-.37 3.59 3.59 0 0 1 .855.063l.037-.046 21.977 3.714a43.53 43.53 0 0 1-17.595 22.105zm19.903-32.42l-21.352-6.15-.02-.09a3.725 3.725 0 0 1-1.46-6.395l-.008-.043 16.482-14.751a44.279 44.279 0 0 1 6.357 27.43z\" class=\"cls-1\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"9.70 9.20 210.86 204.86\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.46551a3.59023 3.59023 0 0 0-.855-.065 3.68515 3.68515 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53012 43.53012 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.6556 3.6556 0 0 0-.733.065l-.037-.045-21.785 3.698a43.69506 43.69506 0 0 0 17.54 21.946l8.442-20.399-.066-.08a3.68318 3.68318 0 0 0 .207-2.291zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.199l-10.738-19.407zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.0385a3.725 3.725 0 0 0 5.913 2.84l.065.028 18.036-12.789a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.922a3.72 3.72 0 0 0 5.904-2.85l.092-.044 1.253-22.139a44.68209 44.68209 0 0 0-4.501.775 43.4669 43.4669 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.728a3.72 3.72 0 0 0 1.462-6.396l.018-.087-16.574-14.825a43.454 43.454 0 0 0-6.168 27.511l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.933 1.514-6.582-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.16651l-17.422-75.675a13.60207 13.60207 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0l-70.507 33.688a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.50607 13.50607 0 0 0 .491 1.718 13.0998 13.0998 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.42334 13.42334 0 0 0 2.574 2.133 13.9237 13.9237 0 0 0 3.857 1.677 13.29785 13.29785 0 0 0 3.43.473h.759l77.504-.018a12.99345 12.99345 0 0 0 1.41-.083 13.46921 13.46921 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.94127 13.94127 0 0 0 1.479-.822 13.30347 13.30347 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67036 13.67036 0 0 0 .5-1.724 13.28215 13.28215 0 0 0-.004-5.809zm-73.147 29.432a14.51575 14.51575 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.37244 39.37244 0 0 0 3.742 6.7 35.06044 35.06044 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.27528 35.27528 0 0 1-1.225-3.868 39.3046 39.3046 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.149-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.731a3.429 3.429 0 0 0-1.819.886 29.51728 29.51728 0 0 0-3.268 7.582 34.89931 34.89931 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.17706 35.17706 0 0 1 2.262-3.373 41.22786 41.22786 0 0 0 3.82-6.866 4.18792 4.18792 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68007 4.68007 0 0 0-2.428-.855 39.52352 39.52352 0 0 0-7.356 2.165 35.58886 35.58886 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.60518.60518 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.38835 39.38835 0 0 0 7.564-1.242 5.83506 5.83506 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.36558 39.36558 0 0 0-6.277-4.41 35.2503 35.2503 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.25285 35.25285 0 0 1 2.78 2.95 39.38314 39.38314 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082a54.28892 54.28892 0 0 1 27.577-15.754 55.05181 55.05181 0 0 1 7.63-1.173l.1-1.784a4.6001 4.6001 0 0 0 1.37-2.184 39.47551 39.47551 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.079-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.861.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.53346 39.53346 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.6901 4.6901 0 0 0 2.56-.278 39.53152 39.53152 0 0 0 5.69-5.148 35.00382 35.00382 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.09523 35.09523 0 0 1-3.507 2.046 39.49476 39.49476 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.7449 4.7449 0 0 0 1.82 1.831 39.46448 39.46448 0 0 0 7.568 1.245 35.64041 35.64041 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.029-.348-.076-.785-.152-1.09-.232a35.1407 35.1407 0 0 1-3.785-1.462 39.47672 39.47672 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.91994 54.91994 0 0 1-24.328 30.605z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "svgColor": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<mask id=\"path-1-inside-1_26091_16410\" fill=\"white\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.6757 13.6635H20.1391H63.0499V37.4639L66.5134 36.8172V12.2C66.5134 11.0954 65.618 10.2 64.5134 10.2H23.6026H20.1391V8.42696V6.73651V4.96348H23.6026H68.2628H71.7263V8.42696V36.7275L75.1898 37.3338V4.96348V3.5C75.1898 2.39543 74.2944 1.5 73.1898 1.5H71.7263H20.1391H18.6757C17.5711 1.5 16.6757 2.39543 16.6757 3.5V4.96348V6.73651V10.2H13.2122H9.99927C8.8947 10.2 7.99927 11.0954 7.99927 12.2V86.5C7.99927 87.6046 8.8947 88.5 9.99927 88.5H64.5134C65.618 88.5 66.5134 87.6046 66.5134 86.5V83.2635V79.8H69.9769H71.7263H73.1898C74.2943 79.8 75.1898 78.9046 75.1898 77.8V76.3365V67.0589C75.149 67.0812 75.1082 67.1033 75.0672 67.1253L71.7263 68.9187V72.873V76.3365H69.9769H68.2628H66.5134V72.873V68.7429L63.5002 67.1253C63.3485 67.0439 63.1984 66.9604 63.0499 66.875V76.3365V79.8V85.0365H11.4627V13.6635H16.6757Z\"/>\n</mask>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.6757 13.6635H20.1391H63.0499V37.4639L66.5134 36.8172V12.2C66.5134 11.0954 65.618 10.2 64.5134 10.2H23.6026H20.1391V8.42696V6.73651V4.96348H23.6026H68.2628H71.7263V8.42696V36.7275L75.1898 37.3338V4.96348V3.5C75.1898 2.39543 74.2944 1.5 73.1898 1.5H71.7263H20.1391H18.6757C17.5711 1.5 16.6757 2.39543 16.6757 3.5V4.96348V6.73651V10.2H13.2122H9.99927C8.8947 10.2 7.99927 11.0954 7.99927 12.2V86.5C7.99927 87.6046 8.8947 88.5 9.99927 88.5H64.5134C65.618 88.5 66.5134 87.6046 66.5134 86.5V83.2635V79.8H69.9769H71.7263H73.1898C74.2943 79.8 75.1898 78.9046 75.1898 77.8V76.3365V67.0589C75.149 67.0812 75.1082 67.1033 75.0672 67.1253L71.7263 68.9187V72.873V76.3365H69.9769H68.2628H66.5134V72.873V68.7429L63.5002 67.1253C63.3485 67.0439 63.1984 66.9604 63.0499 66.875V76.3365V79.8V85.0365H11.4627V13.6635H16.6757Z\" fill=\"white\"/>\n<path d=\"M63.0499 13.6635H66.5134V10.2H63.0499V13.6635ZM63.0499 37.4639H59.5864V41.6339L63.6856 40.8686L63.0499 37.4639ZM66.5134 36.8172L67.1491 40.2219L69.9769 39.6939V36.8172H66.5134ZM20.1391 10.2H16.6757V13.6635H20.1391V10.2ZM20.1391 4.96348V1.5H16.6757V4.96348H20.1391ZM71.7263 4.96348H75.1898V1.5H71.7263V4.96348ZM71.7263 36.7275H68.2628V39.6374L71.1291 40.1392L71.7263 36.7275ZM75.1898 37.3338L74.5926 40.7454L78.6533 41.4562V37.3338H75.1898ZM16.6757 10.2V13.6635H20.1391V10.2H16.6757ZM66.5134 79.8V76.3365H63.0499V79.8H66.5134ZM75.1898 67.0589H78.6533V61.2152L73.527 64.0206L75.1898 67.0589ZM75.0672 67.1253L73.4292 64.0737L73.4291 64.0737L75.0672 67.1253ZM71.7263 68.9187L70.0882 65.8671L68.2628 66.847V68.9187H71.7263ZM71.7263 76.3365V79.8H75.1898V76.3365H71.7263ZM66.5134 76.3365H63.0499V79.8H66.5134V76.3365ZM66.5134 68.7429H69.9769V66.6711L68.1515 65.6913L66.5134 68.7429ZM63.5002 67.1253L65.1383 64.0737L65.1382 64.0737L63.5002 67.1253ZM63.0499 66.875L64.7777 63.8732L59.5864 60.8852V66.875H63.0499ZM63.0499 85.0365V88.5H66.5134V85.0365H63.0499ZM11.4627 85.0365H7.99927V88.5H11.4627V85.0365ZM11.4627 13.6635V10.2H7.99927V13.6635H11.4627ZM20.1391 10.2H16.6757V17.127H20.1391V10.2ZM63.0499 10.2H20.1391V17.127H63.0499V10.2ZM66.5134 37.4639V13.6635H59.5864V37.4639H66.5134ZM65.8777 33.4126L62.4142 34.0593L63.6856 40.8686L67.1491 40.2219L65.8777 33.4126ZM69.9769 36.8172V12.2H63.0499V36.8172H69.9769ZM69.9769 12.2C69.9769 9.1826 67.5308 6.73651 64.5134 6.73651V13.6635C63.7051 13.6635 63.0499 13.0082 63.0499 12.2H69.9769ZM64.5134 6.73651H23.6026V13.6635H64.5134V6.73651ZM23.6026 6.73651H20.1391V13.6635H23.6026V6.73651ZM23.6026 10.2V8.42696H16.6757V10.2H23.6026ZM23.6026 8.42696V6.73651H16.6757V8.42696H23.6026ZM23.6026 6.73651V4.96348H16.6757V6.73651H23.6026ZM20.1391 8.42696H23.6026V1.5H20.1391V8.42696ZM23.6026 8.42696H68.2628V1.5H23.6026V8.42696ZM68.2628 8.42696H71.7263V1.5H68.2628V8.42696ZM68.2628 4.96348V8.42696H75.1898V4.96348H68.2628ZM68.2628 8.42696V36.7275H75.1898V8.42696H68.2628ZM75.787 33.9222L72.3235 33.3159L71.1291 40.1392L74.5926 40.7454L75.787 33.9222ZM78.6533 37.3338V4.96348H71.7263V37.3338H78.6533ZM78.6533 4.96348V3.5H71.7263V4.96348H78.6533ZM78.6533 3.5C78.6533 0.482611 76.2072 -1.96348 73.1898 -1.96348V4.96348C72.3815 4.96348 71.7263 4.30825 71.7263 3.5H78.6533ZM73.1898 -1.96348H71.7263V4.96348H73.1898V-1.96348ZM71.7263 -1.96348H20.1391V4.96348H71.7263V-1.96348ZM20.1391 -1.96348H18.6757V4.96348H20.1391V-1.96348ZM18.6757 -1.96348C15.6583 -1.96348 13.2122 0.482606 13.2122 3.5H20.1391C20.1391 4.30826 19.4839 4.96348 18.6757 4.96348V-1.96348ZM13.2122 3.5V4.96348H20.1391V3.5H13.2122ZM13.2122 4.96348V6.73651H20.1391V4.96348H13.2122ZM13.2122 6.73651V10.2H20.1391V6.73651H13.2122ZM16.6757 6.73651H13.2122V13.6635H16.6757V6.73651ZM13.2122 6.73651H9.99927V13.6635H13.2122V6.73651ZM9.99927 6.73651C6.98187 6.73651 4.53579 9.1826 4.53579 12.2H11.4627C11.4627 13.0082 10.8075 13.6635 9.99927 13.6635V6.73651ZM4.53579 12.2V86.5H11.4627V12.2H4.53579ZM4.53579 86.5C4.53579 89.5174 6.98187 91.9635 9.99927 91.9635V85.0365C10.8075 85.0365 11.4627 85.6917 11.4627 86.5H4.53579ZM9.99927 91.9635H64.5134V85.0365H9.99927V91.9635ZM64.5134 91.9635C67.5308 91.9635 69.9769 89.5174 69.9769 86.5H63.0499C63.0499 85.6917 63.7051 85.0365 64.5134 85.0365V91.9635ZM69.9769 86.5V83.2635H63.0499V86.5H69.9769ZM69.9769 83.2635V79.8H63.0499V83.2635H69.9769ZM66.5134 83.2635H69.9769V76.3365H66.5134V83.2635ZM69.9769 83.2635H71.7263V76.3365H69.9769V83.2635ZM71.7263 83.2635H73.1898V76.3365H71.7263V83.2635ZM73.1898 83.2635C76.2072 83.2635 78.6533 80.8174 78.6533 77.8H71.7263C71.7263 76.9917 72.3815 76.3365 73.1898 76.3365V83.2635ZM78.6533 77.8V76.3365H71.7263V77.8H78.6533ZM78.6533 76.3365V67.0589H71.7263V76.3365H78.6533ZM76.7052 70.177C76.7545 70.1505 76.8036 70.1239 76.8525 70.0971L73.527 64.0206C73.4945 64.0384 73.4619 64.0561 73.4292 64.0737L76.7052 70.177ZM73.3644 71.9703L76.7053 70.1769L73.4291 64.0737L70.0882 65.8671L73.3644 71.9703ZM68.2628 68.9187V72.873H75.1898V68.9187H68.2628ZM68.2628 72.873V76.3365H75.1898V72.873H68.2628ZM71.7263 72.873H69.9769V79.8H71.7263V72.873ZM69.9769 72.873H68.2628V79.8H69.9769V72.873ZM68.2628 72.873H66.5134V79.8H68.2628V72.873ZM69.9769 76.3365V72.873H63.0499V76.3365H69.9769ZM69.9769 72.873V68.7429H63.0499V72.873H69.9769ZM61.862 70.1769L64.8753 71.7944L68.1515 65.6913L65.1383 64.0737L61.862 70.1769ZM61.3221 69.8767C61.5002 69.9792 61.6802 70.0793 61.8621 70.1769L65.1382 64.0737C65.0169 64.0086 64.8967 63.9417 64.7777 63.8732L61.3221 69.8767ZM66.5134 76.3365V66.875H59.5864V76.3365H66.5134ZM66.5134 79.8V76.3365H59.5864V79.8H66.5134ZM66.5134 85.0365V79.8H59.5864V85.0365H66.5134ZM11.4627 88.5H63.0499V81.573H11.4627V88.5ZM7.99927 13.6635V85.0365H14.9262V13.6635H7.99927ZM16.6757 10.2H11.4627V17.127H16.6757V10.2Z\" fill=\"#326CE5\" mask=\"url(#path-1-inside-1_26091_16410)\"/>\n<path d=\"M69.5679 38.9766L59.4791 40.8603C58.5325 41.0371 57.8462 41.8633 57.8462 42.8263V51.5197C57.8462 56.6865 60.6865 61.4349 65.2389 63.8787L68.9776 65.8857C69.5683 66.2028 70.2787 66.2028 70.8695 65.8857L74.6082 63.8787C79.1605 61.4349 82.0008 56.6865 82.0008 51.5197V42.7046C82.0008 41.733 81.3027 40.902 80.3457 40.7345L70.2798 38.9725C70.0442 38.9313 69.8031 38.9326 69.5679 38.9766Z\" fill=\"#326CE5\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<mask id=\"path-1-inside-1_26091_16586\" fill=\"white\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.6757 13.6635H20.1391H63.0499V37.4639L66.5134 36.8172V12.2C66.5134 11.0954 65.618 10.2 64.5134 10.2H23.6026H20.1391V8.42696V6.73651V4.96348H23.6026H68.2628H71.7263V8.42696V36.7275L75.1898 37.3338V4.96348V3.5C75.1898 2.39543 74.2944 1.5 73.1898 1.5H71.7263H20.1391H18.6757C17.5711 1.5 16.6757 2.39543 16.6757 3.5V4.96348V6.73651V10.2H13.2122H9.99927C8.8947 10.2 7.99927 11.0954 7.99927 12.2V86.5C7.99927 87.6046 8.8947 88.5 9.99927 88.5H64.5134C65.618 88.5 66.5134 87.6046 66.5134 86.5V83.2635V79.8H69.9769H71.7263H73.1898C74.2943 79.8 75.1898 78.9046 75.1898 77.8V76.3365V67.0589C75.149 67.0812 75.1082 67.1033 75.0672 67.1253L71.7263 68.9187V72.873V76.3365H69.9769H68.2628H66.5134V72.873V68.7429L63.5002 67.1253C63.3485 67.0439 63.1984 66.9604 63.0499 66.875V76.3365V79.8V85.0365H11.4627V13.6635H16.6757Z\"/>\n</mask>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.6757 13.6635H20.1391H63.0499V37.4639L66.5134 36.8172V12.2C66.5134 11.0954 65.618 10.2 64.5134 10.2H23.6026H20.1391V8.42696V6.73651V4.96348H23.6026H68.2628H71.7263V8.42696V36.7275L75.1898 37.3338V4.96348V3.5C75.1898 2.39543 74.2944 1.5 73.1898 1.5H71.7263H20.1391H18.6757C17.5711 1.5 16.6757 2.39543 16.6757 3.5V4.96348V6.73651V10.2H13.2122H9.99927C8.8947 10.2 7.99927 11.0954 7.99927 12.2V86.5C7.99927 87.6046 8.8947 88.5 9.99927 88.5H64.5134C65.618 88.5 66.5134 87.6046 66.5134 86.5V83.2635V79.8H69.9769H71.7263H73.1898C74.2943 79.8 75.1898 78.9046 75.1898 77.8V76.3365V67.0589C75.149 67.0812 75.1082 67.1033 75.0672 67.1253L71.7263 68.9187V72.873V76.3365H69.9769H68.2628H66.5134V72.873V68.7429L63.5002 67.1253C63.3485 67.0439 63.1984 66.9604 63.0499 66.875V76.3365V79.8V85.0365H11.4627V13.6635H16.6757Z\" fill=\"white\"/>\n<path d=\"M63.0499 13.6635H66.5134V10.2H63.0499V13.6635ZM63.0499 37.4639H59.5864V41.6339L63.6856 40.8686L63.0499 37.4639ZM66.5134 36.8172L67.1491 40.2219L69.9769 39.6939V36.8172H66.5134ZM20.1391 10.2H16.6757V13.6635H20.1391V10.2ZM20.1391 4.96348V1.5H16.6757V4.96348H20.1391ZM71.7263 4.96348H75.1898V1.5H71.7263V4.96348ZM71.7263 36.7275H68.2628V39.6374L71.1291 40.1392L71.7263 36.7275ZM75.1898 37.3338L74.5926 40.7454L78.6533 41.4562V37.3338H75.1898ZM16.6757 10.2V13.6635H20.1391V10.2H16.6757ZM66.5134 79.8V76.3365H63.0499V79.8H66.5134ZM75.1898 67.0589H78.6533V61.2152L73.527 64.0206L75.1898 67.0589ZM75.0672 67.1253L73.4292 64.0737L73.4291 64.0737L75.0672 67.1253ZM71.7263 68.9187L70.0882 65.8671L68.2628 66.847V68.9187H71.7263ZM71.7263 76.3365V79.8H75.1898V76.3365H71.7263ZM66.5134 76.3365H63.0499V79.8H66.5134V76.3365ZM66.5134 68.7429H69.9769V66.6711L68.1515 65.6913L66.5134 68.7429ZM63.5002 67.1253L65.1383 64.0737L65.1382 64.0737L63.5002 67.1253ZM63.0499 66.875L64.7777 63.8732L59.5864 60.8852V66.875H63.0499ZM63.0499 85.0365V88.5H66.5134V85.0365H63.0499ZM11.4627 85.0365H7.99927V88.5H11.4627V85.0365ZM11.4627 13.6635V10.2H7.99927V13.6635H11.4627ZM20.1391 10.2H16.6757V17.127H20.1391V10.2ZM63.0499 10.2H20.1391V17.127H63.0499V10.2ZM66.5134 37.4639V13.6635H59.5864V37.4639H66.5134ZM65.8777 33.4126L62.4142 34.0593L63.6856 40.8686L67.1491 40.2219L65.8777 33.4126ZM69.9769 36.8172V12.2H63.0499V36.8172H69.9769ZM69.9769 12.2C69.9769 9.1826 67.5308 6.73651 64.5134 6.73651V13.6635C63.7051 13.6635 63.0499 13.0082 63.0499 12.2H69.9769ZM64.5134 6.73651H23.6026V13.6635H64.5134V6.73651ZM23.6026 6.73651H20.1391V13.6635H23.6026V6.73651ZM23.6026 10.2V8.42696H16.6757V10.2H23.6026ZM23.6026 8.42696V6.73651H16.6757V8.42696H23.6026ZM23.6026 6.73651V4.96348H16.6757V6.73651H23.6026ZM20.1391 8.42696H23.6026V1.5H20.1391V8.42696ZM23.6026 8.42696H68.2628V1.5H23.6026V8.42696ZM68.2628 8.42696H71.7263V1.5H68.2628V8.42696ZM68.2628 4.96348V8.42696H75.1898V4.96348H68.2628ZM68.2628 8.42696V36.7275H75.1898V8.42696H68.2628ZM75.787 33.9222L72.3235 33.3159L71.1291 40.1392L74.5926 40.7454L75.787 33.9222ZM78.6533 37.3338V4.96348H71.7263V37.3338H78.6533ZM78.6533 4.96348V3.5H71.7263V4.96348H78.6533ZM78.6533 3.5C78.6533 0.482611 76.2072 -1.96348 73.1898 -1.96348V4.96348C72.3815 4.96348 71.7263 4.30825 71.7263 3.5H78.6533ZM73.1898 -1.96348H71.7263V4.96348H73.1898V-1.96348ZM71.7263 -1.96348H20.1391V4.96348H71.7263V-1.96348ZM20.1391 -1.96348H18.6757V4.96348H20.1391V-1.96348ZM18.6757 -1.96348C15.6583 -1.96348 13.2122 0.482606 13.2122 3.5H20.1391C20.1391 4.30826 19.4839 4.96348 18.6757 4.96348V-1.96348ZM13.2122 3.5V4.96348H20.1391V3.5H13.2122ZM13.2122 4.96348V6.73651H20.1391V4.96348H13.2122ZM13.2122 6.73651V10.2H20.1391V6.73651H13.2122ZM16.6757 6.73651H13.2122V13.6635H16.6757V6.73651ZM13.2122 6.73651H9.99927V13.6635H13.2122V6.73651ZM9.99927 6.73651C6.98187 6.73651 4.53579 9.1826 4.53579 12.2H11.4627C11.4627 13.0082 10.8075 13.6635 9.99927 13.6635V6.73651ZM4.53579 12.2V86.5H11.4627V12.2H4.53579ZM4.53579 86.5C4.53579 89.5174 6.98187 91.9635 9.99927 91.9635V85.0365C10.8075 85.0365 11.4627 85.6917 11.4627 86.5H4.53579ZM9.99927 91.9635H64.5134V85.0365H9.99927V91.9635ZM64.5134 91.9635C67.5308 91.9635 69.9769 89.5174 69.9769 86.5H63.0499C63.0499 85.6917 63.7051 85.0365 64.5134 85.0365V91.9635ZM69.9769 86.5V83.2635H63.0499V86.5H69.9769ZM69.9769 83.2635V79.8H63.0499V83.2635H69.9769ZM66.5134 83.2635H69.9769V76.3365H66.5134V83.2635ZM69.9769 83.2635H71.7263V76.3365H69.9769V83.2635ZM71.7263 83.2635H73.1898V76.3365H71.7263V83.2635ZM73.1898 83.2635C76.2072 83.2635 78.6533 80.8174 78.6533 77.8H71.7263C71.7263 76.9917 72.3815 76.3365 73.1898 76.3365V83.2635ZM78.6533 77.8V76.3365H71.7263V77.8H78.6533ZM78.6533 76.3365V67.0589H71.7263V76.3365H78.6533ZM76.7052 70.177C76.7545 70.1505 76.8036 70.1239 76.8525 70.0971L73.527 64.0206C73.4945 64.0384 73.4619 64.0561 73.4292 64.0737L76.7052 70.177ZM73.3644 71.9703L76.7053 70.1769L73.4291 64.0737L70.0882 65.8671L73.3644 71.9703ZM68.2628 68.9187V72.873H75.1898V68.9187H68.2628ZM68.2628 72.873V76.3365H75.1898V72.873H68.2628ZM71.7263 72.873H69.9769V79.8H71.7263V72.873ZM69.9769 72.873H68.2628V79.8H69.9769V72.873ZM68.2628 72.873H66.5134V79.8H68.2628V72.873ZM69.9769 76.3365V72.873H63.0499V76.3365H69.9769ZM69.9769 72.873V68.7429H63.0499V72.873H69.9769ZM61.862 70.1769L64.8753 71.7944L68.1515 65.6913L65.1383 64.0737L61.862 70.1769ZM61.3221 69.8767C61.5002 69.9792 61.6802 70.0793 61.8621 70.1769L65.1382 64.0737C65.0169 64.0086 64.8967 63.9417 64.7777 63.8732L61.3221 69.8767ZM66.5134 76.3365V66.875H59.5864V76.3365H66.5134ZM66.5134 79.8V76.3365H59.5864V79.8H66.5134ZM66.5134 85.0365V79.8H59.5864V85.0365H66.5134ZM11.4627 88.5H63.0499V81.573H11.4627V88.5ZM7.99927 13.6635V85.0365H14.9262V13.6635H7.99927ZM16.6757 10.2H11.4627V17.127H16.6757V10.2Z\" fill=\"white\" mask=\"url(#path-1-inside-1_26091_16586)\"/>\n<path d=\"M69.5679 38.9766L59.4791 40.8603C58.5325 41.0371 57.8462 41.8633 57.8462 42.8263V51.5197C57.8462 56.6865 60.6865 61.4349 65.2389 63.8787L68.9776 65.8857C69.5683 66.2028 70.2787 66.2028 70.8695 65.8857L74.6082 63.8787C79.1605 61.4349 82.0008 56.6865 82.0008 51.5197V42.7046C82.0008 41.733 81.3027 40.902 80.3457 40.7345L70.2798 38.9725C70.0442 38.9313 69.8031 38.9326 69.5679 38.9766Z\" fill=\"white\"/>\n</svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": false, "published": false, "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3"}, "configuration": null, "component": {"version": "certificates.k8s.io/v1alpha1", "kind": "ClusterTrustBundle", "schema": "{\"description\":\"ClusterTrustBundle is a cluster-scoped container for X.509 trust anchors (root certificates).\\n\\nClusterTrustBundle objects are considered to be readable by any authenticated user in the cluster, because they can be mounted by pods using the `clusterTrustBundle` projection.  All service accounts have read access to ClusterTrustBundles by default.  Users who only have namespace-level access to a cluster can read ClusterTrustBundles by impersonating a serviceaccount that they have access to.\\n\\nIt can be optionally associated with a particular assigner, in which case it contains one valid set of trust anchors for that signer. Signers may have multiple associated ClusterTrustBundles; each is an independent set of trust anchors for that signer. Admission control is used to enforce that only users with permissions on the signer can create or modify the corresponding bundle.\",\"properties\":{\"spec\":{\"allOf\":[{\"description\":\"ClusterTrustBundleSpec contains the signer and trust anchors.\",\"properties\":{\"signerName\":{\"description\":\"signerName indicates the associated signer, if any.\\n\\nIn order to create or update a ClusterTrustBundle that sets signerName, you must have the following cluster-scoped permission: group=certificates.k8s.io resource=signers resourceName=\\u003cthe signer name\\u003e verb=attest.\\n\\nIf signerName is not empty, then the ClusterTrustBundle object must be named with the signer name as a prefix (translating slashes to colons). For example, for the signer name `example.com/foo`, valid ClusterTrustBundle object names include `example.com:foo:abc` and `example.com:foo:v1`.\\n\\nIf signerName is empty, then the ClusterTrustBundle object's name must not have such a prefix.\\n\\nList/watch requests for ClusterTrustBundles can filter on this field using a `spec.signerName=NAME` field selector.\",\"type\":\"string\"},\"trustBundle\":{\"default\":\"\",\"description\":\"trustBundle contains the individual X.509 trust anchors for this bundle, as PEM bundle of PEM-wrapped, DER-formatted X.509 certificates.\\n\\nThe data must consist only of PEM certificate blocks that parse as valid X.509 certificates.  Each certificate must include a basic constraints extension with the CA bit set.  The API server will reject objects that contain duplicate certificates, or that use PEM block headers.\\n\\nUsers of ClusterTrustBundles, including Kubelet, are free to reorder and deduplicate certificate blocks in this file according to their own logic, as well as to drop PEM block headers and inter-block data.\",\"type\":\"string\"}},\"required\":[\"trustBundle\"],\"type\":\"object\"}],\"default\":{},\"description\":\"spec contains the signer (if any) and trust anchors.\"}},\"required\":[\"spec\"],\"type\":\"object\",\"x-kubernetes-group-version-kind\":[{\"group\":\"certificates.k8s.io\",\"kind\":\"ClusterTrustBundle\",\"version\":\"v1alpha1\"}]}"}}