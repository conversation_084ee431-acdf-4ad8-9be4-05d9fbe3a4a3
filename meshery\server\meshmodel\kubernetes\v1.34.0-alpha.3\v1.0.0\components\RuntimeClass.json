{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Runtime Class", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubernetes", "displayName": "Kubernetes", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Orchestration & Management"}, "subCategory": "Scheduling & Orchestration", "metadata": {"isAnnotation": false, "primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-0.17 0.08 230.10 223.35\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}.cls-2{fill:#326ce5}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.466a3.59 3.59 0 0 0-.855-.065 3.685 3.685 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53 43.53 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.656 3.656 0 0 0-.733.065l-.037-.045-21.785 3.698a43.695 43.695 0 0 0 17.54 21.946l8.442-20.4-.066-.08a3.683 3.683 0 0 0 .207-2.29zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.2l-10.738-19.406zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.039a3.725 3.725 0 0 0 5.913 2.84l.065.027 18.036-12.788a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.921a3.72 3.72 0 0 0 5.904-2.85l.092-.043 1.253-22.14a44.682 44.682 0 0 0-4.501.776 43.467 43.467 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.729a3.72 3.72 0 0 0 1.462-6.396l.018-.088-16.574-14.824a43.454 43.454 0 0 0-6.168 27.51l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.934 1.514-6.581-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.167l-17.422-75.675a13.602 13.602 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0L38.76 48.437a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.506 13.506 0 0 0 .491 1.718 13.1 13.1 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.423 13.423 0 0 0 2.574 2.133 13.924 13.924 0 0 0 3.857 1.677 13.298 13.298 0 0 0 3.43.473h.759l77.504-.018a12.993 12.993 0 0 0 1.41-.083 13.47 13.47 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.941 13.941 0 0 0 1.479-.822 13.303 13.303 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67 13.67 0 0 0 .5-1.724 13.282 13.282 0 0 0-.004-5.81zm-73.147 29.432a14.516 14.516 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.372 39.372 0 0 0 3.742 6.7 35.06 35.06 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.275 35.275 0 0 1-1.225-3.868 39.305 39.305 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.15-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.73a3.429 3.429 0 0 0-1.819.887 29.517 29.517 0 0 0-3.268 7.582 34.9 34.9 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.177 35.177 0 0 1 2.262-3.373 41.228 41.228 0 0 0 3.82-6.866 4.188 4.188 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68 4.68 0 0 0-2.428-.855 39.524 39.524 0 0 0-7.356 2.165 35.589 35.589 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.605.605 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.388 39.388 0 0 0 7.564-1.242 5.835 5.835 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.366 39.366 0 0 0-6.277-4.41 35.25 35.25 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.253 35.253 0 0 1 2.78 2.95 39.383 39.383 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082A54.289 54.289 0 0 1 102.86 61.89a55.052 55.052 0 0 1 7.63-1.173l.1-1.784a4.6 4.6 0 0 0 1.37-2.184 39.476 39.476 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.08-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.86.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.533 39.533 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.69 4.69 0 0 0 2.56-.278 39.532 39.532 0 0 0 5.69-5.148 35.004 35.004 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.095 35.095 0 0 1-3.507 2.046 39.495 39.495 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.745 4.745 0 0 0 1.82 1.83 39.464 39.464 0 0 0 7.568 1.246 35.64 35.64 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.03-.348-.075-.785-.151-1.09-.231a35.14 35.14 0 0 1-3.785-1.462 39.477 39.477 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.92 54.92 0 0 1-24.328 30.605z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M225.407 135.107L206.4 52.547a14.838 14.838 0 0 0-7.958-9.9l-76.935-36.73a14.825 14.825 0 0 0-12.771 0L31.808 42.669a14.838 14.838 0 0 0-7.961 9.895L4.873 135.129a14.668 14.668 0 0 0 1.995 11.185c.261.4.538.788.838 1.162l53.246 66.205a14.98 14.98 0 0 0 11.499 5.487l85.387-.02a14.986 14.986 0 0 0 11.5-5.48l53.227-66.211a14.72 14.72 0 0 0 2.842-12.347zm-9.197 3.866a13.677 13.677 0 0 1-.498 1.723 13.184 13.184 0 0 1-2.11 3.786l-47.299 58.838-1.486 1.852a13.305 13.305 0 0 1-3.24 2.865 13.945 13.945 0 0 1-1.474.822q-.513.237-1.045.43a13.873 13.873 0 0 1-1.383.445 13.473 13.473 0 0 1-1.989.379 12.988 12.988 0 0 1-1.41.082l-77.504.018h-.76a13.298 13.298 0 0 1-3.429-.472 13.925 13.925 0 0 1-3.855-1.679 13.424 13.424 0 0 1-2.576-2.132c-.233-.246-.468-.487-.68-.75l-48.805-60.679q-.408-.514-.765-1.066a13.102 13.102 0 0 1-1.343-2.726 13.505 13.505 0 0 1-.491-1.719 13.315 13.315 0 0 1 .004-5.809l17.394-75.675a13.598 13.598 0 0 1 7.295-9.07l70.508-33.685a13.589 13.589 0 0 1 11.705 0l70.519 33.67a13.602 13.602 0 0 1 7.293 9.073l17.422 75.674a13.282 13.282 0 0 1 .002 5.807z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M185.814 127.106c-.36-.083-.874-.225-1.227-.29a35.642 35.642 0 0 0-4.046-.326 39.464 39.464 0 0 1-7.57-1.242 4.745 4.745 0 0 1-1.82-1.832l-1.704-.496a54.837 54.837 0 0 0-8.79-37.983c.436-.396 1.262-1.127 1.495-1.342a3.338 3.338 0 0 1 .792-2.39 39.495 39.495 0 0 1 6.277-4.41 35.095 35.095 0 0 0 3.507-2.046c.272-.202.644-.522.929-.75a4.242 4.242 0 1 0-5.256-6.6c-.288.227-.68.525-.936.747a35.004 35.004 0 0 0-2.789 2.95 39.533 39.533 0 0 1-5.69 5.148 4.69 4.69 0 0 1-2.56.278l-1.606 1.147a55.309 55.309 0 0 0-35.032-16.927c-.039-.561-.087-1.577-.102-1.884a3.344 3.344 0 0 1-1.377-2.11 39.533 39.533 0 0 1 .478-7.656 35.112 35.112 0 0 0 .575-4.012c.013-.34-.007-.834-.007-1.201a4.242 4.242 0 1 0-8.441.004c0 .04.009.078.01.118-.004.35-.021.774-.01 1.08a35.476 35.476 0 0 0 .576 4.015 39.475 39.475 0 0 1 .47 7.654 4.601 4.601 0 0 1-1.37 2.182l-.1 1.786a55.052 55.052 0 0 0-7.63 1.173 54.289 54.289 0 0 0-27.574 15.754q-.77-.531-1.526-1.082a3.333 3.333 0 0 1-2.506-.243 39.383 39.383 0 0 1-5.69-5.141 35.255 35.255 0 0 0-2.777-2.95c-.257-.22-.65-.52-.938-.75a5.03 5.03 0 0 0-2.942-1.11 4.066 4.066 0 0 0-3.363 1.49 4.473 4.473 0 0 0 1.038 6.222l.065.046c.276.226.616.515.872.702a35.256 35.256 0 0 0 3.499 2.048 39.367 39.367 0 0 1 6.276 4.412 4.69 4.69 0 0 1 .849 2.434l1.351 1.208a54.613 54.613 0 0 0-8.611 38.073l-1.767.514a5.835 5.835 0 0 1-1.814 1.827 39.39 39.39 0 0 1-7.565 1.247 35.266 35.266 0 0 0-4.049.326c-.324.06-.774.174-1.127.262l-.037.008-.06.018a4.236 4.236 0 1 0 1.875 8.224l.063-.01c.028-.006.052-.02.08-.025.348-.08.786-.163 1.092-.246a35.59 35.59 0 0 0 3.786-1.451 39.527 39.527 0 0 1 7.358-2.165 4.68 4.68 0 0 1 2.426.857l1.84-.315a54.922 54.922 0 0 0 24.34 30.387l-.769 1.84a4.188 4.188 0 0 1 .377 2.387 41.228 41.228 0 0 1-3.82 6.864 35.183 35.183 0 0 0-2.263 3.372c-.168.318-.381.805-.542 1.138a4.238 4.238 0 1 0 7.621 3.616l.007-.008v-.01c.16-.33.387-.763.522-1.072a34.903 34.903 0 0 0 1.218-3.868 29.517 29.517 0 0 1 3.268-7.582 3.43 3.43 0 0 1 1.819-.888l.957-1.73a54.617 54.617 0 0 0 38.946.099c.268.478.768 1.392.9 1.623a3.335 3.335 0 0 1 2.155 1.31 39.306 39.306 0 0 1 2.898 7.096 35.275 35.275 0 0 0 1.225 3.868c.137.312.36.75.522 1.082a4.24 4.24 0 1 0 7.63-3.607c-.161-.333-.383-.82-.55-1.136a35.06 35.06 0 0 0-2.263-3.364 39.372 39.372 0 0 1-3.742-6.7 3.314 3.314 0 0 1 .324-2.49 14.519 14.519 0 0 1-.703-1.703 54.92 54.92 0 0 0 24.328-30.605c.546.087 1.497.253 1.806.316a3.337 3.337 0 0 1 2.36-.877 39.476 39.476 0 0 1 7.36 2.165 35.135 35.135 0 0 0 3.788 1.462c.305.08.74.156 1.09.233.029.008.055.02.083.028l.06.009a4.236 4.236 0 1 0 1.878-8.224zm-40.1-42.987l-18.037 12.787-.063-.03a3.723 3.723 0 0 1-5.913-2.838l-.02-.01-1.253-22.103a43.85 43.85 0 0 1 25.285 12.194zm-33.978 24.228h6.788l4.22 5.276-1.513 6.58-6.096 2.934-6.114-2.94-1.516-6.583zm-6.386-35.648a44.672 44.672 0 0 1 4.503-.774l-1.255 22.137-.092.044a3.72 3.72 0 0 1-5.904 2.852l-.035.02-18.154-12.872a43.467 43.467 0 0 1 20.937-11.407zm-27.52 19.68l16.574 14.824-.018.09a3.72 3.72 0 0 1-1.462 6.395l-.017.072-21.245 6.13a43.454 43.454 0 0 1 6.168-27.51zm22.191 39.38l-8.441 20.397a43.696 43.696 0 0 1-17.536-21.948l21.783-3.7.037.049a3.655 3.655 0 0 1 .73-.065 3.72 3.72 0 0 1 3.364 5.185zm24.916 26.23a43.637 43.637 0 0 1-23.815-1.223l10.713-19.372h.018a3.725 3.725 0 0 1 6.557-.006h.08l10.74 19.404q-2.091.698-4.293 1.199zm13.841-5.751l-8.528-20.605.026-.037a3.725 3.725 0 0 1 1.803-4.823 3.685 3.685 0 0 1 1.425-.37 3.59 3.59 0 0 1 .855.063l.037-.046 21.977 3.714a43.53 43.53 0 0 1-17.595 22.105zm19.903-32.42l-21.352-6.15-.02-.09a3.725 3.725 0 0 1-1.46-6.395l-.008-.043 16.482-14.751a44.279 44.279 0 0 1 6.357 27.43z\" class=\"cls-1\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"9.70 9.20 210.86 204.86\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.46551a3.59023 3.59023 0 0 0-.855-.065 3.68515 3.68515 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53012 43.53012 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.6556 3.6556 0 0 0-.733.065l-.037-.045-21.785 3.698a43.69506 43.69506 0 0 0 17.54 21.946l8.442-20.399-.066-.08a3.68318 3.68318 0 0 0 .207-2.291zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.199l-10.738-19.407zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.0385a3.725 3.725 0 0 0 5.913 2.84l.065.028 18.036-12.789a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.922a3.72 3.72 0 0 0 5.904-2.85l.092-.044 1.253-22.139a44.68209 44.68209 0 0 0-4.501.775 43.4669 43.4669 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.728a3.72 3.72 0 0 0 1.462-6.396l.018-.087-16.574-14.825a43.454 43.454 0 0 0-6.168 27.511l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.933 1.514-6.582-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.16651l-17.422-75.675a13.60207 13.60207 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0l-70.507 33.688a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.50607 13.50607 0 0 0 .491 1.718 13.0998 13.0998 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.42334 13.42334 0 0 0 2.574 2.133 13.9237 13.9237 0 0 0 3.857 1.677 13.29785 13.29785 0 0 0 3.43.473h.759l77.504-.018a12.99345 12.99345 0 0 0 1.41-.083 13.46921 13.46921 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.94127 13.94127 0 0 0 1.479-.822 13.30347 13.30347 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67036 13.67036 0 0 0 .5-1.724 13.28215 13.28215 0 0 0-.004-5.809zm-73.147 29.432a14.51575 14.51575 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.37244 39.37244 0 0 0 3.742 6.7 35.06044 35.06044 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.27528 35.27528 0 0 1-1.225-3.868 39.3046 39.3046 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.149-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.731a3.429 3.429 0 0 0-1.819.886 29.51728 29.51728 0 0 0-3.268 7.582 34.89931 34.89931 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.17706 35.17706 0 0 1 2.262-3.373 41.22786 41.22786 0 0 0 3.82-6.866 4.18792 4.18792 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68007 4.68007 0 0 0-2.428-.855 39.52352 39.52352 0 0 0-7.356 2.165 35.58886 35.58886 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.60518.60518 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.38835 39.38835 0 0 0 7.564-1.242 5.83506 5.83506 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.36558 39.36558 0 0 0-6.277-4.41 35.2503 35.2503 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.25285 35.25285 0 0 1 2.78 2.95 39.38314 39.38314 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082a54.28892 54.28892 0 0 1 27.577-15.754 55.05181 55.05181 0 0 1 7.63-1.173l.1-1.784a4.6001 4.6001 0 0 0 1.37-2.184 39.47551 39.47551 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.079-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.861.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.53346 39.53346 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.6901 4.6901 0 0 0 2.56-.278 39.53152 39.53152 0 0 0 5.69-5.148 35.00382 35.00382 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.09523 35.09523 0 0 1-3.507 2.046 39.49476 39.49476 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.7449 4.7449 0 0 0 1.82 1.831 39.46448 39.46448 0 0 0 7.568 1.245 35.64041 35.64041 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.029-.348-.076-.785-.152-1.09-.232a35.1407 35.1407 0 0 1-3.785-1.462 39.47672 39.47672 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.91994 54.91994 0 0 1-24.328 30.605z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "svgColor": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.0309 14.5118H24.416V7C24.416 5.89543 25.3114 5 26.416 5H33.9604V6.61486V8.22972H27.6457V14.5118H26.0309ZM37.7325 6.61486V5H52.8212V6.61486V8.22972H37.7325V6.61486ZM56.5934 6.61486V5H71.682V6.61486V8.22972H56.5934V6.61486ZM75.4542 6.61486V5H82.9985C84.1031 5 84.9985 5.89543 84.9985 7V14.5118H83.3837H81.7688V8.22972H75.4542V6.61486ZM83.3837 18.2677H84.9985V33.2913H83.3837H81.7688V18.2677H83.3837ZM83.3837 37.0472H84.9985V52.0708H83.3837H81.7688V37.0472H83.3837ZM83.3837 55.8267H84.9985V63.3385C84.9985 64.4431 84.1031 65.3385 82.9985 65.3385H75.4542V63.7236V62.1088H81.7688V55.8267H83.3837Z\" fill=\"#326CE5\"/>\n<mask id=\"path-2-inside-1_26185_14954\" fill=\"white\">\n<rect x=\"14.5801\" y=\"14.6367\" width=\"60.5825\" height=\"60.3385\" rx=\"2\"/>\n</mask>\n<rect x=\"14.5801\" y=\"14.6367\" width=\"60.5825\" height=\"60.3385\" rx=\"2\" stroke=\"#326CE5\" stroke-width=\"6.45944\" stroke-dasharray=\"16 4\" mask=\"url(#path-2-inside-1_26185_14954)\"/>\n<mask id=\"path-3-inside-2_26185_14954\" fill=\"white\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.00195 24.6611C5.89738 24.6611 5.00195 25.5566 5.00195 26.6611V82.9996C5.00195 84.1042 5.89738 84.9996 7.00195 84.9996H63.5845C64.689 84.9996 65.5845 84.1042 65.5845 82.9996V26.6611C65.5845 25.5566 64.689 24.6611 63.5845 24.6611H7.00195ZM53.5987 53.7277C53.5987 63.5184 45.6297 71.4553 35.7994 71.4553C25.969 71.4553 18 63.5184 18 53.7277C18 43.9369 25.969 36 35.7994 36C45.6297 36 53.5987 43.9369 53.5987 53.7277ZM34.8306 54.0499V44.3803H37.4144V53.3495L45.2417 58.4468L43.8317 60.612L35.4175 55.1325L34.8306 54.7503V54.0499Z\"/>\n</mask>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.00195 24.6611C5.89738 24.6611 5.00195 25.5566 5.00195 26.6611V82.9996C5.00195 84.1042 5.89738 84.9996 7.00195 84.9996H63.5845C64.689 84.9996 65.5845 84.1042 65.5845 82.9996V26.6611C65.5845 25.5566 64.689 24.6611 63.5845 24.6611H7.00195ZM53.5987 53.7277C53.5987 63.5184 45.6297 71.4553 35.7994 71.4553C25.969 71.4553 18 63.5184 18 53.7277C18 43.9369 25.969 36 35.7994 36C45.6297 36 53.5987 43.9369 53.5987 53.7277ZM34.8306 54.0499V44.3803H37.4144V53.3495L45.2417 58.4468L43.8317 60.612L35.4175 55.1325L34.8306 54.7503V54.0499Z\" fill=\"#326CE5\"/>\n<path d=\"M34.8306 44.3803V41.1506H31.6009V44.3803H34.8306ZM37.4144 44.3803H40.6441V41.1506H37.4144V44.3803ZM37.4144 53.3495H34.1846V55.1005L35.6519 56.056L37.4144 53.3495ZM45.2417 58.4468L47.9481 60.2093L49.7106 57.5028L47.0042 55.7404L45.2417 58.4468ZM43.8317 60.612L42.0693 63.3184L44.7757 65.0809L46.5382 62.3744L43.8317 60.612ZM35.4175 55.1325L33.655 57.8389L33.655 57.8389L35.4175 55.1325ZM34.8306 54.7503H31.6009V56.5012L33.0681 57.4567L34.8306 54.7503ZM8.23167 26.6611C8.23167 27.3403 7.68111 27.8909 7.00195 27.8909V21.4314C4.11366 21.4314 1.77223 23.7728 1.77223 26.6611H8.23167ZM8.23167 82.9996V26.6611H1.77223V82.9996H8.23167ZM7.00195 81.7699C7.68111 81.7699 8.23167 82.3205 8.23167 82.9996H1.77223C1.77223 85.8879 4.11365 88.2294 7.00195 88.2294V81.7699ZM63.5845 81.7699H7.00195V88.2294H63.5845V81.7699ZM62.3548 82.9996C62.3548 82.3205 62.9053 81.7699 63.5845 81.7699V88.2294C66.4728 88.2294 68.8142 85.8879 68.8142 82.9996H62.3548ZM62.3548 26.6611V82.9996H68.8142V26.6611H62.3548ZM63.5845 27.8909C62.9053 27.8909 62.3548 27.3403 62.3548 26.6611H68.8142C68.8142 23.7728 66.4728 21.4314 63.5845 21.4314V27.8909ZM7.00195 27.8909H63.5845V21.4314H7.00195V27.8909ZM35.7994 74.685C47.4011 74.685 56.8284 65.3144 56.8284 53.7277H50.369C50.369 61.7224 43.8582 68.2256 35.7994 68.2256V74.685ZM14.7703 53.7277C14.7703 65.3144 24.1976 74.685 35.7994 74.685V68.2256C27.7405 68.2256 21.2297 61.7224 21.2297 53.7277H14.7703ZM35.7994 32.7703C24.1976 32.7703 14.7703 42.1409 14.7703 53.7277H21.2297C21.2297 45.7329 27.7405 39.2297 35.7994 39.2297V32.7703ZM56.8284 53.7277C56.8284 42.1409 47.4011 32.7703 35.7994 32.7703V39.2297C43.8582 39.2297 50.369 45.7329 50.369 53.7277H56.8284ZM31.6009 44.3803V54.0499H38.0603V44.3803H31.6009ZM37.4144 41.1506H34.8306V47.61H37.4144V41.1506ZM40.6441 53.3495V44.3803H34.1846V53.3495H40.6441ZM47.0042 55.7404L39.1768 50.6431L35.6519 56.056L43.4792 61.1532L47.0042 55.7404ZM46.5382 62.3744L47.9481 60.2093L42.5353 56.6843L41.1253 58.8495L46.5382 62.3744ZM33.655 57.8389L42.0693 63.3184L45.5942 57.9055L37.18 52.4261L33.655 57.8389ZM33.0681 57.4567L33.655 57.8389L37.18 52.4261L36.5931 52.0439L33.0681 57.4567ZM31.6009 54.0499V54.7503H38.0603V54.0499H31.6009Z\" fill=\"#326CE5\" mask=\"url(#path-3-inside-2_26185_14954)\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.0309 14.5118H24.416V7C24.416 5.89543 25.3114 5 26.416 5H33.9604V6.61486V8.22972H27.6457V14.5118H26.0309ZM37.7325 6.61486V5H52.8212V6.61486V8.22972H37.7325V6.61486ZM56.5934 6.61486V5H71.682V6.61486V8.22972H56.5934V6.61486ZM75.4542 6.61486V5H82.9985C84.1031 5 84.9985 5.89543 84.9985 7V14.5118H83.3837H81.7688V8.22972H75.4542V6.61486ZM83.3837 18.2677H84.9985V33.2913H83.3837H81.7688V18.2677H83.3837ZM83.3837 37.0472H84.9985V52.0708H83.3837H81.7688V37.0472H83.3837ZM83.3837 55.8267H84.9985V63.3385C84.9985 64.4431 84.1031 65.3385 82.9985 65.3385H75.4542V63.7236V62.1088H81.7688V55.8267H83.3837Z\" fill=\"white\"/>\n<mask id=\"path-2-inside-1_26185_14940\" fill=\"white\">\n<rect x=\"14.5801\" y=\"14.6367\" width=\"60.5825\" height=\"60.3385\" rx=\"2\"/>\n</mask>\n<rect x=\"14.5801\" y=\"14.6367\" width=\"60.5825\" height=\"60.3385\" rx=\"2\" stroke=\"white\" stroke-width=\"6.45944\" stroke-dasharray=\"16 4\" mask=\"url(#path-2-inside-1_26185_14940)\"/>\n<mask id=\"path-3-inside-2_26185_14940\" fill=\"white\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.00195 24.6611C5.89738 24.6611 5.00195 25.5566 5.00195 26.6611V82.9996C5.00195 84.1042 5.89738 84.9996 7.00195 84.9996H63.5845C64.689 84.9996 65.5845 84.1042 65.5845 82.9996V26.6611C65.5845 25.5566 64.689 24.6611 63.5845 24.6611H7.00195ZM53.5987 53.7277C53.5987 63.5184 45.6297 71.4553 35.7994 71.4553C25.969 71.4553 18 63.5184 18 53.7277C18 43.9369 25.969 36 35.7994 36C45.6297 36 53.5987 43.9369 53.5987 53.7277ZM34.8306 54.0499V44.3803H37.4144V53.3495L45.2417 58.4468L43.8317 60.612L35.4175 55.1325L34.8306 54.7503V54.0499Z\"/>\n</mask>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.00195 24.6611C5.89738 24.6611 5.00195 25.5566 5.00195 26.6611V82.9996C5.00195 84.1042 5.89738 84.9996 7.00195 84.9996H63.5845C64.689 84.9996 65.5845 84.1042 65.5845 82.9996V26.6611C65.5845 25.5566 64.689 24.6611 63.5845 24.6611H7.00195ZM53.5987 53.7277C53.5987 63.5184 45.6297 71.4553 35.7994 71.4553C25.969 71.4553 18 63.5184 18 53.7277C18 43.9369 25.969 36 35.7994 36C45.6297 36 53.5987 43.9369 53.5987 53.7277ZM34.8306 54.0499V44.3803H37.4144V53.3495L45.2417 58.4468L43.8317 60.612L35.4175 55.1325L34.8306 54.7503V54.0499Z\" fill=\"white\"/>\n<path d=\"M34.8306 44.3803V41.1506H31.6009V44.3803H34.8306ZM37.4144 44.3803H40.6441V41.1506H37.4144V44.3803ZM37.4144 53.3495H34.1846V55.1005L35.6519 56.056L37.4144 53.3495ZM45.2417 58.4468L47.9481 60.2093L49.7106 57.5028L47.0042 55.7404L45.2417 58.4468ZM43.8317 60.612L42.0693 63.3184L44.7757 65.0809L46.5382 62.3744L43.8317 60.612ZM35.4175 55.1325L33.655 57.8389L33.655 57.8389L35.4175 55.1325ZM34.8306 54.7503H31.6009V56.5012L33.0681 57.4567L34.8306 54.7503ZM8.23167 26.6611C8.23167 27.3403 7.68111 27.8909 7.00195 27.8909V21.4314C4.11366 21.4314 1.77223 23.7728 1.77223 26.6611H8.23167ZM8.23167 82.9996V26.6611H1.77223V82.9996H8.23167ZM7.00195 81.7699C7.68111 81.7699 8.23167 82.3205 8.23167 82.9996H1.77223C1.77223 85.8879 4.11365 88.2294 7.00195 88.2294V81.7699ZM63.5845 81.7699H7.00195V88.2294H63.5845V81.7699ZM62.3548 82.9996C62.3548 82.3205 62.9053 81.7699 63.5845 81.7699V88.2294C66.4728 88.2294 68.8142 85.8879 68.8142 82.9996H62.3548ZM62.3548 26.6611V82.9996H68.8142V26.6611H62.3548ZM63.5845 27.8909C62.9053 27.8909 62.3548 27.3403 62.3548 26.6611H68.8142C68.8142 23.7728 66.4728 21.4314 63.5845 21.4314V27.8909ZM7.00195 27.8909H63.5845V21.4314H7.00195V27.8909ZM35.7994 74.685C47.4011 74.685 56.8284 65.3144 56.8284 53.7277H50.369C50.369 61.7224 43.8582 68.2256 35.7994 68.2256V74.685ZM14.7703 53.7277C14.7703 65.3144 24.1976 74.685 35.7994 74.685V68.2256C27.7405 68.2256 21.2297 61.7224 21.2297 53.7277H14.7703ZM35.7994 32.7703C24.1976 32.7703 14.7703 42.1409 14.7703 53.7277H21.2297C21.2297 45.7329 27.7405 39.2297 35.7994 39.2297V32.7703ZM56.8284 53.7277C56.8284 42.1409 47.4011 32.7703 35.7994 32.7703V39.2297C43.8582 39.2297 50.369 45.7329 50.369 53.7277H56.8284ZM31.6009 44.3803V54.0499H38.0603V44.3803H31.6009ZM37.4144 41.1506H34.8306V47.61H37.4144V41.1506ZM40.6441 53.3495V44.3803H34.1846V53.3495H40.6441ZM47.0042 55.7404L39.1768 50.6431L35.6519 56.056L43.4792 61.1532L47.0042 55.7404ZM46.5382 62.3744L47.9481 60.2093L42.5353 56.6843L41.1253 58.8495L46.5382 62.3744ZM33.655 57.8389L42.0693 63.3184L45.5942 57.9055L37.18 52.4261L33.655 57.8389ZM33.0681 57.4567L33.655 57.8389L37.18 52.4261L36.5931 52.0439L33.0681 57.4567ZM31.6009 54.0499V54.7503H38.0603V54.0499H31.6009Z\" fill=\"white\" mask=\"url(#path-3-inside-2_26185_14940)\"/>\n</svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": false, "published": false, "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3"}, "configuration": null, "component": {"version": "node.k8s.io/v1", "kind": "RuntimeClass", "schema": "{\"description\":\"RuntimeClass defines a class of container runtime supported in the cluster. The RuntimeClass is used to determine which container runtime is used to run all containers in a pod. RuntimeClasses are manually defined by a user or cluster provisioner, and referenced in the PodSpec. The Kubelet is responsible for resolving the RuntimeClassName reference before running the pod.  For more details, see https://kubernetes.io/docs/concepts/containers/runtime-class/\",\"properties\":{\"handler\":{\"default\":\"\",\"description\":\"handler specifies the underlying runtime and configuration that the CRI implementation will use to handle pods of this class. The possible values are specific to the node \\u0026 CRI configuration.  It is assumed that all handlers are available on every node, and handlers of the same name are equivalent on every node. For example, a handler called \\\"runc\\\" might specify that the runc OCI runtime (using native Linux containers) will be used to run the containers in a pod. The Handler must be lowercase, conform to the DNS Label (RFC 1123) requirements, and is immutable.\",\"type\":\"string\"},\"overhead\":{\"allOf\":[{\"description\":\"Overhead structure represents the resource overhead associated with running a pod.\",\"properties\":{\"podFixed\":{\"additionalProperties\":{\"description\":\"Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\\n\\nThe serialization format is:\\n\\n``` \\u003cquantity\\u003e        ::= \\u003csignedNumber\\u003e\\u003csuffix\\u003e\\n\\n\\t(Note that \\u003csuffix\\u003e may be empty, from the \\\"\\\" case in \\u003cdecimalSI\\u003e.)\\n\\n\\u003cdigit\\u003e           ::= 0 | 1 | ... | 9 \\u003cdigits\\u003e          ::= \\u003cdigit\\u003e | \\u003cdigit\\u003e\\u003cdigits\\u003e \\u003cnumber\\u003e          ::= \\u003cdigits\\u003e | \\u003cdigits\\u003e.\\u003cdigits\\u003e | \\u003cdigits\\u003e. | .\\u003cdigits\\u003e \\u003csign\\u003e            ::= \\\"+\\\" | \\\"-\\\" \\u003csignedNumber\\u003e    ::= \\u003cnumber\\u003e | \\u003csign\\u003e\\u003cnumber\\u003e \\u003csuffix\\u003e          ::= \\u003cbinarySI\\u003e | \\u003cdecimalExponent\\u003e | \\u003cdecimalSI\\u003e \\u003cbinarySI\\u003e        ::= Ki | Mi | Gi | Ti | Pi | Ei\\n\\n\\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\\n\\n\\u003cdecimalSI\\u003e       ::= m | \\\"\\\" | k | M | G | T | P | E\\n\\n\\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\\n\\n\\u003cdecimalExponent\\u003e ::= \\\"e\\\" \\u003csignedNumber\\u003e | \\\"E\\\" \\u003csignedNumber\\u003e ```\\n\\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\\n\\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\\n\\nBefore serializing, Quantity will be put in \\\"canonical form\\\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\\n\\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\\n\\nThe sign will be omitted unless the number is negative.\\n\\nExamples:\\n\\n- 1.5 will be serialized as \\\"1500m\\\" - 1.5Gi will be serialized as \\\"1536Mi\\\"\\n\\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\\n\\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\\n\\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.\",\"oneOf\":[{\"type\":\"string\"},{\"type\":\"number\"}]},\"description\":\"podFixed represents the fixed resource overhead associated with running a pod.\",\"type\":\"object\"}},\"type\":\"object\"}],\"description\":\"overhead represents the resource overhead associated with running a pod for a given RuntimeClass. For more details, see\\n https://kubernetes.io/docs/concepts/scheduling-eviction/pod-overhead/\"},\"scheduling\":{\"allOf\":[{\"description\":\"Scheduling specifies the scheduling constraints for nodes supporting a RuntimeClass.\",\"properties\":{\"nodeSelector\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"nodeSelector lists labels that must be present on nodes that support this RuntimeClass. Pods using this RuntimeClass can only be scheduled to a node matched by this selector. The RuntimeClass nodeSelector is merged with a pod's existing nodeSelector. Any conflicts will cause the pod to be rejected in admission.\",\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"},\"tolerations\":{\"description\":\"tolerations are appended (excluding duplicates) to pods running with this RuntimeClass during admission, effectively unioning the set of nodes tolerated by the pod and the RuntimeClass.\",\"items\":{\"allOf\":[{\"description\":\"The pod this Toleration is attached to tolerates any taint that matches the triple \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\"properties\":{\"effect\":{\"description\":\"Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.\",\"type\":\"string\"},\"key\":{\"description\":\"Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys.\",\"type\":\"string\"},\"operator\":{\"description\":\"Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a pod can tolerate all taints of a particular category.\",\"type\":\"string\"},\"tolerationSeconds\":{\"description\":\"TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system.\",\"format\":\"int64\",\"type\":\"integer\"},\"value\":{\"description\":\"Value is the taint value the toleration matches to. If the operator is Exists, the value should be empty, otherwise just a regular string.\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"description\":\"scheduling holds the scheduling constraints to ensure that pods running with this RuntimeClass are scheduled to nodes that support it. If scheduling is nil, this RuntimeClass is assumed to be supported by all nodes.\"}},\"required\":[\"handler\"],\"type\":\"object\",\"x-kubernetes-group-version-kind\":[{\"group\":\"node.k8s.io\",\"kind\":\"RuntimeClass\",\"version\":\"v1\"}]}"}}