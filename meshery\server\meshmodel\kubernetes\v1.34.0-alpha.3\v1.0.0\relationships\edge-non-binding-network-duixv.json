{"id": "00000000-0000-0000-0000-000000000000", "evaluationQuery": "", "kind": "edge", "metadata": {"description": "A relationship that defines network edges between components", "styles": {"primaryColor": "", "svgColor": "", "svgWhite": ""}, "isAnnotation": false}, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "schemaVersion": "relationships.meshery.io/v1alpha3", "selectors": [{"allow": {"from": [{"id": null, "kind": "Service", "match": {}, "match_strategy_matrix": [["to_contains_from", "not_null"], ["equal_as_strings", "not_null"], ["equal"]], "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": {"patchStrategy": "replace", "mutatorRef": [["configuration", "spec", "selector"], ["configuration", "spec", "ports", "0", "targetPort"], ["configuration", "spec", "ports", "0", "protocol"]]}}], "to": [{"id": null, "kind": "Deployment", "match": {}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": {"patchStrategy": "replace", "mutatedRef": [["configuration", "spec", "selector", "matchLabels"], ["configuration", "spec", "template", "spec", "containers", "0", "ports", "0", "containerPort"], ["configuration", "spec", "template", "spec", "containers", "0", "ports", "0", "protocol"]]}}]}, "deny": {"from": [{"id": null, "kind": "Service", "match": {}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}], "to": [{"id": null, "kind": "Service", "match": {}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}, {"id": null, "kind": "Pod", "match": {}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}]}}], "subType": "network", "status": "enabled", "type": "non-binding", "version": "v1.0.0"}