{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Tenant Network Policy", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubegems-local", "displayName": "Kubegems Local", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Provisioning"}, "subCategory": "Automation & Configuration", "metadata": {"isAnnotation": false, "primaryColor": "#1c77c8", "secondaryColor": "#00D3A9", "shape": "circle", "source_uri": "https://charts.kubegems.io/kubegems/charts/kubegems-local-1.24.8.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.76 0.49 364.26 356.51\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#1c77c8}.cls-2{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M99.589 251.108a4.575 4.575 0 1 0 4.72 4.573 4.654 4.654 0 0 0-4.72-4.573zm150.361 9.146a4.575 4.575 0 1 0-4.72-4.573 4.653 4.653 0 0 0 4.72 4.573zm-16.086-37.929c20.693 0 37.567-16.148 37.567-35.987 0-14.965-9.619-27.82-23.127-33.248 0-1.068.095-2.158.095-3.249 0-30-25.37-54.386-56.78-54.386-22.649 0-42.029 12.692-51.17 30.998a27.216 27.216 0 0 0-16.038-5.267c-14.559 0-26.206 11.253-26.206 25.105a24.56 24.56 0 0 0 1.122 7.425 36.546 36.546 0 0 0-18.831 31.718c0 20.116 16.97 35.615 37.853 36.798h113.463c.668 0 1.312.093 2.052.093zM87.688 185.434a29.349 29.349 0 0 1 15.197-25.468l5.035-2.866-1.729-5.53a17.369 17.369 0 0 1-.794-5.279c0-9.846 8.398-17.913 19.014-17.913a20.02 20.02 0 0 1 11.786 3.876l6.878 5.04 3.809-7.628c8.173-16.37 25.37-27.019 44.736-27.019 27.472 0 49.587 21.206 49.587 47.194 0 .315-.01.612-.04 1.317a39.31 39.31 0 0 0-.055 1.932v4.86l4.51 1.813c11.185 4.495 18.617 14.959 18.617 26.575 0 15.79-13.58 28.795-30.375 28.795a8.9 8.9 0 0 1-.56-.029 28.085 28.085 0 0 0-.772-.047c-.333-.013-.333-.013-.72-.016H118.349c-17.206-.987-30.661-13.822-30.661-29.607zm88.275 82.21a4.575 4.575 0 1 0 4.72 4.573 4.654 4.654 0 0 0-4.72-4.573z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M267.594 339.93l81.986-102.808a27.308 27.308 0 0 0 5.273-23.103L325.593 85.82a27.307 27.307 0 0 0-14.775-18.527L192.345 10.24a27.309 27.309 0 0 0-23.697 0L50.174 67.293A27.308 27.308 0 0 0 35.4 85.82L6.14 214.02a27.308 27.308 0 0 0 5.273 23.103L93.399 339.93a27.308 27.308 0 0 0 21.35 10.282h131.496a27.308 27.308 0 0 0 21.35-10.282zm-31.11-89.023a14.174 14.174 0 1 1 0 9.547H209.56v-2.411h-.183v-28.612q-11.455-.005-28.64-.009v29.45a14.32 14.32 0 1 1-9.547-.001v-29.45l-28.64-.005v28.627h-.184v2.411h-29.31a14.174 14.174 0 1 1-.001-9.547h19.948v-21.492l-15.061-.002c-24.922-1.412-44.638-20.22-44.638-43.98a43.463 43.463 0 0 1 17.964-35.046 32.577 32.577 0 0 1-.255-4.096c0-17.923 14.965-32.297 33.398-32.297a34.272 34.272 0 0 1 13.372 2.728 64.37 64.37 0 0 1 53.837-28.459c34.843 0 63.252 26.823 63.958 60.303a43.089 43.089 0 0 1 23.045 37.772c0 23.876-20.157 43.18-44.759 43.18-.523 0-.896-.02-1.547-.064-.111-.007-4.585-.013-13.393-.018v21.471z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M232.532 215.057c.243.011.448.024.773.047a8.9 8.9 0 0 0 .56.03c16.793 0 30.374-13.006 30.374-28.796 0-11.616-7.432-22.08-18.618-26.575l-4.51-1.813v-4.86c0-.586.015-1.008.055-1.932.031-.705.041-1.002.041-1.317 0-25.988-22.115-47.194-49.587-47.194-19.365 0-36.563 10.65-44.736 27.02l-3.81 7.628-6.877-5.041a20.02 20.02 0 0 0-11.786-3.876c-10.616 0-19.014 8.067-19.014 17.913a17.369 17.369 0 0 0 .794 5.28l1.729 5.529-5.035 2.866a29.349 29.349 0 0 0-15.197 25.468c0 15.785 13.455 28.62 30.66 29.607h113.464c.387.003.387.003.72.016z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M218.924 222.61v6.826c8.808.005 13.282.011 13.393.018.651.045 1.024.063 1.547.063 24.602 0 44.758-19.303 44.758-43.179a43.089 43.089 0 0 0-23.045-37.772c-.705-33.48-29.114-60.303-63.957-60.303a64.37 64.37 0 0 0-53.837 28.46 34.272 34.272 0 0 0-13.372-2.729c-18.433 0-33.398 14.374-33.398 32.297a32.577 32.577 0 0 0 .255 4.096 43.463 43.463 0 0 0-17.964 35.047c0 23.759 19.716 42.567 44.638 43.979l15.06.002v-6.805h9.548v6.806l28.64.005v-4.449h9.547v4.45q17.178.004 28.64.01v-6.822zm-100.575-.378c-20.884-1.183-37.853-16.682-37.853-36.798a36.546 36.546 0 0 1 18.83-31.718 24.56 24.56 0 0 1-1.121-7.425c0-13.852 11.647-25.105 26.206-25.105a27.216 27.216 0 0 1 16.038 5.267c9.141-18.306 28.521-30.998 51.17-30.998 31.41 0 56.78 24.386 56.78 54.386 0 1.09-.095 2.181-.095 3.249 13.508 5.429 23.127 18.283 23.127 33.248 0 19.838-16.874 35.987-37.567 35.987-.74 0-1.384-.093-2.052-.093z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M218.924 222.61h-9.547v35.433h.183v2.411h26.924a13.773 13.773 0 0 1 0-9.547h-17.56V222.61zm-47.734 2.362v33.899a14.19 14.19 0 0 1 9.547 0v-33.899zm-38.187-2.362v28.297h-19.948a13.775 13.775 0 0 1 0 9.547h29.311v-2.411h.184V222.61z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M113.055 250.907a14.174 14.174 0 1 0 0 9.547 13.775 13.775 0 0 0 0-9.547zm-13.466 9.347a4.575 4.575 0 1 1 4.72-4.573 4.653 4.653 0 0 1-4.72 4.573zm71.601-1.383a14.32 14.32 0 1 0 9.547 0 14.19 14.19 0 0 0-9.547 0zm4.773 17.919a4.575 4.575 0 1 1 4.72-4.573 4.653 4.653 0 0 1-4.72 4.573zm60.521-16.336a14.174 14.174 0 1 0 0-9.547 13.773 13.773 0 0 0 0 9.547zm13.467-9.346a4.575 4.575 0 1 1-4.72 4.573 4.655 4.655 0 0 1 4.72-4.573z\" class=\"cls-2\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-3.82 -0.07 367.64 360.14\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M98.5 253.002a4.621 4.621 0 1 0 4.767 4.62 4.7 4.7 0 0 0-4.767-4.62zm151.861 9.238a4.621 4.621 0 1 0-4.767-4.618 4.7 4.7 0 0 0 4.767 4.618zm-16.246-38.307c20.899 0 37.94-16.31 37.94-36.346 0-15.115-9.714-28.097-23.357-33.58 0-1.079.096-2.18.096-3.281 0-30.3-25.623-54.93-57.345-54.93-22.876 0-42.449 12.82-51.681 31.308a27.488 27.488 0 0 0-16.199-5.32c-14.704 0-26.467 11.366-26.467 25.356a24.805 24.805 0 0 0 1.133 7.5 36.91 36.91 0 0 0-19.019 32.033c0 20.317 17.139 35.97 38.23 37.166h114.596c.674 0 1.325.094 2.073.094zM86.48 186.673a29.642 29.642 0 0 1 15.349-25.722l5.085-2.894-1.746-5.585a17.543 17.543 0 0 1-.802-5.332c0-9.945 8.482-18.091 19.203-18.091a20.22 20.22 0 0 1 11.904 3.914l6.946 5.091 3.847-7.705c8.256-16.532 25.624-27.288 45.183-27.288 27.746 0 50.082 21.417 50.082 47.665 0 .317-.01.618-.042 1.33-.04.933-.055 1.359-.055 1.95v4.91l4.555 1.83c11.297 4.54 18.803 15.109 18.803 26.84 0 15.948-13.716 29.083-30.677 29.083-.098 0-.225-.006-.565-.03a28.708 28.708 0 0 0-.78-.047c-.337-.013-.337-.013-.729-.017H117.447c-17.378-.996-30.967-13.96-30.967-29.902zm84.389 87.649a4.77 4.77 0 1 0 4.766-4.619 4.7 4.7 0 0 0-4.766 4.62z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M268.18 342.71l82.805-103.833a27.58 27.58 0 0 0 5.326-23.333L326.758 86.066a27.58 27.58 0 0 0-14.922-18.712L192.181 9.731a27.581 27.581 0 0 0-23.934 0L48.592 67.354A27.58 27.58 0 0 0 33.67 86.066L4.118 215.544a27.58 27.58 0 0 0 5.325 23.333L92.247 342.71a27.58 27.58 0 0 0 21.563 10.384h132.808a27.58 27.58 0 0 0 21.563-10.384zm-31.419-89.91a14.315 14.315 0 1 1 0 9.642h-27.193v-2.435h-.185V231.11q-11.57-.005-28.926-.01v29.743a14.462 14.462 0 1 1-9.642 0V231.1l-28.926-.005v28.913h-.185v2.435H112.1a14.315 14.315 0 1 1 0-9.642h20.147v-21.707l-15.211-.002c-25.171-1.426-45.084-20.422-45.084-44.418a43.897 43.897 0 0 1 18.143-35.396 32.899 32.899 0 0 1-.257-4.137c0-18.102 15.114-32.619 33.731-32.619a34.614 34.614 0 0 1 13.506 2.755 65.012 65.012 0 0 1 54.374-28.743c35.19 0 63.883 27.091 64.596 60.904a43.519 43.519 0 0 1 23.274 38.15c0 24.114-20.357 43.61-45.204 43.61-.528 0-.905-.02-1.563-.064-.112-.007-4.63-.013-13.527-.019V252.8z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "1.24.8"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#00B39F", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.76 0.49 364.26 356.51\"><defs><style>.cls-1{fill:#1c77c8}.cls-2{fill:#fff}</style></defs><path d=\"M99.589 251.108a4.575 4.575 0 1 0 4.72 4.573 4.654 4.654 0 0 0-4.72-4.573zm150.361 9.146a4.575 4.575 0 1 0-4.72-4.573 4.653 4.653 0 0 0 4.72 4.573zm-16.086-37.929c20.693 0 37.567-16.148 37.567-35.987 0-14.965-9.619-27.82-23.127-33.248 0-1.068.095-2.158.095-3.249 0-30-25.37-54.386-56.78-54.386-22.649 0-42.029 12.692-51.17 30.998a27.216 27.216 0 0 0-16.038-5.267c-14.559 0-26.206 11.253-26.206 25.105a24.56 24.56 0 0 0 1.122 7.425 36.546 36.546 0 0 0-18.831 31.718c0 20.116 16.97 35.615 37.853 36.798h113.463c.668 0 1.312.093 2.052.093zM87.688 185.434a29.349 29.349 0 0 1 15.197-25.468l5.035-2.866-1.729-5.53a17.369 17.369 0 0 1-.794-5.279c0-9.846 8.398-17.913 19.014-17.913a20.02 20.02 0 0 1 11.786 3.876l6.878 5.04 3.809-7.628c8.173-16.37 25.37-27.019 44.736-27.019 27.472 0 49.587 21.206 49.587 47.194 0 .315-.01.612-.04 1.317a39.31 39.31 0 0 0-.055 1.932v4.86l4.51 1.813c11.185 4.495 18.617 14.959 18.617 26.575 0 15.79-13.58 28.795-30.375 28.795a8.9 8.9 0 0 1-.56-.029 28.085 28.085 0 0 0-.772-.047c-.333-.013-.333-.013-.72-.016H118.349c-17.206-.987-30.661-13.822-30.661-29.607zm88.275 82.21a4.575 4.575 0 1 0 4.72 4.573 4.654 4.654 0 0 0-4.72-4.573z\" class=\"cls-1\"/><path d=\"M267.594 339.93l81.986-102.808a27.308 27.308 0 0 0 5.273-23.103L325.593 85.82a27.307 27.307 0 0 0-14.775-18.527L192.345 10.24a27.309 27.309 0 0 0-23.697 0L50.174 67.293A27.308 27.308 0 0 0 35.4 85.82L6.14 214.02a27.308 27.308 0 0 0 5.273 23.103L93.399 339.93a27.308 27.308 0 0 0 21.35 10.282h131.496a27.308 27.308 0 0 0 21.35-10.282zm-31.11-89.023a14.174 14.174 0 1 1 0 9.547H209.56v-2.411h-.183v-28.612q-11.455-.005-28.64-.009v29.45a14.32 14.32 0 1 1-9.547-.001v-29.45l-28.64-.005v28.627h-.184v2.411h-29.31a14.174 14.174 0 1 1-.001-9.547h19.948v-21.492l-15.061-.002c-24.922-1.412-44.638-20.22-44.638-43.98a43.463 43.463 0 0 1 17.964-35.046 32.577 32.577 0 0 1-.255-4.096c0-17.923 14.965-32.297 33.398-32.297a34.272 34.272 0 0 1 13.372 2.728 64.37 64.37 0 0 1 53.837-28.459c34.843 0 63.252 26.823 63.958 60.303a43.089 43.089 0 0 1 23.045 37.772c0 23.876-20.157 43.18-44.759 43.18-.523 0-.896-.02-1.547-.064-.111-.007-4.585-.013-13.393-.018v21.471z\" class=\"cls-1\"/><path d=\"M232.532 215.057c.243.011.448.024.773.047a8.9 8.9 0 0 0 .56.03c16.793 0 30.374-13.006 30.374-28.796 0-11.616-7.432-22.08-18.618-26.575l-4.51-1.813v-4.86c0-.586.015-1.008.055-1.932.031-.705.041-1.002.041-1.317 0-25.988-22.115-47.194-49.587-47.194-19.365 0-36.563 10.65-44.736 27.02l-3.81 7.628-6.877-5.041a20.02 20.02 0 0 0-11.786-3.876c-10.616 0-19.014 8.067-19.014 17.913a17.369 17.369 0 0 0 .794 5.28l1.729 5.529-5.035 2.866a29.349 29.349 0 0 0-15.197 25.468c0 15.785 13.455 28.62 30.66 29.607h113.464c.387.003.387.003.72.016z\" class=\"cls-2\"/><path d=\"M218.924 222.61v6.826c8.808.005 13.282.011 13.393.018.651.045 1.024.063 1.547.063 24.602 0 44.758-19.303 44.758-43.179a43.089 43.089 0 0 0-23.045-37.772c-.705-33.48-29.114-60.303-63.957-60.303a64.37 64.37 0 0 0-53.837 28.46 34.272 34.272 0 0 0-13.372-2.729c-18.433 0-33.398 14.374-33.398 32.297a32.577 32.577 0 0 0 .255 4.096 43.463 43.463 0 0 0-17.964 35.047c0 23.759 19.716 42.567 44.638 43.979l15.06.002v-6.805h9.548v6.806l28.64.005v-4.449h9.547v4.45q17.178.004 28.64.01v-6.822zm-100.575-.378c-20.884-1.183-37.853-16.682-37.853-36.798a36.546 36.546 0 0 1 18.83-31.718 24.56 24.56 0 0 1-1.121-7.425c0-13.852 11.647-25.105 26.206-25.105a27.216 27.216 0 0 1 16.038 5.267c9.141-18.306 28.521-30.998 51.17-30.998 31.41 0 56.78 24.386 56.78 54.386 0 1.09-.095 2.181-.095 3.249 13.508 5.429 23.127 18.283 23.127 33.248 0 19.838-16.874 35.987-37.567 35.987-.74 0-1.384-.093-2.052-.093z\" class=\"cls-2\"/><path d=\"M218.924 222.61h-9.547v35.433h.183v2.411h26.924a13.773 13.773 0 0 1 0-9.547h-17.56V222.61zm-47.734 2.362v33.899a14.19 14.19 0 0 1 9.547 0v-33.899zm-38.187-2.362v28.297h-19.948a13.775 13.775 0 0 1 0 9.547h29.311v-2.411h.184V222.61z\" class=\"cls-2\"/><path d=\"M113.055 250.907a14.174 14.174 0 1 0 0 9.547 13.775 13.775 0 0 0 0-9.547zm-13.466 9.347a4.575 4.575 0 1 1 4.72-4.573 4.653 4.653 0 0 1-4.72 4.573zm71.601-1.383a14.32 14.32 0 1 0 9.547 0 14.19 14.19 0 0 0-9.547 0zm4.773 17.919a4.575 4.575 0 1 1 4.72-4.573 4.653 4.653 0 0 1-4.72 4.573zm60.521-16.336a14.174 14.174 0 1 0 0-9.547 13.773 13.773 0 0 0 0 9.547zm13.467-9.346a4.575 4.575 0 1 1-4.72 4.573 4.655 4.655 0 0 1 4.72-4.573z\" class=\"cls-2\"/></svg>", "svgComplete": "", "svgWhite": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-3.82 -0.07 367.64 360.14\"><defs><style>.cls-1{fill:#fff}</style></defs><path d=\"M98.5 253.002a4.621 4.621 0 1 0 4.767 4.62 4.7 4.7 0 0 0-4.767-4.62zm151.861 9.238a4.621 4.621 0 1 0-4.767-4.618 4.7 4.7 0 0 0 4.767 4.618zm-16.246-38.307c20.899 0 37.94-16.31 37.94-36.346 0-15.115-9.714-28.097-23.357-33.58 0-1.079.096-2.18.096-3.281 0-30.3-25.623-54.93-57.345-54.93-22.876 0-42.449 12.82-51.681 31.308a27.488 27.488 0 0 0-16.199-5.32c-14.704 0-26.467 11.366-26.467 25.356a24.805 24.805 0 0 0 1.133 7.5 36.91 36.91 0 0 0-19.019 32.033c0 20.317 17.139 35.97 38.23 37.166h114.596c.674 0 1.325.094 2.073.094zM86.48 186.673a29.642 29.642 0 0 1 15.349-25.722l5.085-2.894-1.746-5.585a17.543 17.543 0 0 1-.802-5.332c0-9.945 8.482-18.091 19.203-18.091a20.22 20.22 0 0 1 11.904 3.914l6.946 5.091 3.847-7.705c8.256-16.532 25.624-27.288 45.183-27.288 27.746 0 50.082 21.417 50.082 47.665 0 .317-.01.618-.042 1.33-.04.933-.055 1.359-.055 1.95v4.91l4.555 1.83c11.297 4.54 18.803 15.109 18.803 26.84 0 15.948-13.716 29.083-30.677 29.083-.098 0-.225-.006-.565-.03a28.708 28.708 0 0 0-.78-.047c-.337-.013-.337-.013-.729-.017H117.447c-17.378-.996-30.967-13.96-30.967-29.902zm84.389 87.649a4.77 4.77 0 1 0 4.766-4.619 4.7 4.7 0 0 0-4.766 4.62z\" class=\"cls-1\"/><path d=\"M268.18 342.71l82.805-103.833a27.58 27.58 0 0 0 5.326-23.333L326.758 86.066a27.58 27.58 0 0 0-14.922-18.712L192.181 9.731a27.581 27.581 0 0 0-23.934 0L48.592 67.354A27.58 27.58 0 0 0 33.67 86.066L4.118 215.544a27.58 27.58 0 0 0 5.325 23.333L92.247 342.71a27.58 27.58 0 0 0 21.563 10.384h132.808a27.58 27.58 0 0 0 21.563-10.384zm-31.419-89.91a14.315 14.315 0 1 1 0 9.642h-27.193v-2.435h-.185V231.11q-11.57-.005-28.926-.01v29.743a14.462 14.462 0 1 1-9.642 0V231.1l-28.926-.005v28.913h-.185v2.435H112.1a14.315 14.315 0 1 1 0-9.642h20.147v-21.707l-15.211-.002c-25.171-1.426-45.084-20.422-45.084-44.418a43.897 43.897 0 0 1 18.143-35.396 32.899 32.899 0 0 1-.257-4.137c0-18.102 15.114-32.619 33.731-32.619a34.614 34.614 0 0 1 13.506 2.755 65.012 65.012 0 0 1 54.374-28.743c35.19 0 63.883 27.091 64.596 60.904a43.519 43.519 0 0 1 23.274 38.15c0 24.114-20.357 43.61-45.204 43.61-.528 0-.905-.02-1.563-.064-.112-.007-4.63-.013-13.527-.019V252.8z\" class=\"cls-1\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": false, "published": false, "source_uri": "https://charts.kubegems.io/kubegems/charts/kubegems-local-1.24.8.tgz"}, "configuration": null, "component": {"version": "gems.kubegems.io/v1beta1", "kind": "TenantNetworkPolicy", "schema": "{\n \"description\": \"TenantNetworkPolicy is the Schema for the tenantnetworkpolicies API\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"TenantNetworkPolicySpec defines the desired state of TenantNetworkPolicy\",\n   \"properties\": {\n    \"environmentNetworkPolicies\": {\n     \"items\": {\n      \"properties\": {\n       \"name\": {\n        \"type\": \"string\"\n       },\n       \"project\": {\n        \"type\": \"string\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"projectNetworkPolicies\": {\n     \"items\": {\n      \"properties\": {\n       \"name\": {\n        \"type\": \"string\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"tenant\": {\n     \"type\": \"string\"\n    },\n    \"tenantIsolated\": {\n     \"type\": \"boolean\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Tenant Network Policy\",\n \"type\": \"object\"\n}"}}