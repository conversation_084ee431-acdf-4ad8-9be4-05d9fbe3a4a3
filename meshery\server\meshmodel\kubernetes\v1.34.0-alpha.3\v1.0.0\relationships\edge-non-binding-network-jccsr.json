{"id": "00000000-0000-0000-0000-000000000000", "evaluationQuery": "", "kind": "edge", "metadata": {"description": "A relationship that defines network edges between components", "styles": {"primaryColor": "", "svgColor": "", "svgWhite": ""}, "isAnnotation": false}, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "schemaVersion": "relationships.meshery.io/v1alpha3", "selectors": [{"allow": {"from": [{"id": null, "kind": "Ingress", "match": {}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": {"patchStrategy": "replace", "mutatedRef": [["configuration", "spec", "rules", "0", "http", "paths", "0", "backend", "serviceName"], ["configuration", "spec", "rules", "0", "http", "paths", "0", "backend", "servicePort"]]}}], "to": [{"id": null, "kind": "Service", "match": {}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": {"patchStrategy": "replace", "mutatorRef": [["displayName"], ["configuration", "spec", "ports", "0", "port"]]}}]}}], "subType": "network", "status": "enabled", "type": "non-binding", "version": "v1.0.0"}