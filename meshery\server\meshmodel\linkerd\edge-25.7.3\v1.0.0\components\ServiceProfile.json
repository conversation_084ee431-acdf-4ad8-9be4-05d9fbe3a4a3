{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Service Profile", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "linkerd", "displayName": "<PERSON><PERSON>", "description": "git://github.com/meshery/meshery-linkerd/master/templates/meshmodel/components/stable-2.9.5", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Cloud Native Network"}, "subCategory": "Service Mesh", "metadata": {"isAnnotation": false, "primaryColor": "#0DA6E5", "secondaryColor": "#0DA6E5", "shape": "circle", "source_uri": "git://github.com/linkerd/linkerd2/main/charts/linkerd-crds/", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\" height=\"20\" width=\"20\"><style xmlns=\"http://www.w3.org/2000/svg\">svg {enable-background:new 0 0 500 500}</style><style xmlns=\"http://www.w3.org/2000/svg\">.st2{fill:#2beda7}</style><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_1_\" x1=\"477.221\" x2=\"477.221\" y1=\"106.515\" y2=\"308.8\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_1_)\" d=\"M460.4 106.5v182.8l33.7 19.5V126z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_2_\" x1=\"25.459\" x2=\"25.459\" y1=\"106.52\" y2=\"308.812\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_2_)\" d=\"M8.6 308.8l33.7-19.5V106.5L8.6 126z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M173.8 307l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st2\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_3_\" x1=\"477.221\" x2=\"477.221\" y1=\"196.062\" y2=\"382.938\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_3_)\" d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_4_\" x1=\"403.048\" x2=\"403.048\" y1=\"238.884\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_4_)\" d=\"M394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_5_\" x1=\"328.877\" x2=\"328.877\" y1=\"281.704\" y2=\"472.469\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_5_)\" d=\"M312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_6_\" x1=\"173.82\" x2=\"173.82\" y1=\"281.704\" y2=\"472.466\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_6_)\" d=\"M159.6 454.5l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_7_\" x1=\"99.649\" x2=\"99.649\" y1=\"238.883\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_7_)\" d=\"M86.8 412.5l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_8_\" x1=\"25.478\" x2=\"25.478\" y1=\"196.059\" y2=\"382.936\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_8_)\" d=\"M12.6 369.7l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st2\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\" height=\"20\" width=\"20\"><style xmlns=\"http://www.w3.org/2000/svg\">svg {enable-background:new 0 0 500 500}</style><style xmlns=\"http://www.w3.org/2000/svg\">.st0{fill:#fff}</style><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M460.4 106.5v182.8l33.7 19.5V126zM8.6 308.8l33.7-19.5V106.5L8.6 126zm165.2-1.8l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st0\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4zM394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5zM312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3zm-152.4-18l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6zm-72.8-42l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7zm-74.2-42.8l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\" class=\"st0\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st0\"></path></svg>"}, "model": {"version": "edge-25.7.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#0DA6E5", "secondaryColor": "#0DA6E5", "shape": "circle", "svgColor": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\"><style>svg {enable-background:new 0 0 500 500}</style><style>.st2{fill:#2beda7}</style><linearGradient id=\"SVGID_1_\" x1=\"477.221\" x2=\"477.221\" y1=\"106.515\" y2=\"308.8\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_1_)\" d=\"M460.4 106.5v182.8l33.7 19.5V126z\"/><linearGradient id=\"SVGID_2_\" x1=\"25.459\" x2=\"25.459\" y1=\"106.52\" y2=\"308.812\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_2_)\" d=\"M8.6 308.8l33.7-19.5V106.5L8.6 126z\"/><path d=\"M173.8 307l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st2\"/><linearGradient id=\"SVGID_3_\" x1=\"477.221\" x2=\"477.221\" y1=\"196.062\" y2=\"382.938\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_3_)\" d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4z\"/><linearGradient id=\"SVGID_4_\" x1=\"403.048\" x2=\"403.048\" y1=\"238.884\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_4_)\" d=\"M394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5z\"/><linearGradient id=\"SVGID_5_\" x1=\"328.877\" x2=\"328.877\" y1=\"281.704\" y2=\"472.469\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_5_)\" d=\"M312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3z\"/><linearGradient id=\"SVGID_6_\" x1=\"173.82\" x2=\"173.82\" y1=\"281.704\" y2=\"472.466\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_6_)\" d=\"M159.6 454.5l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6z\"/><linearGradient id=\"SVGID_7_\" x1=\"99.649\" x2=\"99.649\" y1=\"238.883\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_7_)\" d=\"M86.8 412.5l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7z\"/><linearGradient id=\"SVGID_8_\" x1=\"25.478\" x2=\"25.478\" y1=\"196.059\" y2=\"382.936\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_8_)\" d=\"M12.6 369.7l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\"/><path d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st2\"/></svg>", "svgComplete": "", "svgWhite": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\"><style>svg {enable-background:new 0 0 500 500}</style><style>.st0{fill:#fff}</style><path d=\"M460.4 106.5v182.8l33.7 19.5V126zM8.6 308.8l33.7-19.5V106.5L8.6 126zm165.2-1.8l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st0\"/><path d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4zM394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5zM312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3zm-152.4-18l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6zm-72.8-42l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7zm-74.2-42.8l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\" class=\"st0\"/><path d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st0\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/linkerd/linkerd2/main/charts/linkerd-crds/"}, "configuration": null, "component": {"version": "linkerd.io/v1alpha1", "kind": "ServiceProfile", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Spec is the custom resource spec\",\n   \"properties\": {\n    \"dstOverrides\": {\n     \"items\": {\n      \"description\": \"WeightedDst is a weighted alternate destination.\",\n      \"properties\": {\n       \"authority\": {\n        \"type\": \"string\"\n       },\n       \"weight\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n        \"x-kubernetes-int-or-string\": true\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"required\": [\n      \"authority\",\n      \"weight\"\n     ],\n     \"type\": \"array\"\n    },\n    \"opaquePorts\": {\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"retryBudget\": {\n     \"description\": \"RetryBudget describes the maximum number of retries that should be issued to this service.\",\n     \"properties\": {\n      \"minRetriesPerSecond\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"retryRatio\": {\n       \"format\": \"float\",\n       \"type\": \"number\"\n      },\n      \"ttl\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"minRetriesPerSecond\",\n      \"retryRatio\",\n      \"ttl\"\n     ],\n     \"type\": \"object\"\n    },\n    \"routes\": {\n     \"items\": {\n      \"description\": \"RouteSpec specifies a Route resource.\",\n      \"properties\": {\n       \"condition\": {\n        \"description\": \"RequestMatch describes the conditions under which to match a Route.\",\n        \"properties\": {\n         \"all\": {\n          \"items\": {\n           \"format\": \"textarea\",\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"any\": {\n          \"items\": {\n           \"format\": \"textarea\",\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"method\": {\n          \"type\": \"string\"\n         },\n         \"not\": {\n          \"items\": {\n           \"format\": \"textarea\",\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"pathRegex\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"isRetryable\": {\n        \"type\": \"boolean\"\n       },\n       \"name\": {\n        \"type\": \"string\"\n       },\n       \"responseClasses\": {\n        \"items\": {\n         \"description\": \"ResponseClass describes how to classify a response (e.g. success or failures).\",\n         \"properties\": {\n          \"condition\": {\n           \"description\": \"ResponseMatch describes the conditions under which to classify a response.\",\n           \"properties\": {\n            \"all\": {\n             \"items\": {\n              \"format\": \"textarea\",\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"any\": {\n             \"items\": {\n              \"format\": \"textarea\",\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"not\": {\n             \"format\": \"textarea\",\n             \"type\": \"string\"\n            },\n            \"status\": {\n             \"description\": \"Range describes a range of integers (e.g. status codes).\",\n             \"properties\": {\n              \"max\": {\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"min\": {\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              }\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"isFailure\": {\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"condition\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"timeout\": {\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"condition\",\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    }\n   },\n   \"required\": [\n    \"routes\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Service Profile\",\n \"type\": \"object\"\n}"}}