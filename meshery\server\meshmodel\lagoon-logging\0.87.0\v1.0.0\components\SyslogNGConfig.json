{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Syslog NG Config", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "lagoon-logging", "displayName": "Lagoon Logging", "status": "ignored", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Uncategorized"}, "subCategory": "Uncategorized", "metadata": {"isAnnotation": false, "primaryColor": "#00B39F", "secondaryColor": "#00D3A9", "shape": "circle", "source_uri": "https://github.com/uselagoon/lagoon-charts/releases/download/lagoon-logging-0.87.0/lagoon-logging-0.87.0.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 32 32\" fill=\"none\">\n<g xmlns=\"http://www.w3.org/2000/svg\" clip-path=\"url(#clip0_36_80)\">\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.4632 7.69351V15.2015L22.9702 11.4346L16.4632 7.69351Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.4632 16.7705V24.3157L23.0307 20.5607L16.4632 16.7705Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.5274 15.1502V7.75632L9.10194 11.4416L15.5274 15.1502Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M2.63699 24.2878C3.89756 26.3157 5.60178 28.031 7.62134 29.3047V21.4033L2.63699 24.2878Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.5274 24.2785V16.8264L9.08579 20.556L15.5274 24.2785Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.55965 28.8344L15.0829 25.1049L8.55965 21.3335V28.8344Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4753 28.8742V21.3848L16.9615 25.1096L23.4753 28.8742Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M29.852 23.4194C30.9655 21.341 31.5949 19.0378 31.6935 16.6819L24.9119 20.5651L29.852 23.4194Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M24.4136 19.7691L30.96 16.0256L24.4136 12.2634V19.7691Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4755 10.6455V3.20041L16.9919 6.91827L23.4755 10.6455Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4754 19.7364V12.2239L16.9779 15.986L23.4754 19.7364Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.55965 12.2099V19.7784L15.1061 15.9882L8.55965 12.2099Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.5274 0.285706C13.1176 0.353534 10.756 0.977397 8.6271 2.10855L15.5274 6.06621V0.285706Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.55965 3.1492V10.6734L15.1107 6.91597L8.55965 3.1492Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.62134 2.69299C5.60228 3.96735 3.89818 5.6826 2.63699 7.7099L7.62134 10.5873V2.69299Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4335 2.14811C21.2869 0.992986 18.9001 0.355226 16.4632 0.285706V6.14069L23.4335 2.14811Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M0.285713 16.5517C0.367085 18.9754 1.01023 21.3471 2.16447 23.4799L7.21396 20.5559L0.285713 16.5517Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M2.16447 8.51996C1.01384 10.6433 0.370833 13.0043 0.285713 15.4178L7.22097 11.4393L2.16447 8.51996Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.61544 29.8822C10.7469 31.0189 13.1128 31.6461 15.5274 31.7143V25.9291L8.61544 29.8822Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M29.3675 7.73539C28.1143 5.71396 26.4208 4.00147 24.4136 2.72543V10.5987L29.3675 7.73539Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M24.4136 29.2791C26.4312 27.994 28.1314 26.2684 29.3863 24.2321L24.4136 21.3591V29.2791Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M31.7143 15.3738C31.6251 12.9835 30.9879 10.6458 29.8518 8.54102L24.8441 11.4325L31.7143 15.3738Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.4632 31.7143C18.8725 31.6467 21.2333 31.0229 23.3613 29.8914L16.4632 25.8942V31.7143Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.62141 19.711V12.2892L1.17738 15.9838L7.62141 19.711Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n</g>\n<defs xmlns=\"http://www.w3.org/2000/svg\">\n<clipPath xmlns=\"http://www.w3.org/2000/svg\" id=\"clip0_36_80\">\n<rect xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" fill=\"white\"></rect>\n</clipPath>\n</defs>\n</svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 32 32\" fill=\"none\"><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.405 8.732v6.57l5.694-3.297-5.694-3.273Zm0 7.942v6.602l5.747-3.285-5.747-3.317Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.586 15.256v-6.47l-5.622 3.225 5.622 3.245ZM4.307 23.252a13.809 13.809 0 0 0 4.362 4.39v-6.914l-4.362 2.524Zm11.279-.008v-6.52L9.95 19.985l5.636 3.258Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"m9.49 27.23 5.707-3.263-5.707-3.3v6.563Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M22.54 27.265v-6.553l-5.699 3.259 5.7 3.294Zm5.58-4.773a13.697 13.697 0 0 0 1.612-5.895l-5.934 3.397 4.323 2.498Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"m23.362 19.298 5.728-3.276-5.728-3.291v6.567Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M22.541 11.315V4.8l-5.673 3.253 5.673 3.262Zm0 7.955v-6.574l-5.685 3.292 5.685 3.281Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.49 12.684v6.622l5.728-3.316-5.728-3.306Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.586 2.25a13.69 13.69 0 0 0-6.037 1.595l6.037 3.463V2.25Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.49 4.756v6.583l5.732-3.288L9.49 4.756Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.669 4.356a13.83 13.83 0 0 0-4.362 4.39l4.362 2.518V4.356Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M22.504 3.88a13.695 13.695 0 0 0-6.099-1.63v5.123l6.1-3.493ZM2.25 16.483c.071 2.12.634 4.196 1.644 6.062l4.418-2.559-6.062-3.503Zm1.644-7.028a13.68 13.68 0 0 0-1.644 6.036l6.068-3.482-4.424-2.554Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.539 28.147a13.673 13.673 0 0 0 6.047 1.603v-5.062L9.54 28.147Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M27.697 8.768a13.83 13.83 0 0 0-4.335-4.383v6.889l4.335-2.506ZM23.362 27.62a13.851 13.851 0 0 0 4.351-4.417l-4.351-2.514v6.93Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M29.75 15.452a13.659 13.659 0 0 0-1.63-5.979l-4.381 2.53 6.011 3.45Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.405 29.75a13.673 13.673 0 0 0 6.036-1.595l-6.036-3.498v5.093Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.669 19.247v-6.494L3.03 15.986l5.639 3.261Z\" fill=\"#fff\" fill-opacity=\".8\"></path></svg>"}, "model": {"version": "0.87.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#00B39F", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg width=\"18\" height=\"18\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_36_80)\">\n<path d=\"M16.4632 7.69351V15.2015L22.9702 11.4346L16.4632 7.69351Z\" fill=\"white\"/>\n<path d=\"M16.4632 16.7705V24.3157L23.0307 20.5607L16.4632 16.7705Z\" fill=\"white\"/>\n<path d=\"M15.5274 15.1502V7.75632L9.10194 11.4416L15.5274 15.1502Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M2.63699 24.2878C3.89756 26.3157 5.60178 28.031 7.62134 29.3047V21.4033L2.63699 24.2878Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M15.5274 24.2785V16.8264L9.08579 20.556L15.5274 24.2785Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M8.55965 28.8344L15.0829 25.1049L8.55965 21.3335V28.8344Z\" fill=\"white\"/>\n<path d=\"M23.4753 28.8742V21.3848L16.9615 25.1096L23.4753 28.8742Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M29.852 23.4194C30.9655 21.341 31.5949 19.0378 31.6935 16.6819L24.9119 20.5651L29.852 23.4194Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M24.4136 19.7691L30.96 16.0256L24.4136 12.2634V19.7691Z\" fill=\"white\"/>\n<path d=\"M23.4755 10.6455V3.20041L16.9919 6.91827L23.4755 10.6455Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M23.4754 19.7364V12.2239L16.9779 15.986L23.4754 19.7364Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M8.55965 12.2099V19.7784L15.1061 15.9882L8.55965 12.2099Z\" fill=\"white\"/>\n<path d=\"M15.5274 0.285706C13.1176 0.353534 10.756 0.977397 8.6271 2.10855L15.5274 6.06621V0.285706Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M8.55965 3.1492V10.6734L15.1107 6.91597L8.55965 3.1492Z\" fill=\"white\"/>\n<path d=\"M7.62134 2.69299C5.60228 3.96735 3.89818 5.6826 2.63699 7.7099L7.62134 10.5873V2.69299Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M23.4335 2.14811C21.2869 0.992986 18.9001 0.355226 16.4632 0.285706V6.14069L23.4335 2.14811Z\" fill=\"white\"/>\n<path d=\"M0.285713 16.5517C0.367085 18.9754 1.01023 21.3471 2.16447 23.4799L7.21396 20.5559L0.285713 16.5517Z\" fill=\"white\"/>\n<path d=\"M2.16447 8.51996C1.01384 10.6433 0.370833 13.0043 0.285713 15.4178L7.22097 11.4393L2.16447 8.51996Z\" fill=\"white\"/>\n<path d=\"M8.61544 29.8822C10.7469 31.0189 13.1128 31.6461 15.5274 31.7143V25.9291L8.61544 29.8822Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M29.3675 7.73539C28.1143 5.71396 26.4208 4.00147 24.4136 2.72543V10.5987L29.3675 7.73539Z\" fill=\"white\"/>\n<path d=\"M24.4136 29.2791C26.4312 27.994 28.1314 26.2684 29.3863 24.2321L24.4136 21.3591V29.2791Z\" fill=\"white\"/>\n<path d=\"M31.7143 15.3738C31.6251 12.9835 30.9879 10.6458 29.8518 8.54102L24.8441 11.4325L31.7143 15.3738Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M16.4632 31.7143C18.8725 31.6467 21.2333 31.0229 23.3613 29.8914L16.4632 25.8942V31.7143Z\" fill=\"white\"/>\n<path d=\"M7.62141 19.711V12.2892L1.17738 15.9838L7.62141 19.711Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_36_80\">\n<rect width=\"32\" height=\"32\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>", "svgComplete": "", "svgWhite": "<svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M16.405 8.732v6.57l5.694-3.297-5.694-3.273Zm0 7.942v6.602l5.747-3.285-5.747-3.317Z\" fill=\"#fff\"/><path d=\"M15.586 15.256v-6.47l-5.622 3.225 5.622 3.245ZM4.307 23.252a13.809 13.809 0 0 0 4.362 4.39v-6.914l-4.362 2.524Zm11.279-.008v-6.52L9.95 19.985l5.636 3.258Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"m9.49 27.23 5.707-3.263-5.707-3.3v6.563Z\" fill=\"#fff\"/><path d=\"M22.54 27.265v-6.553l-5.699 3.259 5.7 3.294Zm5.58-4.773a13.697 13.697 0 0 0 1.612-5.895l-5.934 3.397 4.323 2.498Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"m23.362 19.298 5.728-3.276-5.728-3.291v6.567Z\" fill=\"#fff\"/><path d=\"M22.541 11.315V4.8l-5.673 3.253 5.673 3.262Zm0 7.955v-6.574l-5.685 3.292 5.685 3.281Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M9.49 12.684v6.622l5.728-3.316-5.728-3.306Z\" fill=\"#fff\"/><path d=\"M15.586 2.25a13.69 13.69 0 0 0-6.037 1.595l6.037 3.463V2.25Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M9.49 4.756v6.583l5.732-3.288L9.49 4.756Z\" fill=\"#fff\"/><path d=\"M8.669 4.356a13.83 13.83 0 0 0-4.362 4.39l4.362 2.518V4.356Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M22.504 3.88a13.695 13.695 0 0 0-6.099-1.63v5.123l6.1-3.493ZM2.25 16.483c.071 2.12.634 4.196 1.644 6.062l4.418-2.559-6.062-3.503Zm1.644-7.028a13.68 13.68 0 0 0-1.644 6.036l6.068-3.482-4.424-2.554Z\" fill=\"#fff\"/><path d=\"M9.539 28.147a13.673 13.673 0 0 0 6.047 1.603v-5.062L9.54 28.147Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M27.697 8.768a13.83 13.83 0 0 0-4.335-4.383v6.889l4.335-2.506ZM23.362 27.62a13.851 13.851 0 0 0 4.351-4.417l-4.351-2.514v6.93Z\" fill=\"#fff\"/><path d=\"M29.75 15.452a13.659 13.659 0 0 0-1.63-5.979l-4.381 2.53 6.011 3.45Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M16.405 29.75a13.673 13.673 0 0 0 6.036-1.595l-6.036-3.498v5.093Z\" fill=\"#fff\"/><path d=\"M8.669 19.247v-6.494L3.03 15.986l5.639 3.261Z\" fill=\"#fff\" fill-opacity=\".8\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/uselagoon/lagoon-charts/releases/download/lagoon-logging-0.87.0/lagoon-logging-0.87.0.tgz"}, "configuration": null, "component": {"version": "logging.banzaicloud.io/v1beta1", "kind": "SyslogNGConfig", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"properties\": {\n    \"bufferVolumeMetrics\": {\n     \"properties\": {\n      \"interval\": {\n       \"type\": \"string\"\n      },\n      \"mount_name\": {\n       \"type\": \"string\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"port\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"prometheusAnnotations\": {\n       \"type\": \"boolean\"\n      },\n      \"prometheusRules\": {\n       \"type\": \"boolean\"\n      },\n      \"prometheusRulesOverride\": {\n       \"items\": {\n        \"properties\": {\n         \"alert\": {\n          \"type\": \"string\"\n         },\n         \"annotations\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"expr\": {\n          \"anyOf\": [\n           {\n            \"type\": \"integer\"\n           },\n           {\n            \"type\": \"string\"\n           }\n          ],\n          \"x-kubernetes-int-or-string\": true\n         },\n         \"for\": {\n          \"pattern\": \"^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n          \"type\": \"string\"\n         },\n         \"keep_firing_for\": {\n          \"minLength\": 1,\n          \"pattern\": \"^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n          \"type\": \"string\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"record\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"serviceMonitor\": {\n       \"type\": \"boolean\"\n      },\n      \"serviceMonitorConfig\": {\n       \"properties\": {\n        \"additionalLabels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"honorLabels\": {\n         \"type\": \"boolean\"\n        },\n        \"metricRelabelings\": {\n         \"items\": {\n          \"properties\": {\n           \"action\": {\n            \"default\": \"replace\",\n            \"enum\": [\n             \"replace\",\n             \"Replace\",\n             \"keep\",\n             \"Keep\",\n             \"drop\",\n             \"Drop\",\n             \"hashmod\",\n             \"HashMod\",\n             \"labelmap\",\n             \"LabelMap\",\n             \"labeldrop\",\n             \"LabelDrop\",\n             \"labelkeep\",\n             \"LabelKeep\",\n             \"lowercase\",\n             \"Lowercase\",\n             \"uppercase\",\n             \"Uppercase\",\n             \"keepequal\",\n             \"KeepEqual\",\n             \"dropequal\",\n             \"DropEqual\"\n            ],\n            \"type\": \"string\"\n           },\n           \"modulus\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"regex\": {\n            \"type\": \"string\"\n           },\n           \"replacement\": {\n            \"type\": \"string\"\n           },\n           \"separator\": {\n            \"type\": \"string\"\n           },\n           \"sourceLabels\": {\n            \"items\": {\n             \"pattern\": \"^[a-zA-Z_][a-zA-Z0-9_]*$\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"targetLabel\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"relabelings\": {\n         \"items\": {\n          \"properties\": {\n           \"action\": {\n            \"default\": \"replace\",\n            \"enum\": [\n             \"replace\",\n             \"Replace\",\n             \"keep\",\n             \"Keep\",\n             \"drop\",\n             \"Drop\",\n             \"hashmod\",\n             \"HashMod\",\n             \"labelmap\",\n             \"LabelMap\",\n             \"labeldrop\",\n             \"LabelDrop\",\n             \"labelkeep\",\n             \"LabelKeep\",\n             \"lowercase\",\n             \"Lowercase\",\n             \"uppercase\",\n             \"Uppercase\",\n             \"keepequal\",\n             \"KeepEqual\",\n             \"dropequal\",\n             \"DropEqual\"\n            ],\n            \"type\": \"string\"\n           },\n           \"modulus\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"regex\": {\n            \"type\": \"string\"\n           },\n           \"replacement\": {\n            \"type\": \"string\"\n           },\n           \"separator\": {\n            \"type\": \"string\"\n           },\n           \"sourceLabels\": {\n            \"items\": {\n             \"pattern\": \"^[a-zA-Z_][a-zA-Z0-9_]*$\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"targetLabel\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"scheme\": {\n         \"type\": \"string\"\n        },\n        \"tlsConfig\": {\n         \"properties\": {\n          \"ca\": {\n           \"properties\": {\n            \"configMap\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"secret\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"caFile\": {\n           \"type\": \"string\"\n          },\n          \"cert\": {\n           \"properties\": {\n            \"configMap\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"secret\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"certFile\": {\n           \"type\": \"string\"\n          },\n          \"insecureSkipVerify\": {\n           \"type\": \"boolean\"\n          },\n          \"keyFile\": {\n           \"type\": \"string\"\n          },\n          \"keySecret\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"maxVersion\": {\n           \"enum\": [\n            \"TLS10\",\n            \"TLS11\",\n            \"TLS12\",\n            \"TLS13\"\n           ],\n           \"type\": \"string\"\n          },\n          \"minVersion\": {\n           \"enum\": [\n            \"TLS10\",\n            \"TLS11\",\n            \"TLS12\",\n            \"TLS13\"\n           ],\n           \"type\": \"string\"\n          },\n          \"serverName\": {\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"timeout\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"bufferVolumeMetricsImage\": {\n     \"properties\": {\n      \"repository\": {\n       \"type\": \"string\"\n      },\n      \"tag\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"bufferVolumeMetricsLivenessProbe\": {\n     \"properties\": {\n      \"exec\": {\n       \"properties\": {\n        \"command\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"failureThreshold\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"grpc\": {\n       \"properties\": {\n        \"port\": {\n         \"format\": \"int32\",\n         \"type\": \"integer\"\n        },\n        \"service\": {\n         \"default\": \"\",\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"port\"\n       ],\n       \"type\": \"object\"\n      },\n      \"httpGet\": {\n       \"properties\": {\n        \"host\": {\n         \"type\": \"string\"\n        },\n        \"httpHeaders\": {\n         \"items\": {\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"value\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\",\n           \"value\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"port\": {\n         \"anyOf\": [\n          {\n           \"type\": \"integer\"\n          },\n          {\n           \"type\": \"string\"\n          }\n         ],\n         \"x-kubernetes-int-or-string\": true\n        },\n        \"scheme\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"port\"\n       ],\n       \"type\": \"object\"\n      },\n      \"initialDelaySeconds\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"periodSeconds\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"successThreshold\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"tcpSocket\": {\n       \"properties\": {\n        \"host\": {\n         \"type\": \"string\"\n        },\n        \"port\": {\n         \"anyOf\": [\n          {\n           \"type\": \"integer\"\n          },\n          {\n           \"type\": \"string\"\n          }\n         ],\n         \"x-kubernetes-int-or-string\": true\n        }\n       },\n       \"required\": [\n        \"port\"\n       ],\n       \"type\": \"object\"\n      },\n      \"terminationGracePeriodSeconds\": {\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"timeoutSeconds\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"bufferVolumeMetricsResources\": {\n     \"properties\": {\n      \"claims\": {\n       \"items\": {\n        \"properties\": {\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"request\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"name\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-map-keys\": [\n        \"name\"\n       ],\n       \"x-kubernetes-list-type\": \"map\"\n      },\n      \"limits\": {\n       \"additionalProperties\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n        \"x-kubernetes-int-or-string\": true\n       },\n       \"type\": \"object\"\n      },\n      \"requests\": {\n       \"additionalProperties\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n        \"x-kubernetes-int-or-string\": true\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"bufferVolumeMetricsService\": {\n     \"properties\": {\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"properties\": {\n        \"allocateLoadBalancerNodePorts\": {\n         \"type\": \"boolean\"\n        },\n        \"clusterIP\": {\n         \"type\": \"string\"\n        },\n        \"clusterIPs\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"externalIPs\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"externalName\": {\n         \"type\": \"string\"\n        },\n        \"externalTrafficPolicy\": {\n         \"type\": \"string\"\n        },\n        \"healthCheckNodePort\": {\n         \"format\": \"int32\",\n         \"type\": \"integer\"\n        },\n        \"internalTrafficPolicy\": {\n         \"type\": \"string\"\n        },\n        \"ipFamilies\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"ipFamilyPolicy\": {\n         \"type\": \"string\"\n        },\n        \"loadBalancerClass\": {\n         \"type\": \"string\"\n        },\n        \"loadBalancerIP\": {\n         \"type\": \"string\"\n        },\n        \"loadBalancerSourceRanges\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"ports\": {\n         \"items\": {\n          \"properties\": {\n           \"appProtocol\": {\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"nodePort\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"port\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"protocol\": {\n            \"default\": \"TCP\",\n            \"type\": \"string\"\n           },\n           \"targetPort\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-map-keys\": [\n          \"port\",\n          \"protocol\"\n         ],\n         \"x-kubernetes-list-type\": \"map\"\n        },\n        \"publishNotReadyAddresses\": {\n         \"type\": \"boolean\"\n        },\n        \"selector\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"sessionAffinity\": {\n         \"type\": \"string\"\n        },\n        \"sessionAffinityConfig\": {\n         \"properties\": {\n          \"clientIP\": {\n           \"properties\": {\n            \"timeoutSeconds\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"trafficDistribution\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"configCheck\": {\n     \"properties\": {\n      \"labels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"object\"\n      },\n      \"strategy\": {\n       \"type\": \"string\"\n      },\n      \"timeoutSeconds\": {\n       \"type\": \"integer\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"configCheckPod\": {\n     \"properties\": {\n      \"activeDeadlineSeconds\": {\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"affinity\": {\n       \"properties\": {\n        \"nodeAffinity\": {\n         \"properties\": {\n          \"preferredDuringSchedulingIgnoredDuringExecution\": {\n           \"items\": {\n            \"properties\": {\n             \"preference\": {\n              \"properties\": {\n               \"matchExpressions\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchFields\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"weight\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"required\": [\n             \"preference\",\n             \"weight\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"requiredDuringSchedulingIgnoredDuringExecution\": {\n           \"properties\": {\n            \"nodeSelectorTerms\": {\n             \"items\": {\n              \"properties\": {\n               \"matchExpressions\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchFields\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"required\": [\n            \"nodeSelectorTerms\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"podAffinity\": {\n         \"properties\": {\n          \"preferredDuringSchedulingIgnoredDuringExecution\": {\n           \"items\": {\n            \"properties\": {\n             \"podAffinityTerm\": {\n              \"properties\": {\n               \"labelSelector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"matchLabelKeys\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"mismatchLabelKeys\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"namespaceSelector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaces\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"topologyKey\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"topologyKey\"\n              ],\n              \"type\": \"object\"\n             },\n             \"weight\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"required\": [\n             \"podAffinityTerm\",\n             \"weight\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"requiredDuringSchedulingIgnoredDuringExecution\": {\n           \"items\": {\n            \"properties\": {\n             \"labelSelector\": {\n              \"properties\": {\n               \"matchExpressions\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"matchLabelKeys\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"mismatchLabelKeys\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"namespaceSelector\": {\n              \"properties\": {\n               \"matchExpressions\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"namespaces\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"topologyKey\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"topologyKey\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"podAntiAffinity\": {\n         \"properties\": {\n          \"preferredDuringSchedulingIgnoredDuringExecution\": {\n           \"items\": {\n            \"properties\": {\n             \"podAffinityTerm\": {\n              \"properties\": {\n               \"labelSelector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"matchLabelKeys\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"mismatchLabelKeys\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"namespaceSelector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaces\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"topologyKey\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"topologyKey\"\n              ],\n              \"type\": \"object\"\n             },\n             \"weight\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"required\": [\n             \"podAffinityTerm\",\n             \"weight\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"requiredDuringSchedulingIgnoredDuringExecution\": {\n           \"items\": {\n            \"properties\": {\n             \"labelSelector\": {\n              \"properties\": {\n               \"matchExpressions\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"matchLabelKeys\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"mismatchLabelKeys\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"namespaceSelector\": {\n              \"properties\": {\n               \"matchExpressions\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"namespaces\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"topologyKey\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"topologyKey\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"automountServiceAccountToken\": {\n       \"type\": \"boolean\"\n      },\n      \"containers\": {\n       \"items\": {\n        \"properties\": {\n         \"args\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"command\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"env\": {\n          \"items\": {\n           \"properties\": {\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"value\": {\n             \"type\": \"string\"\n            },\n            \"valueFrom\": {\n             \"properties\": {\n              \"configMapKeyRef\": {\n               \"properties\": {\n                \"key\": {\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"fieldRef\": {\n               \"properties\": {\n                \"apiVersion\": {\n                 \"type\": \"string\"\n                },\n                \"fieldPath\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"fieldPath\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"resourceFieldRef\": {\n               \"properties\": {\n                \"containerName\": {\n                 \"type\": \"string\"\n                },\n                \"divisor\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"resource\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"resource\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"secretKeyRef\": {\n               \"properties\": {\n                \"key\": {\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"name\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"envFrom\": {\n          \"items\": {\n           \"properties\": {\n            \"configMapRef\": {\n             \"properties\": {\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"prefix\": {\n             \"type\": \"string\"\n            },\n            \"secretRef\": {\n             \"properties\": {\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"image\": {\n          \"type\": \"string\"\n         },\n         \"imagePullPolicy\": {\n          \"type\": \"string\"\n         },\n         \"lifecycle\": {\n          \"properties\": {\n           \"postStart\": {\n            \"properties\": {\n             \"exec\": {\n              \"properties\": {\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"path\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"sleep\": {\n              \"properties\": {\n               \"seconds\": {\n                \"format\": \"int64\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"seconds\"\n              ],\n              \"type\": \"object\"\n             },\n             \"tcpSocket\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"preStop\": {\n            \"properties\": {\n             \"exec\": {\n              \"properties\": {\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"path\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"sleep\": {\n              \"properties\": {\n               \"seconds\": {\n                \"format\": \"int64\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"seconds\"\n              ],\n              \"type\": \"object\"\n             },\n             \"tcpSocket\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"livenessProbe\": {\n          \"properties\": {\n           \"exec\": {\n            \"properties\": {\n             \"command\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"failureThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"grpc\": {\n            \"properties\": {\n             \"port\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"service\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"initialDelaySeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"periodSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"successThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"tcpSocket\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terminationGracePeriodSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"timeoutSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"ports\": {\n          \"items\": {\n           \"properties\": {\n            \"containerPort\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"hostIP\": {\n             \"type\": \"string\"\n            },\n            \"hostPort\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"protocol\": {\n             \"default\": \"TCP\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"containerPort\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"containerPort\",\n           \"protocol\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"readinessProbe\": {\n          \"properties\": {\n           \"exec\": {\n            \"properties\": {\n             \"command\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"failureThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"grpc\": {\n            \"properties\": {\n             \"port\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"service\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"initialDelaySeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"periodSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"successThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"tcpSocket\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terminationGracePeriodSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"timeoutSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"resizePolicy\": {\n          \"items\": {\n           \"properties\": {\n            \"resourceName\": {\n             \"type\": \"string\"\n            },\n            \"restartPolicy\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"resourceName\",\n            \"restartPolicy\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"resources\": {\n          \"properties\": {\n           \"claims\": {\n            \"items\": {\n             \"properties\": {\n              \"name\": {\n               \"type\": \"string\"\n              },\n              \"request\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"name\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"limits\": {\n            \"additionalProperties\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n             \"x-kubernetes-int-or-string\": true\n            },\n            \"type\": \"object\"\n           },\n           \"requests\": {\n            \"additionalProperties\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n             \"x-kubernetes-int-or-string\": true\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"restartPolicy\": {\n          \"type\": \"string\"\n         },\n         \"securityContext\": {\n          \"properties\": {\n           \"allowPrivilegeEscalation\": {\n            \"type\": \"boolean\"\n           },\n           \"appArmorProfile\": {\n            \"properties\": {\n             \"localhostProfile\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"type\"\n            ],\n            \"type\": \"object\"\n           },\n           \"capabilities\": {\n            \"properties\": {\n             \"add\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"drop\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"privileged\": {\n            \"type\": \"boolean\"\n           },\n           \"procMount\": {\n            \"type\": \"string\"\n           },\n           \"readOnlyRootFilesystem\": {\n            \"type\": \"boolean\"\n           },\n           \"runAsGroup\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"runAsNonRoot\": {\n            \"type\": \"boolean\"\n           },\n           \"runAsUser\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"seLinuxOptions\": {\n            \"properties\": {\n             \"level\": {\n              \"type\": \"string\"\n             },\n             \"role\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             },\n             \"user\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"seccompProfile\": {\n            \"properties\": {\n             \"localhostProfile\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"type\"\n            ],\n            \"type\": \"object\"\n           },\n           \"windowsOptions\": {\n            \"properties\": {\n             \"gmsaCredentialSpec\": {\n              \"type\": \"string\"\n             },\n             \"gmsaCredentialSpecName\": {\n              \"type\": \"string\"\n             },\n             \"hostProcess\": {\n              \"type\": \"boolean\"\n             },\n             \"runAsUserName\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"startupProbe\": {\n          \"properties\": {\n           \"exec\": {\n            \"properties\": {\n             \"command\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"failureThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"grpc\": {\n            \"properties\": {\n             \"port\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"service\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"initialDelaySeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"periodSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"successThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"tcpSocket\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terminationGracePeriodSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"timeoutSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"stdin\": {\n          \"type\": \"boolean\"\n         },\n         \"stdinOnce\": {\n          \"type\": \"boolean\"\n         },\n         \"terminationMessagePath\": {\n          \"type\": \"string\"\n         },\n         \"terminationMessagePolicy\": {\n          \"type\": \"string\"\n         },\n         \"tty\": {\n          \"type\": \"boolean\"\n         },\n         \"volumeDevices\": {\n          \"items\": {\n           \"properties\": {\n            \"devicePath\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"devicePath\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"devicePath\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"volumeMounts\": {\n          \"items\": {\n           \"properties\": {\n            \"mountPath\": {\n             \"type\": \"string\"\n            },\n            \"mountPropagation\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"readOnly\": {\n             \"type\": \"boolean\"\n            },\n            \"recursiveReadOnly\": {\n             \"type\": \"string\"\n            },\n            \"subPath\": {\n             \"type\": \"string\"\n            },\n            \"subPathExpr\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"mountPath\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"mountPath\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"workingDir\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"name\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"dnsConfig\": {\n       \"properties\": {\n        \"nameservers\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"options\": {\n         \"items\": {\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"value\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"searches\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"dnsPolicy\": {\n       \"type\": \"string\"\n      },\n      \"enableServiceLinks\": {\n       \"type\": \"boolean\"\n      },\n      \"ephemeralContainers\": {\n       \"items\": {\n        \"properties\": {\n         \"args\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"command\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"env\": {\n          \"items\": {\n           \"properties\": {\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"value\": {\n             \"type\": \"string\"\n            },\n            \"valueFrom\": {\n             \"properties\": {\n              \"configMapKeyRef\": {\n               \"properties\": {\n                \"key\": {\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"fieldRef\": {\n               \"properties\": {\n                \"apiVersion\": {\n                 \"type\": \"string\"\n                },\n                \"fieldPath\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"fieldPath\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"resourceFieldRef\": {\n               \"properties\": {\n                \"containerName\": {\n                 \"type\": \"string\"\n                },\n                \"divisor\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"resource\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"resource\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"secretKeyRef\": {\n               \"properties\": {\n                \"key\": {\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"name\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"envFrom\": {\n          \"items\": {\n           \"properties\": {\n            \"configMapRef\": {\n             \"properties\": {\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"prefix\": {\n             \"type\": \"string\"\n            },\n            \"secretRef\": {\n             \"properties\": {\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"image\": {\n          \"type\": \"string\"\n         },\n         \"imagePullPolicy\": {\n          \"type\": \"string\"\n         },\n         \"lifecycle\": {\n          \"properties\": {\n           \"postStart\": {\n            \"properties\": {\n             \"exec\": {\n              \"properties\": {\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"path\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"sleep\": {\n              \"properties\": {\n               \"seconds\": {\n                \"format\": \"int64\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"seconds\"\n              ],\n              \"type\": \"object\"\n             },\n             \"tcpSocket\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"preStop\": {\n            \"properties\": {\n             \"exec\": {\n              \"properties\": {\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"path\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"sleep\": {\n              \"properties\": {\n               \"seconds\": {\n                \"format\": \"int64\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"seconds\"\n              ],\n              \"type\": \"object\"\n             },\n             \"tcpSocket\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"livenessProbe\": {\n          \"properties\": {\n           \"exec\": {\n            \"properties\": {\n             \"command\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"failureThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"grpc\": {\n            \"properties\": {\n             \"port\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"service\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"initialDelaySeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"periodSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"successThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"tcpSocket\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terminationGracePeriodSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"timeoutSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"ports\": {\n          \"items\": {\n           \"properties\": {\n            \"containerPort\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"hostIP\": {\n             \"type\": \"string\"\n            },\n            \"hostPort\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"protocol\": {\n             \"default\": \"TCP\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"containerPort\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"containerPort\",\n           \"protocol\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"readinessProbe\": {\n          \"properties\": {\n           \"exec\": {\n            \"properties\": {\n             \"command\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"failureThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"grpc\": {\n            \"properties\": {\n             \"port\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"service\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"initialDelaySeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"periodSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"successThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"tcpSocket\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terminationGracePeriodSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"timeoutSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"resizePolicy\": {\n          \"items\": {\n           \"properties\": {\n            \"resourceName\": {\n             \"type\": \"string\"\n            },\n            \"restartPolicy\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"resourceName\",\n            \"restartPolicy\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"resources\": {\n          \"properties\": {\n           \"claims\": {\n            \"items\": {\n             \"properties\": {\n              \"name\": {\n               \"type\": \"string\"\n              },\n              \"request\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"name\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"limits\": {\n            \"additionalProperties\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n             \"x-kubernetes-int-or-string\": true\n            },\n            \"type\": \"object\"\n           },\n           \"requests\": {\n            \"additionalProperties\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n             \"x-kubernetes-int-or-string\": true\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"restartPolicy\": {\n          \"type\": \"string\"\n         },\n         \"securityContext\": {\n          \"properties\": {\n           \"allowPrivilegeEscalation\": {\n            \"type\": \"boolean\"\n           },\n           \"appArmorProfile\": {\n            \"properties\": {\n             \"localhostProfile\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"type\"\n            ],\n            \"type\": \"object\"\n           },\n           \"capabilities\": {\n            \"properties\": {\n             \"add\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"drop\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"privileged\": {\n            \"type\": \"boolean\"\n           },\n           \"procMount\": {\n            \"type\": \"string\"\n           },\n           \"readOnlyRootFilesystem\": {\n            \"type\": \"boolean\"\n           },\n           \"runAsGroup\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"runAsNonRoot\": {\n            \"type\": \"boolean\"\n           },\n           \"runAsUser\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"seLinuxOptions\": {\n            \"properties\": {\n             \"level\": {\n              \"type\": \"string\"\n             },\n             \"role\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             },\n             \"user\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"seccompProfile\": {\n            \"properties\": {\n             \"localhostProfile\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"type\"\n            ],\n            \"type\": \"object\"\n           },\n           \"windowsOptions\": {\n            \"properties\": {\n             \"gmsaCredentialSpec\": {\n              \"type\": \"string\"\n             },\n             \"gmsaCredentialSpecName\": {\n              \"type\": \"string\"\n             },\n             \"hostProcess\": {\n              \"type\": \"boolean\"\n             },\n             \"runAsUserName\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"startupProbe\": {\n          \"properties\": {\n           \"exec\": {\n            \"properties\": {\n             \"command\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"failureThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"grpc\": {\n            \"properties\": {\n             \"port\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"service\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"initialDelaySeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"periodSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"successThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"tcpSocket\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terminationGracePeriodSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"timeoutSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"stdin\": {\n          \"type\": \"boolean\"\n         },\n         \"stdinOnce\": {\n          \"type\": \"boolean\"\n         },\n         \"targetContainerName\": {\n          \"type\": \"string\"\n         },\n         \"terminationMessagePath\": {\n          \"type\": \"string\"\n         },\n         \"terminationMessagePolicy\": {\n          \"type\": \"string\"\n         },\n         \"tty\": {\n          \"type\": \"boolean\"\n         },\n         \"volumeDevices\": {\n          \"items\": {\n           \"properties\": {\n            \"devicePath\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"devicePath\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"devicePath\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"volumeMounts\": {\n          \"items\": {\n           \"properties\": {\n            \"mountPath\": {\n             \"type\": \"string\"\n            },\n            \"mountPropagation\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"readOnly\": {\n             \"type\": \"boolean\"\n            },\n            \"recursiveReadOnly\": {\n             \"type\": \"string\"\n            },\n            \"subPath\": {\n             \"type\": \"string\"\n            },\n            \"subPathExpr\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"mountPath\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"mountPath\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"workingDir\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"name\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"hostAliases\": {\n       \"items\": {\n        \"properties\": {\n         \"hostnames\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"ip\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"ip\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"hostIPC\": {\n       \"type\": \"boolean\"\n      },\n      \"hostNetwork\": {\n       \"type\": \"boolean\"\n      },\n      \"hostPID\": {\n       \"type\": \"boolean\"\n      },\n      \"hostname\": {\n       \"type\": \"string\"\n      },\n      \"imagePullSecrets\": {\n       \"items\": {\n        \"properties\": {\n         \"name\": {\n          \"default\": \"\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\",\n        \"x-kubernetes-map-type\": \"atomic\"\n       },\n       \"type\": \"array\"\n      },\n      \"initContainers\": {\n       \"items\": {\n        \"properties\": {\n         \"args\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"command\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"env\": {\n          \"items\": {\n           \"properties\": {\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"value\": {\n             \"type\": \"string\"\n            },\n            \"valueFrom\": {\n             \"properties\": {\n              \"configMapKeyRef\": {\n               \"properties\": {\n                \"key\": {\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"fieldRef\": {\n               \"properties\": {\n                \"apiVersion\": {\n                 \"type\": \"string\"\n                },\n                \"fieldPath\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"fieldPath\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"resourceFieldRef\": {\n               \"properties\": {\n                \"containerName\": {\n                 \"type\": \"string\"\n                },\n                \"divisor\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"resource\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"resource\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"secretKeyRef\": {\n               \"properties\": {\n                \"key\": {\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"name\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"envFrom\": {\n          \"items\": {\n           \"properties\": {\n            \"configMapRef\": {\n             \"properties\": {\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"prefix\": {\n             \"type\": \"string\"\n            },\n            \"secretRef\": {\n             \"properties\": {\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"image\": {\n          \"type\": \"string\"\n         },\n         \"imagePullPolicy\": {\n          \"type\": \"string\"\n         },\n         \"lifecycle\": {\n          \"properties\": {\n           \"postStart\": {\n            \"properties\": {\n             \"exec\": {\n              \"properties\": {\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"path\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"sleep\": {\n              \"properties\": {\n               \"seconds\": {\n                \"format\": \"int64\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"seconds\"\n              ],\n              \"type\": \"object\"\n             },\n             \"tcpSocket\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"preStop\": {\n            \"properties\": {\n             \"exec\": {\n              \"properties\": {\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"path\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"sleep\": {\n              \"properties\": {\n               \"seconds\": {\n                \"format\": \"int64\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"seconds\"\n              ],\n              \"type\": \"object\"\n             },\n             \"tcpSocket\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"livenessProbe\": {\n          \"properties\": {\n           \"exec\": {\n            \"properties\": {\n             \"command\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"failureThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"grpc\": {\n            \"properties\": {\n             \"port\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"service\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"initialDelaySeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"periodSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"successThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"tcpSocket\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terminationGracePeriodSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"timeoutSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"ports\": {\n          \"items\": {\n           \"properties\": {\n            \"containerPort\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"hostIP\": {\n             \"type\": \"string\"\n            },\n            \"hostPort\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"protocol\": {\n             \"default\": \"TCP\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"containerPort\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"containerPort\",\n           \"protocol\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"readinessProbe\": {\n          \"properties\": {\n           \"exec\": {\n            \"properties\": {\n             \"command\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"failureThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"grpc\": {\n            \"properties\": {\n             \"port\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"service\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"initialDelaySeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"periodSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"successThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"tcpSocket\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terminationGracePeriodSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"timeoutSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"resizePolicy\": {\n          \"items\": {\n           \"properties\": {\n            \"resourceName\": {\n             \"type\": \"string\"\n            },\n            \"restartPolicy\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"resourceName\",\n            \"restartPolicy\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"resources\": {\n          \"properties\": {\n           \"claims\": {\n            \"items\": {\n             \"properties\": {\n              \"name\": {\n               \"type\": \"string\"\n              },\n              \"request\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"name\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"limits\": {\n            \"additionalProperties\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n             \"x-kubernetes-int-or-string\": true\n            },\n            \"type\": \"object\"\n           },\n           \"requests\": {\n            \"additionalProperties\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n             \"x-kubernetes-int-or-string\": true\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"restartPolicy\": {\n          \"type\": \"string\"\n         },\n         \"securityContext\": {\n          \"properties\": {\n           \"allowPrivilegeEscalation\": {\n            \"type\": \"boolean\"\n           },\n           \"appArmorProfile\": {\n            \"properties\": {\n             \"localhostProfile\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"type\"\n            ],\n            \"type\": \"object\"\n           },\n           \"capabilities\": {\n            \"properties\": {\n             \"add\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"drop\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"privileged\": {\n            \"type\": \"boolean\"\n           },\n           \"procMount\": {\n            \"type\": \"string\"\n           },\n           \"readOnlyRootFilesystem\": {\n            \"type\": \"boolean\"\n           },\n           \"runAsGroup\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"runAsNonRoot\": {\n            \"type\": \"boolean\"\n           },\n           \"runAsUser\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"seLinuxOptions\": {\n            \"properties\": {\n             \"level\": {\n              \"type\": \"string\"\n             },\n             \"role\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             },\n             \"user\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"seccompProfile\": {\n            \"properties\": {\n             \"localhostProfile\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"type\"\n            ],\n            \"type\": \"object\"\n           },\n           \"windowsOptions\": {\n            \"properties\": {\n             \"gmsaCredentialSpec\": {\n              \"type\": \"string\"\n             },\n             \"gmsaCredentialSpecName\": {\n              \"type\": \"string\"\n             },\n             \"hostProcess\": {\n              \"type\": \"boolean\"\n             },\n             \"runAsUserName\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"startupProbe\": {\n          \"properties\": {\n           \"exec\": {\n            \"properties\": {\n             \"command\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"failureThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"grpc\": {\n            \"properties\": {\n             \"port\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"service\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"initialDelaySeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"periodSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"successThreshold\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"tcpSocket\": {\n            \"properties\": {\n             \"host\": {\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"terminationGracePeriodSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"timeoutSeconds\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"stdin\": {\n          \"type\": \"boolean\"\n         },\n         \"stdinOnce\": {\n          \"type\": \"boolean\"\n         },\n         \"terminationMessagePath\": {\n          \"type\": \"string\"\n         },\n         \"terminationMessagePolicy\": {\n          \"type\": \"string\"\n         },\n         \"tty\": {\n          \"type\": \"boolean\"\n         },\n         \"volumeDevices\": {\n          \"items\": {\n           \"properties\": {\n            \"devicePath\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"devicePath\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"devicePath\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"volumeMounts\": {\n          \"items\": {\n           \"properties\": {\n            \"mountPath\": {\n             \"type\": \"string\"\n            },\n            \"mountPropagation\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"readOnly\": {\n             \"type\": \"boolean\"\n            },\n            \"recursiveReadOnly\": {\n             \"type\": \"string\"\n            },\n            \"subPath\": {\n             \"type\": \"string\"\n            },\n            \"subPathExpr\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"mountPath\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"mountPath\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"workingDir\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"name\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"nodeName\": {\n       \"type\": \"string\"\n      },\n      \"nodeSelector\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"object\"\n      },\n      \"overhead\": {\n       \"additionalProperties\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n        \"x-kubernetes-int-or-string\": true\n       },\n       \"type\": \"object\"\n      },\n      \"preemptionPolicy\": {\n       \"type\": \"string\"\n      },\n      \"priority\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"priorityClassName\": {\n       \"type\": \"string\"\n      },\n      \"readinessGates\": {\n       \"items\": {\n        \"properties\": {\n         \"conditionType\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"conditionType\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"restartPolicy\": {\n       \"type\": \"string\"\n      },\n      \"runtimeClassName\": {\n       \"type\": \"string\"\n      },\n      \"schedulerName\": {\n       \"type\": \"string\"\n      },\n      \"securityContext\": {\n       \"properties\": {\n        \"appArmorProfile\": {\n         \"properties\": {\n          \"localhostProfile\": {\n           \"type\": \"string\"\n          },\n          \"type\": {\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"type\"\n         ],\n         \"type\": \"object\"\n        },\n        \"fsGroup\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"fsGroupChangePolicy\": {\n         \"type\": \"string\"\n        },\n        \"runAsGroup\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"runAsNonRoot\": {\n         \"type\": \"boolean\"\n        },\n        \"runAsUser\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"seLinuxOptions\": {\n         \"properties\": {\n          \"level\": {\n           \"type\": \"string\"\n          },\n          \"role\": {\n           \"type\": \"string\"\n          },\n          \"type\": {\n           \"type\": \"string\"\n          },\n          \"user\": {\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"seccompProfile\": {\n         \"properties\": {\n          \"localhostProfile\": {\n           \"type\": \"string\"\n          },\n          \"type\": {\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"type\"\n         ],\n         \"type\": \"object\"\n        },\n        \"supplementalGroups\": {\n         \"items\": {\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"supplementalGroupsPolicy\": {\n         \"type\": \"string\"\n        },\n        \"sysctls\": {\n         \"items\": {\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"value\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\",\n           \"value\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"windowsOptions\": {\n         \"properties\": {\n          \"gmsaCredentialSpec\": {\n           \"type\": \"string\"\n          },\n          \"gmsaCredentialSpecName\": {\n           \"type\": \"string\"\n          },\n          \"hostProcess\": {\n           \"type\": \"boolean\"\n          },\n          \"runAsUserName\": {\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"serviceAccountName\": {\n       \"type\": \"string\"\n      },\n      \"setHostnameAsFQDN\": {\n       \"type\": \"boolean\"\n      },\n      \"shareProcessNamespace\": {\n       \"type\": \"boolean\"\n      },\n      \"subdomain\": {\n       \"type\": \"string\"\n      },\n      \"terminationGracePeriodSeconds\": {\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"tolerations\": {\n       \"items\": {\n        \"properties\": {\n         \"effect\": {\n          \"type\": \"string\"\n         },\n         \"key\": {\n          \"type\": \"string\"\n         },\n         \"operator\": {\n          \"type\": \"string\"\n         },\n         \"tolerationSeconds\": {\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"value\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"topologySpreadConstraints\": {\n       \"items\": {\n        \"properties\": {\n         \"labelSelector\": {\n          \"properties\": {\n           \"matchExpressions\": {\n            \"items\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"operator\": {\n               \"type\": \"string\"\n              },\n              \"values\": {\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              }\n             },\n             \"required\": [\n              \"key\",\n              \"operator\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"matchLabels\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"matchLabelKeys\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"maxSkew\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"minDomains\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"nodeAffinityPolicy\": {\n          \"type\": \"string\"\n         },\n         \"nodeTaintsPolicy\": {\n          \"type\": \"string\"\n         },\n         \"topologyKey\": {\n          \"type\": \"string\"\n         },\n         \"whenUnsatisfiable\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"maxSkew\",\n         \"topologyKey\",\n         \"whenUnsatisfiable\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-map-keys\": [\n        \"topologyKey\",\n        \"whenUnsatisfiable\"\n       ],\n       \"x-kubernetes-list-type\": \"map\"\n      },\n      \"volumes\": {\n       \"items\": {\n        \"properties\": {\n         \"awsElasticBlockStore\": {\n          \"properties\": {\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"partition\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"volumeID\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"volumeID\"\n          ],\n          \"type\": \"object\"\n         },\n         \"azureDisk\": {\n          \"properties\": {\n           \"cachingMode\": {\n            \"type\": \"string\"\n           },\n           \"diskName\": {\n            \"type\": \"string\"\n           },\n           \"diskURI\": {\n            \"type\": \"string\"\n           },\n           \"fsType\": {\n            \"default\": \"ext4\",\n            \"type\": \"string\"\n           },\n           \"kind\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"default\": false,\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"diskName\",\n           \"diskURI\"\n          ],\n          \"type\": \"object\"\n         },\n         \"azureFile\": {\n          \"properties\": {\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"secretName\": {\n            \"type\": \"string\"\n           },\n           \"shareName\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"secretName\",\n           \"shareName\"\n          ],\n          \"type\": \"object\"\n         },\n         \"cephfs\": {\n          \"properties\": {\n           \"monitors\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"path\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"secretFile\": {\n            \"type\": \"string\"\n           },\n           \"secretRef\": {\n            \"properties\": {\n             \"name\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"user\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"monitors\"\n          ],\n          \"type\": \"object\"\n         },\n         \"cinder\": {\n          \"properties\": {\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"secretRef\": {\n            \"properties\": {\n             \"name\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"volumeID\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"volumeID\"\n          ],\n          \"type\": \"object\"\n         },\n         \"configMap\": {\n          \"properties\": {\n           \"defaultMode\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"items\": {\n            \"items\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"mode\": {\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"path\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"key\",\n              \"path\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"type\": \"boolean\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"csi\": {\n          \"properties\": {\n           \"driver\": {\n            \"type\": \"string\"\n           },\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"nodePublishSecretRef\": {\n            \"properties\": {\n             \"name\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"volumeAttributes\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"driver\"\n          ],\n          \"type\": \"object\"\n         },\n         \"downwardAPI\": {\n          \"properties\": {\n           \"defaultMode\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"items\": {\n            \"items\": {\n             \"properties\": {\n              \"fieldRef\": {\n               \"properties\": {\n                \"apiVersion\": {\n                 \"type\": \"string\"\n                },\n                \"fieldPath\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"fieldPath\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"mode\": {\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"path\": {\n               \"type\": \"string\"\n              },\n              \"resourceFieldRef\": {\n               \"properties\": {\n                \"containerName\": {\n                 \"type\": \"string\"\n                },\n                \"divisor\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"resource\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"resource\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"emptyDir\": {\n          \"properties\": {\n           \"medium\": {\n            \"type\": \"string\"\n           },\n           \"sizeLimit\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"ephemeral\": {\n          \"properties\": {\n           \"volumeClaimTemplate\": {\n            \"properties\": {\n             \"metadata\": {\n              \"type\": \"object\"\n             },\n             \"spec\": {\n              \"properties\": {\n               \"accessModes\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"dataSource\": {\n                \"properties\": {\n                 \"apiGroup\": {\n                  \"type\": \"string\"\n                 },\n                 \"kind\": {\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"kind\",\n                 \"name\"\n                ],\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"dataSourceRef\": {\n                \"properties\": {\n                 \"apiGroup\": {\n                  \"type\": \"string\"\n                 },\n                 \"kind\": {\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"type\": \"string\"\n                 },\n                 \"namespace\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"kind\",\n                 \"name\"\n                ],\n                \"type\": \"object\"\n               },\n               \"resources\": {\n                \"properties\": {\n                 \"limits\": {\n                  \"additionalProperties\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"requests\": {\n                  \"additionalProperties\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"selector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"storageClassName\": {\n                \"type\": \"string\"\n               },\n               \"volumeAttributesClassName\": {\n                \"type\": \"string\"\n               },\n               \"volumeMode\": {\n                \"type\": \"string\"\n               },\n               \"volumeName\": {\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"spec\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"fc\": {\n          \"properties\": {\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"lun\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"targetWWNs\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"wwids\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"flexVolume\": {\n          \"properties\": {\n           \"driver\": {\n            \"type\": \"string\"\n           },\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"options\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"object\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"secretRef\": {\n            \"properties\": {\n             \"name\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           }\n          },\n          \"required\": [\n           \"driver\"\n          ],\n          \"type\": \"object\"\n         },\n         \"flocker\": {\n          \"properties\": {\n           \"datasetName\": {\n            \"type\": \"string\"\n           },\n           \"datasetUUID\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"gcePersistentDisk\": {\n          \"properties\": {\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"partition\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"pdName\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"pdName\"\n          ],\n          \"type\": \"object\"\n         },\n         \"gitRepo\": {\n          \"properties\": {\n           \"directory\": {\n            \"type\": \"string\"\n           },\n           \"repository\": {\n            \"type\": \"string\"\n           },\n           \"revision\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"repository\"\n          ],\n          \"type\": \"object\"\n         },\n         \"glusterfs\": {\n          \"properties\": {\n           \"endpoints\": {\n            \"type\": \"string\"\n           },\n           \"path\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"endpoints\",\n           \"path\"\n          ],\n          \"type\": \"object\"\n         },\n         \"hostPath\": {\n          \"properties\": {\n           \"path\": {\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"path\"\n          ],\n          \"type\": \"object\"\n         },\n         \"image\": {\n          \"properties\": {\n           \"pullPolicy\": {\n            \"type\": \"string\"\n           },\n           \"reference\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"iscsi\": {\n          \"properties\": {\n           \"chapAuthDiscovery\": {\n            \"type\": \"boolean\"\n           },\n           \"chapAuthSession\": {\n            \"type\": \"boolean\"\n           },\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"initiatorName\": {\n            \"type\": \"string\"\n           },\n           \"iqn\": {\n            \"type\": \"string\"\n           },\n           \"iscsiInterface\": {\n            \"default\": \"default\",\n            \"type\": \"string\"\n           },\n           \"lun\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"portals\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"secretRef\": {\n            \"properties\": {\n             \"name\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"targetPortal\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"iqn\",\n           \"lun\",\n           \"targetPortal\"\n          ],\n          \"type\": \"object\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"nfs\": {\n          \"properties\": {\n           \"path\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"server\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"path\",\n           \"server\"\n          ],\n          \"type\": \"object\"\n         },\n         \"persistentVolumeClaim\": {\n          \"properties\": {\n           \"claimName\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"claimName\"\n          ],\n          \"type\": \"object\"\n         },\n         \"photonPersistentDisk\": {\n          \"properties\": {\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"pdID\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"pdID\"\n          ],\n          \"type\": \"object\"\n         },\n         \"portworxVolume\": {\n          \"properties\": {\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"volumeID\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"volumeID\"\n          ],\n          \"type\": \"object\"\n         },\n         \"projected\": {\n          \"properties\": {\n           \"defaultMode\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"sources\": {\n            \"items\": {\n             \"properties\": {\n              \"clusterTrustBundle\": {\n               \"properties\": {\n                \"labelSelector\": {\n                 \"properties\": {\n                  \"matchExpressions\": {\n                   \"items\": {\n                    \"properties\": {\n                     \"key\": {\n                      \"type\": \"string\"\n                     },\n                     \"operator\": {\n                      \"type\": \"string\"\n                     },\n                     \"values\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     }\n                    },\n                    \"required\": [\n                     \"key\",\n                     \"operator\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  },\n                  \"matchLabels\": {\n                   \"additionalProperties\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"object\"\n                  }\n                 },\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                },\n                \"path\": {\n                 \"type\": \"string\"\n                },\n                \"signerName\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"path\"\n               ],\n               \"type\": \"object\"\n              },\n              \"configMap\": {\n               \"properties\": {\n                \"items\": {\n                 \"items\": {\n                  \"properties\": {\n                   \"key\": {\n                    \"type\": \"string\"\n                   },\n                   \"mode\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"path\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"downwardAPI\": {\n               \"properties\": {\n                \"items\": {\n                 \"items\": {\n                  \"properties\": {\n                   \"fieldRef\": {\n                    \"properties\": {\n                     \"apiVersion\": {\n                      \"type\": \"string\"\n                     },\n                     \"fieldPath\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"fieldPath\"\n                    ],\n                    \"type\": \"object\",\n                    \"x-kubernetes-map-type\": \"atomic\"\n                   },\n                   \"mode\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"resourceFieldRef\": {\n                    \"properties\": {\n                     \"containerName\": {\n                      \"type\": \"string\"\n                     },\n                     \"divisor\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                      \"x-kubernetes-int-or-string\": true\n                     },\n                     \"resource\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"resource\"\n                    ],\n                    \"type\": \"object\",\n                    \"x-kubernetes-map-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"path\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"secret\": {\n               \"properties\": {\n                \"items\": {\n                 \"items\": {\n                  \"properties\": {\n                   \"key\": {\n                    \"type\": \"string\"\n                   },\n                   \"mode\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"path\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"serviceAccountToken\": {\n               \"properties\": {\n                \"audience\": {\n                 \"type\": \"string\"\n                },\n                \"expirationSeconds\": {\n                 \"format\": \"int64\",\n                 \"type\": \"integer\"\n                },\n                \"path\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"path\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"quobyte\": {\n          \"properties\": {\n           \"group\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"registry\": {\n            \"type\": \"string\"\n           },\n           \"tenant\": {\n            \"type\": \"string\"\n           },\n           \"user\": {\n            \"type\": \"string\"\n           },\n           \"volume\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"registry\",\n           \"volume\"\n          ],\n          \"type\": \"object\"\n         },\n         \"rbd\": {\n          \"properties\": {\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"image\": {\n            \"type\": \"string\"\n           },\n           \"keyring\": {\n            \"default\": \"/etc/ceph/keyring\",\n            \"type\": \"string\"\n           },\n           \"monitors\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"pool\": {\n            \"default\": \"rbd\",\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"secretRef\": {\n            \"properties\": {\n             \"name\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"user\": {\n            \"default\": \"admin\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"image\",\n           \"monitors\"\n          ],\n          \"type\": \"object\"\n         },\n         \"scaleIO\": {\n          \"properties\": {\n           \"fsType\": {\n            \"default\": \"xfs\",\n            \"type\": \"string\"\n           },\n           \"gateway\": {\n            \"type\": \"string\"\n           },\n           \"protectionDomain\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"secretRef\": {\n            \"properties\": {\n             \"name\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"sslEnabled\": {\n            \"type\": \"boolean\"\n           },\n           \"storageMode\": {\n            \"default\": \"ThinProvisioned\",\n            \"type\": \"string\"\n           },\n           \"storagePool\": {\n            \"type\": \"string\"\n           },\n           \"system\": {\n            \"type\": \"string\"\n           },\n           \"volumeName\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"gateway\",\n           \"secretRef\",\n           \"system\"\n          ],\n          \"type\": \"object\"\n         },\n         \"secret\": {\n          \"properties\": {\n           \"defaultMode\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"items\": {\n            \"items\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"mode\": {\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"path\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"key\",\n              \"path\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"optional\": {\n            \"type\": \"boolean\"\n           },\n           \"secretName\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"storageos\": {\n          \"properties\": {\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"secretRef\": {\n            \"properties\": {\n             \"name\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"volumeName\": {\n            \"type\": \"string\"\n           },\n           \"volumeNamespace\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"vsphereVolume\": {\n          \"properties\": {\n           \"fsType\": {\n            \"type\": \"string\"\n           },\n           \"storagePolicyID\": {\n            \"type\": \"string\"\n           },\n           \"storagePolicyName\": {\n            \"type\": \"string\"\n           },\n           \"volumePath\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"volumePath\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"name\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"configReloadImage\": {\n     \"properties\": {\n      \"repository\": {\n       \"type\": \"string\"\n      },\n      \"tag\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"globalOptions\": {\n     \"properties\": {\n      \"log_level\": {\n       \"type\": \"string\"\n      },\n      \"stats\": {\n       \"properties\": {\n        \"freq\": {\n         \"type\": \"integer\"\n        },\n        \"level\": {\n         \"type\": \"integer\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"stats_freq\": {\n       \"type\": \"integer\"\n      },\n      \"stats_level\": {\n       \"type\": \"integer\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"jsonKeyDelim\": {\n     \"type\": \"string\"\n    },\n    \"jsonKeyPrefix\": {\n     \"type\": \"string\"\n    },\n    \"logIWSize\": {\n     \"type\": \"integer\"\n    },\n    \"maxConnections\": {\n     \"type\": \"integer\"\n    },\n    \"metrics\": {\n     \"properties\": {\n      \"interval\": {\n       \"type\": \"string\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"port\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"prometheusAnnotations\": {\n       \"type\": \"boolean\"\n      },\n      \"prometheusRules\": {\n       \"type\": \"boolean\"\n      },\n      \"prometheusRulesOverride\": {\n       \"items\": {\n        \"properties\": {\n         \"alert\": {\n          \"type\": \"string\"\n         },\n         \"annotations\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"expr\": {\n          \"anyOf\": [\n           {\n            \"type\": \"integer\"\n           },\n           {\n            \"type\": \"string\"\n           }\n          ],\n          \"x-kubernetes-int-or-string\": true\n         },\n         \"for\": {\n          \"pattern\": \"^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n          \"type\": \"string\"\n         },\n         \"keep_firing_for\": {\n          \"minLength\": 1,\n          \"pattern\": \"^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n          \"type\": \"string\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"record\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"serviceMonitor\": {\n       \"type\": \"boolean\"\n      },\n      \"serviceMonitorConfig\": {\n       \"properties\": {\n        \"additionalLabels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"honorLabels\": {\n         \"type\": \"boolean\"\n        },\n        \"metricRelabelings\": {\n         \"items\": {\n          \"properties\": {\n           \"action\": {\n            \"default\": \"replace\",\n            \"enum\": [\n             \"replace\",\n             \"Replace\",\n             \"keep\",\n             \"Keep\",\n             \"drop\",\n             \"Drop\",\n             \"hashmod\",\n             \"HashMod\",\n             \"labelmap\",\n             \"LabelMap\",\n             \"labeldrop\",\n             \"LabelDrop\",\n             \"labelkeep\",\n             \"LabelKeep\",\n             \"lowercase\",\n             \"Lowercase\",\n             \"uppercase\",\n             \"Uppercase\",\n             \"keepequal\",\n             \"KeepEqual\",\n             \"dropequal\",\n             \"DropEqual\"\n            ],\n            \"type\": \"string\"\n           },\n           \"modulus\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"regex\": {\n            \"type\": \"string\"\n           },\n           \"replacement\": {\n            \"type\": \"string\"\n           },\n           \"separator\": {\n            \"type\": \"string\"\n           },\n           \"sourceLabels\": {\n            \"items\": {\n             \"pattern\": \"^[a-zA-Z_][a-zA-Z0-9_]*$\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"targetLabel\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"relabelings\": {\n         \"items\": {\n          \"properties\": {\n           \"action\": {\n            \"default\": \"replace\",\n            \"enum\": [\n             \"replace\",\n             \"Replace\",\n             \"keep\",\n             \"Keep\",\n             \"drop\",\n             \"Drop\",\n             \"hashmod\",\n             \"HashMod\",\n             \"labelmap\",\n             \"LabelMap\",\n             \"labeldrop\",\n             \"LabelDrop\",\n             \"labelkeep\",\n             \"LabelKeep\",\n             \"lowercase\",\n             \"Lowercase\",\n             \"uppercase\",\n             \"Uppercase\",\n             \"keepequal\",\n             \"KeepEqual\",\n             \"dropequal\",\n             \"DropEqual\"\n            ],\n            \"type\": \"string\"\n           },\n           \"modulus\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"regex\": {\n            \"type\": \"string\"\n           },\n           \"replacement\": {\n            \"type\": \"string\"\n           },\n           \"separator\": {\n            \"type\": \"string\"\n           },\n           \"sourceLabels\": {\n            \"items\": {\n             \"pattern\": \"^[a-zA-Z_][a-zA-Z0-9_]*$\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"targetLabel\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"scheme\": {\n         \"type\": \"string\"\n        },\n        \"tlsConfig\": {\n         \"properties\": {\n          \"ca\": {\n           \"properties\": {\n            \"configMap\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"secret\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"caFile\": {\n           \"type\": \"string\"\n          },\n          \"cert\": {\n           \"properties\": {\n            \"configMap\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"secret\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"certFile\": {\n           \"type\": \"string\"\n          },\n          \"insecureSkipVerify\": {\n           \"type\": \"boolean\"\n          },\n          \"keyFile\": {\n           \"type\": \"string\"\n          },\n          \"keySecret\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"maxVersion\": {\n           \"enum\": [\n            \"TLS10\",\n            \"TLS11\",\n            \"TLS12\",\n            \"TLS13\"\n           ],\n           \"type\": \"string\"\n          },\n          \"minVersion\": {\n           \"enum\": [\n            \"TLS10\",\n            \"TLS11\",\n            \"TLS12\",\n            \"TLS13\"\n           ],\n           \"type\": \"string\"\n          },\n          \"serverName\": {\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"timeout\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"metricsExporterImage\": {\n     \"properties\": {\n      \"repository\": {\n       \"type\": \"string\"\n      },\n      \"tag\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"metricsService\": {\n     \"properties\": {\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"properties\": {\n        \"allocateLoadBalancerNodePorts\": {\n         \"type\": \"boolean\"\n        },\n        \"clusterIP\": {\n         \"type\": \"string\"\n        },\n        \"clusterIPs\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"externalIPs\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"externalName\": {\n         \"type\": \"string\"\n        },\n        \"externalTrafficPolicy\": {\n         \"type\": \"string\"\n        },\n        \"healthCheckNodePort\": {\n         \"format\": \"int32\",\n         \"type\": \"integer\"\n        },\n        \"internalTrafficPolicy\": {\n         \"type\": \"string\"\n        },\n        \"ipFamilies\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"ipFamilyPolicy\": {\n         \"type\": \"string\"\n        },\n        \"loadBalancerClass\": {\n         \"type\": \"string\"\n        },\n        \"loadBalancerIP\": {\n         \"type\": \"string\"\n        },\n        \"loadBalancerSourceRanges\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"ports\": {\n         \"items\": {\n          \"properties\": {\n           \"appProtocol\": {\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"nodePort\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"port\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"protocol\": {\n            \"default\": \"TCP\",\n            \"type\": \"string\"\n           },\n           \"targetPort\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-map-keys\": [\n          \"port\",\n          \"protocol\"\n         ],\n         \"x-kubernetes-list-type\": \"map\"\n        },\n        \"publishNotReadyAddresses\": {\n         \"type\": \"boolean\"\n        },\n        \"selector\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"sessionAffinity\": {\n         \"type\": \"string\"\n        },\n        \"sessionAffinityConfig\": {\n         \"properties\": {\n          \"clientIP\": {\n           \"properties\": {\n            \"timeoutSeconds\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"trafficDistribution\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"readinessDefaultCheck\": {\n     \"properties\": {\n      \"bufferFileNumber\": {\n       \"type\": \"boolean\"\n      },\n      \"bufferFileNumberMax\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"bufferFreeSpace\": {\n       \"type\": \"boolean\"\n      },\n      \"bufferFreeSpaceThreshold\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"failureThreshold\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"initialDelaySeconds\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"periodSeconds\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"successThreshold\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"timeoutSeconds\": {\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"service\": {\n     \"properties\": {\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"properties\": {\n        \"allocateLoadBalancerNodePorts\": {\n         \"type\": \"boolean\"\n        },\n        \"clusterIP\": {\n         \"type\": \"string\"\n        },\n        \"clusterIPs\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"externalIPs\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"externalName\": {\n         \"type\": \"string\"\n        },\n        \"externalTrafficPolicy\": {\n         \"type\": \"string\"\n        },\n        \"healthCheckNodePort\": {\n         \"format\": \"int32\",\n         \"type\": \"integer\"\n        },\n        \"internalTrafficPolicy\": {\n         \"type\": \"string\"\n        },\n        \"ipFamilies\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"ipFamilyPolicy\": {\n         \"type\": \"string\"\n        },\n        \"loadBalancerClass\": {\n         \"type\": \"string\"\n        },\n        \"loadBalancerIP\": {\n         \"type\": \"string\"\n        },\n        \"loadBalancerSourceRanges\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"ports\": {\n         \"items\": {\n          \"properties\": {\n           \"appProtocol\": {\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"nodePort\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"port\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"protocol\": {\n            \"default\": \"TCP\",\n            \"type\": \"string\"\n           },\n           \"targetPort\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-map-keys\": [\n          \"port\",\n          \"protocol\"\n         ],\n         \"x-kubernetes-list-type\": \"map\"\n        },\n        \"publishNotReadyAddresses\": {\n         \"type\": \"boolean\"\n        },\n        \"selector\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"sessionAffinity\": {\n         \"type\": \"string\"\n        },\n        \"sessionAffinityConfig\": {\n         \"properties\": {\n          \"clientIP\": {\n           \"properties\": {\n            \"timeoutSeconds\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"trafficDistribution\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"serviceAccount\": {\n     \"properties\": {\n      \"automountServiceAccountToken\": {\n       \"type\": \"boolean\"\n      },\n      \"imagePullSecrets\": {\n       \"items\": {\n        \"properties\": {\n         \"name\": {\n          \"default\": \"\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\",\n        \"x-kubernetes-map-type\": \"atomic\"\n       },\n       \"type\": \"array\"\n      },\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"secrets\": {\n       \"items\": {\n        \"properties\": {\n         \"apiVersion\": {\n          \"type\": \"string\"\n         },\n         \"fieldPath\": {\n          \"type\": \"string\"\n         },\n         \"kind\": {\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"type\": \"string\"\n         },\n         \"resourceVersion\": {\n          \"type\": \"string\"\n         },\n         \"uid\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\",\n        \"x-kubernetes-map-type\": \"atomic\"\n       },\n       \"type\": \"array\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"skipRBACCreate\": {\n     \"type\": \"boolean\"\n    },\n    \"sourceDateParser\": {\n     \"properties\": {\n      \"format\": {\n       \"type\": \"string\"\n      },\n      \"template\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"sourceMetrics\": {\n     \"items\": {\n      \"properties\": {\n       \"key\": {\n        \"type\": \"string\"\n       },\n       \"labels\": {\n        \"additionalProperties\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"object\"\n       },\n       \"level\": {\n        \"type\": \"integer\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"statefulSet\": {\n     \"properties\": {\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"properties\": {\n        \"podManagementPolicy\": {\n         \"type\": \"string\"\n        },\n        \"replicas\": {\n         \"format\": \"int32\",\n         \"type\": \"integer\"\n        },\n        \"revisionHistoryLimit\": {\n         \"format\": \"int32\",\n         \"type\": \"integer\"\n        },\n        \"selector\": {\n         \"properties\": {\n          \"matchExpressions\": {\n           \"items\": {\n            \"properties\": {\n             \"key\": {\n              \"type\": \"string\"\n             },\n             \"operator\": {\n              \"type\": \"string\"\n             },\n             \"values\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"required\": [\n             \"key\",\n             \"operator\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"matchLabels\": {\n           \"additionalProperties\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"serviceName\": {\n         \"type\": \"string\"\n        },\n        \"template\": {\n         \"properties\": {\n          \"metadata\": {\n           \"properties\": {\n            \"annotations\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"object\"\n            },\n            \"labels\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"spec\": {\n           \"properties\": {\n            \"activeDeadlineSeconds\": {\n             \"format\": \"int64\",\n             \"type\": \"integer\"\n            },\n            \"affinity\": {\n             \"properties\": {\n              \"nodeAffinity\": {\n               \"properties\": {\n                \"preferredDuringSchedulingIgnoredDuringExecution\": {\n                 \"items\": {\n                  \"properties\": {\n                   \"preference\": {\n                    \"properties\": {\n                     \"matchExpressions\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"key\": {\n                         \"type\": \"string\"\n                        },\n                        \"operator\": {\n                         \"type\": \"string\"\n                        },\n                        \"values\": {\n                         \"items\": {\n                          \"type\": \"string\"\n                         },\n                         \"type\": \"array\",\n                         \"x-kubernetes-list-type\": \"atomic\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"operator\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"matchFields\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"key\": {\n                         \"type\": \"string\"\n                        },\n                        \"operator\": {\n                         \"type\": \"string\"\n                        },\n                        \"values\": {\n                         \"items\": {\n                          \"type\": \"string\"\n                         },\n                         \"type\": \"array\",\n                         \"x-kubernetes-list-type\": \"atomic\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"operator\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     }\n                    },\n                    \"type\": \"object\",\n                    \"x-kubernetes-map-type\": \"atomic\"\n                   },\n                   \"weight\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   }\n                  },\n                  \"required\": [\n                   \"preference\",\n                   \"weight\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"requiredDuringSchedulingIgnoredDuringExecution\": {\n                 \"properties\": {\n                  \"nodeSelectorTerms\": {\n                   \"items\": {\n                    \"properties\": {\n                     \"matchExpressions\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"key\": {\n                         \"type\": \"string\"\n                        },\n                        \"operator\": {\n                         \"type\": \"string\"\n                        },\n                        \"values\": {\n                         \"items\": {\n                          \"type\": \"string\"\n                         },\n                         \"type\": \"array\",\n                         \"x-kubernetes-list-type\": \"atomic\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"operator\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"matchFields\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"key\": {\n                         \"type\": \"string\"\n                        },\n                        \"operator\": {\n                         \"type\": \"string\"\n                        },\n                        \"values\": {\n                         \"items\": {\n                          \"type\": \"string\"\n                         },\n                         \"type\": \"array\",\n                         \"x-kubernetes-list-type\": \"atomic\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"operator\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     }\n                    },\n                    \"type\": \"object\",\n                    \"x-kubernetes-map-type\": \"atomic\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"nodeSelectorTerms\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"podAffinity\": {\n               \"properties\": {\n                \"preferredDuringSchedulingIgnoredDuringExecution\": {\n                 \"items\": {\n                  \"properties\": {\n                   \"podAffinityTerm\": {\n                    \"properties\": {\n                     \"labelSelector\": {\n                      \"properties\": {\n                       \"matchExpressions\": {\n                        \"items\": {\n                         \"properties\": {\n                          \"key\": {\n                           \"type\": \"string\"\n                          },\n                          \"operator\": {\n                           \"type\": \"string\"\n                          },\n                          \"values\": {\n                           \"items\": {\n                            \"type\": \"string\"\n                           },\n                           \"type\": \"array\",\n                           \"x-kubernetes-list-type\": \"atomic\"\n                          }\n                         },\n                         \"required\": [\n                          \"key\",\n                          \"operator\"\n                         ],\n                         \"type\": \"object\"\n                        },\n                        \"type\": \"array\",\n                        \"x-kubernetes-list-type\": \"atomic\"\n                       },\n                       \"matchLabels\": {\n                        \"additionalProperties\": {\n                         \"type\": \"string\"\n                        },\n                        \"type\": \"object\"\n                       }\n                      },\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     },\n                     \"matchLabelKeys\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"mismatchLabelKeys\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"namespaceSelector\": {\n                      \"properties\": {\n                       \"matchExpressions\": {\n                        \"items\": {\n                         \"properties\": {\n                          \"key\": {\n                           \"type\": \"string\"\n                          },\n                          \"operator\": {\n                           \"type\": \"string\"\n                          },\n                          \"values\": {\n                           \"items\": {\n                            \"type\": \"string\"\n                           },\n                           \"type\": \"array\",\n                           \"x-kubernetes-list-type\": \"atomic\"\n                          }\n                         },\n                         \"required\": [\n                          \"key\",\n                          \"operator\"\n                         ],\n                         \"type\": \"object\"\n                        },\n                        \"type\": \"array\",\n                        \"x-kubernetes-list-type\": \"atomic\"\n                       },\n                       \"matchLabels\": {\n                        \"additionalProperties\": {\n                         \"type\": \"string\"\n                        },\n                        \"type\": \"object\"\n                       }\n                      },\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     },\n                     \"namespaces\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"topologyKey\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"topologyKey\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"weight\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   }\n                  },\n                  \"required\": [\n                   \"podAffinityTerm\",\n                   \"weight\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"requiredDuringSchedulingIgnoredDuringExecution\": {\n                 \"items\": {\n                  \"properties\": {\n                   \"labelSelector\": {\n                    \"properties\": {\n                     \"matchExpressions\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"key\": {\n                         \"type\": \"string\"\n                        },\n                        \"operator\": {\n                         \"type\": \"string\"\n                        },\n                        \"values\": {\n                         \"items\": {\n                          \"type\": \"string\"\n                         },\n                         \"type\": \"array\",\n                         \"x-kubernetes-list-type\": \"atomic\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"operator\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"matchLabels\": {\n                      \"additionalProperties\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"object\"\n                     }\n                    },\n                    \"type\": \"object\",\n                    \"x-kubernetes-map-type\": \"atomic\"\n                   },\n                   \"matchLabelKeys\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"mismatchLabelKeys\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"namespaceSelector\": {\n                    \"properties\": {\n                     \"matchExpressions\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"key\": {\n                         \"type\": \"string\"\n                        },\n                        \"operator\": {\n                         \"type\": \"string\"\n                        },\n                        \"values\": {\n                         \"items\": {\n                          \"type\": \"string\"\n                         },\n                         \"type\": \"array\",\n                         \"x-kubernetes-list-type\": \"atomic\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"operator\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"matchLabels\": {\n                      \"additionalProperties\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"object\"\n                     }\n                    },\n                    \"type\": \"object\",\n                    \"x-kubernetes-map-type\": \"atomic\"\n                   },\n                   \"namespaces\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"topologyKey\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"topologyKey\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"podAntiAffinity\": {\n               \"properties\": {\n                \"preferredDuringSchedulingIgnoredDuringExecution\": {\n                 \"items\": {\n                  \"properties\": {\n                   \"podAffinityTerm\": {\n                    \"properties\": {\n                     \"labelSelector\": {\n                      \"properties\": {\n                       \"matchExpressions\": {\n                        \"items\": {\n                         \"properties\": {\n                          \"key\": {\n                           \"type\": \"string\"\n                          },\n                          \"operator\": {\n                           \"type\": \"string\"\n                          },\n                          \"values\": {\n                           \"items\": {\n                            \"type\": \"string\"\n                           },\n                           \"type\": \"array\",\n                           \"x-kubernetes-list-type\": \"atomic\"\n                          }\n                         },\n                         \"required\": [\n                          \"key\",\n                          \"operator\"\n                         ],\n                         \"type\": \"object\"\n                        },\n                        \"type\": \"array\",\n                        \"x-kubernetes-list-type\": \"atomic\"\n                       },\n                       \"matchLabels\": {\n                        \"additionalProperties\": {\n                         \"type\": \"string\"\n                        },\n                        \"type\": \"object\"\n                       }\n                      },\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     },\n                     \"matchLabelKeys\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"mismatchLabelKeys\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"namespaceSelector\": {\n                      \"properties\": {\n                       \"matchExpressions\": {\n                        \"items\": {\n                         \"properties\": {\n                          \"key\": {\n                           \"type\": \"string\"\n                          },\n                          \"operator\": {\n                           \"type\": \"string\"\n                          },\n                          \"values\": {\n                           \"items\": {\n                            \"type\": \"string\"\n                           },\n                           \"type\": \"array\",\n                           \"x-kubernetes-list-type\": \"atomic\"\n                          }\n                         },\n                         \"required\": [\n                          \"key\",\n                          \"operator\"\n                         ],\n                         \"type\": \"object\"\n                        },\n                        \"type\": \"array\",\n                        \"x-kubernetes-list-type\": \"atomic\"\n                       },\n                       \"matchLabels\": {\n                        \"additionalProperties\": {\n                         \"type\": \"string\"\n                        },\n                        \"type\": \"object\"\n                       }\n                      },\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     },\n                     \"namespaces\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"topologyKey\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"topologyKey\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"weight\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   }\n                  },\n                  \"required\": [\n                   \"podAffinityTerm\",\n                   \"weight\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"requiredDuringSchedulingIgnoredDuringExecution\": {\n                 \"items\": {\n                  \"properties\": {\n                   \"labelSelector\": {\n                    \"properties\": {\n                     \"matchExpressions\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"key\": {\n                         \"type\": \"string\"\n                        },\n                        \"operator\": {\n                         \"type\": \"string\"\n                        },\n                        \"values\": {\n                         \"items\": {\n                          \"type\": \"string\"\n                         },\n                         \"type\": \"array\",\n                         \"x-kubernetes-list-type\": \"atomic\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"operator\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"matchLabels\": {\n                      \"additionalProperties\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"object\"\n                     }\n                    },\n                    \"type\": \"object\",\n                    \"x-kubernetes-map-type\": \"atomic\"\n                   },\n                   \"matchLabelKeys\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"mismatchLabelKeys\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"namespaceSelector\": {\n                    \"properties\": {\n                     \"matchExpressions\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"key\": {\n                         \"type\": \"string\"\n                        },\n                        \"operator\": {\n                         \"type\": \"string\"\n                        },\n                        \"values\": {\n                         \"items\": {\n                          \"type\": \"string\"\n                         },\n                         \"type\": \"array\",\n                         \"x-kubernetes-list-type\": \"atomic\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"operator\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"matchLabels\": {\n                      \"additionalProperties\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"object\"\n                     }\n                    },\n                    \"type\": \"object\",\n                    \"x-kubernetes-map-type\": \"atomic\"\n                   },\n                   \"namespaces\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"topologyKey\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"topologyKey\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"automountServiceAccountToken\": {\n             \"type\": \"boolean\"\n            },\n            \"containers\": {\n             \"items\": {\n              \"properties\": {\n               \"args\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"env\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  },\n                  \"valueFrom\": {\n                   \"properties\": {\n                    \"configMapKeyRef\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"name\": {\n                       \"default\": \"\",\n                       \"type\": \"string\"\n                      },\n                      \"optional\": {\n                       \"type\": \"boolean\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"fieldRef\": {\n                     \"properties\": {\n                      \"apiVersion\": {\n                       \"type\": \"string\"\n                      },\n                      \"fieldPath\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"fieldPath\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"resourceFieldRef\": {\n                     \"properties\": {\n                      \"containerName\": {\n                       \"type\": \"string\"\n                      },\n                      \"divisor\": {\n                       \"anyOf\": [\n                        {\n                         \"type\": \"integer\"\n                        },\n                        {\n                         \"type\": \"string\"\n                        }\n                       ],\n                       \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                       \"x-kubernetes-int-or-string\": true\n                      },\n                      \"resource\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"resource\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"secretKeyRef\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"name\": {\n                       \"default\": \"\",\n                       \"type\": \"string\"\n                      },\n                      \"optional\": {\n                       \"type\": \"boolean\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"name\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"envFrom\": {\n                \"items\": {\n                 \"properties\": {\n                  \"configMapRef\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"default\": \"\",\n                     \"type\": \"string\"\n                    },\n                    \"optional\": {\n                     \"type\": \"boolean\"\n                    }\n                   },\n                   \"type\": \"object\",\n                   \"x-kubernetes-map-type\": \"atomic\"\n                  },\n                  \"prefix\": {\n                   \"type\": \"string\"\n                  },\n                  \"secretRef\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"default\": \"\",\n                     \"type\": \"string\"\n                    },\n                    \"optional\": {\n                     \"type\": \"boolean\"\n                    }\n                   },\n                   \"type\": \"object\",\n                   \"x-kubernetes-map-type\": \"atomic\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"image\": {\n                \"type\": \"string\"\n               },\n               \"imagePullPolicy\": {\n                \"type\": \"string\"\n               },\n               \"lifecycle\": {\n                \"properties\": {\n                 \"postStart\": {\n                  \"properties\": {\n                   \"exec\": {\n                    \"properties\": {\n                     \"command\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"httpGet\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"httpHeaders\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"name\": {\n                         \"type\": \"string\"\n                        },\n                        \"value\": {\n                         \"type\": \"string\"\n                        }\n                       },\n                       \"required\": [\n                        \"name\",\n                        \"value\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"path\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     },\n                     \"scheme\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"sleep\": {\n                    \"properties\": {\n                     \"seconds\": {\n                      \"format\": \"int64\",\n                      \"type\": \"integer\"\n                     }\n                    },\n                    \"required\": [\n                     \"seconds\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"tcpSocket\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"preStop\": {\n                  \"properties\": {\n                   \"exec\": {\n                    \"properties\": {\n                     \"command\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"httpGet\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"httpHeaders\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"name\": {\n                         \"type\": \"string\"\n                        },\n                        \"value\": {\n                         \"type\": \"string\"\n                        }\n                       },\n                       \"required\": [\n                        \"name\",\n                        \"value\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"path\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     },\n                     \"scheme\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"sleep\": {\n                    \"properties\": {\n                     \"seconds\": {\n                      \"format\": \"int64\",\n                      \"type\": \"integer\"\n                     }\n                    },\n                    \"required\": [\n                     \"seconds\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"tcpSocket\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"livenessProbe\": {\n                \"properties\": {\n                 \"exec\": {\n                  \"properties\": {\n                   \"command\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"failureThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"grpc\": {\n                  \"properties\": {\n                   \"port\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"service\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"httpGet\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"httpHeaders\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"name\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"scheme\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"initialDelaySeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"periodSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"successThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"tcpSocket\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"terminationGracePeriodSeconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"timeoutSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"ports\": {\n                \"items\": {\n                 \"properties\": {\n                  \"containerPort\": {\n                   \"format\": \"int32\",\n                   \"type\": \"integer\"\n                  },\n                  \"hostIP\": {\n                   \"type\": \"string\"\n                  },\n                  \"hostPort\": {\n                   \"format\": \"int32\",\n                   \"type\": \"integer\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"protocol\": {\n                   \"default\": \"TCP\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"containerPort\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"containerPort\",\n                 \"protocol\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"readinessProbe\": {\n                \"properties\": {\n                 \"exec\": {\n                  \"properties\": {\n                   \"command\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"failureThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"grpc\": {\n                  \"properties\": {\n                   \"port\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"service\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"httpGet\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"httpHeaders\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"name\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"scheme\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"initialDelaySeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"periodSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"successThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"tcpSocket\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"terminationGracePeriodSeconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"timeoutSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"resizePolicy\": {\n                \"items\": {\n                 \"properties\": {\n                  \"resourceName\": {\n                   \"type\": \"string\"\n                  },\n                  \"restartPolicy\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resourceName\",\n                  \"restartPolicy\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"resources\": {\n                \"properties\": {\n                 \"claims\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"type\": \"string\"\n                    },\n                    \"request\": {\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-map-keys\": [\n                   \"name\"\n                  ],\n                  \"x-kubernetes-list-type\": \"map\"\n                 },\n                 \"limits\": {\n                  \"additionalProperties\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"requests\": {\n                  \"additionalProperties\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"restartPolicy\": {\n                \"type\": \"string\"\n               },\n               \"securityContext\": {\n                \"properties\": {\n                 \"allowPrivilegeEscalation\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"appArmorProfile\": {\n                  \"properties\": {\n                   \"localhostProfile\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"type\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"capabilities\": {\n                  \"properties\": {\n                   \"add\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"drop\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"privileged\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"procMount\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnlyRootFilesystem\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"runAsGroup\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"runAsNonRoot\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"runAsUser\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"seLinuxOptions\": {\n                  \"properties\": {\n                   \"level\": {\n                    \"type\": \"string\"\n                   },\n                   \"role\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": {\n                    \"type\": \"string\"\n                   },\n                   \"user\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"seccompProfile\": {\n                  \"properties\": {\n                   \"localhostProfile\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"type\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"windowsOptions\": {\n                  \"properties\": {\n                   \"gmsaCredentialSpec\": {\n                    \"type\": \"string\"\n                   },\n                   \"gmsaCredentialSpecName\": {\n                    \"type\": \"string\"\n                   },\n                   \"hostProcess\": {\n                    \"type\": \"boolean\"\n                   },\n                   \"runAsUserName\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"startupProbe\": {\n                \"properties\": {\n                 \"exec\": {\n                  \"properties\": {\n                   \"command\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"failureThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"grpc\": {\n                  \"properties\": {\n                   \"port\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"service\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"httpGet\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"httpHeaders\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"name\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"scheme\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"initialDelaySeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"periodSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"successThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"tcpSocket\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"terminationGracePeriodSeconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"timeoutSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"stdin\": {\n                \"type\": \"boolean\"\n               },\n               \"stdinOnce\": {\n                \"type\": \"boolean\"\n               },\n               \"terminationMessagePath\": {\n                \"type\": \"string\"\n               },\n               \"terminationMessagePolicy\": {\n                \"type\": \"string\"\n               },\n               \"tty\": {\n                \"type\": \"boolean\"\n               },\n               \"volumeDevices\": {\n                \"items\": {\n                 \"properties\": {\n                  \"devicePath\": {\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"devicePath\",\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"devicePath\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"volumeMounts\": {\n                \"items\": {\n                 \"properties\": {\n                  \"mountPath\": {\n                   \"type\": \"string\"\n                  },\n                  \"mountPropagation\": {\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"readOnly\": {\n                   \"type\": \"boolean\"\n                  },\n                  \"recursiveReadOnly\": {\n                   \"type\": \"string\"\n                  },\n                  \"subPath\": {\n                   \"type\": \"string\"\n                  },\n                  \"subPathExpr\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"mountPath\",\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"mountPath\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"workingDir\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"dnsConfig\": {\n             \"properties\": {\n              \"nameservers\": {\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"options\": {\n               \"items\": {\n                \"properties\": {\n                 \"name\": {\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"searches\": {\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"dnsPolicy\": {\n             \"type\": \"string\"\n            },\n            \"enableServiceLinks\": {\n             \"type\": \"boolean\"\n            },\n            \"ephemeralContainers\": {\n             \"items\": {\n              \"properties\": {\n               \"args\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"env\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  },\n                  \"valueFrom\": {\n                   \"properties\": {\n                    \"configMapKeyRef\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"name\": {\n                       \"default\": \"\",\n                       \"type\": \"string\"\n                      },\n                      \"optional\": {\n                       \"type\": \"boolean\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"fieldRef\": {\n                     \"properties\": {\n                      \"apiVersion\": {\n                       \"type\": \"string\"\n                      },\n                      \"fieldPath\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"fieldPath\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"resourceFieldRef\": {\n                     \"properties\": {\n                      \"containerName\": {\n                       \"type\": \"string\"\n                      },\n                      \"divisor\": {\n                       \"anyOf\": [\n                        {\n                         \"type\": \"integer\"\n                        },\n                        {\n                         \"type\": \"string\"\n                        }\n                       ],\n                       \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                       \"x-kubernetes-int-or-string\": true\n                      },\n                      \"resource\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"resource\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"secretKeyRef\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"name\": {\n                       \"default\": \"\",\n                       \"type\": \"string\"\n                      },\n                      \"optional\": {\n                       \"type\": \"boolean\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"name\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"envFrom\": {\n                \"items\": {\n                 \"properties\": {\n                  \"configMapRef\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"default\": \"\",\n                     \"type\": \"string\"\n                    },\n                    \"optional\": {\n                     \"type\": \"boolean\"\n                    }\n                   },\n                   \"type\": \"object\",\n                   \"x-kubernetes-map-type\": \"atomic\"\n                  },\n                  \"prefix\": {\n                   \"type\": \"string\"\n                  },\n                  \"secretRef\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"default\": \"\",\n                     \"type\": \"string\"\n                    },\n                    \"optional\": {\n                     \"type\": \"boolean\"\n                    }\n                   },\n                   \"type\": \"object\",\n                   \"x-kubernetes-map-type\": \"atomic\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"image\": {\n                \"type\": \"string\"\n               },\n               \"imagePullPolicy\": {\n                \"type\": \"string\"\n               },\n               \"lifecycle\": {\n                \"properties\": {\n                 \"postStart\": {\n                  \"properties\": {\n                   \"exec\": {\n                    \"properties\": {\n                     \"command\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"httpGet\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"httpHeaders\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"name\": {\n                         \"type\": \"string\"\n                        },\n                        \"value\": {\n                         \"type\": \"string\"\n                        }\n                       },\n                       \"required\": [\n                        \"name\",\n                        \"value\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"path\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     },\n                     \"scheme\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"sleep\": {\n                    \"properties\": {\n                     \"seconds\": {\n                      \"format\": \"int64\",\n                      \"type\": \"integer\"\n                     }\n                    },\n                    \"required\": [\n                     \"seconds\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"tcpSocket\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"preStop\": {\n                  \"properties\": {\n                   \"exec\": {\n                    \"properties\": {\n                     \"command\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"httpGet\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"httpHeaders\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"name\": {\n                         \"type\": \"string\"\n                        },\n                        \"value\": {\n                         \"type\": \"string\"\n                        }\n                       },\n                       \"required\": [\n                        \"name\",\n                        \"value\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"path\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     },\n                     \"scheme\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"sleep\": {\n                    \"properties\": {\n                     \"seconds\": {\n                      \"format\": \"int64\",\n                      \"type\": \"integer\"\n                     }\n                    },\n                    \"required\": [\n                     \"seconds\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"tcpSocket\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"livenessProbe\": {\n                \"properties\": {\n                 \"exec\": {\n                  \"properties\": {\n                   \"command\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"failureThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"grpc\": {\n                  \"properties\": {\n                   \"port\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"service\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"httpGet\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"httpHeaders\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"name\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"scheme\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"initialDelaySeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"periodSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"successThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"tcpSocket\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"terminationGracePeriodSeconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"timeoutSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"ports\": {\n                \"items\": {\n                 \"properties\": {\n                  \"containerPort\": {\n                   \"format\": \"int32\",\n                   \"type\": \"integer\"\n                  },\n                  \"hostIP\": {\n                   \"type\": \"string\"\n                  },\n                  \"hostPort\": {\n                   \"format\": \"int32\",\n                   \"type\": \"integer\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"protocol\": {\n                   \"default\": \"TCP\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"containerPort\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"containerPort\",\n                 \"protocol\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"readinessProbe\": {\n                \"properties\": {\n                 \"exec\": {\n                  \"properties\": {\n                   \"command\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"failureThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"grpc\": {\n                  \"properties\": {\n                   \"port\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"service\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"httpGet\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"httpHeaders\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"name\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"scheme\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"initialDelaySeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"periodSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"successThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"tcpSocket\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"terminationGracePeriodSeconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"timeoutSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"resizePolicy\": {\n                \"items\": {\n                 \"properties\": {\n                  \"resourceName\": {\n                   \"type\": \"string\"\n                  },\n                  \"restartPolicy\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resourceName\",\n                  \"restartPolicy\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"resources\": {\n                \"properties\": {\n                 \"claims\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"type\": \"string\"\n                    },\n                    \"request\": {\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-map-keys\": [\n                   \"name\"\n                  ],\n                  \"x-kubernetes-list-type\": \"map\"\n                 },\n                 \"limits\": {\n                  \"additionalProperties\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"requests\": {\n                  \"additionalProperties\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"restartPolicy\": {\n                \"type\": \"string\"\n               },\n               \"securityContext\": {\n                \"properties\": {\n                 \"allowPrivilegeEscalation\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"appArmorProfile\": {\n                  \"properties\": {\n                   \"localhostProfile\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"type\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"capabilities\": {\n                  \"properties\": {\n                   \"add\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"drop\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"privileged\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"procMount\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnlyRootFilesystem\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"runAsGroup\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"runAsNonRoot\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"runAsUser\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"seLinuxOptions\": {\n                  \"properties\": {\n                   \"level\": {\n                    \"type\": \"string\"\n                   },\n                   \"role\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": {\n                    \"type\": \"string\"\n                   },\n                   \"user\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"seccompProfile\": {\n                  \"properties\": {\n                   \"localhostProfile\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"type\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"windowsOptions\": {\n                  \"properties\": {\n                   \"gmsaCredentialSpec\": {\n                    \"type\": \"string\"\n                   },\n                   \"gmsaCredentialSpecName\": {\n                    \"type\": \"string\"\n                   },\n                   \"hostProcess\": {\n                    \"type\": \"boolean\"\n                   },\n                   \"runAsUserName\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"startupProbe\": {\n                \"properties\": {\n                 \"exec\": {\n                  \"properties\": {\n                   \"command\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"failureThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"grpc\": {\n                  \"properties\": {\n                   \"port\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"service\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"httpGet\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"httpHeaders\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"name\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"scheme\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"initialDelaySeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"periodSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"successThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"tcpSocket\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"terminationGracePeriodSeconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"timeoutSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"stdin\": {\n                \"type\": \"boolean\"\n               },\n               \"stdinOnce\": {\n                \"type\": \"boolean\"\n               },\n               \"targetContainerName\": {\n                \"type\": \"string\"\n               },\n               \"terminationMessagePath\": {\n                \"type\": \"string\"\n               },\n               \"terminationMessagePolicy\": {\n                \"type\": \"string\"\n               },\n               \"tty\": {\n                \"type\": \"boolean\"\n               },\n               \"volumeDevices\": {\n                \"items\": {\n                 \"properties\": {\n                  \"devicePath\": {\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"devicePath\",\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"devicePath\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"volumeMounts\": {\n                \"items\": {\n                 \"properties\": {\n                  \"mountPath\": {\n                   \"type\": \"string\"\n                  },\n                  \"mountPropagation\": {\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"readOnly\": {\n                   \"type\": \"boolean\"\n                  },\n                  \"recursiveReadOnly\": {\n                   \"type\": \"string\"\n                  },\n                  \"subPath\": {\n                   \"type\": \"string\"\n                  },\n                  \"subPathExpr\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"mountPath\",\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"mountPath\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"workingDir\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"hostAliases\": {\n             \"items\": {\n              \"properties\": {\n               \"hostnames\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"ip\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"ip\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"hostIPC\": {\n             \"type\": \"boolean\"\n            },\n            \"hostNetwork\": {\n             \"type\": \"boolean\"\n            },\n            \"hostPID\": {\n             \"type\": \"boolean\"\n            },\n            \"hostname\": {\n             \"type\": \"string\"\n            },\n            \"imagePullSecrets\": {\n             \"items\": {\n              \"properties\": {\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"type\": \"array\"\n            },\n            \"initContainers\": {\n             \"items\": {\n              \"properties\": {\n               \"args\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"env\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  },\n                  \"valueFrom\": {\n                   \"properties\": {\n                    \"configMapKeyRef\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"name\": {\n                       \"default\": \"\",\n                       \"type\": \"string\"\n                      },\n                      \"optional\": {\n                       \"type\": \"boolean\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"fieldRef\": {\n                     \"properties\": {\n                      \"apiVersion\": {\n                       \"type\": \"string\"\n                      },\n                      \"fieldPath\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"fieldPath\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"resourceFieldRef\": {\n                     \"properties\": {\n                      \"containerName\": {\n                       \"type\": \"string\"\n                      },\n                      \"divisor\": {\n                       \"anyOf\": [\n                        {\n                         \"type\": \"integer\"\n                        },\n                        {\n                         \"type\": \"string\"\n                        }\n                       ],\n                       \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                       \"x-kubernetes-int-or-string\": true\n                      },\n                      \"resource\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"resource\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"secretKeyRef\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"name\": {\n                       \"default\": \"\",\n                       \"type\": \"string\"\n                      },\n                      \"optional\": {\n                       \"type\": \"boolean\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"name\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"envFrom\": {\n                \"items\": {\n                 \"properties\": {\n                  \"configMapRef\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"default\": \"\",\n                     \"type\": \"string\"\n                    },\n                    \"optional\": {\n                     \"type\": \"boolean\"\n                    }\n                   },\n                   \"type\": \"object\",\n                   \"x-kubernetes-map-type\": \"atomic\"\n                  },\n                  \"prefix\": {\n                   \"type\": \"string\"\n                  },\n                  \"secretRef\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"default\": \"\",\n                     \"type\": \"string\"\n                    },\n                    \"optional\": {\n                     \"type\": \"boolean\"\n                    }\n                   },\n                   \"type\": \"object\",\n                   \"x-kubernetes-map-type\": \"atomic\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"image\": {\n                \"type\": \"string\"\n               },\n               \"imagePullPolicy\": {\n                \"type\": \"string\"\n               },\n               \"lifecycle\": {\n                \"properties\": {\n                 \"postStart\": {\n                  \"properties\": {\n                   \"exec\": {\n                    \"properties\": {\n                     \"command\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"httpGet\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"httpHeaders\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"name\": {\n                         \"type\": \"string\"\n                        },\n                        \"value\": {\n                         \"type\": \"string\"\n                        }\n                       },\n                       \"required\": [\n                        \"name\",\n                        \"value\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"path\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     },\n                     \"scheme\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"sleep\": {\n                    \"properties\": {\n                     \"seconds\": {\n                      \"format\": \"int64\",\n                      \"type\": \"integer\"\n                     }\n                    },\n                    \"required\": [\n                     \"seconds\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"tcpSocket\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"preStop\": {\n                  \"properties\": {\n                   \"exec\": {\n                    \"properties\": {\n                     \"command\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"httpGet\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"httpHeaders\": {\n                      \"items\": {\n                       \"properties\": {\n                        \"name\": {\n                         \"type\": \"string\"\n                        },\n                        \"value\": {\n                         \"type\": \"string\"\n                        }\n                       },\n                       \"required\": [\n                        \"name\",\n                        \"value\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"path\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     },\n                     \"scheme\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"sleep\": {\n                    \"properties\": {\n                     \"seconds\": {\n                      \"format\": \"int64\",\n                      \"type\": \"integer\"\n                     }\n                    },\n                    \"required\": [\n                     \"seconds\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"tcpSocket\": {\n                    \"properties\": {\n                     \"host\": {\n                      \"type\": \"string\"\n                     },\n                     \"port\": {\n                      \"anyOf\": [\n                       {\n                        \"type\": \"integer\"\n                       },\n                       {\n                        \"type\": \"string\"\n                       }\n                      ],\n                      \"x-kubernetes-int-or-string\": true\n                     }\n                    },\n                    \"required\": [\n                     \"port\"\n                    ],\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"livenessProbe\": {\n                \"properties\": {\n                 \"exec\": {\n                  \"properties\": {\n                   \"command\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"failureThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"grpc\": {\n                  \"properties\": {\n                   \"port\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"service\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"httpGet\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"httpHeaders\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"name\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"scheme\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"initialDelaySeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"periodSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"successThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"tcpSocket\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"terminationGracePeriodSeconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"timeoutSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"ports\": {\n                \"items\": {\n                 \"properties\": {\n                  \"containerPort\": {\n                   \"format\": \"int32\",\n                   \"type\": \"integer\"\n                  },\n                  \"hostIP\": {\n                   \"type\": \"string\"\n                  },\n                  \"hostPort\": {\n                   \"format\": \"int32\",\n                   \"type\": \"integer\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"protocol\": {\n                   \"default\": \"TCP\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"containerPort\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"containerPort\",\n                 \"protocol\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"readinessProbe\": {\n                \"properties\": {\n                 \"exec\": {\n                  \"properties\": {\n                   \"command\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"failureThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"grpc\": {\n                  \"properties\": {\n                   \"port\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"service\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"httpGet\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"httpHeaders\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"name\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"scheme\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"initialDelaySeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"periodSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"successThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"tcpSocket\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"terminationGracePeriodSeconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"timeoutSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"resizePolicy\": {\n                \"items\": {\n                 \"properties\": {\n                  \"resourceName\": {\n                   \"type\": \"string\"\n                  },\n                  \"restartPolicy\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resourceName\",\n                  \"restartPolicy\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"resources\": {\n                \"properties\": {\n                 \"claims\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"type\": \"string\"\n                    },\n                    \"request\": {\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-map-keys\": [\n                   \"name\"\n                  ],\n                  \"x-kubernetes-list-type\": \"map\"\n                 },\n                 \"limits\": {\n                  \"additionalProperties\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"requests\": {\n                  \"additionalProperties\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"restartPolicy\": {\n                \"type\": \"string\"\n               },\n               \"securityContext\": {\n                \"properties\": {\n                 \"allowPrivilegeEscalation\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"appArmorProfile\": {\n                  \"properties\": {\n                   \"localhostProfile\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"type\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"capabilities\": {\n                  \"properties\": {\n                   \"add\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"drop\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"privileged\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"procMount\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnlyRootFilesystem\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"runAsGroup\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"runAsNonRoot\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"runAsUser\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"seLinuxOptions\": {\n                  \"properties\": {\n                   \"level\": {\n                    \"type\": \"string\"\n                   },\n                   \"role\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": {\n                    \"type\": \"string\"\n                   },\n                   \"user\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"seccompProfile\": {\n                  \"properties\": {\n                   \"localhostProfile\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"type\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"windowsOptions\": {\n                  \"properties\": {\n                   \"gmsaCredentialSpec\": {\n                    \"type\": \"string\"\n                   },\n                   \"gmsaCredentialSpecName\": {\n                    \"type\": \"string\"\n                   },\n                   \"hostProcess\": {\n                    \"type\": \"boolean\"\n                   },\n                   \"runAsUserName\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"startupProbe\": {\n                \"properties\": {\n                 \"exec\": {\n                  \"properties\": {\n                   \"command\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"failureThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"grpc\": {\n                  \"properties\": {\n                   \"port\": {\n                    \"format\": \"int32\",\n                    \"type\": \"integer\"\n                   },\n                   \"service\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"httpGet\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"httpHeaders\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"value\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"name\",\n                      \"value\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"path\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"scheme\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"initialDelaySeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"periodSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"successThreshold\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"tcpSocket\": {\n                  \"properties\": {\n                   \"host\": {\n                    \"type\": \"string\"\n                   },\n                   \"port\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"x-kubernetes-int-or-string\": true\n                   }\n                  },\n                  \"required\": [\n                   \"port\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"terminationGracePeriodSeconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 },\n                 \"timeoutSeconds\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"stdin\": {\n                \"type\": \"boolean\"\n               },\n               \"stdinOnce\": {\n                \"type\": \"boolean\"\n               },\n               \"terminationMessagePath\": {\n                \"type\": \"string\"\n               },\n               \"terminationMessagePolicy\": {\n                \"type\": \"string\"\n               },\n               \"tty\": {\n                \"type\": \"boolean\"\n               },\n               \"volumeDevices\": {\n                \"items\": {\n                 \"properties\": {\n                  \"devicePath\": {\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"devicePath\",\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"devicePath\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"volumeMounts\": {\n                \"items\": {\n                 \"properties\": {\n                  \"mountPath\": {\n                   \"type\": \"string\"\n                  },\n                  \"mountPropagation\": {\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"readOnly\": {\n                   \"type\": \"boolean\"\n                  },\n                  \"recursiveReadOnly\": {\n                   \"type\": \"string\"\n                  },\n                  \"subPath\": {\n                   \"type\": \"string\"\n                  },\n                  \"subPathExpr\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"mountPath\",\n                  \"name\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"mountPath\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"workingDir\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"nodeName\": {\n             \"type\": \"string\"\n            },\n            \"nodeSelector\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"object\"\n            },\n            \"overhead\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"type\": \"object\"\n            },\n            \"preemptionPolicy\": {\n             \"type\": \"string\"\n            },\n            \"priority\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"priorityClassName\": {\n             \"type\": \"string\"\n            },\n            \"readinessGates\": {\n             \"items\": {\n              \"properties\": {\n               \"conditionType\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"conditionType\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"restartPolicy\": {\n             \"type\": \"string\"\n            },\n            \"runtimeClassName\": {\n             \"type\": \"string\"\n            },\n            \"schedulerName\": {\n             \"type\": \"string\"\n            },\n            \"securityContext\": {\n             \"properties\": {\n              \"appArmorProfile\": {\n               \"properties\": {\n                \"localhostProfile\": {\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              },\n              \"fsGroup\": {\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              },\n              \"fsGroupChangePolicy\": {\n               \"type\": \"string\"\n              },\n              \"runAsGroup\": {\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              },\n              \"runAsNonRoot\": {\n               \"type\": \"boolean\"\n              },\n              \"runAsUser\": {\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              },\n              \"seLinuxOptions\": {\n               \"properties\": {\n                \"level\": {\n                 \"type\": \"string\"\n                },\n                \"role\": {\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"type\": \"string\"\n                },\n                \"user\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"seccompProfile\": {\n               \"properties\": {\n                \"localhostProfile\": {\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              },\n              \"supplementalGroups\": {\n               \"items\": {\n                \"format\": \"int64\",\n                \"type\": \"integer\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"supplementalGroupsPolicy\": {\n               \"type\": \"string\"\n              },\n              \"sysctls\": {\n               \"items\": {\n                \"properties\": {\n                 \"name\": {\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\",\n                 \"value\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"windowsOptions\": {\n               \"properties\": {\n                \"gmsaCredentialSpec\": {\n                 \"type\": \"string\"\n                },\n                \"gmsaCredentialSpecName\": {\n                 \"type\": \"string\"\n                },\n                \"hostProcess\": {\n                 \"type\": \"boolean\"\n                },\n                \"runAsUserName\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"serviceAccountName\": {\n             \"type\": \"string\"\n            },\n            \"setHostnameAsFQDN\": {\n             \"type\": \"boolean\"\n            },\n            \"shareProcessNamespace\": {\n             \"type\": \"boolean\"\n            },\n            \"subdomain\": {\n             \"type\": \"string\"\n            },\n            \"terminationGracePeriodSeconds\": {\n             \"format\": \"int64\",\n             \"type\": \"integer\"\n            },\n            \"tolerations\": {\n             \"items\": {\n              \"properties\": {\n               \"effect\": {\n                \"type\": \"string\"\n               },\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"operator\": {\n                \"type\": \"string\"\n               },\n               \"tolerationSeconds\": {\n                \"format\": \"int64\",\n                \"type\": \"integer\"\n               },\n               \"value\": {\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"topologySpreadConstraints\": {\n             \"items\": {\n              \"properties\": {\n               \"labelSelector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"matchLabelKeys\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"maxSkew\": {\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"minDomains\": {\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"nodeAffinityPolicy\": {\n                \"type\": \"string\"\n               },\n               \"nodeTaintsPolicy\": {\n                \"type\": \"string\"\n               },\n               \"topologyKey\": {\n                \"type\": \"string\"\n               },\n               \"whenUnsatisfiable\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"maxSkew\",\n               \"topologyKey\",\n               \"whenUnsatisfiable\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-map-keys\": [\n              \"topologyKey\",\n              \"whenUnsatisfiable\"\n             ],\n             \"x-kubernetes-list-type\": \"map\"\n            },\n            \"volumes\": {\n             \"items\": {\n              \"properties\": {\n               \"awsElasticBlockStore\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"partition\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"volumeID\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"volumeID\"\n                ],\n                \"type\": \"object\"\n               },\n               \"azureDisk\": {\n                \"properties\": {\n                 \"cachingMode\": {\n                  \"type\": \"string\"\n                 },\n                 \"diskName\": {\n                  \"type\": \"string\"\n                 },\n                 \"diskURI\": {\n                  \"type\": \"string\"\n                 },\n                 \"fsType\": {\n                  \"default\": \"ext4\",\n                  \"type\": \"string\"\n                 },\n                 \"kind\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"default\": false,\n                  \"type\": \"boolean\"\n                 }\n                },\n                \"required\": [\n                 \"diskName\",\n                 \"diskURI\"\n                ],\n                \"type\": \"object\"\n               },\n               \"azureFile\": {\n                \"properties\": {\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"secretName\": {\n                  \"type\": \"string\"\n                 },\n                 \"shareName\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"secretName\",\n                 \"shareName\"\n                ],\n                \"type\": \"object\"\n               },\n               \"cephfs\": {\n                \"properties\": {\n                 \"monitors\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"path\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"secretFile\": {\n                  \"type\": \"string\"\n                 },\n                 \"secretRef\": {\n                  \"properties\": {\n                   \"name\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"user\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"monitors\"\n                ],\n                \"type\": \"object\"\n               },\n               \"cinder\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"secretRef\": {\n                  \"properties\": {\n                   \"name\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"volumeID\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"volumeID\"\n                ],\n                \"type\": \"object\"\n               },\n               \"configMap\": {\n                \"properties\": {\n                 \"defaultMode\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"items\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"mode\": {\n                     \"format\": \"int32\",\n                     \"type\": \"integer\"\n                    },\n                    \"path\": {\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"path\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"name\": {\n                  \"default\": \"\",\n                  \"type\": \"string\"\n                 },\n                 \"optional\": {\n                  \"type\": \"boolean\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"csi\": {\n                \"properties\": {\n                 \"driver\": {\n                  \"type\": \"string\"\n                 },\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"nodePublishSecretRef\": {\n                  \"properties\": {\n                   \"name\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"volumeAttributes\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"required\": [\n                 \"driver\"\n                ],\n                \"type\": \"object\"\n               },\n               \"downwardAPI\": {\n                \"properties\": {\n                 \"defaultMode\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"items\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"fieldRef\": {\n                     \"properties\": {\n                      \"apiVersion\": {\n                       \"type\": \"string\"\n                      },\n                      \"fieldPath\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"fieldPath\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"mode\": {\n                     \"format\": \"int32\",\n                     \"type\": \"integer\"\n                    },\n                    \"path\": {\n                     \"type\": \"string\"\n                    },\n                    \"resourceFieldRef\": {\n                     \"properties\": {\n                      \"containerName\": {\n                       \"type\": \"string\"\n                      },\n                      \"divisor\": {\n                       \"anyOf\": [\n                        {\n                         \"type\": \"integer\"\n                        },\n                        {\n                         \"type\": \"string\"\n                        }\n                       ],\n                       \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                       \"x-kubernetes-int-or-string\": true\n                      },\n                      \"resource\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"resource\"\n                     ],\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"path\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"emptyDir\": {\n                \"properties\": {\n                 \"medium\": {\n                  \"type\": \"string\"\n                 },\n                 \"sizeLimit\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                  \"x-kubernetes-int-or-string\": true\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"ephemeral\": {\n                \"properties\": {\n                 \"volumeClaimTemplate\": {\n                  \"properties\": {\n                   \"metadata\": {\n                    \"type\": \"object\"\n                   },\n                   \"spec\": {\n                    \"properties\": {\n                     \"accessModes\": {\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\",\n                      \"x-kubernetes-list-type\": \"atomic\"\n                     },\n                     \"dataSource\": {\n                      \"properties\": {\n                       \"apiGroup\": {\n                        \"type\": \"string\"\n                       },\n                       \"kind\": {\n                        \"type\": \"string\"\n                       },\n                       \"name\": {\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"required\": [\n                       \"kind\",\n                       \"name\"\n                      ],\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     },\n                     \"dataSourceRef\": {\n                      \"properties\": {\n                       \"apiGroup\": {\n                        \"type\": \"string\"\n                       },\n                       \"kind\": {\n                        \"type\": \"string\"\n                       },\n                       \"name\": {\n                        \"type\": \"string\"\n                       },\n                       \"namespace\": {\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"required\": [\n                       \"kind\",\n                       \"name\"\n                      ],\n                      \"type\": \"object\"\n                     },\n                     \"resources\": {\n                      \"properties\": {\n                       \"limits\": {\n                        \"additionalProperties\": {\n                         \"anyOf\": [\n                          {\n                           \"type\": \"integer\"\n                          },\n                          {\n                           \"type\": \"string\"\n                          }\n                         ],\n                         \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                         \"x-kubernetes-int-or-string\": true\n                        },\n                        \"type\": \"object\"\n                       },\n                       \"requests\": {\n                        \"additionalProperties\": {\n                         \"anyOf\": [\n                          {\n                           \"type\": \"integer\"\n                          },\n                          {\n                           \"type\": \"string\"\n                          }\n                         ],\n                         \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                         \"x-kubernetes-int-or-string\": true\n                        },\n                        \"type\": \"object\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"selector\": {\n                      \"properties\": {\n                       \"matchExpressions\": {\n                        \"items\": {\n                         \"properties\": {\n                          \"key\": {\n                           \"type\": \"string\"\n                          },\n                          \"operator\": {\n                           \"type\": \"string\"\n                          },\n                          \"values\": {\n                           \"items\": {\n                            \"type\": \"string\"\n                           },\n                           \"type\": \"array\",\n                           \"x-kubernetes-list-type\": \"atomic\"\n                          }\n                         },\n                         \"required\": [\n                          \"key\",\n                          \"operator\"\n                         ],\n                         \"type\": \"object\"\n                        },\n                        \"type\": \"array\",\n                        \"x-kubernetes-list-type\": \"atomic\"\n                       },\n                       \"matchLabels\": {\n                        \"additionalProperties\": {\n                         \"type\": \"string\"\n                        },\n                        \"type\": \"object\"\n                       }\n                      },\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     },\n                     \"storageClassName\": {\n                      \"type\": \"string\"\n                     },\n                     \"volumeAttributesClassName\": {\n                      \"type\": \"string\"\n                     },\n                     \"volumeMode\": {\n                      \"type\": \"string\"\n                     },\n                     \"volumeName\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"required\": [\n                   \"spec\"\n                  ],\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"fc\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"lun\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"targetWWNs\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"wwids\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"flexVolume\": {\n                \"properties\": {\n                 \"driver\": {\n                  \"type\": \"string\"\n                 },\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"options\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"secretRef\": {\n                  \"properties\": {\n                   \"name\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 }\n                },\n                \"required\": [\n                 \"driver\"\n                ],\n                \"type\": \"object\"\n               },\n               \"flocker\": {\n                \"properties\": {\n                 \"datasetName\": {\n                  \"type\": \"string\"\n                 },\n                 \"datasetUUID\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"gcePersistentDisk\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"partition\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"pdName\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 }\n                },\n                \"required\": [\n                 \"pdName\"\n                ],\n                \"type\": \"object\"\n               },\n               \"gitRepo\": {\n                \"properties\": {\n                 \"directory\": {\n                  \"type\": \"string\"\n                 },\n                 \"repository\": {\n                  \"type\": \"string\"\n                 },\n                 \"revision\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"repository\"\n                ],\n                \"type\": \"object\"\n               },\n               \"glusterfs\": {\n                \"properties\": {\n                 \"endpoints\": {\n                  \"type\": \"string\"\n                 },\n                 \"path\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 }\n                },\n                \"required\": [\n                 \"endpoints\",\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"hostPath\": {\n                \"properties\": {\n                 \"path\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"image\": {\n                \"properties\": {\n                 \"pullPolicy\": {\n                  \"type\": \"string\"\n                 },\n                 \"reference\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"iscsi\": {\n                \"properties\": {\n                 \"chapAuthDiscovery\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"chapAuthSession\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"initiatorName\": {\n                  \"type\": \"string\"\n                 },\n                 \"iqn\": {\n                  \"type\": \"string\"\n                 },\n                 \"iscsiInterface\": {\n                  \"default\": \"default\",\n                  \"type\": \"string\"\n                 },\n                 \"lun\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"portals\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"secretRef\": {\n                  \"properties\": {\n                   \"name\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"targetPortal\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"iqn\",\n                 \"lun\",\n                 \"targetPortal\"\n                ],\n                \"type\": \"object\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"nfs\": {\n                \"properties\": {\n                 \"path\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"server\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"path\",\n                 \"server\"\n                ],\n                \"type\": \"object\"\n               },\n               \"persistentVolumeClaim\": {\n                \"properties\": {\n                 \"claimName\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 }\n                },\n                \"required\": [\n                 \"claimName\"\n                ],\n                \"type\": \"object\"\n               },\n               \"photonPersistentDisk\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"pdID\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"pdID\"\n                ],\n                \"type\": \"object\"\n               },\n               \"portworxVolume\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"volumeID\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"volumeID\"\n                ],\n                \"type\": \"object\"\n               },\n               \"projected\": {\n                \"properties\": {\n                 \"defaultMode\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"sources\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"clusterTrustBundle\": {\n                     \"properties\": {\n                      \"labelSelector\": {\n                       \"properties\": {\n                        \"matchExpressions\": {\n                         \"items\": {\n                          \"properties\": {\n                           \"key\": {\n                            \"type\": \"string\"\n                           },\n                           \"operator\": {\n                            \"type\": \"string\"\n                           },\n                           \"values\": {\n                            \"items\": {\n                             \"type\": \"string\"\n                            },\n                            \"type\": \"array\",\n                            \"x-kubernetes-list-type\": \"atomic\"\n                           }\n                          },\n                          \"required\": [\n                           \"key\",\n                           \"operator\"\n                          ],\n                          \"type\": \"object\"\n                         },\n                         \"type\": \"array\",\n                         \"x-kubernetes-list-type\": \"atomic\"\n                        },\n                        \"matchLabels\": {\n                         \"additionalProperties\": {\n                          \"type\": \"string\"\n                         },\n                         \"type\": \"object\"\n                        }\n                       },\n                       \"type\": \"object\",\n                       \"x-kubernetes-map-type\": \"atomic\"\n                      },\n                      \"name\": {\n                       \"type\": \"string\"\n                      },\n                      \"optional\": {\n                       \"type\": \"boolean\"\n                      },\n                      \"path\": {\n                       \"type\": \"string\"\n                      },\n                      \"signerName\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"path\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"configMap\": {\n                     \"properties\": {\n                      \"items\": {\n                       \"items\": {\n                        \"properties\": {\n                         \"key\": {\n                          \"type\": \"string\"\n                         },\n                         \"mode\": {\n                          \"format\": \"int32\",\n                          \"type\": \"integer\"\n                         },\n                         \"path\": {\n                          \"type\": \"string\"\n                         }\n                        },\n                        \"required\": [\n                         \"key\",\n                         \"path\"\n                        ],\n                        \"type\": \"object\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      },\n                      \"name\": {\n                       \"default\": \"\",\n                       \"type\": \"string\"\n                      },\n                      \"optional\": {\n                       \"type\": \"boolean\"\n                      }\n                     },\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"downwardAPI\": {\n                     \"properties\": {\n                      \"items\": {\n                       \"items\": {\n                        \"properties\": {\n                         \"fieldRef\": {\n                          \"properties\": {\n                           \"apiVersion\": {\n                            \"type\": \"string\"\n                           },\n                           \"fieldPath\": {\n                            \"type\": \"string\"\n                           }\n                          },\n                          \"required\": [\n                           \"fieldPath\"\n                          ],\n                          \"type\": \"object\",\n                          \"x-kubernetes-map-type\": \"atomic\"\n                         },\n                         \"mode\": {\n                          \"format\": \"int32\",\n                          \"type\": \"integer\"\n                         },\n                         \"path\": {\n                          \"type\": \"string\"\n                         },\n                         \"resourceFieldRef\": {\n                          \"properties\": {\n                           \"containerName\": {\n                            \"type\": \"string\"\n                           },\n                           \"divisor\": {\n                            \"anyOf\": [\n                             {\n                              \"type\": \"integer\"\n                             },\n                             {\n                              \"type\": \"string\"\n                             }\n                            ],\n                            \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                            \"x-kubernetes-int-or-string\": true\n                           },\n                           \"resource\": {\n                            \"type\": \"string\"\n                           }\n                          },\n                          \"required\": [\n                           \"resource\"\n                          ],\n                          \"type\": \"object\",\n                          \"x-kubernetes-map-type\": \"atomic\"\n                         }\n                        },\n                        \"required\": [\n                         \"path\"\n                        ],\n                        \"type\": \"object\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"type\": \"object\"\n                    },\n                    \"secret\": {\n                     \"properties\": {\n                      \"items\": {\n                       \"items\": {\n                        \"properties\": {\n                         \"key\": {\n                          \"type\": \"string\"\n                         },\n                         \"mode\": {\n                          \"format\": \"int32\",\n                          \"type\": \"integer\"\n                         },\n                         \"path\": {\n                          \"type\": \"string\"\n                         }\n                        },\n                        \"required\": [\n                         \"key\",\n                         \"path\"\n                        ],\n                        \"type\": \"object\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      },\n                      \"name\": {\n                       \"default\": \"\",\n                       \"type\": \"string\"\n                      },\n                      \"optional\": {\n                       \"type\": \"boolean\"\n                      }\n                     },\n                     \"type\": \"object\",\n                     \"x-kubernetes-map-type\": \"atomic\"\n                    },\n                    \"serviceAccountToken\": {\n                     \"properties\": {\n                      \"audience\": {\n                       \"type\": \"string\"\n                      },\n                      \"expirationSeconds\": {\n                       \"format\": \"int64\",\n                       \"type\": \"integer\"\n                      },\n                      \"path\": {\n                       \"type\": \"string\"\n                      }\n                     },\n                     \"required\": [\n                      \"path\"\n                     ],\n                     \"type\": \"object\"\n                    }\n                   },\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"quobyte\": {\n                \"properties\": {\n                 \"group\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"registry\": {\n                  \"type\": \"string\"\n                 },\n                 \"tenant\": {\n                  \"type\": \"string\"\n                 },\n                 \"user\": {\n                  \"type\": \"string\"\n                 },\n                 \"volume\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"registry\",\n                 \"volume\"\n                ],\n                \"type\": \"object\"\n               },\n               \"rbd\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"image\": {\n                  \"type\": \"string\"\n                 },\n                 \"keyring\": {\n                  \"default\": \"/etc/ceph/keyring\",\n                  \"type\": \"string\"\n                 },\n                 \"monitors\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"pool\": {\n                  \"default\": \"rbd\",\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"secretRef\": {\n                  \"properties\": {\n                   \"name\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"user\": {\n                  \"default\": \"admin\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"image\",\n                 \"monitors\"\n                ],\n                \"type\": \"object\"\n               },\n               \"scaleIO\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"default\": \"xfs\",\n                  \"type\": \"string\"\n                 },\n                 \"gateway\": {\n                  \"type\": \"string\"\n                 },\n                 \"protectionDomain\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"secretRef\": {\n                  \"properties\": {\n                   \"name\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"sslEnabled\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"storageMode\": {\n                  \"default\": \"ThinProvisioned\",\n                  \"type\": \"string\"\n                 },\n                 \"storagePool\": {\n                  \"type\": \"string\"\n                 },\n                 \"system\": {\n                  \"type\": \"string\"\n                 },\n                 \"volumeName\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"gateway\",\n                 \"secretRef\",\n                 \"system\"\n                ],\n                \"type\": \"object\"\n               },\n               \"secret\": {\n                \"properties\": {\n                 \"defaultMode\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"items\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"mode\": {\n                     \"format\": \"int32\",\n                     \"type\": \"integer\"\n                    },\n                    \"path\": {\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"path\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"optional\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"secretName\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"storageos\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"readOnly\": {\n                  \"type\": \"boolean\"\n                 },\n                 \"secretRef\": {\n                  \"properties\": {\n                   \"name\": {\n                    \"default\": \"\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"volumeName\": {\n                  \"type\": \"string\"\n                 },\n                 \"volumeNamespace\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"vsphereVolume\": {\n                \"properties\": {\n                 \"fsType\": {\n                  \"type\": \"string\"\n                 },\n                 \"storagePolicyID\": {\n                  \"type\": \"string\"\n                 },\n                 \"storagePolicyName\": {\n                  \"type\": \"string\"\n                 },\n                 \"volumePath\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"volumePath\"\n                ],\n                \"type\": \"object\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"updateStrategy\": {\n         \"properties\": {\n          \"rollingUpdate\": {\n           \"properties\": {\n            \"maxUnavailable\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"x-kubernetes-int-or-string\": true\n            },\n            \"partition\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": {\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"volumeClaimTemplates\": {\n         \"items\": {\n          \"properties\": {\n           \"metadata\": {\n            \"properties\": {\n             \"annotations\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"object\"\n             },\n             \"labels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"object\"\n             },\n             \"name\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"spec\": {\n            \"properties\": {\n             \"accessModes\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"dataSource\": {\n              \"properties\": {\n               \"apiGroup\": {\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"dataSourceRef\": {\n              \"properties\": {\n               \"apiGroup\": {\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"resources\": {\n              \"properties\": {\n               \"limits\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"type\": \"object\"\n               },\n               \"requests\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"selector\": {\n              \"properties\": {\n               \"matchExpressions\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"storageClassName\": {\n              \"type\": \"string\"\n             },\n             \"volumeAttributesClassName\": {\n              \"type\": \"string\"\n             },\n             \"volumeMode\": {\n              \"type\": \"string\"\n             },\n             \"volumeName\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"syslogNGImage\": {\n     \"properties\": {\n      \"repository\": {\n       \"type\": \"string\"\n      },\n      \"tag\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"tls\": {\n     \"properties\": {\n      \"enabled\": {\n       \"type\": \"boolean\"\n      },\n      \"secretName\": {\n       \"type\": \"string\"\n      },\n      \"sharedKey\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"enabled\"\n     ],\n     \"type\": \"object\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Syslog NG Config\",\n \"type\": \"object\"\n}"}}