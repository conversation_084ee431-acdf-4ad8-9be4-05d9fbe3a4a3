{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Vault Server", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Security & Compliance"}, "subCategory": "Uncategorized", "metadata": {"isAnnotation": false, "primaryColor": "#326ce5", "secondaryColor": "#00D3A9", "shape": "circle", "source_uri": "https://charts.appscode.com/stable/kubevault/kubevault-v2025.5.30.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" id=\"Layer_1\" xmlns:_xmlns=\"xmlns\" _xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 32 32\" style=\"enable-background:new 0 0 32 32;\" xml:space=\"preserve\" height=\"20\" width=\"20\">\n<style xmlns=\"http://www.w3.org/2000/svg\" type=\"text/css\">\n.st0{fill:#326CE5;}\n</style>\n<path xmlns=\"http://www.w3.org/2000/svg\" class=\"st0\" d=\"M15.9,31.8L0,0h31.8l-7.2,14.4h-0.4c-0.8,0-0.9,0.1-1,1.6c0,0.6-1.7,1.6-2.6,1.6c-0.1,0-0.2,0-0.3,0l-0.2-0.1&#xA;c-0.7-0.5-1-0.8-1.4-0.8c-0.3,0-0.7,0.3-1.3,1L17,18c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.2,0.1,0.4,0.3,0.5&#xA;c0.2,0.2,0.4,0.4,0.5,0.6c0.1,0.2,0.2,0.5,0.3,0.8c0,0.3-0.1,0.6-0.2,0.9l-0.4,0.9c0,0.2-0.1,0.3-0.2,0.4c-0.1,0.1-0.3,0.2-0.5,0.2&#xA;h-0.7h-0.2c-0.3,0-0.7,0-0.8,0.3c0,0.2,0,0.4,0,0.5V25c0,1.2,0,1.2,1.2,1.3h0.4c0.5,0,1.2,1,1.5,1.8L15.9,31.8z M15,14.7v1.8h1.9&#xA;v-1.8H15z M12.2,11.9v1.9h1.8v-1.9H12.2z M17.8,11.9v1.8h1.9v-1.8H17.8z M15,11.9v1.9h1.9v-1.9H15z M12.2,9.1V11h1.8V9.1H12.2z&#xA;M15,9.1v1.8h1.9V9.1H15z M17.8,9.1v1.8h1.9V9.1H17.8z M12.2,6.4v1.9h1.8V6.4H12.2z M17.8,6.4v1.8h1.8V6.4H17.8z M15,6.4v1.8h1.9&#xA;V6.4H15z\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" class=\"st0\" d=\"M31,23.4c-0.3-0.2-0.6-0.5-0.8-0.9s-0.3-0.8-0.2-1.2v-0.1c0.8-1,0.8-1-0.1-1.9c-0.9-0.9-0.7-0.6-1.3,0&#xA;c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.4,0c-0.1,0-0.2-0.1-0.3-0.2c-0.1-0.1-0.2-0.1-0.4-0.2h-0.1&#xA;c-0.1,0-0.2-0.1-0.3-0.1c-0.1-0.1-0.2-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.1-0.3c0-0.1,0-0.2,0-0.4l0,0c0,0,0-0.2,0-0.3&#xA;c0-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.2-0.1-0.3-0.1H25c-1.1,0-1.3-0.2-1.4,1.1c0,0.5-1.6,1.3-2,1.1h-0.1c-1-1-1-1-1.8-0.2L19.4,20&#xA;c-0.1,0.1-0.2,0.2-0.2,0.3c0,0.1,0,0.2,0.1,0.3l0,0c0.2,0.1,0.3,0.3,0.4,0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.2-0.1,0.4-0.2,0.6&#xA;l-0.3,0.6c0,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.2,0.1-0.3,0.1h-0.5c-0.2,0-0.6,0-0.7,0.2c0,0.1,0,0.3,0,0.4v1c0,0.8,0,0.8,0.8,0.9h0.3&#xA;c0.6,0,1.4,1.5,1.1,2v0.1c-0.7,1-0.7,1,0.2,1.9l0.2,0.2c0.2,0.2,0.5,0.3,0.8,0v-0.1c0.1-0.1,0.3-0.2,0.4-0.3&#xA;c0.2-0.1,0.3-0.1,0.5-0.1c0.2,0,0.3,0,0.5,0.1c0.2,0.1,0.3,0.2,0.4,0.3l0.3,0.1c0.1,0,0.3,0.1,0.4,0.2c0.1,0.1,0.1,0.3,0.1,0.4&#xA;c0,0.2,0,0.4,0,0.6c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0.1,0.2c0,0.1,0.1,0.1,0.1,0.1C24,32,24,32,24.1,32c0.1,0,0.1,0,0.2,0h1.1&#xA;c0.4,0,0.7-0.2,0.6-0.6c0,0,0,0,0-0.1c0-0.3,0.1-0.6,0.3-0.9c0.2-0.2,0.5-0.4,0.8-0.4l0,0c0.1,0,0.2-0.1,0.3-0.1l0,0&#xA;c0.2-0.1,0.4-0.2,0.6-0.1c0.2,0,0.4,0.1,0.5,0.3l0,0c0.2,0.2,0.4,0.3,0.6,0.5c0.7-0.3,1.2-0.8,1.5-1.4c0-0.1-0.1-0.3-0.2-0.4&#xA;c-0.3-0.2-0.5-0.5-0.5-0.8c-0.1-0.3,0-0.7,0.2-0.9l0.1-0.2c0-0.1,0-0.1,0-0.2c0-0.2,0.1-0.4,0.3-0.5c0.1-0.1,0.3-0.2,0.5-0.1&#xA;c1,0,1,0,1-1.1C32,23.8,32.2,23.6,31,23.4z M28,24.7c0,0.6-0.2,1.2-0.5,1.7c-0.3,0.5-0.8,0.9-1.4,1.1c-0.6,0.2-1.2,0.3-1.8,0.1&#xA;c-0.6-0.1-1.1-0.4-1.6-0.9c-0.4-0.4-0.7-1-0.8-1.6s0-1.2,0.2-1.8c0.2-0.6,0.6-1,1.2-1.4c0.5-0.3,1.1-0.5,1.7-0.5&#xA;c0.8,0,1.6,0.3,2.1,0.9S28,23.9,28,24.7L28,24.7z\"></path>\n</svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" id=\"Layer_1\" xmlns:_xmlns=\"xmlns\" _xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 32 32\" style=\"enable-background:new 0 0 32 32;\" xml:space=\"preserve\" height=\"20\" width=\"20\">\n<style xmlns=\"http://www.w3.org/2000/svg\" type=\"text/css\">\n.st0{fill:#FFFFFF;}\n</style>\n<path xmlns=\"http://www.w3.org/2000/svg\" class=\"st0\" d=\"M15.9,31.8L0,0h31.8l-7.2,14.4h-0.4c-0.8,0-0.9,0.1-1,1.6c0,0.6-1.7,1.6-2.6,1.6c-0.1,0-0.2,0-0.3,0l-0.2-0.1&#xA;c-0.7-0.5-1-0.8-1.4-0.8c-0.3,0-0.7,0.3-1.3,1L17,18c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.2,0.1,0.4,0.3,0.5&#xA;c0.2,0.2,0.4,0.4,0.5,0.6c0.1,0.2,0.2,0.5,0.3,0.8c0,0.3-0.1,0.6-0.2,0.9l-0.4,0.9c0,0.2-0.1,0.3-0.2,0.4c-0.1,0.1-0.3,0.2-0.5,0.2&#xA;h-0.7h-0.2c-0.3,0-0.7,0-0.8,0.3c0,0.2,0,0.4,0,0.5V25c0,1.2,0,1.2,1.2,1.3h0.4c0.5,0,1.2,1,1.5,1.8L15.9,31.8z M15,14.7v1.8h1.9&#xA;v-1.8H15z M12.2,11.9v1.9h1.8v-1.9H12.2z M17.8,11.9v1.8h1.9v-1.8H17.8z M15,11.9v1.9h1.9v-1.9H15z M12.2,9.1V11h1.8V9.1H12.2z&#xA;M15,9.1v1.8h1.9V9.1H15z M17.8,9.1v1.8h1.9V9.1H17.8z M12.2,6.4v1.9h1.8V6.4H12.2z M17.8,6.4v1.8h1.8V6.4H17.8z M15,6.4v1.8h1.9&#xA;V6.4H15z\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" class=\"st0\" d=\"M31,23.4c-0.3-0.2-0.6-0.5-0.8-0.9s-0.3-0.8-0.2-1.2v-0.1c0.8-1,0.8-1-0.1-1.9c-0.9-0.9-0.7-0.6-1.3,0&#xA;c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.4,0c-0.1,0-0.2-0.1-0.3-0.2c-0.1-0.1-0.2-0.1-0.4-0.2h-0.1&#xA;c-0.1,0-0.2-0.1-0.3-0.1c-0.1-0.1-0.2-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.1-0.3c0-0.1,0-0.2,0-0.4l0,0c0,0,0-0.2,0-0.3&#xA;c0-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.2-0.1-0.3-0.1H25c-1.1,0-1.3-0.2-1.4,1.1c0,0.5-1.6,1.3-2,1.1h-0.1c-1-1-1-1-1.8-0.2L19.4,20&#xA;c-0.1,0.1-0.2,0.2-0.2,0.3c0,0.1,0,0.2,0.1,0.3l0,0c0.2,0.1,0.3,0.3,0.4,0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.2-0.1,0.4-0.2,0.6&#xA;l-0.3,0.6c0,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.2,0.1-0.3,0.1h-0.5c-0.2,0-0.6,0-0.7,0.2c0,0.1,0,0.3,0,0.4v1c0,0.8,0,0.8,0.8,0.9h0.3&#xA;c0.6,0,1.4,1.5,1.1,2v0.1c-0.7,1-0.7,1,0.2,1.9l0.2,0.2c0.2,0.2,0.5,0.3,0.8,0v-0.1c0.1-0.1,0.3-0.2,0.4-0.3&#xA;c0.2-0.1,0.3-0.1,0.5-0.1c0.2,0,0.3,0,0.5,0.1c0.2,0.1,0.3,0.2,0.4,0.3l0.3,0.1c0.1,0,0.3,0.1,0.4,0.2c0.1,0.1,0.1,0.3,0.1,0.4&#xA;c0,0.2,0,0.4,0,0.6c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0.1,0.2c0,0.1,0.1,0.1,0.1,0.1C24,32,24,32,24.1,32c0.1,0,0.1,0,0.2,0h1.1&#xA;c0.4,0,0.7-0.2,0.6-0.6c0,0,0,0,0-0.1c0-0.3,0.1-0.6,0.3-0.9c0.2-0.2,0.5-0.4,0.8-0.4l0,0c0.1,0,0.2-0.1,0.3-0.1l0,0&#xA;c0.2-0.1,0.4-0.2,0.6-0.1c0.2,0,0.4,0.1,0.5,0.3l0,0c0.2,0.2,0.4,0.3,0.6,0.5c0.7-0.3,1.2-0.8,1.5-1.4c0-0.1-0.1-0.3-0.2-0.4&#xA;c-0.3-0.2-0.5-0.5-0.5-0.8c-0.1-0.3,0-0.7,0.2-0.9l0.1-0.2c0-0.1,0-0.1,0-0.2c0-0.2,0.1-0.4,0.3-0.5c0.1-0.1,0.3-0.2,0.5-0.1&#xA;c1,0,1,0,1-1.1C32,23.8,32.2,23.6,31,23.4z M28,24.7c0,0.6-0.2,1.2-0.5,1.7c-0.3,0.5-0.8,0.9-1.4,1.1c-0.6,0.2-1.2,0.3-1.8,0.1&#xA;c-0.6-0.1-1.1-0.4-1.6-0.9c-0.4-0.4-0.7-1-0.8-1.6s0-1.2,0.2-1.8c0.2-0.6,0.6-1,1.2-1.4c0.5-0.3,1.1-0.5,1.7-0.5&#xA;c0.8,0,1.6,0.3,2.1,0.9S28,23.9,28,24.7L28,24.7z\"></path>\n</svg>"}, "model": {"version": "2025.5.30"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#326ce5", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\"\n\t viewBox=\"0 0 32 32\" style=\"enable-background:new 0 0 32 32;\" xml:space=\"preserve\">\n<style type=\"text/css\">\n\t.st0{fill:#326CE5;}\n</style>\n<path class=\"st0\" d=\"M15.9,31.8L0,0h31.8l-7.2,14.4h-0.4c-0.8,0-0.9,0.1-1,1.6c0,0.6-1.7,1.6-2.6,1.6c-0.1,0-0.2,0-0.3,0l-0.2-0.1\n\tc-0.7-0.5-1-0.8-1.4-0.8c-0.3,0-0.7,0.3-1.3,1L17,18c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.2,0.1,0.4,0.3,0.5\n\tc0.2,0.2,0.4,0.4,0.5,0.6c0.1,0.2,0.2,0.5,0.3,0.8c0,0.3-0.1,0.6-0.2,0.9l-0.4,0.9c0,0.2-0.1,0.3-0.2,0.4c-0.1,0.1-0.3,0.2-0.5,0.2\n\th-0.7h-0.2c-0.3,0-0.7,0-0.8,0.3c0,0.2,0,0.4,0,0.5V25c0,1.2,0,1.2,1.2,1.3h0.4c0.5,0,1.2,1,1.5,1.8L15.9,31.8z M15,14.7v1.8h1.9\n\tv-1.8H15z M12.2,11.9v1.9h1.8v-1.9H12.2z M17.8,11.9v1.8h1.9v-1.8H17.8z M15,11.9v1.9h1.9v-1.9H15z M12.2,9.1V11h1.8V9.1H12.2z\n\t M15,9.1v1.8h1.9V9.1H15z M17.8,9.1v1.8h1.9V9.1H17.8z M12.2,6.4v1.9h1.8V6.4H12.2z M17.8,6.4v1.8h1.8V6.4H17.8z M15,6.4v1.8h1.9\n\tV6.4H15z\"/>\n<path class=\"st0\" d=\"M31,23.4c-0.3-0.2-0.6-0.5-0.8-0.9s-0.3-0.8-0.2-1.2v-0.1c0.8-1,0.8-1-0.1-1.9c-0.9-0.9-0.7-0.6-1.3,0\n\tc-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.4,0c-0.1,0-0.2-0.1-0.3-0.2c-0.1-0.1-0.2-0.1-0.4-0.2h-0.1\n\tc-0.1,0-0.2-0.1-0.3-0.1c-0.1-0.1-0.2-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.1-0.3c0-0.1,0-0.2,0-0.4l0,0c0,0,0-0.2,0-0.3\n\tc0-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.2-0.1-0.3-0.1H25c-1.1,0-1.3-0.2-1.4,1.1c0,0.5-1.6,1.3-2,1.1h-0.1c-1-1-1-1-1.8-0.2L19.4,20\n\tc-0.1,0.1-0.2,0.2-0.2,0.3c0,0.1,0,0.2,0.1,0.3l0,0c0.2,0.1,0.3,0.3,0.4,0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.2-0.1,0.4-0.2,0.6\n\tl-0.3,0.6c0,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.2,0.1-0.3,0.1h-0.5c-0.2,0-0.6,0-0.7,0.2c0,0.1,0,0.3,0,0.4v1c0,0.8,0,0.8,0.8,0.9h0.3\n\tc0.6,0,1.4,1.5,1.1,2v0.1c-0.7,1-0.7,1,0.2,1.9l0.2,0.2c0.2,0.2,0.5,0.3,0.8,0v-0.1c0.1-0.1,0.3-0.2,0.4-0.3\n\tc0.2-0.1,0.3-0.1,0.5-0.1c0.2,0,0.3,0,0.5,0.1c0.2,0.1,0.3,0.2,0.4,0.3l0.3,0.1c0.1,0,0.3,0.1,0.4,0.2c0.1,0.1,0.1,0.3,0.1,0.4\n\tc0,0.2,0,0.4,0,0.6c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0.1,0.2c0,0.1,0.1,0.1,0.1,0.1C24,32,24,32,24.1,32c0.1,0,0.1,0,0.2,0h1.1\n\tc0.4,0,0.7-0.2,0.6-0.6c0,0,0,0,0-0.1c0-0.3,0.1-0.6,0.3-0.9c0.2-0.2,0.5-0.4,0.8-0.4l0,0c0.1,0,0.2-0.1,0.3-0.1l0,0\n\tc0.2-0.1,0.4-0.2,0.6-0.1c0.2,0,0.4,0.1,0.5,0.3l0,0c0.2,0.2,0.4,0.3,0.6,0.5c0.7-0.3,1.2-0.8,1.5-1.4c0-0.1-0.1-0.3-0.2-0.4\n\tc-0.3-0.2-0.5-0.5-0.5-0.8c-0.1-0.3,0-0.7,0.2-0.9l0.1-0.2c0-0.1,0-0.1,0-0.2c0-0.2,0.1-0.4,0.3-0.5c0.1-0.1,0.3-0.2,0.5-0.1\n\tc1,0,1,0,1-1.1C32,23.8,32.2,23.6,31,23.4z M28,24.7c0,0.6-0.2,1.2-0.5,1.7c-0.3,0.5-0.8,0.9-1.4,1.1c-0.6,0.2-1.2,0.3-1.8,0.1\n\tc-0.6-0.1-1.1-0.4-1.6-0.9c-0.4-0.4-0.7-1-0.8-1.6s0-1.2,0.2-1.8c0.2-0.6,0.6-1,1.2-1.4c0.5-0.3,1.1-0.5,1.7-0.5\n\tc0.8,0,1.6,0.3,2.1,0.9S28,23.9,28,24.7L28,24.7z\"/>\n</svg>", "svgComplete": "", "svgWhite": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\"\n         viewBox=\"0 0 32 32\" style=\"enable-background:new 0 0 32 32;\" xml:space=\"preserve\">\n<style type=\"text/css\">\n        .st0{fill:#FFFFFF;}\n</style>\n<path class=\"st0\" d=\"M15.9,31.8L0,0h31.8l-7.2,14.4h-0.4c-0.8,0-0.9,0.1-1,1.6c0,0.6-1.7,1.6-2.6,1.6c-0.1,0-0.2,0-0.3,0l-0.2-0.1\n        c-0.7-0.5-1-0.8-1.4-0.8c-0.3,0-0.7,0.3-1.3,1L17,18c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.2,0.1,0.4,0.3,0.5\n        c0.2,0.2,0.4,0.4,0.5,0.6c0.1,0.2,0.2,0.5,0.3,0.8c0,0.3-0.1,0.6-0.2,0.9l-0.4,0.9c0,0.2-0.1,0.3-0.2,0.4c-0.1,0.1-0.3,0.2-0.5,0.2\n        h-0.7h-0.2c-0.3,0-0.7,0-0.8,0.3c0,0.2,0,0.4,0,0.5V25c0,1.2,0,1.2,1.2,1.3h0.4c0.5,0,1.2,1,1.5,1.8L15.9,31.8z M15,14.7v1.8h1.9\n        v-1.8H15z M12.2,11.9v1.9h1.8v-1.9H12.2z M17.8,11.9v1.8h1.9v-1.8H17.8z M15,11.9v1.9h1.9v-1.9H15z M12.2,9.1V11h1.8V9.1H12.2z\n         M15,9.1v1.8h1.9V9.1H15z M17.8,9.1v1.8h1.9V9.1H17.8z M12.2,6.4v1.9h1.8V6.4H12.2z M17.8,6.4v1.8h1.8V6.4H17.8z M15,6.4v1.8h1.9\n        V6.4H15z\"/>\n<path class=\"st0\" d=\"M31,23.4c-0.3-0.2-0.6-0.5-0.8-0.9s-0.3-0.8-0.2-1.2v-0.1c0.8-1,0.8-1-0.1-1.9c-0.9-0.9-0.7-0.6-1.3,0\n        c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.4,0c-0.1,0-0.2-0.1-0.3-0.2c-0.1-0.1-0.2-0.1-0.4-0.2h-0.1\n        c-0.1,0-0.2-0.1-0.3-0.1c-0.1-0.1-0.2-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.1-0.3c0-0.1,0-0.2,0-0.4l0,0c0,0,0-0.2,0-0.3\n        c0-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.2-0.1-0.3-0.1H25c-1.1,0-1.3-0.2-1.4,1.1c0,0.5-1.6,1.3-2,1.1h-0.1c-1-1-1-1-1.8-0.2L19.4,20\n        c-0.1,0.1-0.2,0.2-0.2,0.3c0,0.1,0,0.2,0.1,0.3l0,0c0.2,0.1,0.3,0.3,0.4,0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.2-0.1,0.4-0.2,0.6\n        l-0.3,0.6c0,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.2,0.1-0.3,0.1h-0.5c-0.2,0-0.6,0-0.7,0.2c0,0.1,0,0.3,0,0.4v1c0,0.8,0,0.8,0.8,0.9h0.3\n        c0.6,0,1.4,1.5,1.1,2v0.1c-0.7,1-0.7,1,0.2,1.9l0.2,0.2c0.2,0.2,0.5,0.3,0.8,0v-0.1c0.1-0.1,0.3-0.2,0.4-0.3\n        c0.2-0.1,0.3-0.1,0.5-0.1c0.2,0,0.3,0,0.5,0.1c0.2,0.1,0.3,0.2,0.4,0.3l0.3,0.1c0.1,0,0.3,0.1,0.4,0.2c0.1,0.1,0.1,0.3,0.1,0.4\n        c0,0.2,0,0.4,0,0.6c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0.1,0.2c0,0.1,0.1,0.1,0.1,0.1C24,32,24,32,24.1,32c0.1,0,0.1,0,0.2,0h1.1\n        c0.4,0,0.7-0.2,0.6-0.6c0,0,0,0,0-0.1c0-0.3,0.1-0.6,0.3-0.9c0.2-0.2,0.5-0.4,0.8-0.4l0,0c0.1,0,0.2-0.1,0.3-0.1l0,0\n        c0.2-0.1,0.4-0.2,0.6-0.1c0.2,0,0.4,0.1,0.5,0.3l0,0c0.2,0.2,0.4,0.3,0.6,0.5c0.7-0.3,1.2-0.8,1.5-1.4c0-0.1-0.1-0.3-0.2-0.4\n        c-0.3-0.2-0.5-0.5-0.5-0.8c-0.1-0.3,0-0.7,0.2-0.9l0.1-0.2c0-0.1,0-0.1,0-0.2c0-0.2,0.1-0.4,0.3-0.5c0.1-0.1,0.3-0.2,0.5-0.1\n        c1,0,1,0,1-1.1C32,23.8,32.2,23.6,31,23.4z M28,24.7c0,0.6-0.2,1.2-0.5,1.7c-0.3,0.5-0.8,0.9-1.4,1.1c-0.6,0.2-1.2,0.3-1.8,0.1\n        c-0.6-0.1-1.1-0.4-1.6-0.9c-0.4-0.4-0.7-1-0.8-1.6s0-1.2,0.2-1.8c0.2-0.6,0.6-1,1.2-1.4c0.5-0.3,1.1-0.5,1.7-0.5\n        c0.8,0,1.6,0.3,2.1,0.9S28,23.9,28,24.7L28,24.7z\"/>\n</svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://charts.appscode.com/stable/kubevault/kubevault-v2025.5.30.tgz"}, "configuration": null, "component": {"version": "kubevault.com/v1alpha1", "kind": "VaultServer", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"properties\": {\n    \"allowedSecretEngines\": {\n     \"default\": {\n      \"namespaces\": {\n       \"from\": \"Same\"\n      }\n     },\n     \"properties\": {\n      \"namespaces\": {\n       \"default\": {\n        \"from\": \"Same\"\n       },\n       \"properties\": {\n        \"from\": {\n         \"default\": \"Same\",\n         \"enum\": [\n          \"All\",\n          \"Selector\",\n          \"Same\"\n         ],\n         \"type\": \"string\"\n        },\n        \"selector\": {\n         \"properties\": {\n          \"matchExpressions\": {\n           \"items\": {\n            \"properties\": {\n             \"key\": {\n              \"type\": \"string\"\n             },\n             \"operator\": {\n              \"type\": \"string\"\n             },\n             \"values\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"required\": [\n             \"key\",\n             \"operator\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"matchLabels\": {\n           \"additionalProperties\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"secretEngines\": {\n       \"items\": {\n        \"enum\": [\n         \"kv\",\n         \"pki\",\n         \"aws\",\n         \"azure\",\n         \"gcp\",\n         \"postgres\",\n         \"mongodb\",\n         \"mysql\",\n         \"elasticsearch\",\n         \"redis\"\n        ],\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"authMethods\": {\n     \"items\": {\n      \"properties\": {\n       \"config\": {\n        \"properties\": {\n         \"auditNonHMACRequestKeys\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"auditNonHMACResponseKeys\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"defaultLeaseTTL\": {\n          \"type\": \"string\"\n         },\n         \"listingVisibility\": {\n          \"type\": \"string\"\n         },\n         \"maxLeaseTTL\": {\n          \"type\": \"string\"\n         },\n         \"passthroughRequestHeaders\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"pluginName\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"description\": {\n        \"type\": \"string\"\n       },\n       \"local\": {\n        \"type\": \"boolean\"\n       },\n       \"path\": {\n        \"type\": \"string\"\n       },\n       \"pluginName\": {\n        \"type\": \"string\"\n       },\n       \"type\": {\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"path\",\n       \"type\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"backend\": {\n     \"properties\": {\n      \"azure\": {\n       \"properties\": {\n        \"accountKeySecret\": {\n         \"type\": \"string\"\n        },\n        \"accountName\": {\n         \"type\": \"string\"\n        },\n        \"container\": {\n         \"type\": \"string\"\n        },\n        \"maxParallel\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        }\n       },\n       \"required\": [\n        \"accountKeySecret\",\n        \"accountName\",\n        \"container\"\n       ],\n       \"type\": \"object\"\n      },\n      \"consul\": {\n       \"properties\": {\n        \"aclTokenSecretName\": {\n         \"type\": \"string\"\n        },\n        \"address\": {\n         \"type\": \"string\"\n        },\n        \"checkTimeout\": {\n         \"type\": \"string\"\n        },\n        \"consistencyMode\": {\n         \"type\": \"string\"\n        },\n        \"disableRegistration\": {\n         \"type\": \"string\"\n        },\n        \"lockWaitTime\": {\n         \"type\": \"string\"\n        },\n        \"maxParallel\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"scheme\": {\n         \"type\": \"string\"\n        },\n        \"service\": {\n         \"type\": \"string\"\n        },\n        \"serviceAddress\": {\n         \"type\": \"string\"\n        },\n        \"serviceTags\": {\n         \"type\": \"string\"\n        },\n        \"sessionTTL\": {\n         \"type\": \"string\"\n        },\n        \"tlsMinVersion\": {\n         \"type\": \"string\"\n        },\n        \"tlsSecretName\": {\n         \"type\": \"string\"\n        },\n        \"tlsSkipVerify\": {\n         \"type\": \"boolean\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"dynamodb\": {\n       \"properties\": {\n        \"credentialSecret\": {\n         \"type\": \"string\"\n        },\n        \"endpoint\": {\n         \"type\": \"string\"\n        },\n        \"haEnabled\": {\n         \"type\": \"boolean\"\n        },\n        \"maxParallel\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"readCapacity\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"region\": {\n         \"type\": \"string\"\n        },\n        \"sessionTokenSecret\": {\n         \"type\": \"string\"\n        },\n        \"table\": {\n         \"type\": \"string\"\n        },\n        \"writeCapacity\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"etcd\": {\n       \"properties\": {\n        \"address\": {\n         \"type\": \"string\"\n        },\n        \"credentialSecretName\": {\n         \"type\": \"string\"\n        },\n        \"discoverySrv\": {\n         \"type\": \"string\"\n        },\n        \"etcdApi\": {\n         \"type\": \"string\"\n        },\n        \"haEnable\": {\n         \"type\": \"boolean\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"sync\": {\n         \"type\": \"boolean\"\n        },\n        \"tlsSecretName\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"address\"\n       ],\n       \"type\": \"object\"\n      },\n      \"file\": {\n       \"properties\": {\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"volumeClaimTemplate\": {\n         \"properties\": {\n          \"apiVersion\": {\n           \"type\": \"string\"\n          },\n          \"kind\": {\n           \"type\": \"string\"\n          },\n          \"metadata\": {\n           \"properties\": {\n            \"annotations\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"object\"\n            },\n            \"generateName\": {\n             \"type\": \"string\"\n            },\n            \"labels\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"object\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"namespace\": {\n             \"type\": \"string\"\n            },\n            \"ownerReferences\": {\n             \"items\": {\n              \"properties\": {\n               \"apiVersion\": {\n                \"type\": \"string\"\n               },\n               \"blockOwnerDeletion\": {\n                \"type\": \"boolean\"\n               },\n               \"controller\": {\n                \"type\": \"boolean\"\n               },\n               \"kind\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"uid\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"apiVersion\",\n               \"kind\",\n               \"name\",\n               \"uid\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"spec\": {\n           \"properties\": {\n            \"accessModes\": {\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"dataSource\": {\n             \"properties\": {\n              \"apiGroup\": {\n               \"type\": \"string\"\n              },\n              \"kind\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"kind\",\n              \"name\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"dataSourceRef\": {\n             \"properties\": {\n              \"apiGroup\": {\n               \"type\": \"string\"\n              },\n              \"kind\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"kind\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"resources\": {\n             \"properties\": {\n              \"limits\": {\n               \"additionalProperties\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"type\": \"object\"\n              },\n              \"requests\": {\n               \"additionalProperties\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"selector\": {\n             \"properties\": {\n              \"matchExpressions\": {\n               \"items\": {\n                \"properties\": {\n                 \"key\": {\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"type\": \"string\"\n                 },\n                 \"values\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"operator\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"matchLabels\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"storageClassName\": {\n             \"type\": \"string\"\n            },\n            \"volumeAttributesClassName\": {\n             \"type\": \"string\"\n            },\n            \"volumeMode\": {\n             \"type\": \"string\"\n            },\n            \"volumeName\": {\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"status\": {\n           \"properties\": {\n            \"accessModes\": {\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"allocatedResourceStatuses\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"granular\"\n            },\n            \"allocatedResources\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"type\": \"object\"\n            },\n            \"capacity\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"type\": \"object\"\n            },\n            \"conditions\": {\n             \"items\": {\n              \"properties\": {\n               \"lastProbeTime\": {\n                \"format\": \"date-time\",\n                \"type\": \"string\"\n               },\n               \"lastTransitionTime\": {\n                \"format\": \"date-time\",\n                \"type\": \"string\"\n               },\n               \"message\": {\n                \"type\": \"string\"\n               },\n               \"reason\": {\n                \"type\": \"string\"\n               },\n               \"status\": {\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"status\",\n               \"type\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-map-keys\": [\n              \"type\"\n             ],\n             \"x-kubernetes-list-type\": \"map\"\n            },\n            \"currentVolumeAttributesClassName\": {\n             \"type\": \"string\"\n            },\n            \"modifyVolumeStatus\": {\n             \"properties\": {\n              \"status\": {\n               \"type\": \"string\"\n              },\n              \"targetVolumeAttributesClassName\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"status\"\n             ],\n             \"type\": \"object\"\n            },\n            \"phase\": {\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"required\": [\n        \"path\",\n        \"volumeClaimTemplate\"\n       ],\n       \"type\": \"object\"\n      },\n      \"gcs\": {\n       \"properties\": {\n        \"bucket\": {\n         \"type\": \"string\"\n        },\n        \"chunkSize\": {\n         \"type\": \"string\"\n        },\n        \"credentialSecret\": {\n         \"type\": \"string\"\n        },\n        \"haEnabled\": {\n         \"type\": \"boolean\"\n        },\n        \"maxParallel\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        }\n       },\n       \"required\": [\n        \"bucket\"\n       ],\n       \"type\": \"object\"\n      },\n      \"inmem\": {\n       \"type\": \"object\"\n      },\n      \"mysql\": {\n       \"properties\": {\n        \"address\": {\n         \"type\": \"string\"\n        },\n        \"database\": {\n         \"type\": \"string\"\n        },\n        \"maxParallel\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"table\": {\n         \"type\": \"string\"\n        },\n        \"tlsCASecret\": {\n         \"type\": \"string\"\n        },\n        \"userCredentialSecret\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"userCredentialSecret\"\n       ],\n       \"type\": \"object\"\n      },\n      \"postgresql\": {\n       \"properties\": {\n        \"connectionURLSecret\": {\n         \"type\": \"string\"\n        },\n        \"maxParallel\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"table\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"connectionURLSecret\"\n       ],\n       \"type\": \"object\"\n      },\n      \"raft\": {\n       \"properties\": {\n        \"autopilotReconcileInterval\": {\n         \"type\": \"string\"\n        },\n        \"maxEntrySize\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"performanceMultiplier\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"snapshotThreshold\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"storage\": {\n         \"properties\": {\n          \"accessModes\": {\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"dataSource\": {\n           \"properties\": {\n            \"apiGroup\": {\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"kind\",\n            \"name\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"dataSourceRef\": {\n           \"properties\": {\n            \"apiGroup\": {\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"namespace\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"kind\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"resources\": {\n           \"properties\": {\n            \"limits\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"type\": \"object\"\n            },\n            \"requests\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"selector\": {\n           \"properties\": {\n            \"matchExpressions\": {\n             \"items\": {\n              \"properties\": {\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"operator\": {\n                \"type\": \"string\"\n               },\n               \"values\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"required\": [\n               \"key\",\n               \"operator\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"matchLabels\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"storageClassName\": {\n           \"type\": \"string\"\n          },\n          \"volumeAttributesClassName\": {\n           \"type\": \"string\"\n          },\n          \"volumeMode\": {\n           \"type\": \"string\"\n          },\n          \"volumeName\": {\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"trailingLogs\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"s3\": {\n       \"properties\": {\n        \"bucket\": {\n         \"type\": \"string\"\n        },\n        \"credentialSecret\": {\n         \"type\": \"string\"\n        },\n        \"disableSSL\": {\n         \"type\": \"boolean\"\n        },\n        \"endpoint\": {\n         \"type\": \"string\"\n        },\n        \"forcePathStyle\": {\n         \"type\": \"boolean\"\n        },\n        \"maxParallel\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"region\": {\n         \"type\": \"string\"\n        },\n        \"sessionTokenSecret\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"bucket\"\n       ],\n       \"type\": \"object\"\n      },\n      \"swift\": {\n       \"properties\": {\n        \"authTokenSecret\": {\n         \"type\": \"string\"\n        },\n        \"authURL\": {\n         \"type\": \"string\"\n        },\n        \"container\": {\n         \"type\": \"string\"\n        },\n        \"credentialSecret\": {\n         \"type\": \"string\"\n        },\n        \"domain\": {\n         \"type\": \"string\"\n        },\n        \"maxParallel\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"projectDomain\": {\n         \"type\": \"string\"\n        },\n        \"region\": {\n         \"type\": \"string\"\n        },\n        \"storageURL\": {\n         \"type\": \"string\"\n        },\n        \"tenant\": {\n         \"type\": \"string\"\n        },\n        \"tenantID\": {\n         \"type\": \"string\"\n        },\n        \"trustID\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"authURL\",\n        \"container\",\n        \"credentialSecret\"\n       ],\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"configSecret\": {\n     \"properties\": {\n      \"name\": {\n       \"default\": \"\",\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\",\n     \"x-kubernetes-map-type\": \"atomic\"\n    },\n    \"dataSources\": {\n     \"items\": {\n      \"properties\": {\n       \"awsElasticBlockStore\": {\n        \"properties\": {\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"partition\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"volumeID\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"azureDisk\": {\n        \"properties\": {\n         \"cachingMode\": {\n          \"type\": \"string\"\n         },\n         \"diskName\": {\n          \"type\": \"string\"\n         },\n         \"diskURI\": {\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"default\": \"ext4\",\n          \"type\": \"string\"\n         },\n         \"kind\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"default\": false,\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"diskName\",\n         \"diskURI\"\n        ],\n        \"type\": \"object\"\n       },\n       \"azureFile\": {\n        \"properties\": {\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"secretName\": {\n          \"type\": \"string\"\n         },\n         \"shareName\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"secretName\",\n         \"shareName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"cephfs\": {\n        \"properties\": {\n         \"monitors\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"path\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"secretFile\": {\n          \"type\": \"string\"\n         },\n         \"secretRef\": {\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"user\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"monitors\"\n        ],\n        \"type\": \"object\"\n       },\n       \"cinder\": {\n        \"properties\": {\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"volumeID\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"configMap\": {\n        \"properties\": {\n         \"defaultMode\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"items\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"mode\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"name\": {\n          \"default\": \"\",\n          \"type\": \"string\"\n         },\n         \"optional\": {\n          \"type\": \"boolean\"\n         }\n        },\n        \"type\": \"object\",\n        \"x-kubernetes-map-type\": \"atomic\"\n       },\n       \"csi\": {\n        \"properties\": {\n         \"driver\": {\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"nodePublishSecretRef\": {\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"volumeAttributes\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"driver\"\n        ],\n        \"type\": \"object\"\n       },\n       \"downwardAPI\": {\n        \"properties\": {\n         \"defaultMode\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"items\": {\n           \"properties\": {\n            \"fieldRef\": {\n             \"properties\": {\n              \"apiVersion\": {\n               \"type\": \"string\"\n              },\n              \"fieldPath\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"fieldPath\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"mode\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"type\": \"string\"\n            },\n            \"resourceFieldRef\": {\n             \"properties\": {\n              \"containerName\": {\n               \"type\": \"string\"\n              },\n              \"divisor\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"resource\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"resource\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"required\": [\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"emptyDir\": {\n        \"properties\": {\n         \"medium\": {\n          \"type\": \"string\"\n         },\n         \"sizeLimit\": {\n          \"anyOf\": [\n           {\n            \"type\": \"integer\"\n           },\n           {\n            \"type\": \"string\"\n           }\n          ],\n          \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n          \"x-kubernetes-int-or-string\": true\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"ephemeral\": {\n        \"properties\": {\n         \"volumeClaimTemplate\": {\n          \"properties\": {\n           \"metadata\": {\n            \"properties\": {\n             \"annotations\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"object\"\n             },\n             \"finalizers\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"labels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"object\"\n             },\n             \"name\": {\n              \"type\": \"string\"\n             },\n             \"namespace\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"spec\": {\n            \"properties\": {\n             \"accessModes\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"dataSource\": {\n              \"properties\": {\n               \"apiGroup\": {\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"dataSourceRef\": {\n              \"properties\": {\n               \"apiGroup\": {\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"resources\": {\n              \"properties\": {\n               \"limits\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"type\": \"object\"\n               },\n               \"requests\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"selector\": {\n              \"properties\": {\n               \"matchExpressions\": {\n                \"items\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"storageClassName\": {\n              \"type\": \"string\"\n             },\n             \"volumeAttributesClassName\": {\n              \"type\": \"string\"\n             },\n             \"volumeMode\": {\n              \"type\": \"string\"\n             },\n             \"volumeName\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"spec\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"fc\": {\n        \"properties\": {\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"lun\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"targetWWNs\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"wwids\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"flexVolume\": {\n        \"properties\": {\n         \"driver\": {\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"options\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         }\n        },\n        \"required\": [\n         \"driver\"\n        ],\n        \"type\": \"object\"\n       },\n       \"flocker\": {\n        \"properties\": {\n         \"datasetName\": {\n          \"type\": \"string\"\n         },\n         \"datasetUUID\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"gcePersistentDisk\": {\n        \"properties\": {\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"partition\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"pdName\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"pdName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"gitRepo\": {\n        \"properties\": {\n         \"directory\": {\n          \"type\": \"string\"\n         },\n         \"repository\": {\n          \"type\": \"string\"\n         },\n         \"revision\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"repository\"\n        ],\n        \"type\": \"object\"\n       },\n       \"glusterfs\": {\n        \"properties\": {\n         \"endpoints\": {\n          \"type\": \"string\"\n         },\n         \"path\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"endpoints\",\n         \"path\"\n        ],\n        \"type\": \"object\"\n       },\n       \"hostPath\": {\n        \"properties\": {\n         \"path\": {\n          \"type\": \"string\"\n         },\n         \"type\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"path\"\n        ],\n        \"type\": \"object\"\n       },\n       \"image\": {\n        \"properties\": {\n         \"pullPolicy\": {\n          \"type\": \"string\"\n         },\n         \"reference\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"iscsi\": {\n        \"properties\": {\n         \"chapAuthDiscovery\": {\n          \"type\": \"boolean\"\n         },\n         \"chapAuthSession\": {\n          \"type\": \"boolean\"\n         },\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"initiatorName\": {\n          \"type\": \"string\"\n         },\n         \"iqn\": {\n          \"type\": \"string\"\n         },\n         \"iscsiInterface\": {\n          \"default\": \"default\",\n          \"type\": \"string\"\n         },\n         \"lun\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"portals\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"targetPortal\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"iqn\",\n         \"lun\",\n         \"targetPortal\"\n        ],\n        \"type\": \"object\"\n       },\n       \"nfs\": {\n        \"properties\": {\n         \"path\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"server\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"path\",\n         \"server\"\n        ],\n        \"type\": \"object\"\n       },\n       \"persistentVolumeClaim\": {\n        \"properties\": {\n         \"claimName\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"claimName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"photonPersistentDisk\": {\n        \"properties\": {\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"pdID\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"pdID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"portworxVolume\": {\n        \"properties\": {\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"volumeID\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"projected\": {\n        \"properties\": {\n         \"defaultMode\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"sources\": {\n          \"items\": {\n           \"properties\": {\n            \"clusterTrustBundle\": {\n             \"properties\": {\n              \"labelSelector\": {\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"items\": {\n                  \"properties\": {\n                   \"key\": {\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"name\": {\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              },\n              \"path\": {\n               \"type\": \"string\"\n              },\n              \"signerName\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            },\n            \"configMap\": {\n             \"properties\": {\n              \"items\": {\n               \"items\": {\n                \"properties\": {\n                 \"key\": {\n                  \"type\": \"string\"\n                 },\n                 \"mode\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"downwardAPI\": {\n             \"properties\": {\n              \"items\": {\n               \"items\": {\n                \"properties\": {\n                 \"fieldRef\": {\n                  \"properties\": {\n                   \"apiVersion\": {\n                    \"type\": \"string\"\n                   },\n                   \"fieldPath\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"fieldPath\"\n                  ],\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"mode\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"type\": \"string\"\n                 },\n                 \"resourceFieldRef\": {\n                  \"properties\": {\n                   \"containerName\": {\n                    \"type\": \"string\"\n                   },\n                   \"divisor\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"resource\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"resource\"\n                  ],\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 }\n                },\n                \"required\": [\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"secret\": {\n             \"properties\": {\n              \"items\": {\n               \"items\": {\n                \"properties\": {\n                 \"key\": {\n                  \"type\": \"string\"\n                 },\n                 \"mode\": {\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"serviceAccountToken\": {\n             \"properties\": {\n              \"audience\": {\n               \"type\": \"string\"\n              },\n              \"expirationSeconds\": {\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              },\n              \"path\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"quobyte\": {\n        \"properties\": {\n         \"group\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"registry\": {\n          \"type\": \"string\"\n         },\n         \"tenant\": {\n          \"type\": \"string\"\n         },\n         \"user\": {\n          \"type\": \"string\"\n         },\n         \"volume\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"registry\",\n         \"volume\"\n        ],\n        \"type\": \"object\"\n       },\n       \"rbd\": {\n        \"properties\": {\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"image\": {\n          \"type\": \"string\"\n         },\n         \"keyring\": {\n          \"default\": \"/etc/ceph/keyring\",\n          \"type\": \"string\"\n         },\n         \"monitors\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"pool\": {\n          \"default\": \"rbd\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"user\": {\n          \"default\": \"admin\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"image\",\n         \"monitors\"\n        ],\n        \"type\": \"object\"\n       },\n       \"scaleIO\": {\n        \"properties\": {\n         \"fsType\": {\n          \"default\": \"xfs\",\n          \"type\": \"string\"\n         },\n         \"gateway\": {\n          \"type\": \"string\"\n         },\n         \"protectionDomain\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"sslEnabled\": {\n          \"type\": \"boolean\"\n         },\n         \"storageMode\": {\n          \"default\": \"ThinProvisioned\",\n          \"type\": \"string\"\n         },\n         \"storagePool\": {\n          \"type\": \"string\"\n         },\n         \"system\": {\n          \"type\": \"string\"\n         },\n         \"volumeName\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"gateway\",\n         \"secretRef\",\n         \"system\"\n        ],\n        \"type\": \"object\"\n       },\n       \"secret\": {\n        \"properties\": {\n         \"defaultMode\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"items\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"mode\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"optional\": {\n          \"type\": \"boolean\"\n         },\n         \"secretName\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"storageos\": {\n        \"properties\": {\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"volumeName\": {\n          \"type\": \"string\"\n         },\n         \"volumeNamespace\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"vsphereVolume\": {\n        \"properties\": {\n         \"fsType\": {\n          \"type\": \"string\"\n         },\n         \"storagePolicyID\": {\n          \"type\": \"string\"\n         },\n         \"storagePolicyName\": {\n          \"type\": \"string\"\n         },\n         \"volumePath\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumePath\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"halted\": {\n     \"type\": \"boolean\"\n    },\n    \"monitor\": {\n     \"properties\": {\n      \"agent\": {\n       \"enum\": [\n        \"prometheus.io/operator\",\n        \"prometheus.io\",\n        \"prometheus.io/builtin\"\n       ],\n       \"type\": \"string\"\n      },\n      \"prometheus\": {\n       \"properties\": {\n        \"exporter\": {\n         \"properties\": {\n          \"args\": {\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"env\": {\n           \"items\": {\n            \"properties\": {\n             \"name\": {\n              \"type\": \"string\"\n             },\n             \"value\": {\n              \"type\": \"string\"\n             },\n             \"valueFrom\": {\n              \"properties\": {\n               \"configMapKeyRef\": {\n                \"properties\": {\n                 \"key\": {\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"default\": \"\",\n                  \"type\": \"string\"\n                 },\n                 \"optional\": {\n                  \"type\": \"boolean\"\n                 }\n                },\n                \"required\": [\n                 \"key\"\n                ],\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"fieldRef\": {\n                \"properties\": {\n                 \"apiVersion\": {\n                  \"type\": \"string\"\n                 },\n                 \"fieldPath\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"fieldPath\"\n                ],\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"resourceFieldRef\": {\n                \"properties\": {\n                 \"containerName\": {\n                  \"type\": \"string\"\n                 },\n                 \"divisor\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                  \"x-kubernetes-int-or-string\": true\n                 },\n                 \"resource\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"resource\"\n                ],\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"secretKeyRef\": {\n                \"properties\": {\n                 \"key\": {\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"default\": \"\",\n                  \"type\": \"string\"\n                 },\n                 \"optional\": {\n                  \"type\": \"boolean\"\n                 }\n                },\n                \"required\": [\n                 \"key\"\n                ],\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"name\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\"\n          },\n          \"port\": {\n           \"default\": 56790,\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"resources\": {\n           \"properties\": {\n            \"claims\": {\n             \"items\": {\n              \"properties\": {\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"request\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-map-keys\": [\n              \"name\"\n             ],\n             \"x-kubernetes-list-type\": \"map\"\n            },\n            \"limits\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"type\": \"object\"\n            },\n            \"requests\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"securityContext\": {\n           \"properties\": {\n            \"allowPrivilegeEscalation\": {\n             \"type\": \"boolean\"\n            },\n            \"appArmorProfile\": {\n             \"properties\": {\n              \"localhostProfile\": {\n               \"type\": \"string\"\n              },\n              \"type\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"type\"\n             ],\n             \"type\": \"object\"\n            },\n            \"capabilities\": {\n             \"properties\": {\n              \"add\": {\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"drop\": {\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"privileged\": {\n             \"type\": \"boolean\"\n            },\n            \"procMount\": {\n             \"type\": \"string\"\n            },\n            \"readOnlyRootFilesystem\": {\n             \"type\": \"boolean\"\n            },\n            \"runAsGroup\": {\n             \"format\": \"int64\",\n             \"type\": \"integer\"\n            },\n            \"runAsNonRoot\": {\n             \"type\": \"boolean\"\n            },\n            \"runAsUser\": {\n             \"format\": \"int64\",\n             \"type\": \"integer\"\n            },\n            \"seLinuxOptions\": {\n             \"properties\": {\n              \"level\": {\n               \"type\": \"string\"\n              },\n              \"role\": {\n               \"type\": \"string\"\n              },\n              \"type\": {\n               \"type\": \"string\"\n              },\n              \"user\": {\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"seccompProfile\": {\n             \"properties\": {\n              \"localhostProfile\": {\n               \"type\": \"string\"\n              },\n              \"type\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"type\"\n             ],\n             \"type\": \"object\"\n            },\n            \"windowsOptions\": {\n             \"properties\": {\n              \"gmsaCredentialSpec\": {\n               \"type\": \"string\"\n              },\n              \"gmsaCredentialSpecName\": {\n               \"type\": \"string\"\n              },\n              \"hostProcess\": {\n               \"type\": \"boolean\"\n              },\n              \"runAsUserName\": {\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"serviceMonitor\": {\n         \"properties\": {\n          \"interval\": {\n           \"type\": \"string\"\n          },\n          \"labels\": {\n           \"additionalProperties\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"podTemplate\": {\n     \"properties\": {\n      \"controller\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"properties\": {\n        \"affinity\": {\n         \"properties\": {\n          \"nodeAffinity\": {\n           \"properties\": {\n            \"preferredDuringSchedulingIgnoredDuringExecution\": {\n             \"items\": {\n              \"properties\": {\n               \"preference\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchFields\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"weight\": {\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"preference\",\n               \"weight\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"requiredDuringSchedulingIgnoredDuringExecution\": {\n             \"properties\": {\n              \"nodeSelectorTerms\": {\n               \"items\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchFields\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              }\n             },\n             \"required\": [\n              \"nodeSelectorTerms\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"podAffinity\": {\n           \"properties\": {\n            \"preferredDuringSchedulingIgnoredDuringExecution\": {\n             \"items\": {\n              \"properties\": {\n               \"podAffinityTerm\": {\n                \"properties\": {\n                 \"labelSelector\": {\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"matchLabelKeys\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"mismatchLabelKeys\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"namespaceSelector\": {\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"namespaces\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"topologyKey\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"topologyKey\"\n                ],\n                \"type\": \"object\"\n               },\n               \"weight\": {\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"podAffinityTerm\",\n               \"weight\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"requiredDuringSchedulingIgnoredDuringExecution\": {\n             \"items\": {\n              \"properties\": {\n               \"labelSelector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"matchLabelKeys\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"mismatchLabelKeys\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"namespaceSelector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaces\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"topologyKey\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"topologyKey\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"podAntiAffinity\": {\n           \"properties\": {\n            \"preferredDuringSchedulingIgnoredDuringExecution\": {\n             \"items\": {\n              \"properties\": {\n               \"podAffinityTerm\": {\n                \"properties\": {\n                 \"labelSelector\": {\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"matchLabelKeys\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"mismatchLabelKeys\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"namespaceSelector\": {\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"namespaces\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"topologyKey\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"topologyKey\"\n                ],\n                \"type\": \"object\"\n               },\n               \"weight\": {\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"podAffinityTerm\",\n               \"weight\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"requiredDuringSchedulingIgnoredDuringExecution\": {\n             \"items\": {\n              \"properties\": {\n               \"labelSelector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"matchLabelKeys\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"mismatchLabelKeys\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"namespaceSelector\": {\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"key\": {\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaces\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"topologyKey\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"topologyKey\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"args\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"containerSecurityContext\": {\n         \"properties\": {\n          \"allowPrivilegeEscalation\": {\n           \"type\": \"boolean\"\n          },\n          \"appArmorProfile\": {\n           \"properties\": {\n            \"localhostProfile\": {\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"capabilities\": {\n           \"properties\": {\n            \"add\": {\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"drop\": {\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"privileged\": {\n           \"type\": \"boolean\"\n          },\n          \"procMount\": {\n           \"type\": \"string\"\n          },\n          \"readOnlyRootFilesystem\": {\n           \"type\": \"boolean\"\n          },\n          \"runAsGroup\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"runAsNonRoot\": {\n           \"type\": \"boolean\"\n          },\n          \"runAsUser\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"seLinuxOptions\": {\n           \"properties\": {\n            \"level\": {\n             \"type\": \"string\"\n            },\n            \"role\": {\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"type\": \"string\"\n            },\n            \"user\": {\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"seccompProfile\": {\n           \"properties\": {\n            \"localhostProfile\": {\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"windowsOptions\": {\n           \"properties\": {\n            \"gmsaCredentialSpec\": {\n             \"type\": \"string\"\n            },\n            \"gmsaCredentialSpecName\": {\n             \"type\": \"string\"\n            },\n            \"hostProcess\": {\n             \"type\": \"boolean\"\n            },\n            \"runAsUserName\": {\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"dnsConfig\": {\n         \"properties\": {\n          \"nameservers\": {\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"options\": {\n           \"items\": {\n            \"properties\": {\n             \"name\": {\n              \"type\": \"string\"\n             },\n             \"value\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"searches\": {\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"dnsPolicy\": {\n         \"type\": \"string\"\n        },\n        \"enableServiceLinks\": {\n         \"type\": \"boolean\"\n        },\n        \"env\": {\n         \"items\": {\n          \"properties\": {\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"value\": {\n            \"type\": \"string\"\n           },\n           \"valueFrom\": {\n            \"properties\": {\n             \"configMapKeyRef\": {\n              \"properties\": {\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"fieldRef\": {\n              \"properties\": {\n               \"apiVersion\": {\n                \"type\": \"string\"\n               },\n               \"fieldPath\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"fieldPath\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"resourceFieldRef\": {\n              \"properties\": {\n               \"containerName\": {\n                \"type\": \"string\"\n               },\n               \"divisor\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"resource\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"resource\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"secretKeyRef\": {\n              \"properties\": {\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"hostIPC\": {\n         \"type\": \"boolean\"\n        },\n        \"hostNetwork\": {\n         \"type\": \"boolean\"\n        },\n        \"hostPID\": {\n         \"type\": \"boolean\"\n        },\n        \"imagePullSecrets\": {\n         \"items\": {\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"type\": \"array\"\n        },\n        \"initContainers\": {\n         \"items\": {\n          \"properties\": {\n           \"args\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"command\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"env\": {\n            \"items\": {\n             \"properties\": {\n              \"name\": {\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"type\": \"string\"\n              },\n              \"valueFrom\": {\n               \"properties\": {\n                \"configMapKeyRef\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"default\": \"\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"fieldRef\": {\n                 \"properties\": {\n                  \"apiVersion\": {\n                   \"type\": \"string\"\n                  },\n                  \"fieldPath\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"fieldPath\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"resourceFieldRef\": {\n                 \"properties\": {\n                  \"containerName\": {\n                   \"type\": \"string\"\n                  },\n                  \"divisor\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"resource\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resource\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"secretKeyRef\": {\n                 \"properties\": {\n                  \"key\": {\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"default\": \"\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"name\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"envFrom\": {\n            \"items\": {\n             \"properties\": {\n              \"configMapRef\": {\n               \"properties\": {\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"prefix\": {\n               \"type\": \"string\"\n              },\n              \"secretRef\": {\n               \"properties\": {\n                \"name\": {\n                 \"default\": \"\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"image\": {\n            \"type\": \"string\"\n           },\n           \"imagePullPolicy\": {\n            \"type\": \"string\"\n           },\n           \"lifecycle\": {\n            \"properties\": {\n             \"postStart\": {\n              \"properties\": {\n               \"exec\": {\n                \"properties\": {\n                 \"command\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"httpGet\": {\n                \"properties\": {\n                 \"host\": {\n                  \"type\": \"string\"\n                 },\n                 \"httpHeaders\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"path\": {\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"x-kubernetes-int-or-string\": true\n                 },\n                 \"scheme\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               },\n               \"sleep\": {\n                \"properties\": {\n                 \"seconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"required\": [\n                 \"seconds\"\n                ],\n                \"type\": \"object\"\n               },\n               \"tcpSocket\": {\n                \"properties\": {\n                 \"host\": {\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"x-kubernetes-int-or-string\": true\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"preStop\": {\n              \"properties\": {\n               \"exec\": {\n                \"properties\": {\n                 \"command\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"httpGet\": {\n                \"properties\": {\n                 \"host\": {\n                  \"type\": \"string\"\n                 },\n                 \"httpHeaders\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"name\": {\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"path\": {\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"x-kubernetes-int-or-string\": true\n                 },\n                 \"scheme\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               },\n               \"sleep\": {\n                \"properties\": {\n                 \"seconds\": {\n                  \"format\": \"int64\",\n                  \"type\": \"integer\"\n                 }\n                },\n                \"required\": [\n                 \"seconds\"\n                ],\n                \"type\": \"object\"\n               },\n               \"tcpSocket\": {\n                \"properties\": {\n                 \"host\": {\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"x-kubernetes-int-or-string\": true\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"livenessProbe\": {\n            \"properties\": {\n             \"exec\": {\n              \"properties\": {\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"properties\": {\n               \"port\": {\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"path\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"ports\": {\n            \"items\": {\n             \"properties\": {\n              \"containerPort\": {\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"hostIP\": {\n               \"type\": \"string\"\n              },\n              \"hostPort\": {\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"name\": {\n               \"type\": \"string\"\n              },\n              \"protocol\": {\n               \"default\": \"TCP\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"containerPort\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"containerPort\",\n             \"protocol\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"readinessProbe\": {\n            \"properties\": {\n             \"exec\": {\n              \"properties\": {\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"properties\": {\n               \"port\": {\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"path\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"resizePolicy\": {\n            \"items\": {\n             \"properties\": {\n              \"resourceName\": {\n               \"type\": \"string\"\n              },\n              \"restartPolicy\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"resourceName\",\n              \"restartPolicy\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"resources\": {\n            \"properties\": {\n             \"claims\": {\n              \"items\": {\n               \"properties\": {\n                \"name\": {\n                 \"type\": \"string\"\n                },\n                \"request\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-map-keys\": [\n               \"name\"\n              ],\n              \"x-kubernetes-list-type\": \"map\"\n             },\n             \"limits\": {\n              \"additionalProperties\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"type\": \"object\"\n             },\n             \"requests\": {\n              \"additionalProperties\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"restartPolicy\": {\n            \"type\": \"string\"\n           },\n           \"securityContext\": {\n            \"properties\": {\n             \"allowPrivilegeEscalation\": {\n              \"type\": \"boolean\"\n             },\n             \"appArmorProfile\": {\n              \"properties\": {\n               \"localhostProfile\": {\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"type\"\n              ],\n              \"type\": \"object\"\n             },\n             \"capabilities\": {\n              \"properties\": {\n               \"add\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"drop\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"privileged\": {\n              \"type\": \"boolean\"\n             },\n             \"procMount\": {\n              \"type\": \"string\"\n             },\n             \"readOnlyRootFilesystem\": {\n              \"type\": \"boolean\"\n             },\n             \"runAsGroup\": {\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"runAsNonRoot\": {\n              \"type\": \"boolean\"\n             },\n             \"runAsUser\": {\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"seLinuxOptions\": {\n              \"properties\": {\n               \"level\": {\n                \"type\": \"string\"\n               },\n               \"role\": {\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"type\": \"string\"\n               },\n               \"user\": {\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"seccompProfile\": {\n              \"properties\": {\n               \"localhostProfile\": {\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"type\"\n              ],\n              \"type\": \"object\"\n             },\n             \"windowsOptions\": {\n              \"properties\": {\n               \"gmsaCredentialSpec\": {\n                \"type\": \"string\"\n               },\n               \"gmsaCredentialSpecName\": {\n                \"type\": \"string\"\n               },\n               \"hostProcess\": {\n                \"type\": \"boolean\"\n               },\n               \"runAsUserName\": {\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"startupProbe\": {\n            \"properties\": {\n             \"exec\": {\n              \"properties\": {\n               \"command\": {\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"properties\": {\n               \"port\": {\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"items\": {\n                 \"properties\": {\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"path\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"properties\": {\n               \"host\": {\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"stdin\": {\n            \"type\": \"boolean\"\n           },\n           \"stdinOnce\": {\n            \"type\": \"boolean\"\n           },\n           \"terminationMessagePath\": {\n            \"type\": \"string\"\n           },\n           \"terminationMessagePolicy\": {\n            \"type\": \"string\"\n           },\n           \"tty\": {\n            \"type\": \"boolean\"\n           },\n           \"volumeDevices\": {\n            \"items\": {\n             \"properties\": {\n              \"devicePath\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"devicePath\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"devicePath\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"volumeMounts\": {\n            \"items\": {\n             \"properties\": {\n              \"mountPath\": {\n               \"type\": \"string\"\n              },\n              \"mountPropagation\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"type\": \"string\"\n              },\n              \"readOnly\": {\n               \"type\": \"boolean\"\n              },\n              \"recursiveReadOnly\": {\n               \"type\": \"string\"\n              },\n              \"subPath\": {\n               \"type\": \"string\"\n              },\n              \"subPathExpr\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"mountPath\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"mountPath\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"workingDir\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"lifecycle\": {\n         \"properties\": {\n          \"postStart\": {\n           \"properties\": {\n            \"exec\": {\n             \"properties\": {\n              \"command\": {\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"httpGet\": {\n             \"properties\": {\n              \"host\": {\n               \"type\": \"string\"\n              },\n              \"httpHeaders\": {\n               \"items\": {\n                \"properties\": {\n                 \"name\": {\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\",\n                 \"value\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"path\": {\n               \"type\": \"string\"\n              },\n              \"port\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"scheme\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"port\"\n             ],\n             \"type\": \"object\"\n            },\n            \"sleep\": {\n             \"properties\": {\n              \"seconds\": {\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              }\n             },\n             \"required\": [\n              \"seconds\"\n             ],\n             \"type\": \"object\"\n            },\n            \"tcpSocket\": {\n             \"properties\": {\n              \"host\": {\n               \"type\": \"string\"\n              },\n              \"port\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"x-kubernetes-int-or-string\": true\n              }\n             },\n             \"required\": [\n              \"port\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"preStop\": {\n           \"properties\": {\n            \"exec\": {\n             \"properties\": {\n              \"command\": {\n               \"items\": {\n                \"type\": \"string\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"httpGet\": {\n             \"properties\": {\n              \"host\": {\n               \"type\": \"string\"\n              },\n              \"httpHeaders\": {\n               \"items\": {\n                \"properties\": {\n                 \"name\": {\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"name\",\n                 \"value\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"path\": {\n               \"type\": \"string\"\n              },\n              \"port\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"scheme\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"port\"\n             ],\n             \"type\": \"object\"\n            },\n            \"sleep\": {\n             \"properties\": {\n              \"seconds\": {\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              }\n             },\n             \"required\": [\n              \"seconds\"\n             ],\n             \"type\": \"object\"\n            },\n            \"tcpSocket\": {\n             \"properties\": {\n              \"host\": {\n               \"type\": \"string\"\n              },\n              \"port\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"x-kubernetes-int-or-string\": true\n              }\n             },\n             \"required\": [\n              \"port\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"livenessProbe\": {\n         \"properties\": {\n          \"exec\": {\n           \"properties\": {\n            \"command\": {\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"failureThreshold\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"grpc\": {\n           \"properties\": {\n            \"port\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"service\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"port\"\n           ],\n           \"type\": \"object\"\n          },\n          \"httpGet\": {\n           \"properties\": {\n            \"host\": {\n             \"type\": \"string\"\n            },\n            \"httpHeaders\": {\n             \"items\": {\n              \"properties\": {\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"value\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\",\n               \"value\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"path\": {\n             \"type\": \"string\"\n            },\n            \"port\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"x-kubernetes-int-or-string\": true\n            },\n            \"scheme\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"port\"\n           ],\n           \"type\": \"object\"\n          },\n          \"initialDelaySeconds\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"periodSeconds\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"successThreshold\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"tcpSocket\": {\n           \"properties\": {\n            \"host\": {\n             \"type\": \"string\"\n            },\n            \"port\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"x-kubernetes-int-or-string\": true\n            }\n           },\n           \"required\": [\n            \"port\"\n           ],\n           \"type\": \"object\"\n          },\n          \"terminationGracePeriodSeconds\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"timeoutSeconds\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"nodeSelector\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"podPlacementPolicy\": {\n         \"properties\": {\n          \"name\": {\n           \"default\": \"\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"priority\": {\n         \"format\": \"int32\",\n         \"type\": \"integer\"\n        },\n        \"priorityClassName\": {\n         \"type\": \"string\"\n        },\n        \"readinessProbe\": {\n         \"properties\": {\n          \"exec\": {\n           \"properties\": {\n            \"command\": {\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"failureThreshold\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"grpc\": {\n           \"properties\": {\n            \"port\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"service\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"port\"\n           ],\n           \"type\": \"object\"\n          },\n          \"httpGet\": {\n           \"properties\": {\n            \"host\": {\n             \"type\": \"string\"\n            },\n            \"httpHeaders\": {\n             \"items\": {\n              \"properties\": {\n               \"name\": {\n                \"type\": \"string\"\n               },\n               \"value\": {\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\",\n               \"value\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"path\": {\n             \"type\": \"string\"\n            },\n            \"port\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"x-kubernetes-int-or-string\": true\n            },\n            \"scheme\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"port\"\n           ],\n           \"type\": \"object\"\n          },\n          \"initialDelaySeconds\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"periodSeconds\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"successThreshold\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"tcpSocket\": {\n           \"properties\": {\n            \"host\": {\n             \"type\": \"string\"\n            },\n            \"port\": {\n             \"anyOf\": [\n              {\n               \"type\": \"integer\"\n              },\n              {\n               \"type\": \"string\"\n              }\n             ],\n             \"x-kubernetes-int-or-string\": true\n            }\n           },\n           \"required\": [\n            \"port\"\n           ],\n           \"type\": \"object\"\n          },\n          \"terminationGracePeriodSeconds\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"timeoutSeconds\": {\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"resources\": {\n         \"properties\": {\n          \"claims\": {\n           \"items\": {\n            \"properties\": {\n             \"name\": {\n              \"type\": \"string\"\n             },\n             \"request\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"name\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-map-keys\": [\n            \"name\"\n           ],\n           \"x-kubernetes-list-type\": \"map\"\n          },\n          \"limits\": {\n           \"additionalProperties\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"type\": \"object\"\n          },\n          \"requests\": {\n           \"additionalProperties\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"runtimeClassName\": {\n         \"type\": \"string\"\n        },\n        \"schedulerName\": {\n         \"type\": \"string\"\n        },\n        \"securityContext\": {\n         \"properties\": {\n          \"appArmorProfile\": {\n           \"properties\": {\n            \"localhostProfile\": {\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"fsGroup\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"fsGroupChangePolicy\": {\n           \"type\": \"string\"\n          },\n          \"runAsGroup\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"runAsNonRoot\": {\n           \"type\": \"boolean\"\n          },\n          \"runAsUser\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"seLinuxChangePolicy\": {\n           \"type\": \"string\"\n          },\n          \"seLinuxOptions\": {\n           \"properties\": {\n            \"level\": {\n             \"type\": \"string\"\n            },\n            \"role\": {\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"type\": \"string\"\n            },\n            \"user\": {\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"seccompProfile\": {\n           \"properties\": {\n            \"localhostProfile\": {\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"supplementalGroups\": {\n           \"items\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"supplementalGroupsPolicy\": {\n           \"type\": \"string\"\n          },\n          \"sysctls\": {\n           \"items\": {\n            \"properties\": {\n             \"name\": {\n              \"type\": \"string\"\n             },\n             \"value\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"name\",\n             \"value\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"windowsOptions\": {\n           \"properties\": {\n            \"gmsaCredentialSpec\": {\n             \"type\": \"string\"\n            },\n            \"gmsaCredentialSpecName\": {\n             \"type\": \"string\"\n            },\n            \"hostProcess\": {\n             \"type\": \"boolean\"\n            },\n            \"runAsUserName\": {\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"serviceAccountName\": {\n         \"type\": \"string\"\n        },\n        \"shareProcessNamespace\": {\n         \"type\": \"boolean\"\n        },\n        \"terminationGracePeriodSeconds\": {\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"tolerations\": {\n         \"items\": {\n          \"properties\": {\n           \"effect\": {\n            \"type\": \"string\"\n           },\n           \"key\": {\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"type\": \"string\"\n           },\n           \"tolerationSeconds\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"value\": {\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"topologySpreadConstraints\": {\n         \"items\": {\n          \"properties\": {\n           \"labelSelector\": {\n            \"properties\": {\n             \"matchExpressions\": {\n              \"items\": {\n               \"properties\": {\n                \"key\": {\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"matchLabelKeys\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"maxSkew\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"minDomains\": {\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"nodeAffinityPolicy\": {\n            \"type\": \"string\"\n           },\n           \"nodeTaintsPolicy\": {\n            \"type\": \"string\"\n           },\n           \"topologyKey\": {\n            \"type\": \"string\"\n           },\n           \"whenUnsatisfiable\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"maxSkew\",\n           \"topologyKey\",\n           \"whenUnsatisfiable\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-map-keys\": [\n          \"topologyKey\",\n          \"whenUnsatisfiable\"\n         ],\n         \"x-kubernetes-list-type\": \"map\"\n        },\n        \"volumeMounts\": {\n         \"items\": {\n          \"properties\": {\n           \"mountPath\": {\n            \"type\": \"string\"\n           },\n           \"mountPropagation\": {\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"readOnly\": {\n            \"type\": \"boolean\"\n           },\n           \"recursiveReadOnly\": {\n            \"type\": \"string\"\n           },\n           \"subPath\": {\n            \"type\": \"string\"\n           },\n           \"subPathExpr\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"mountPath\",\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"volumes\": {\n         \"items\": {\n          \"properties\": {\n           \"awsElasticBlockStore\": {\n            \"properties\": {\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"partition\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"volumeID\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"volumeID\"\n            ],\n            \"type\": \"object\"\n           },\n           \"azureDisk\": {\n            \"properties\": {\n             \"cachingMode\": {\n              \"type\": \"string\"\n             },\n             \"diskName\": {\n              \"type\": \"string\"\n             },\n             \"diskURI\": {\n              \"type\": \"string\"\n             },\n             \"fsType\": {\n              \"default\": \"ext4\",\n              \"type\": \"string\"\n             },\n             \"kind\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"default\": false,\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"diskName\",\n             \"diskURI\"\n            ],\n            \"type\": \"object\"\n           },\n           \"azureFile\": {\n            \"properties\": {\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"secretName\": {\n              \"type\": \"string\"\n             },\n             \"shareName\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"secretName\",\n             \"shareName\"\n            ],\n            \"type\": \"object\"\n           },\n           \"cephfs\": {\n            \"properties\": {\n             \"monitors\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"secretFile\": {\n              \"type\": \"string\"\n             },\n             \"secretRef\": {\n              \"properties\": {\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"user\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"monitors\"\n            ],\n            \"type\": \"object\"\n           },\n           \"cinder\": {\n            \"properties\": {\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"properties\": {\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"volumeID\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"volumeID\"\n            ],\n            \"type\": \"object\"\n           },\n           \"configMap\": {\n            \"properties\": {\n             \"defaultMode\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"items\": {\n              \"items\": {\n               \"properties\": {\n                \"key\": {\n                 \"type\": \"string\"\n                },\n                \"mode\": {\n                 \"format\": \"int32\",\n                 \"type\": \"integer\"\n                },\n                \"path\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"path\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"name\": {\n              \"default\": \"\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"type\": \"boolean\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"csi\": {\n            \"properties\": {\n             \"driver\": {\n              \"type\": \"string\"\n             },\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"nodePublishSecretRef\": {\n              \"properties\": {\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"volumeAttributes\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"driver\"\n            ],\n            \"type\": \"object\"\n           },\n           \"downwardAPI\": {\n            \"properties\": {\n             \"defaultMode\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"items\": {\n              \"items\": {\n               \"properties\": {\n                \"fieldRef\": {\n                 \"properties\": {\n                  \"apiVersion\": {\n                   \"type\": \"string\"\n                  },\n                  \"fieldPath\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"fieldPath\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"mode\": {\n                 \"format\": \"int32\",\n                 \"type\": \"integer\"\n                },\n                \"path\": {\n                 \"type\": \"string\"\n                },\n                \"resourceFieldRef\": {\n                 \"properties\": {\n                  \"containerName\": {\n                   \"type\": \"string\"\n                  },\n                  \"divisor\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"resource\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resource\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"path\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"emptyDir\": {\n            \"properties\": {\n             \"medium\": {\n              \"type\": \"string\"\n             },\n             \"sizeLimit\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"ephemeral\": {\n            \"properties\": {\n             \"volumeClaimTemplate\": {\n              \"properties\": {\n               \"metadata\": {\n                \"properties\": {\n                 \"annotations\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"generateName\": {\n                  \"type\": \"string\"\n                 },\n                 \"labels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"name\": {\n                  \"type\": \"string\"\n                 },\n                 \"namespace\": {\n                  \"type\": \"string\"\n                 },\n                 \"ownerReferences\": {\n                  \"items\": {\n                   \"properties\": {\n                    \"apiVersion\": {\n                     \"type\": \"string\"\n                    },\n                    \"blockOwnerDeletion\": {\n                     \"type\": \"boolean\"\n                    },\n                    \"controller\": {\n                     \"type\": \"boolean\"\n                    },\n                    \"kind\": {\n                     \"type\": \"string\"\n                    },\n                    \"name\": {\n                     \"type\": \"string\"\n                    },\n                    \"uid\": {\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"apiVersion\",\n                    \"kind\",\n                    \"name\",\n                    \"uid\"\n                   ],\n                   \"type\": \"object\",\n                   \"x-kubernetes-map-type\": \"atomic\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"spec\": {\n                \"properties\": {\n                 \"accessModes\": {\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 },\n                 \"dataSource\": {\n                  \"properties\": {\n                   \"apiGroup\": {\n                    \"type\": \"string\"\n                   },\n                   \"kind\": {\n                    \"type\": \"string\"\n                   },\n                   \"name\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"kind\",\n                   \"name\"\n                  ],\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"dataSourceRef\": {\n                  \"properties\": {\n                   \"apiGroup\": {\n                    \"type\": \"string\"\n                   },\n                   \"kind\": {\n                    \"type\": \"string\"\n                   },\n                   \"name\": {\n                    \"type\": \"string\"\n                   },\n                   \"namespace\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"kind\",\n                   \"name\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"resources\": {\n                  \"properties\": {\n                   \"limits\": {\n                    \"additionalProperties\": {\n                     \"anyOf\": [\n                      {\n                       \"type\": \"integer\"\n                      },\n                      {\n                       \"type\": \"string\"\n                      }\n                     ],\n                     \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                     \"x-kubernetes-int-or-string\": true\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"requests\": {\n                    \"additionalProperties\": {\n                     \"anyOf\": [\n                      {\n                       \"type\": \"integer\"\n                      },\n                      {\n                       \"type\": \"string\"\n                      }\n                     ],\n                     \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                     \"x-kubernetes-int-or-string\": true\n                    },\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"selector\": {\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"items\": {\n                     \"properties\": {\n                      \"key\": {\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\",\n                       \"x-kubernetes-list-type\": \"atomic\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"storageClassName\": {\n                  \"type\": \"string\"\n                 },\n                 \"volumeAttributesClassName\": {\n                  \"type\": \"string\"\n                 },\n                 \"volumeMode\": {\n                  \"type\": \"string\"\n                 },\n                 \"volumeName\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"required\": [\n               \"spec\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"fc\": {\n            \"properties\": {\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"lun\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"targetWWNs\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"wwids\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"flexVolume\": {\n            \"properties\": {\n             \"driver\": {\n              \"type\": \"string\"\n             },\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"options\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"object\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"properties\": {\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"required\": [\n             \"driver\"\n            ],\n            \"type\": \"object\"\n           },\n           \"flocker\": {\n            \"properties\": {\n             \"datasetName\": {\n              \"type\": \"string\"\n             },\n             \"datasetUUID\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"gcePersistentDisk\": {\n            \"properties\": {\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"partition\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"pdName\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"pdName\"\n            ],\n            \"type\": \"object\"\n           },\n           \"glusterfs\": {\n            \"properties\": {\n             \"endpoints\": {\n              \"type\": \"string\"\n             },\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"endpoints\",\n             \"path\"\n            ],\n            \"type\": \"object\"\n           },\n           \"hostPath\": {\n            \"properties\": {\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"path\"\n            ],\n            \"type\": \"object\"\n           },\n           \"iscsi\": {\n            \"properties\": {\n             \"chapAuthDiscovery\": {\n              \"type\": \"boolean\"\n             },\n             \"chapAuthSession\": {\n              \"type\": \"boolean\"\n             },\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"initiatorName\": {\n              \"type\": \"string\"\n             },\n             \"iqn\": {\n              \"type\": \"string\"\n             },\n             \"iscsiInterface\": {\n              \"default\": \"default\",\n              \"type\": \"string\"\n             },\n             \"lun\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"portals\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"properties\": {\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"targetPortal\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"iqn\",\n             \"lun\",\n             \"targetPortal\"\n            ],\n            \"type\": \"object\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           },\n           \"nfs\": {\n            \"properties\": {\n             \"path\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"server\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"path\",\n             \"server\"\n            ],\n            \"type\": \"object\"\n           },\n           \"persistentVolumeClaim\": {\n            \"properties\": {\n             \"claimName\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"claimName\"\n            ],\n            \"type\": \"object\"\n           },\n           \"photonPersistentDisk\": {\n            \"properties\": {\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"pdID\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"pdID\"\n            ],\n            \"type\": \"object\"\n           },\n           \"portworxVolume\": {\n            \"properties\": {\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"volumeID\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"volumeID\"\n            ],\n            \"type\": \"object\"\n           },\n           \"projected\": {\n            \"properties\": {\n             \"defaultMode\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"sources\": {\n              \"items\": {\n               \"properties\": {\n                \"clusterTrustBundle\": {\n                 \"properties\": {\n                  \"labelSelector\": {\n                   \"properties\": {\n                    \"matchExpressions\": {\n                     \"items\": {\n                      \"properties\": {\n                       \"key\": {\n                        \"type\": \"string\"\n                       },\n                       \"operator\": {\n                        \"type\": \"string\"\n                       },\n                       \"values\": {\n                        \"items\": {\n                         \"type\": \"string\"\n                        },\n                        \"type\": \"array\",\n                        \"x-kubernetes-list-type\": \"atomic\"\n                       }\n                      },\n                      \"required\": [\n                       \"key\",\n                       \"operator\"\n                      ],\n                      \"type\": \"object\"\n                     },\n                     \"type\": \"array\",\n                     \"x-kubernetes-list-type\": \"atomic\"\n                    },\n                    \"matchLabels\": {\n                     \"additionalProperties\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"object\"\n                    }\n                   },\n                   \"type\": \"object\",\n                   \"x-kubernetes-map-type\": \"atomic\"\n                  },\n                  \"name\": {\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"type\": \"boolean\"\n                  },\n                  \"path\": {\n                   \"type\": \"string\"\n                  },\n                  \"signerName\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"path\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"configMap\": {\n                 \"properties\": {\n                  \"items\": {\n                   \"items\": {\n                    \"properties\": {\n                     \"key\": {\n                      \"type\": \"string\"\n                     },\n                     \"mode\": {\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"path\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"key\",\n                     \"path\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  },\n                  \"name\": {\n                   \"default\": \"\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"downwardAPI\": {\n                 \"properties\": {\n                  \"items\": {\n                   \"items\": {\n                    \"properties\": {\n                     \"fieldRef\": {\n                      \"properties\": {\n                       \"apiVersion\": {\n                        \"type\": \"string\"\n                       },\n                       \"fieldPath\": {\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"required\": [\n                       \"fieldPath\"\n                      ],\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     },\n                     \"mode\": {\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"path\": {\n                      \"type\": \"string\"\n                     },\n                     \"resourceFieldRef\": {\n                      \"properties\": {\n                       \"containerName\": {\n                        \"type\": \"string\"\n                       },\n                       \"divisor\": {\n                        \"anyOf\": [\n                         {\n                          \"type\": \"integer\"\n                         },\n                         {\n                          \"type\": \"string\"\n                         }\n                        ],\n                        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                        \"x-kubernetes-int-or-string\": true\n                       },\n                       \"resource\": {\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"required\": [\n                       \"resource\"\n                      ],\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     }\n                    },\n                    \"required\": [\n                     \"path\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"secret\": {\n                 \"properties\": {\n                  \"items\": {\n                   \"items\": {\n                    \"properties\": {\n                     \"key\": {\n                      \"type\": \"string\"\n                     },\n                     \"mode\": {\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"path\": {\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"key\",\n                     \"path\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  },\n                  \"name\": {\n                   \"default\": \"\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"serviceAccountToken\": {\n                 \"properties\": {\n                  \"audience\": {\n                   \"type\": \"string\"\n                  },\n                  \"expirationSeconds\": {\n                   \"format\": \"int64\",\n                   \"type\": \"integer\"\n                  },\n                  \"path\": {\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"path\"\n                 ],\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"quobyte\": {\n            \"properties\": {\n             \"group\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"registry\": {\n              \"type\": \"string\"\n             },\n             \"tenant\": {\n              \"type\": \"string\"\n             },\n             \"user\": {\n              \"type\": \"string\"\n             },\n             \"volume\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"registry\",\n             \"volume\"\n            ],\n            \"type\": \"object\"\n           },\n           \"rbd\": {\n            \"properties\": {\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"image\": {\n              \"type\": \"string\"\n             },\n             \"keyring\": {\n              \"default\": \"/etc/ceph/keyring\",\n              \"type\": \"string\"\n             },\n             \"monitors\": {\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"pool\": {\n              \"default\": \"rbd\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"properties\": {\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"user\": {\n              \"default\": \"admin\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"image\",\n             \"monitors\"\n            ],\n            \"type\": \"object\"\n           },\n           \"scaleIO\": {\n            \"properties\": {\n             \"fsType\": {\n              \"default\": \"xfs\",\n              \"type\": \"string\"\n             },\n             \"gateway\": {\n              \"type\": \"string\"\n             },\n             \"protectionDomain\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"properties\": {\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"sslEnabled\": {\n              \"type\": \"boolean\"\n             },\n             \"storageMode\": {\n              \"default\": \"ThinProvisioned\",\n              \"type\": \"string\"\n             },\n             \"storagePool\": {\n              \"type\": \"string\"\n             },\n             \"system\": {\n              \"type\": \"string\"\n             },\n             \"volumeName\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"gateway\",\n             \"secretRef\",\n             \"system\"\n            ],\n            \"type\": \"object\"\n           },\n           \"secret\": {\n            \"properties\": {\n             \"defaultMode\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"items\": {\n              \"items\": {\n               \"properties\": {\n                \"key\": {\n                 \"type\": \"string\"\n                },\n                \"mode\": {\n                 \"format\": \"int32\",\n                 \"type\": \"integer\"\n                },\n                \"path\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"path\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"optional\": {\n              \"type\": \"boolean\"\n             },\n             \"secretName\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"storageos\": {\n            \"properties\": {\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"properties\": {\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"volumeName\": {\n              \"type\": \"string\"\n             },\n             \"volumeNamespace\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"vsphereVolume\": {\n            \"properties\": {\n             \"fsType\": {\n              \"type\": \"string\"\n             },\n             \"storagePolicyID\": {\n              \"type\": \"string\"\n             },\n             \"storagePolicyName\": {\n              \"type\": \"string\"\n             },\n             \"volumePath\": {\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"volumePath\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"replicas\": {\n     \"format\": \"int32\",\n     \"type\": \"integer\"\n    },\n    \"serviceTemplates\": {\n     \"items\": {\n      \"properties\": {\n       \"alias\": {\n        \"enum\": [\n         \"internal\",\n         \"vault\",\n         \"stats\"\n        ],\n        \"type\": \"string\"\n       },\n       \"metadata\": {\n        \"properties\": {\n         \"annotations\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"spec\": {\n        \"properties\": {\n         \"clusterIP\": {\n          \"type\": \"string\"\n         },\n         \"externalIPs\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"externalTrafficPolicy\": {\n          \"type\": \"string\"\n         },\n         \"healthCheckNodePort\": {\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"loadBalancerIP\": {\n          \"type\": \"string\"\n         },\n         \"loadBalancerSourceRanges\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"ports\": {\n          \"items\": {\n           \"properties\": {\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"nodePort\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"port\": {\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            }\n           },\n           \"required\": [\n            \"port\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"sessionAffinityConfig\": {\n          \"properties\": {\n           \"clientIP\": {\n            \"properties\": {\n             \"timeoutSeconds\": {\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": {\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"alias\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"terminationPolicy\": {\n     \"enum\": [\n      \"Halt\",\n      \"Delete\",\n      \"WipeOut\",\n      \"DoNotTerminate\"\n     ],\n     \"type\": \"string\"\n    },\n    \"tls\": {\n     \"properties\": {\n      \"certificates\": {\n       \"items\": {\n        \"properties\": {\n         \"alias\": {\n          \"type\": \"string\"\n         },\n         \"dnsNames\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"duration\": {\n          \"type\": \"string\"\n         },\n         \"emailAddresses\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"ipAddresses\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"issuerRef\": {\n          \"properties\": {\n           \"apiGroup\": {\n            \"type\": \"string\"\n           },\n           \"kind\": {\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"kind\",\n           \"name\"\n          ],\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"privateKey\": {\n          \"properties\": {\n           \"encoding\": {\n            \"enum\": [\n             \"PKCS1\",\n             \"PKCS8\"\n            ],\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"renewBefore\": {\n          \"type\": \"string\"\n         },\n         \"secretName\": {\n          \"type\": \"string\"\n         },\n         \"subject\": {\n          \"properties\": {\n           \"countries\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"localities\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"organizationalUnits\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"organizations\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"postalCodes\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"provinces\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"serialNumber\": {\n            \"type\": \"string\"\n           },\n           \"streetAddresses\": {\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"uris\": {\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"required\": [\n         \"alias\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"issuerRef\": {\n       \"properties\": {\n        \"apiGroup\": {\n         \"type\": \"string\"\n        },\n        \"kind\": {\n         \"type\": \"string\"\n        },\n        \"name\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"kind\",\n        \"name\"\n       ],\n       \"type\": \"object\",\n       \"x-kubernetes-map-type\": \"atomic\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"unsealer\": {\n     \"properties\": {\n      \"mode\": {\n       \"properties\": {\n        \"awsKmsSsm\": {\n         \"properties\": {\n          \"credentialSecret\": {\n           \"type\": \"string\"\n          },\n          \"endpoint\": {\n           \"type\": \"string\"\n          },\n          \"kmsKeyID\": {\n           \"type\": \"string\"\n          },\n          \"region\": {\n           \"type\": \"string\"\n          },\n          \"ssmKeyPrefix\": {\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"kmsKeyID\"\n         ],\n         \"type\": \"object\"\n        },\n        \"azureKeyVault\": {\n         \"properties\": {\n          \"aadClientSecret\": {\n           \"type\": \"string\"\n          },\n          \"clientCertSecret\": {\n           \"type\": \"string\"\n          },\n          \"cloud\": {\n           \"type\": \"string\"\n          },\n          \"tenantID\": {\n           \"type\": \"string\"\n          },\n          \"useManagedIdentity\": {\n           \"type\": \"boolean\"\n          },\n          \"vaultBaseURL\": {\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"tenantID\",\n          \"vaultBaseURL\"\n         ],\n         \"type\": \"object\"\n        },\n        \"googleKmsGcs\": {\n         \"properties\": {\n          \"bucket\": {\n           \"type\": \"string\"\n          },\n          \"credentialSecret\": {\n           \"type\": \"string\"\n          },\n          \"kmsCryptoKey\": {\n           \"type\": \"string\"\n          },\n          \"kmsKeyRing\": {\n           \"type\": \"string\"\n          },\n          \"kmsLocation\": {\n           \"type\": \"string\"\n          },\n          \"kmsProject\": {\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"bucket\",\n          \"kmsCryptoKey\",\n          \"kmsKeyRing\",\n          \"kmsLocation\",\n          \"kmsProject\"\n         ],\n         \"type\": \"object\"\n        },\n        \"kubernetesSecret\": {\n         \"properties\": {\n          \"secretName\": {\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"secretName\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"overwriteExisting\": {\n       \"type\": \"boolean\"\n      },\n      \"retryPeriodSeconds\": {\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"secretShares\": {\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"secretThreshold\": {\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"storeRootToken\": {\n       \"type\": \"boolean\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"version\": {\n     \"type\": \"string\"\n    }\n   },\n   \"required\": [\n    \"backend\",\n    \"version\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Vault Server\",\n \"type\": \"object\"\n}"}}