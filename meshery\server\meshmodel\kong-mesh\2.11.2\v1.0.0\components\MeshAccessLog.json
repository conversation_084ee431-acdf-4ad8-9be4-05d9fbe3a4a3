{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Mesh Access Log", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kong-mesh", "displayName": "Kong Mesh", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Cloud Native Network"}, "subCategory": "Service Mesh", "metadata": {"isAnnotation": false, "primaryColor": "#003459", "shape": "circle", "source_uri": "https://github.com/Kong/kong-mesh-charts/releases/download/kong-mesh-2.11.2/kong-mesh-2.11.2.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" id=\"Layer_1\" xmlns:_xmlns=\"xmlns\" _xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 45.3 40.8\" style=\"enable-background:new 0 0 45.3 40.8;\" xml:space=\"preserve\" height=\"20\" width=\"20\">\n<style xmlns=\"http://www.w3.org/2000/svg\" type=\"text/css\">\n.st0{fill-rule:evenodd;clip-rule:evenodd;fill:#003459;}\n</style>\n<path xmlns=\"http://www.w3.org/2000/svg\" class=\"st0\" d=\"M14.9,33.6h8.3l4.3,5.4l-0.7,1.9H16l0.3-1.9l-2.5-4L14.9,33.6z M20.8,9.7h4.5l20,23.9l-1.6,7.3h-8.6l0.5-2&#xA;L17,16.5L20.8,9.7z M28.7,0L38,7.3l-1.2,1.2l1.6,2.2v2.4L33.8,17L26,7.8h-4.5l1.8-3.4L28.7,0z M9.1,23.4l6.5-5.6l8.6,10.4L21.8,32&#xA;h-7.9l-5.5,7.2l-1.3,1.7H0V32l6.6-8.6H9.1z\"></path>\n</svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" id=\"Layer_1\" xmlns:_xmlns=\"xmlns\" _xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 45.3 40.8\" style=\"enable-background:new 0 0 45.3 40.8;\" xml:space=\"preserve\" height=\"20\" width=\"20\">\n<style xmlns=\"http://www.w3.org/2000/svg\" type=\"text/css\">\n.st0{fill:#FFFFFF;}\n</style>\n<path xmlns=\"http://www.w3.org/2000/svg\" class=\"st0\" d=\"M14.9,33.6h8.3l4.3,5.4l-0.7,1.9H16l0.3-1.9l-2.5-4L14.9,33.6z M20.8,9.7h4.5l20,23.9l-1.6,7.3h-8.6l0.5-2&#xA;L17,16.5L20.8,9.7z M28.7,0L38,7.3l-1.2,1.2l1.6,2.2v2.4L33.8,17L26,7.8h-4.5l1.8-3.4L28.7,0z M9.1,23.4l6.5-5.6l8.6,10.4L21.8,32&#xA;h-7.9l-5.5,7.2l-1.3,1.7H0V32l6.6-8.6H9.1z\"></path>\n</svg>"}, "model": {"version": "2.11.2"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#003477", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 45.3 40.8\" style=\"enable-background:new 0 0 45.3 40.8;\" xml:space=\"preserve\"> <style type=\"text/css\"> .st0{fill-rule:evenodd;clip-rule:evenodd;fill:#003459;} </style> <path class=\"st0\" d=\"M14.9,33.6h8.3l4.3,5.4l-0.7,1.9H16l0.3-1.9l-2.5-4L14.9,33.6z M20.8,9.7h4.5l20,23.9l-1.6,7.3h-8.6l0.5-2 L17,16.5L20.8,9.7z M28.7,0L38,7.3l-1.2,1.2l1.6,2.2v2.4L33.8,17L26,7.8h-4.5l1.8-3.4L28.7,0z M9.1,23.4l6.5-5.6l8.6,10.4L21.8,32 h-7.9l-5.5,7.2l-1.3,1.7H0V32l6.6-8.6H9.1z\"/> </svg>", "svgComplete": "", "svgWhite": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 45.3 40.8\" style=\"enable-background:new 0 0 45.3 40.8;\" xml:space=\"preserve\"> <style type=\"text/css\"> .st0{fill:#FFFFFF;} </style> <path class=\"st0\" d=\"M14.9,33.6h8.3l4.3,5.4l-0.7,1.9H16l0.3-1.9l-2.5-4L14.9,33.6z M20.8,9.7h4.5l20,23.9l-1.6,7.3h-8.6l0.5-2 L17,16.5L20.8,9.7z M28.7,0L38,7.3l-1.2,1.2l1.6,2.2v2.4L33.8,17L26,7.8h-4.5l1.8-3.4L28.7,0z M9.1,23.4l6.5-5.6l8.6,10.4L21.8,32 h-7.9l-5.5,7.2l-1.3,1.7H0V32l6.6-8.6H9.1z\"/> </svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/Kong/kong-mesh-charts/releases/download/kong-mesh-2.11.2/kong-mesh-2.11.2.tgz"}, "configuration": null, "component": {"version": "kuma.io/v1alpha1", "kind": "MeshAccessLog", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Spec is the specification of the Kuma MeshAccessLog resource.\",\n   \"properties\": {\n    \"from\": {\n     \"description\": \"From list makes a match between clients and corresponding configurations\",\n     \"items\": {\n      \"properties\": {\n       \"default\": {\n        \"description\": \"Default is a configuration specific to the group of clients referenced in\\n'targetRef'\",\n        \"properties\": {\n         \"backends\": {\n          \"items\": {\n           \"properties\": {\n            \"file\": {\n             \"description\": \"FileBackend defines configuration for file based access logs\",\n             \"properties\": {\n              \"format\": {\n               \"description\": \"Format of access logs. Placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"properties\": {\n                \"json\": {\n                 \"example\": [\n                  {\n                   \"key\": \"start_time\",\n                   \"value\": \"%START_TIME%\"\n                  },\n                  {\n                   \"key\": \"bytes_received\",\n                   \"value\": \"%BYTES_RECEIVED%\"\n                  }\n                 ],\n                 \"items\": {\n                  \"properties\": {\n                   \"key\": {\n                    \"type\": \"string\"\n                   },\n                   \"value\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"value\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"omitEmptyValues\": {\n                 \"default\": false,\n                 \"type\": \"boolean\"\n                },\n                \"plain\": {\n                 \"example\": \"[%START_TIME%] %KUMA_MESH% %UPSTREAM_HOST%\",\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"enum\": [\n                  \"Plain\",\n                  \"Json\"\n                 ],\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              },\n              \"path\": {\n               \"description\": \"Path to a file that logs will be written to\",\n               \"example\": \"/tmp/access.log\",\n               \"minLength\": 1,\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            },\n            \"openTelemetry\": {\n             \"description\": \"Defines an OpenTelemetry logging backend.\",\n             \"properties\": {\n              \"attributes\": {\n               \"description\": \"Attributes can contain placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"example\": [\n                {\n                 \"key\": \"mesh\",\n                 \"value\": \"%KUMA_MESH%\"\n                }\n               ],\n               \"items\": {\n                \"properties\": {\n                 \"key\": {\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"value\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"body\": {\n               \"description\": \"Body is a raw string or an OTLP any value as described at\\nhttps://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/logs/data-model.md#field-body\\nIt can contain placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"example\": {\n                \"kvlistValue\": {\n                 \"values\": [\n                  {\n                   \"key\": \"mesh\",\n                   \"value\": {\n                    \"stringValue\": \"%KUMA_MESH%\"\n                   }\n                  }\n                 ]\n                }\n               },\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"endpoint\": {\n               \"description\": \"Endpoint of OpenTelemetry collector. An empty port defaults to 4317.\",\n               \"example\": \"otel-collector:4317\",\n               \"minLength\": 1,\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"endpoint\"\n             ],\n             \"type\": \"object\"\n            },\n            \"tcp\": {\n             \"description\": \"TCPBackend defines a TCP logging backend.\",\n             \"properties\": {\n              \"address\": {\n               \"description\": \"Address of the TCP logging backend\",\n               \"example\": \"127.0.0.1:5000\",\n               \"minLength\": 1,\n               \"type\": \"string\"\n              },\n              \"format\": {\n               \"description\": \"Format of access logs. Placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"properties\": {\n                \"json\": {\n                 \"example\": [\n                  {\n                   \"key\": \"start_time\",\n                   \"value\": \"%START_TIME%\"\n                  },\n                  {\n                   \"key\": \"bytes_received\",\n                   \"value\": \"%BYTES_RECEIVED%\"\n                  }\n                 ],\n                 \"items\": {\n                  \"properties\": {\n                   \"key\": {\n                    \"type\": \"string\"\n                   },\n                   \"value\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"value\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"omitEmptyValues\": {\n                 \"default\": false,\n                 \"type\": \"boolean\"\n                },\n                \"plain\": {\n                 \"example\": \"[%START_TIME%] %KUMA_MESH% %UPSTREAM_HOST%\",\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"enum\": [\n                  \"Plain\",\n                  \"Json\"\n                 ],\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"address\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": {\n             \"enum\": [\n              \"Tcp\",\n              \"File\",\n              \"OpenTelemetry\"\n             ],\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"targetRef\": {\n        \"description\": \"TargetRef is a reference to the resource that represents a group of\\nclients.\",\n        \"properties\": {\n         \"kind\": {\n          \"description\": \"Kind of the referenced resource\",\n          \"enum\": [\n           \"Mesh\",\n           \"MeshSubset\",\n           \"MeshGateway\",\n           \"MeshService\",\n           \"MeshExternalService\",\n           \"MeshMultiZoneService\",\n           \"MeshServiceSubset\",\n           \"MeshHTTPRoute\",\n           \"Dataplane\"\n          ],\n          \"type\": \"string\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n          \"type\": \"object\"\n         },\n         \"mesh\": {\n          \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n          \"type\": \"string\"\n         },\n         \"proxyTypes\": {\n          \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n          \"items\": {\n           \"enum\": [\n            \"Sidecar\",\n            \"Gateway\"\n           ],\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"sectionName\": {\n          \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n          \"type\": \"string\"\n         },\n         \"tags\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"kind\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"default\",\n       \"targetRef\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"rules\": {\n     \"description\": \"Rules defines inbound access log configurations. Currently limited to\\nselecting all inbound traffic, as L7 matching is not yet implemented.\",\n     \"items\": {\n      \"properties\": {\n       \"default\": {\n        \"description\": \"Default contains configuration of the inbound access logging\",\n        \"properties\": {\n         \"backends\": {\n          \"items\": {\n           \"properties\": {\n            \"file\": {\n             \"description\": \"FileBackend defines configuration for file based access logs\",\n             \"properties\": {\n              \"format\": {\n               \"description\": \"Format of access logs. Placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"properties\": {\n                \"json\": {\n                 \"example\": [\n                  {\n                   \"key\": \"start_time\",\n                   \"value\": \"%START_TIME%\"\n                  },\n                  {\n                   \"key\": \"bytes_received\",\n                   \"value\": \"%BYTES_RECEIVED%\"\n                  }\n                 ],\n                 \"items\": {\n                  \"properties\": {\n                   \"key\": {\n                    \"type\": \"string\"\n                   },\n                   \"value\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"value\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"omitEmptyValues\": {\n                 \"default\": false,\n                 \"type\": \"boolean\"\n                },\n                \"plain\": {\n                 \"example\": \"[%START_TIME%] %KUMA_MESH% %UPSTREAM_HOST%\",\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"enum\": [\n                  \"Plain\",\n                  \"Json\"\n                 ],\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              },\n              \"path\": {\n               \"description\": \"Path to a file that logs will be written to\",\n               \"example\": \"/tmp/access.log\",\n               \"minLength\": 1,\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            },\n            \"openTelemetry\": {\n             \"description\": \"Defines an OpenTelemetry logging backend.\",\n             \"properties\": {\n              \"attributes\": {\n               \"description\": \"Attributes can contain placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"example\": [\n                {\n                 \"key\": \"mesh\",\n                 \"value\": \"%KUMA_MESH%\"\n                }\n               ],\n               \"items\": {\n                \"properties\": {\n                 \"key\": {\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"value\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"body\": {\n               \"description\": \"Body is a raw string or an OTLP any value as described at\\nhttps://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/logs/data-model.md#field-body\\nIt can contain placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"example\": {\n                \"kvlistValue\": {\n                 \"values\": [\n                  {\n                   \"key\": \"mesh\",\n                   \"value\": {\n                    \"stringValue\": \"%KUMA_MESH%\"\n                   }\n                  }\n                 ]\n                }\n               },\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"endpoint\": {\n               \"description\": \"Endpoint of OpenTelemetry collector. An empty port defaults to 4317.\",\n               \"example\": \"otel-collector:4317\",\n               \"minLength\": 1,\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"endpoint\"\n             ],\n             \"type\": \"object\"\n            },\n            \"tcp\": {\n             \"description\": \"TCPBackend defines a TCP logging backend.\",\n             \"properties\": {\n              \"address\": {\n               \"description\": \"Address of the TCP logging backend\",\n               \"example\": \"127.0.0.1:5000\",\n               \"minLength\": 1,\n               \"type\": \"string\"\n              },\n              \"format\": {\n               \"description\": \"Format of access logs. Placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"properties\": {\n                \"json\": {\n                 \"example\": [\n                  {\n                   \"key\": \"start_time\",\n                   \"value\": \"%START_TIME%\"\n                  },\n                  {\n                   \"key\": \"bytes_received\",\n                   \"value\": \"%BYTES_RECEIVED%\"\n                  }\n                 ],\n                 \"items\": {\n                  \"properties\": {\n                   \"key\": {\n                    \"type\": \"string\"\n                   },\n                   \"value\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"value\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"omitEmptyValues\": {\n                 \"default\": false,\n                 \"type\": \"boolean\"\n                },\n                \"plain\": {\n                 \"example\": \"[%START_TIME%] %KUMA_MESH% %UPSTREAM_HOST%\",\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"enum\": [\n                  \"Plain\",\n                  \"Json\"\n                 ],\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"address\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": {\n             \"enum\": [\n              \"Tcp\",\n              \"File\",\n              \"OpenTelemetry\"\n             ],\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"default\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"targetRef\": {\n     \"description\": \"TargetRef is a reference to the resource the policy takes an effect on.\\nThe resource could be either a real store object or virtual resource\\ndefined in-place.\",\n     \"properties\": {\n      \"kind\": {\n       \"description\": \"Kind of the referenced resource\",\n       \"enum\": [\n        \"Mesh\",\n        \"MeshSubset\",\n        \"MeshGateway\",\n        \"MeshService\",\n        \"MeshExternalService\",\n        \"MeshMultiZoneService\",\n        \"MeshServiceSubset\",\n        \"MeshHTTPRoute\",\n        \"Dataplane\"\n       ],\n       \"type\": \"string\"\n      },\n      \"labels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n       \"type\": \"object\"\n      },\n      \"mesh\": {\n       \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n       \"type\": \"string\"\n      },\n      \"name\": {\n       \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n       \"type\": \"string\"\n      },\n      \"namespace\": {\n       \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n       \"type\": \"string\"\n      },\n      \"proxyTypes\": {\n       \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n       \"items\": {\n        \"enum\": [\n         \"Sidecar\",\n         \"Gateway\"\n        ],\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      },\n      \"sectionName\": {\n       \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n       \"type\": \"string\"\n      },\n      \"tags\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n       \"type\": \"object\"\n      }\n     },\n     \"required\": [\n      \"kind\"\n     ],\n     \"type\": \"object\"\n    },\n    \"to\": {\n     \"description\": \"To list makes a match between the consumed services and corresponding configurations\",\n     \"items\": {\n      \"properties\": {\n       \"default\": {\n        \"description\": \"Default is a configuration specific to the group of destinations referenced in\\n'targetRef'\",\n        \"properties\": {\n         \"backends\": {\n          \"items\": {\n           \"properties\": {\n            \"file\": {\n             \"description\": \"FileBackend defines configuration for file based access logs\",\n             \"properties\": {\n              \"format\": {\n               \"description\": \"Format of access logs. Placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"properties\": {\n                \"json\": {\n                 \"example\": [\n                  {\n                   \"key\": \"start_time\",\n                   \"value\": \"%START_TIME%\"\n                  },\n                  {\n                   \"key\": \"bytes_received\",\n                   \"value\": \"%BYTES_RECEIVED%\"\n                  }\n                 ],\n                 \"items\": {\n                  \"properties\": {\n                   \"key\": {\n                    \"type\": \"string\"\n                   },\n                   \"value\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"value\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"omitEmptyValues\": {\n                 \"default\": false,\n                 \"type\": \"boolean\"\n                },\n                \"plain\": {\n                 \"example\": \"[%START_TIME%] %KUMA_MESH% %UPSTREAM_HOST%\",\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"enum\": [\n                  \"Plain\",\n                  \"Json\"\n                 ],\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              },\n              \"path\": {\n               \"description\": \"Path to a file that logs will be written to\",\n               \"example\": \"/tmp/access.log\",\n               \"minLength\": 1,\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            },\n            \"openTelemetry\": {\n             \"description\": \"Defines an OpenTelemetry logging backend.\",\n             \"properties\": {\n              \"attributes\": {\n               \"description\": \"Attributes can contain placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"example\": [\n                {\n                 \"key\": \"mesh\",\n                 \"value\": \"%KUMA_MESH%\"\n                }\n               ],\n               \"items\": {\n                \"properties\": {\n                 \"key\": {\n                  \"type\": \"string\"\n                 },\n                 \"value\": {\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"value\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\"\n              },\n              \"body\": {\n               \"description\": \"Body is a raw string or an OTLP any value as described at\\nhttps://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/logs/data-model.md#field-body\\nIt can contain placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"example\": {\n                \"kvlistValue\": {\n                 \"values\": [\n                  {\n                   \"key\": \"mesh\",\n                   \"value\": {\n                    \"stringValue\": \"%KUMA_MESH%\"\n                   }\n                  }\n                 ]\n                }\n               },\n               \"format\": \"textarea\",\n               \"type\": \"string\"\n              },\n              \"endpoint\": {\n               \"description\": \"Endpoint of OpenTelemetry collector. An empty port defaults to 4317.\",\n               \"example\": \"otel-collector:4317\",\n               \"minLength\": 1,\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"endpoint\"\n             ],\n             \"type\": \"object\"\n            },\n            \"tcp\": {\n             \"description\": \"TCPBackend defines a TCP logging backend.\",\n             \"properties\": {\n              \"address\": {\n               \"description\": \"Address of the TCP logging backend\",\n               \"example\": \"127.0.0.1:5000\",\n               \"minLength\": 1,\n               \"type\": \"string\"\n              },\n              \"format\": {\n               \"description\": \"Format of access logs. Placeholders available on\\nhttps://www.envoyproxy.io/docs/envoy/latest/configuration/observability/access_log/usage#command-operators\",\n               \"properties\": {\n                \"json\": {\n                 \"example\": [\n                  {\n                   \"key\": \"start_time\",\n                   \"value\": \"%START_TIME%\"\n                  },\n                  {\n                   \"key\": \"bytes_received\",\n                   \"value\": \"%BYTES_RECEIVED%\"\n                  }\n                 ],\n                 \"items\": {\n                  \"properties\": {\n                   \"key\": {\n                    \"type\": \"string\"\n                   },\n                   \"value\": {\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"value\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"omitEmptyValues\": {\n                 \"default\": false,\n                 \"type\": \"boolean\"\n                },\n                \"plain\": {\n                 \"example\": \"[%START_TIME%] %KUMA_MESH% %UPSTREAM_HOST%\",\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"enum\": [\n                  \"Plain\",\n                  \"Json\"\n                 ],\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"address\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": {\n             \"enum\": [\n              \"Tcp\",\n              \"File\",\n              \"OpenTelemetry\"\n             ],\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"targetRef\": {\n        \"description\": \"TargetRef is a reference to the resource that represents a group of\\ndestinations.\",\n        \"properties\": {\n         \"kind\": {\n          \"description\": \"Kind of the referenced resource\",\n          \"enum\": [\n           \"Mesh\",\n           \"MeshSubset\",\n           \"MeshGateway\",\n           \"MeshService\",\n           \"MeshExternalService\",\n           \"MeshMultiZoneService\",\n           \"MeshServiceSubset\",\n           \"MeshHTTPRoute\",\n           \"Dataplane\"\n          ],\n          \"type\": \"string\"\n         },\n         \"labels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Labels are used to select group of MeshServices that match labels. Either Labels or\\nName and Namespace can be used.\",\n          \"type\": \"object\"\n         },\n         \"mesh\": {\n          \"description\": \"Mesh is reserved for future use to identify cross mesh resources.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"description\": \"Name of the referenced resource. Can only be used with kinds: `MeshService`,\\n`MeshServiceSubset` and `MeshGatewayRoute`\",\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"description\": \"Namespace specifies the namespace of target resource. If empty only resources in policy namespace\\nwill be targeted.\",\n          \"type\": \"string\"\n         },\n         \"proxyTypes\": {\n          \"description\": \"ProxyTypes specifies the data plane types that are subject to the policy. When not specified,\\nall data plane types are targeted by the policy.\",\n          \"items\": {\n           \"enum\": [\n            \"Sidecar\",\n            \"Gateway\"\n           ],\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"sectionName\": {\n          \"description\": \"SectionName is used to target specific section of resource.\\nFor example, you can target port from MeshService.ports[] by its name. Only traffic to this port will be affected.\",\n          \"type\": \"string\"\n         },\n         \"tags\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"Tags used to select a subset of proxies by tags. Can only be used with kinds\\n`MeshSubset` and `MeshServiceSubset`\",\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"kind\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"default\",\n       \"targetRef\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Mesh Access Log\",\n \"type\": \"object\"\n}"}}