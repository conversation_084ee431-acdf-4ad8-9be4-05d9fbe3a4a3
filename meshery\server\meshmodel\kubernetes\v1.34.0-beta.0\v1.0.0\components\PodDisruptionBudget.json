{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Pod Disruption Budget", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubernetes", "displayName": "Kubernetes", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Orchestration & Management"}, "subCategory": "Scheduling & Orchestration", "metadata": {"isAnnotation": false, "primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-0.17 0.08 230.10 223.35\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}.cls-2{fill:#326ce5}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.466a3.59 3.59 0 0 0-.855-.065 3.685 3.685 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53 43.53 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.656 3.656 0 0 0-.733.065l-.037-.045-21.785 3.698a43.695 43.695 0 0 0 17.54 21.946l8.442-20.4-.066-.08a3.683 3.683 0 0 0 .207-2.29zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.2l-10.738-19.406zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.039a3.725 3.725 0 0 0 5.913 2.84l.065.027 18.036-12.788a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.921a3.72 3.72 0 0 0 5.904-2.85l.092-.043 1.253-22.14a44.682 44.682 0 0 0-4.501.776 43.467 43.467 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.729a3.72 3.72 0 0 0 1.462-6.396l.018-.088-16.574-14.824a43.454 43.454 0 0 0-6.168 27.51l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.934 1.514-6.581-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.167l-17.422-75.675a13.602 13.602 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0L38.76 48.437a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.506 13.506 0 0 0 .491 1.718 13.1 13.1 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.423 13.423 0 0 0 2.574 2.133 13.924 13.924 0 0 0 3.857 1.677 13.298 13.298 0 0 0 3.43.473h.759l77.504-.018a12.993 12.993 0 0 0 1.41-.083 13.47 13.47 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.941 13.941 0 0 0 1.479-.822 13.303 13.303 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67 13.67 0 0 0 .5-1.724 13.282 13.282 0 0 0-.004-5.81zm-73.147 29.432a14.516 14.516 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.372 39.372 0 0 0 3.742 6.7 35.06 35.06 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.275 35.275 0 0 1-1.225-3.868 39.305 39.305 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.15-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.73a3.429 3.429 0 0 0-1.819.887 29.517 29.517 0 0 0-3.268 7.582 34.9 34.9 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.177 35.177 0 0 1 2.262-3.373 41.228 41.228 0 0 0 3.82-6.866 4.188 4.188 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68 4.68 0 0 0-2.428-.855 39.524 39.524 0 0 0-7.356 2.165 35.589 35.589 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.605.605 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.388 39.388 0 0 0 7.564-1.242 5.835 5.835 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.366 39.366 0 0 0-6.277-4.41 35.25 35.25 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.253 35.253 0 0 1 2.78 2.95 39.383 39.383 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082A54.289 54.289 0 0 1 102.86 61.89a55.052 55.052 0 0 1 7.63-1.173l.1-1.784a4.6 4.6 0 0 0 1.37-2.184 39.476 39.476 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.08-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.86.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.533 39.533 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.69 4.69 0 0 0 2.56-.278 39.532 39.532 0 0 0 5.69-5.148 35.004 35.004 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.095 35.095 0 0 1-3.507 2.046 39.495 39.495 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.745 4.745 0 0 0 1.82 1.83 39.464 39.464 0 0 0 7.568 1.246 35.64 35.64 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.03-.348-.075-.785-.151-1.09-.231a35.14 35.14 0 0 1-3.785-1.462 39.477 39.477 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.92 54.92 0 0 1-24.328 30.605z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M225.407 135.107L206.4 52.547a14.838 14.838 0 0 0-7.958-9.9l-76.935-36.73a14.825 14.825 0 0 0-12.771 0L31.808 42.669a14.838 14.838 0 0 0-7.961 9.895L4.873 135.129a14.668 14.668 0 0 0 1.995 11.185c.261.4.538.788.838 1.162l53.246 66.205a14.98 14.98 0 0 0 11.499 5.487l85.387-.02a14.986 14.986 0 0 0 11.5-5.48l53.227-66.211a14.72 14.72 0 0 0 2.842-12.347zm-9.197 3.866a13.677 13.677 0 0 1-.498 1.723 13.184 13.184 0 0 1-2.11 3.786l-47.299 58.838-1.486 1.852a13.305 13.305 0 0 1-3.24 2.865 13.945 13.945 0 0 1-1.474.822q-.513.237-1.045.43a13.873 13.873 0 0 1-1.383.445 13.473 13.473 0 0 1-1.989.379 12.988 12.988 0 0 1-1.41.082l-77.504.018h-.76a13.298 13.298 0 0 1-3.429-.472 13.925 13.925 0 0 1-3.855-1.679 13.424 13.424 0 0 1-2.576-2.132c-.233-.246-.468-.487-.68-.75l-48.805-60.679q-.408-.514-.765-1.066a13.102 13.102 0 0 1-1.343-2.726 13.505 13.505 0 0 1-.491-1.719 13.315 13.315 0 0 1 .004-5.809l17.394-75.675a13.598 13.598 0 0 1 7.295-9.07l70.508-33.685a13.589 13.589 0 0 1 11.705 0l70.519 33.67a13.602 13.602 0 0 1 7.293 9.073l17.422 75.674a13.282 13.282 0 0 1 .002 5.807z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M185.814 127.106c-.36-.083-.874-.225-1.227-.29a35.642 35.642 0 0 0-4.046-.326 39.464 39.464 0 0 1-7.57-1.242 4.745 4.745 0 0 1-1.82-1.832l-1.704-.496a54.837 54.837 0 0 0-8.79-37.983c.436-.396 1.262-1.127 1.495-1.342a3.338 3.338 0 0 1 .792-2.39 39.495 39.495 0 0 1 6.277-4.41 35.095 35.095 0 0 0 3.507-2.046c.272-.202.644-.522.929-.75a4.242 4.242 0 1 0-5.256-6.6c-.288.227-.68.525-.936.747a35.004 35.004 0 0 0-2.789 2.95 39.533 39.533 0 0 1-5.69 5.148 4.69 4.69 0 0 1-2.56.278l-1.606 1.147a55.309 55.309 0 0 0-35.032-16.927c-.039-.561-.087-1.577-.102-1.884a3.344 3.344 0 0 1-1.377-2.11 39.533 39.533 0 0 1 .478-7.656 35.112 35.112 0 0 0 .575-4.012c.013-.34-.007-.834-.007-1.201a4.242 4.242 0 1 0-8.441.004c0 .04.009.078.01.118-.004.35-.021.774-.01 1.08a35.476 35.476 0 0 0 .576 4.015 39.475 39.475 0 0 1 .47 7.654 4.601 4.601 0 0 1-1.37 2.182l-.1 1.786a55.052 55.052 0 0 0-7.63 1.173 54.289 54.289 0 0 0-27.574 15.754q-.77-.531-1.526-1.082a3.333 3.333 0 0 1-2.506-.243 39.383 39.383 0 0 1-5.69-5.141 35.255 35.255 0 0 0-2.777-2.95c-.257-.22-.65-.52-.938-.75a5.03 5.03 0 0 0-2.942-1.11 4.066 4.066 0 0 0-3.363 1.49 4.473 4.473 0 0 0 1.038 6.222l.065.046c.276.226.616.515.872.702a35.256 35.256 0 0 0 3.499 2.048 39.367 39.367 0 0 1 6.276 4.412 4.69 4.69 0 0 1 .849 2.434l1.351 1.208a54.613 54.613 0 0 0-8.611 38.073l-1.767.514a5.835 5.835 0 0 1-1.814 1.827 39.39 39.39 0 0 1-7.565 1.247 35.266 35.266 0 0 0-4.049.326c-.324.06-.774.174-1.127.262l-.037.008-.06.018a4.236 4.236 0 1 0 1.875 8.224l.063-.01c.028-.006.052-.02.08-.025.348-.08.786-.163 1.092-.246a35.59 35.59 0 0 0 3.786-1.451 39.527 39.527 0 0 1 7.358-2.165 4.68 4.68 0 0 1 2.426.857l1.84-.315a54.922 54.922 0 0 0 24.34 30.387l-.769 1.84a4.188 4.188 0 0 1 .377 2.387 41.228 41.228 0 0 1-3.82 6.864 35.183 35.183 0 0 0-2.263 3.372c-.168.318-.381.805-.542 1.138a4.238 4.238 0 1 0 7.621 3.616l.007-.008v-.01c.16-.33.387-.763.522-1.072a34.903 34.903 0 0 0 1.218-3.868 29.517 29.517 0 0 1 3.268-7.582 3.43 3.43 0 0 1 1.819-.888l.957-1.73a54.617 54.617 0 0 0 38.946.099c.268.478.768 1.392.9 1.623a3.335 3.335 0 0 1 2.155 1.31 39.306 39.306 0 0 1 2.898 7.096 35.275 35.275 0 0 0 1.225 3.868c.137.312.36.75.522 1.082a4.24 4.24 0 1 0 7.63-3.607c-.161-.333-.383-.82-.55-1.136a35.06 35.06 0 0 0-2.263-3.364 39.372 39.372 0 0 1-3.742-6.7 3.314 3.314 0 0 1 .324-2.49 14.519 14.519 0 0 1-.703-1.703 54.92 54.92 0 0 0 24.328-30.605c.546.087 1.497.253 1.806.316a3.337 3.337 0 0 1 2.36-.877 39.476 39.476 0 0 1 7.36 2.165 35.135 35.135 0 0 0 3.788 1.462c.305.08.74.156 1.09.233.029.008.055.02.083.028l.06.009a4.236 4.236 0 1 0 1.878-8.224zm-40.1-42.987l-18.037 12.787-.063-.03a3.723 3.723 0 0 1-5.913-2.838l-.02-.01-1.253-22.103a43.85 43.85 0 0 1 25.285 12.194zm-33.978 24.228h6.788l4.22 5.276-1.513 6.58-6.096 2.934-6.114-2.94-1.516-6.583zm-6.386-35.648a44.672 44.672 0 0 1 4.503-.774l-1.255 22.137-.092.044a3.72 3.72 0 0 1-5.904 2.852l-.035.02-18.154-12.872a43.467 43.467 0 0 1 20.937-11.407zm-27.52 19.68l16.574 14.824-.018.09a3.72 3.72 0 0 1-1.462 6.395l-.017.072-21.245 6.13a43.454 43.454 0 0 1 6.168-27.51zm22.191 39.38l-8.441 20.397a43.696 43.696 0 0 1-17.536-21.948l21.783-3.7.037.049a3.655 3.655 0 0 1 .73-.065 3.72 3.72 0 0 1 3.364 5.185zm24.916 26.23a43.637 43.637 0 0 1-23.815-1.223l10.713-19.372h.018a3.725 3.725 0 0 1 6.557-.006h.08l10.74 19.404q-2.091.698-4.293 1.199zm13.841-5.751l-8.528-20.605.026-.037a3.725 3.725 0 0 1 1.803-4.823 3.685 3.685 0 0 1 1.425-.37 3.59 3.59 0 0 1 .855.063l.037-.046 21.977 3.714a43.53 43.53 0 0 1-17.595 22.105zm19.903-32.42l-21.352-6.15-.02-.09a3.725 3.725 0 0 1-1.46-6.395l-.008-.043 16.482-14.751a44.279 44.279 0 0 1 6.357 27.43z\" class=\"cls-1\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"9.70 9.20 210.86 204.86\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.46551a3.59023 3.59023 0 0 0-.855-.065 3.68515 3.68515 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53012 43.53012 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.6556 3.6556 0 0 0-.733.065l-.037-.045-21.785 3.698a43.69506 43.69506 0 0 0 17.54 21.946l8.442-20.399-.066-.08a3.68318 3.68318 0 0 0 .207-2.291zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.199l-10.738-19.407zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.0385a3.725 3.725 0 0 0 5.913 2.84l.065.028 18.036-12.789a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.922a3.72 3.72 0 0 0 5.904-2.85l.092-.044 1.253-22.139a44.68209 44.68209 0 0 0-4.501.775 43.4669 43.4669 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.728a3.72 3.72 0 0 0 1.462-6.396l.018-.087-16.574-14.825a43.454 43.454 0 0 0-6.168 27.511l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.933 1.514-6.582-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.16651l-17.422-75.675a13.60207 13.60207 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0l-70.507 33.688a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.50607 13.50607 0 0 0 .491 1.718 13.0998 13.0998 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.42334 13.42334 0 0 0 2.574 2.133 13.9237 13.9237 0 0 0 3.857 1.677 13.29785 13.29785 0 0 0 3.43.473h.759l77.504-.018a12.99345 12.99345 0 0 0 1.41-.083 13.46921 13.46921 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.94127 13.94127 0 0 0 1.479-.822 13.30347 13.30347 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67036 13.67036 0 0 0 .5-1.724 13.28215 13.28215 0 0 0-.004-5.809zm-73.147 29.432a14.51575 14.51575 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.37244 39.37244 0 0 0 3.742 6.7 35.06044 35.06044 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.27528 35.27528 0 0 1-1.225-3.868 39.3046 39.3046 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.149-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.731a3.429 3.429 0 0 0-1.819.886 29.51728 29.51728 0 0 0-3.268 7.582 34.89931 34.89931 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.17706 35.17706 0 0 1 2.262-3.373 41.22786 41.22786 0 0 0 3.82-6.866 4.18792 4.18792 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68007 4.68007 0 0 0-2.428-.855 39.52352 39.52352 0 0 0-7.356 2.165 35.58886 35.58886 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.60518.60518 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.38835 39.38835 0 0 0 7.564-1.242 5.83506 5.83506 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.36558 39.36558 0 0 0-6.277-4.41 35.2503 35.2503 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.25285 35.25285 0 0 1 2.78 2.95 39.38314 39.38314 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082a54.28892 54.28892 0 0 1 27.577-15.754 55.05181 55.05181 0 0 1 7.63-1.173l.1-1.784a4.6001 4.6001 0 0 0 1.37-2.184 39.47551 39.47551 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.079-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.861.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.53346 39.53346 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.6901 4.6901 0 0 0 2.56-.278 39.53152 39.53152 0 0 0 5.69-5.148 35.00382 35.00382 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.09523 35.09523 0 0 1-3.507 2.046 39.49476 39.49476 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.7449 4.7449 0 0 0 1.82 1.831 39.46448 39.46448 0 0 0 7.568 1.245 35.64041 35.64041 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.029-.348-.076-.785-.152-1.09-.232a35.1407 35.1407 0 0 1-3.785-1.462 39.47672 39.47672 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.91994 54.91994 0 0 1-24.328 30.605z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "v1.34.0-beta.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "concave-hexagon", "svgColor": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.7442 13.275L22.1119 14.7705L7.56633 23.0347V66.1336L22.2158 75.2495L24.7863 76.8491L21.5873 81.9901L19.0167 80.3905L2.93924 70.386C2.05115 69.8334 1.51123 68.8615 1.51123 67.8155V21.2728C1.51123 20.1836 2.09623 19.1784 3.04319 18.6404L19.1207 9.50584L21.7531 8.01025L24.7442 13.275ZM65.256 13.275L67.8884 14.7705L82.4339 23.0347V66.1336L67.7844 75.2495L65.2139 76.8491L68.413 81.9901L70.9835 80.3905L87.061 70.386C87.9491 69.8334 88.489 68.8615 88.489 67.8155V21.2728C88.489 20.1836 87.904 19.1784 86.9571 18.6404L70.8795 9.50584L68.2472 8.01025L65.256 13.275ZM45.6653 44.3201C45.6653 43.9505 45.965 43.6508 46.3346 43.6508H74.5609C74.9305 43.6508 75.2302 43.9505 75.2302 44.3201V64.2645C75.2302 64.6342 74.9305 64.9338 74.5609 64.9338H46.3346C45.965 64.9338 45.6653 64.6342 45.6653 64.2645V44.3201ZM51.1672 59.8473C51.5299 59.8473 51.8322 59.5127 51.8322 59.1111V49.4066C51.8322 49.005 51.5299 48.6704 51.1672 48.6704C50.8044 48.6704 50.5021 49.005 50.5021 49.4066V59.1111C50.5021 59.5127 50.8044 59.8473 51.1672 59.8473ZM57.3341 59.8473C57.6968 59.8473 57.9991 59.5127 57.9991 59.1111V49.4066C57.9991 49.005 57.6968 48.6704 57.3341 48.6704C56.9713 48.6704 56.669 49.005 56.669 49.4066V59.1111C56.669 59.5127 56.9713 59.8473 57.3341 59.8473ZM63.501 59.8473C63.8637 59.8473 64.166 59.5127 64.166 59.1111V49.4066C64.166 49.005 63.8637 48.6704 63.501 48.6704C63.1382 48.6704 62.8359 49.005 62.8359 49.4066V59.1111C62.8359 59.5127 63.1382 59.8473 63.501 59.8473ZM69.6679 59.8473C70.0306 59.8473 70.3329 59.5127 70.3329 59.1111V49.4066C70.3329 49.005 70.0306 48.6704 69.6679 48.6704C69.3051 48.6704 69.0028 49.005 69.0028 49.4066V59.1111C69.0028 59.5127 69.3051 59.8473 69.6679 59.8473ZM30.8565 20.8959C30.4868 20.8959 30.1871 21.1956 30.1871 21.5653V41.5096C30.1871 41.8793 30.4868 42.179 30.8565 42.179H59.0827C59.4524 42.179 59.752 41.8793 59.752 41.5096V21.5653C59.752 21.1956 59.4524 20.8959 59.0827 20.8959H30.8565ZM36.354 36.4232C36.354 36.8247 36.0518 37.1594 35.689 37.1594C35.3262 37.1594 35.0239 36.8247 35.0239 36.4232V26.7186C35.0239 26.3171 35.3262 25.9824 35.689 25.9824C36.0518 25.9824 36.354 26.3171 36.354 26.7186V36.4232ZM42.5814 36.4232C42.5814 36.8247 42.2791 37.1594 41.9164 37.1594C41.5536 37.1594 41.2513 36.8247 41.2513 36.4232V26.7186C41.2513 26.3171 41.5536 25.9824 41.9164 25.9824C42.2791 25.9824 42.5814 26.3171 42.5814 26.7186V36.4232ZM48.7483 36.4232C48.7483 36.8247 48.446 37.1594 48.0833 37.1594C47.7205 37.1594 47.4182 36.8247 47.4182 36.4232V26.7186C47.4182 26.3171 47.7205 25.9824 48.0833 25.9824C48.446 25.9824 48.7483 26.3171 48.7483 26.7186V36.4232ZM54.9152 36.4232C54.9152 36.8247 54.6129 37.1594 54.2502 37.1594C53.8874 37.1594 53.5851 36.8247 53.5851 36.4232V26.7186C53.5851 26.3171 53.8874 25.9824 54.2502 25.9824C54.6129 25.9824 54.9152 26.3171 54.9152 26.7186V36.4232ZM14.7697 44.3201C14.7697 43.9505 15.0694 43.6508 15.4391 43.6508H43.6653C44.035 43.6508 44.3346 43.9505 44.3346 44.3201V64.2645C44.3346 64.6342 44.035 64.9338 43.6653 64.9338H15.4391C15.0694 64.9338 14.7697 64.6342 14.7697 64.2645V44.3201ZM20.2716 59.8473C20.6344 59.8473 20.9367 59.5127 20.9367 59.1111V49.4066C20.9367 49.005 20.6344 48.6704 20.2716 48.6704C19.9088 48.6704 19.6065 49.005 19.6065 49.4066V59.1111C19.6065 59.5127 19.9088 59.8473 20.2716 59.8473ZM26.4385 59.8473C26.8013 59.8473 27.1036 59.5127 27.1036 59.1111V49.4066C27.1036 49.005 26.8013 48.6704 26.4385 48.6704C26.0757 48.6704 25.7734 49.005 25.7734 49.4066V59.1111C25.7734 59.5127 26.0757 59.8473 26.4385 59.8473ZM32.6054 59.8473C32.9682 59.8473 33.2705 59.5127 33.2705 59.1111V49.4066C33.2705 49.005 32.9682 48.6704 32.6054 48.6704C32.2427 48.6704 31.9404 49.005 31.9404 49.4066V59.1111C31.9404 59.5127 32.2427 59.8473 32.6054 59.8473ZM38.8328 59.8473C39.1955 59.8473 39.4978 59.5127 39.4978 59.1111V49.4066C39.4978 49.005 39.1955 48.6704 38.8328 48.6704C38.47 48.6704 38.1677 49.005 38.1677 49.4066V59.1111C38.1677 59.5127 38.47 59.8473 38.8328 59.8473Z\" fill=\"#326CE5\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.7442 13.275L22.1119 14.7705L7.56633 23.0347V66.1336L22.2158 75.2495L24.7863 76.8491L21.5873 81.9901L19.0167 80.3905L2.93924 70.386C2.05115 69.8334 1.51123 68.8615 1.51123 67.8155V21.2728C1.51123 20.1836 2.09623 19.1784 3.04319 18.6404L19.1207 9.50584L21.7531 8.01025L24.7442 13.275ZM65.256 13.275L67.8884 14.7705L82.4339 23.0347V66.1336L67.7844 75.2495L65.2139 76.8491L68.413 81.9901L70.9835 80.3905L87.061 70.386C87.9491 69.8334 88.489 68.8615 88.489 67.8155V21.2728C88.489 20.1836 87.904 19.1784 86.9571 18.6404L70.8795 9.50584L68.2472 8.01025L65.256 13.275ZM45.6653 44.3201C45.6653 43.9505 45.965 43.6508 46.3346 43.6508H74.5609C74.9305 43.6508 75.2302 43.9505 75.2302 44.3201V64.2645C75.2302 64.6342 74.9305 64.9338 74.5609 64.9338H46.3346C45.965 64.9338 45.6653 64.6342 45.6653 64.2645V44.3201ZM51.1672 59.8473C51.5299 59.8473 51.8322 59.5127 51.8322 59.1111V49.4066C51.8322 49.005 51.5299 48.6704 51.1672 48.6704C50.8044 48.6704 50.5021 49.005 50.5021 49.4066V59.1111C50.5021 59.5127 50.8044 59.8473 51.1672 59.8473ZM57.3341 59.8473C57.6968 59.8473 57.9991 59.5127 57.9991 59.1111V49.4066C57.9991 49.005 57.6968 48.6704 57.3341 48.6704C56.9713 48.6704 56.669 49.005 56.669 49.4066V59.1111C56.669 59.5127 56.9713 59.8473 57.3341 59.8473ZM63.501 59.8473C63.8637 59.8473 64.166 59.5127 64.166 59.1111V49.4066C64.166 49.005 63.8637 48.6704 63.501 48.6704C63.1382 48.6704 62.8359 49.005 62.8359 49.4066V59.1111C62.8359 59.5127 63.1382 59.8473 63.501 59.8473ZM69.6679 59.8473C70.0306 59.8473 70.3329 59.5127 70.3329 59.1111V49.4066C70.3329 49.005 70.0306 48.6704 69.6679 48.6704C69.3051 48.6704 69.0028 49.005 69.0028 49.4066V59.1111C69.0028 59.5127 69.3051 59.8473 69.6679 59.8473ZM30.8565 20.8959C30.4868 20.8959 30.1871 21.1956 30.1871 21.5653V41.5096C30.1871 41.8793 30.4868 42.179 30.8565 42.179H59.0827C59.4524 42.179 59.752 41.8793 59.752 41.5096V21.5653C59.752 21.1956 59.4524 20.8959 59.0827 20.8959H30.8565ZM36.354 36.4232C36.354 36.8247 36.0518 37.1594 35.689 37.1594C35.3262 37.1594 35.0239 36.8247 35.0239 36.4232V26.7186C35.0239 26.3171 35.3262 25.9824 35.689 25.9824C36.0518 25.9824 36.354 26.3171 36.354 26.7186V36.4232ZM42.5814 36.4232C42.5814 36.8247 42.2791 37.1594 41.9164 37.1594C41.5536 37.1594 41.2513 36.8247 41.2513 36.4232V26.7186C41.2513 26.3171 41.5536 25.9824 41.9164 25.9824C42.2791 25.9824 42.5814 26.3171 42.5814 26.7186V36.4232ZM48.7483 36.4232C48.7483 36.8247 48.446 37.1594 48.0833 37.1594C47.7205 37.1594 47.4182 36.8247 47.4182 36.4232V26.7186C47.4182 26.3171 47.7205 25.9824 48.0833 25.9824C48.446 25.9824 48.7483 26.3171 48.7483 26.7186V36.4232ZM54.9152 36.4232C54.9152 36.8247 54.6129 37.1594 54.2502 37.1594C53.8874 37.1594 53.5851 36.8247 53.5851 36.4232V26.7186C53.5851 26.3171 53.8874 25.9824 54.2502 25.9824C54.6129 25.9824 54.9152 26.3171 54.9152 26.7186V36.4232ZM14.7697 44.3201C14.7697 43.9505 15.0694 43.6508 15.4391 43.6508H43.6653C44.035 43.6508 44.3346 43.9505 44.3346 44.3201V64.2645C44.3346 64.6342 44.035 64.9338 43.6653 64.9338H15.4391C15.0694 64.9338 14.7697 64.6342 14.7697 64.2645V44.3201ZM20.2716 59.8473C20.6344 59.8473 20.9367 59.5127 20.9367 59.1111V49.4066C20.9367 49.005 20.6344 48.6704 20.2716 48.6704C19.9088 48.6704 19.6065 49.005 19.6065 49.4066V59.1111C19.6065 59.5127 19.9088 59.8473 20.2716 59.8473ZM26.4385 59.8473C26.8013 59.8473 27.1036 59.5127 27.1036 59.1111V49.4066C27.1036 49.005 26.8013 48.6704 26.4385 48.6704C26.0757 48.6704 25.7734 49.005 25.7734 49.4066V59.1111C25.7734 59.5127 26.0757 59.8473 26.4385 59.8473ZM32.6054 59.8473C32.9682 59.8473 33.2705 59.5127 33.2705 59.1111V49.4066C33.2705 49.005 32.9682 48.6704 32.6054 48.6704C32.2427 48.6704 31.9404 49.005 31.9404 49.4066V59.1111C31.9404 59.5127 32.2427 59.8473 32.6054 59.8473ZM38.8328 59.8473C39.1955 59.8473 39.4978 59.5127 39.4978 59.1111V49.4066C39.4978 49.005 39.1955 48.6704 38.8328 48.6704C38.47 48.6704 38.1677 49.005 38.1677 49.4066V59.1111C38.1677 59.5127 38.47 59.8473 38.8328 59.8473Z\" fill=\"white\"/>\n</svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3"}, "configuration": null, "component": {"version": "policy/v1", "kind": "PodDisruptionBudget", "schema": "{\"description\":\"PodDisruptionBudget is an object to define the max disruption that can be caused to a collection of pods\",\"properties\":{\"spec\":{\"allOf\":[{\"description\":\"PodDisruptionBudgetSpec is a description of a PodDisruptionBudget.\",\"properties\":{\"maxUnavailable\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"An eviction is allowed if at most \\\"maxUnavailable\\\" pods selected by \\\"selector\\\" are unavailable after the eviction, i.e. even in absence of the evicted pod. For example, one can prevent all voluntary evictions by specifying 0. This is a mutually exclusive setting with \\\"minAvailable\\\".\"},\"minAvailable\":{\"allOf\":[{\"description\":\"IntOrString is a type that can hold an int32 or a string.  When used in JSON or YAML marshalling and unmarshalling, it produces or consumes the inner type.  This allows you to have, for example, a JSON field that can accept a name or number.\",\"format\":\"int-or-string\",\"oneOf\":[{\"type\":\"integer\"},{\"type\":\"string\"}]}],\"description\":\"An eviction is allowed if at least \\\"minAvailable\\\" pods selected by \\\"selector\\\" will still be available after the eviction, i.e. even in the absence of the evicted pod.  So for example you can prevent all voluntary evictions by specifying \\\"100%\\\".\"},\"selector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"Label query over pods whose evictions are managed by the disruption budget. A null selector will match no pods, while an empty ({}) selector will select all pods within the namespace.\",\"x-kubernetes-patch-strategy\":\"replace\"},\"unhealthyPodEvictionPolicy\":{\"description\":\"UnhealthyPodEvictionPolicy defines the criteria for when unhealthy pods should be considered for eviction. Current implementation considers healthy pods, as pods that have status.conditions item with type=\\\"Ready\\\",status=\\\"True\\\".\\n\\nValid policies are IfHealthyBudget and AlwaysAllow. If no policy is specified, the default behavior will be used, which corresponds to the IfHealthyBudget policy.\\n\\nIfHealthyBudget policy means that running pods (status.phase=\\\"Running\\\"), but not yet healthy can be evicted only if the guarded application is not disrupted (status.currentHealthy is at least equal to status.desiredHealthy). Healthy pods will be subject to the PDB for eviction.\\n\\nAlwaysAllow policy means that all running pods (status.phase=\\\"Running\\\"), but not yet healthy are considered disrupted and can be evicted regardless of whether the criteria in a PDB is met. This means perspective running pods of a disrupted application might not get a chance to become healthy. Healthy pods will be subject to the PDB for eviction.\\n\\nAdditional policies may be added in the future. Clients making eviction decisions should disallow eviction of unhealthy pods if they encounter an unrecognized policy in this field.\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"Specification of the desired behavior of the PodDisruptionBudget.\"}},\"type\":\"object\",\"x-kubernetes-group-version-kind\":[{\"group\":\"policy\",\"kind\":\"PodDisruptionBudget\",\"version\":\"v1\"}]}"}}