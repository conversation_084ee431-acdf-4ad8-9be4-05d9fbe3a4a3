{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Custom Resource Definition", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubernetes", "displayName": "Kubernetes", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Orchestration & Management"}, "subCategory": "Scheduling & Orchestration", "metadata": {"isAnnotation": false, "primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-0.17 0.08 230.10 223.35\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}.cls-2{fill:#326ce5}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.466a3.59 3.59 0 0 0-.855-.065 3.685 3.685 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53 43.53 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.656 3.656 0 0 0-.733.065l-.037-.045-21.785 3.698a43.695 43.695 0 0 0 17.54 21.946l8.442-20.4-.066-.08a3.683 3.683 0 0 0 .207-2.29zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.2l-10.738-19.406zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.039a3.725 3.725 0 0 0 5.913 2.84l.065.027 18.036-12.788a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.921a3.72 3.72 0 0 0 5.904-2.85l.092-.043 1.253-22.14a44.682 44.682 0 0 0-4.501.776 43.467 43.467 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.729a3.72 3.72 0 0 0 1.462-6.396l.018-.088-16.574-14.824a43.454 43.454 0 0 0-6.168 27.51l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.934 1.514-6.581-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.167l-17.422-75.675a13.602 13.602 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0L38.76 48.437a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.506 13.506 0 0 0 .491 1.718 13.1 13.1 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.423 13.423 0 0 0 2.574 2.133 13.924 13.924 0 0 0 3.857 1.677 13.298 13.298 0 0 0 3.43.473h.759l77.504-.018a12.993 12.993 0 0 0 1.41-.083 13.47 13.47 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.941 13.941 0 0 0 1.479-.822 13.303 13.303 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67 13.67 0 0 0 .5-1.724 13.282 13.282 0 0 0-.004-5.81zm-73.147 29.432a14.516 14.516 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.372 39.372 0 0 0 3.742 6.7 35.06 35.06 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.275 35.275 0 0 1-1.225-3.868 39.305 39.305 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.15-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.73a3.429 3.429 0 0 0-1.819.887 29.517 29.517 0 0 0-3.268 7.582 34.9 34.9 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.177 35.177 0 0 1 2.262-3.373 41.228 41.228 0 0 0 3.82-6.866 4.188 4.188 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68 4.68 0 0 0-2.428-.855 39.524 39.524 0 0 0-7.356 2.165 35.589 35.589 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.605.605 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.388 39.388 0 0 0 7.564-1.242 5.835 5.835 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.366 39.366 0 0 0-6.277-4.41 35.25 35.25 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.253 35.253 0 0 1 2.78 2.95 39.383 39.383 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082A54.289 54.289 0 0 1 102.86 61.89a55.052 55.052 0 0 1 7.63-1.173l.1-1.784a4.6 4.6 0 0 0 1.37-2.184 39.476 39.476 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.08-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.86.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.533 39.533 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.69 4.69 0 0 0 2.56-.278 39.532 39.532 0 0 0 5.69-5.148 35.004 35.004 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.095 35.095 0 0 1-3.507 2.046 39.495 39.495 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.745 4.745 0 0 0 1.82 1.83 39.464 39.464 0 0 0 7.568 1.246 35.64 35.64 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.03-.348-.075-.785-.151-1.09-.231a35.14 35.14 0 0 1-3.785-1.462 39.477 39.477 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.92 54.92 0 0 1-24.328 30.605z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M225.407 135.107L206.4 52.547a14.838 14.838 0 0 0-7.958-9.9l-76.935-36.73a14.825 14.825 0 0 0-12.771 0L31.808 42.669a14.838 14.838 0 0 0-7.961 9.895L4.873 135.129a14.668 14.668 0 0 0 1.995 11.185c.261.4.538.788.838 1.162l53.246 66.205a14.98 14.98 0 0 0 11.499 5.487l85.387-.02a14.986 14.986 0 0 0 11.5-5.48l53.227-66.211a14.72 14.72 0 0 0 2.842-12.347zm-9.197 3.866a13.677 13.677 0 0 1-.498 1.723 13.184 13.184 0 0 1-2.11 3.786l-47.299 58.838-1.486 1.852a13.305 13.305 0 0 1-3.24 2.865 13.945 13.945 0 0 1-1.474.822q-.513.237-1.045.43a13.873 13.873 0 0 1-1.383.445 13.473 13.473 0 0 1-1.989.379 12.988 12.988 0 0 1-1.41.082l-77.504.018h-.76a13.298 13.298 0 0 1-3.429-.472 13.925 13.925 0 0 1-3.855-1.679 13.424 13.424 0 0 1-2.576-2.132c-.233-.246-.468-.487-.68-.75l-48.805-60.679q-.408-.514-.765-1.066a13.102 13.102 0 0 1-1.343-2.726 13.505 13.505 0 0 1-.491-1.719 13.315 13.315 0 0 1 .004-5.809l17.394-75.675a13.598 13.598 0 0 1 7.295-9.07l70.508-33.685a13.589 13.589 0 0 1 11.705 0l70.519 33.67a13.602 13.602 0 0 1 7.293 9.073l17.422 75.674a13.282 13.282 0 0 1 .002 5.807z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M185.814 127.106c-.36-.083-.874-.225-1.227-.29a35.642 35.642 0 0 0-4.046-.326 39.464 39.464 0 0 1-7.57-1.242 4.745 4.745 0 0 1-1.82-1.832l-1.704-.496a54.837 54.837 0 0 0-8.79-37.983c.436-.396 1.262-1.127 1.495-1.342a3.338 3.338 0 0 1 .792-2.39 39.495 39.495 0 0 1 6.277-4.41 35.095 35.095 0 0 0 3.507-2.046c.272-.202.644-.522.929-.75a4.242 4.242 0 1 0-5.256-6.6c-.288.227-.68.525-.936.747a35.004 35.004 0 0 0-2.789 2.95 39.533 39.533 0 0 1-5.69 5.148 4.69 4.69 0 0 1-2.56.278l-1.606 1.147a55.309 55.309 0 0 0-35.032-16.927c-.039-.561-.087-1.577-.102-1.884a3.344 3.344 0 0 1-1.377-2.11 39.533 39.533 0 0 1 .478-7.656 35.112 35.112 0 0 0 .575-4.012c.013-.34-.007-.834-.007-1.201a4.242 4.242 0 1 0-8.441.004c0 .04.009.078.01.118-.004.35-.021.774-.01 1.08a35.476 35.476 0 0 0 .576 4.015 39.475 39.475 0 0 1 .47 7.654 4.601 4.601 0 0 1-1.37 2.182l-.1 1.786a55.052 55.052 0 0 0-7.63 1.173 54.289 54.289 0 0 0-27.574 15.754q-.77-.531-1.526-1.082a3.333 3.333 0 0 1-2.506-.243 39.383 39.383 0 0 1-5.69-5.141 35.255 35.255 0 0 0-2.777-2.95c-.257-.22-.65-.52-.938-.75a5.03 5.03 0 0 0-2.942-1.11 4.066 4.066 0 0 0-3.363 1.49 4.473 4.473 0 0 0 1.038 6.222l.065.046c.276.226.616.515.872.702a35.256 35.256 0 0 0 3.499 2.048 39.367 39.367 0 0 1 6.276 4.412 4.69 4.69 0 0 1 .849 2.434l1.351 1.208a54.613 54.613 0 0 0-8.611 38.073l-1.767.514a5.835 5.835 0 0 1-1.814 1.827 39.39 39.39 0 0 1-7.565 1.247 35.266 35.266 0 0 0-4.049.326c-.324.06-.774.174-1.127.262l-.037.008-.06.018a4.236 4.236 0 1 0 1.875 8.224l.063-.01c.028-.006.052-.02.08-.025.348-.08.786-.163 1.092-.246a35.59 35.59 0 0 0 3.786-1.451 39.527 39.527 0 0 1 7.358-2.165 4.68 4.68 0 0 1 2.426.857l1.84-.315a54.922 54.922 0 0 0 24.34 30.387l-.769 1.84a4.188 4.188 0 0 1 .377 2.387 41.228 41.228 0 0 1-3.82 6.864 35.183 35.183 0 0 0-2.263 3.372c-.168.318-.381.805-.542 1.138a4.238 4.238 0 1 0 7.621 3.616l.007-.008v-.01c.16-.33.387-.763.522-1.072a34.903 34.903 0 0 0 1.218-3.868 29.517 29.517 0 0 1 3.268-7.582 3.43 3.43 0 0 1 1.819-.888l.957-1.73a54.617 54.617 0 0 0 38.946.099c.268.478.768 1.392.9 1.623a3.335 3.335 0 0 1 2.155 1.31 39.306 39.306 0 0 1 2.898 7.096 35.275 35.275 0 0 0 1.225 3.868c.137.312.36.75.522 1.082a4.24 4.24 0 1 0 7.63-3.607c-.161-.333-.383-.82-.55-1.136a35.06 35.06 0 0 0-2.263-3.364 39.372 39.372 0 0 1-3.742-6.7 3.314 3.314 0 0 1 .324-2.49 14.519 14.519 0 0 1-.703-1.703 54.92 54.92 0 0 0 24.328-30.605c.546.087 1.497.253 1.806.316a3.337 3.337 0 0 1 2.36-.877 39.476 39.476 0 0 1 7.36 2.165 35.135 35.135 0 0 0 3.788 1.462c.305.08.74.156 1.09.233.029.008.055.02.083.028l.06.009a4.236 4.236 0 1 0 1.878-8.224zm-40.1-42.987l-18.037 12.787-.063-.03a3.723 3.723 0 0 1-5.913-2.838l-.02-.01-1.253-22.103a43.85 43.85 0 0 1 25.285 12.194zm-33.978 24.228h6.788l4.22 5.276-1.513 6.58-6.096 2.934-6.114-2.94-1.516-6.583zm-6.386-35.648a44.672 44.672 0 0 1 4.503-.774l-1.255 22.137-.092.044a3.72 3.72 0 0 1-5.904 2.852l-.035.02-18.154-12.872a43.467 43.467 0 0 1 20.937-11.407zm-27.52 19.68l16.574 14.824-.018.09a3.72 3.72 0 0 1-1.462 6.395l-.017.072-21.245 6.13a43.454 43.454 0 0 1 6.168-27.51zm22.191 39.38l-8.441 20.397a43.696 43.696 0 0 1-17.536-21.948l21.783-3.7.037.049a3.655 3.655 0 0 1 .73-.065 3.72 3.72 0 0 1 3.364 5.185zm24.916 26.23a43.637 43.637 0 0 1-23.815-1.223l10.713-19.372h.018a3.725 3.725 0 0 1 6.557-.006h.08l10.74 19.404q-2.091.698-4.293 1.199zm13.841-5.751l-8.528-20.605.026-.037a3.725 3.725 0 0 1 1.803-4.823 3.685 3.685 0 0 1 1.425-.37 3.59 3.59 0 0 1 .855.063l.037-.046 21.977 3.714a43.53 43.53 0 0 1-17.595 22.105zm19.903-32.42l-21.352-6.15-.02-.09a3.725 3.725 0 0 1-1.46-6.395l-.008-.043 16.482-14.751a44.279 44.279 0 0 1 6.357 27.43z\" class=\"cls-1\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"9.70 9.20 210.86 204.86\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.46551a3.59023 3.59023 0 0 0-.855-.065 3.68515 3.68515 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53012 43.53012 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.6556 3.6556 0 0 0-.733.065l-.037-.045-21.785 3.698a43.69506 43.69506 0 0 0 17.54 21.946l8.442-20.399-.066-.08a3.68318 3.68318 0 0 0 .207-2.291zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.199l-10.738-19.407zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.0385a3.725 3.725 0 0 0 5.913 2.84l.065.028 18.036-12.789a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.922a3.72 3.72 0 0 0 5.904-2.85l.092-.044 1.253-22.139a44.68209 44.68209 0 0 0-4.501.775 43.4669 43.4669 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.728a3.72 3.72 0 0 0 1.462-6.396l.018-.087-16.574-14.825a43.454 43.454 0 0 0-6.168 27.511l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.933 1.514-6.582-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.16651l-17.422-75.675a13.60207 13.60207 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0l-70.507 33.688a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.50607 13.50607 0 0 0 .491 1.718 13.0998 13.0998 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.42334 13.42334 0 0 0 2.574 2.133 13.9237 13.9237 0 0 0 3.857 1.677 13.29785 13.29785 0 0 0 3.43.473h.759l77.504-.018a12.99345 12.99345 0 0 0 1.41-.083 13.46921 13.46921 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.94127 13.94127 0 0 0 1.479-.822 13.30347 13.30347 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67036 13.67036 0 0 0 .5-1.724 13.28215 13.28215 0 0 0-.004-5.809zm-73.147 29.432a14.51575 14.51575 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.37244 39.37244 0 0 0 3.742 6.7 35.06044 35.06044 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.27528 35.27528 0 0 1-1.225-3.868 39.3046 39.3046 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.149-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.731a3.429 3.429 0 0 0-1.819.886 29.51728 29.51728 0 0 0-3.268 7.582 34.89931 34.89931 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.17706 35.17706 0 0 1 2.262-3.373 41.22786 41.22786 0 0 0 3.82-6.866 4.18792 4.18792 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68007 4.68007 0 0 0-2.428-.855 39.52352 39.52352 0 0 0-7.356 2.165 35.58886 35.58886 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.60518.60518 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.38835 39.38835 0 0 0 7.564-1.242 5.83506 5.83506 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.36558 39.36558 0 0 0-6.277-4.41 35.2503 35.2503 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.25285 35.25285 0 0 1 2.78 2.95 39.38314 39.38314 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082a54.28892 54.28892 0 0 1 27.577-15.754 55.05181 55.05181 0 0 1 7.63-1.173l.1-1.784a4.6001 4.6001 0 0 0 1.37-2.184 39.47551 39.47551 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.079-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.861.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.53346 39.53346 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.6901 4.6901 0 0 0 2.56-.278 39.53152 39.53152 0 0 0 5.69-5.148 35.00382 35.00382 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.09523 35.09523 0 0 1-3.507 2.046 39.49476 39.49476 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.7449 4.7449 0 0 0 1.82 1.831 39.46448 39.46448 0 0 0 7.568 1.245 35.64041 35.64041 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.029-.348-.076-.785-.152-1.09-.232a35.1407 35.1407 0 0 1-3.785-1.462 39.47672 39.47672 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.91994 54.91994 0 0 1-24.328 30.605z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "svgColor": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M28.783 15.8108H7.16387C5.96974 15.8108 5.00171 16.7788 5.00171 17.973V39.5945C10.9716 39.5954 15.8108 44.4352 15.8108 50.4053C15.8108 56.3754 10.9716 61.2152 5.00171 61.2161V82.8378C5.00171 84.032 5.96974 85 7.16387 85H28.783V84.9996C28.783 79.0289 33.6231 74.1887 39.5938 74.1887C45.5644 74.1887 50.4046 79.0289 50.4046 84.9996V85H72.0287C73.2229 85 74.1909 84.032 74.1909 82.8378V61.2163C80.16 61.2145 84.9983 56.3751 84.9983 50.4055C84.9983 44.436 80.16 39.5965 74.1909 39.5947V17.973C74.1909 16.7788 73.2229 15.8108 72.0287 15.8108H50.4046C50.4046 9.84017 45.5644 5 39.5938 5C33.6231 5 28.783 9.84017 28.783 15.8108Z\" fill=\"#326CE5\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M28.783 15.8108H7.16387C5.96974 15.8108 5.00171 16.7788 5.00171 17.973V39.5945C10.9716 39.5954 15.8108 44.4352 15.8108 50.4053C15.8108 56.3754 10.9716 61.2152 5.00171 61.2161V82.8378C5.00171 84.032 5.96974 85 7.16387 85H28.783V84.9996C28.783 79.0289 33.6231 74.1887 39.5938 74.1887C45.5644 74.1887 50.4046 79.0289 50.4046 84.9996V85H72.0287C73.2229 85 74.1909 84.032 74.1909 82.8378V61.2163C80.16 61.2145 84.9983 56.3751 84.9983 50.4055C84.9983 44.436 80.16 39.5965 74.1909 39.5947V17.973C74.1909 16.7788 73.2229 15.8108 72.0287 15.8108H50.4046C50.4046 9.84017 45.5644 5 39.5938 5C33.6231 5 28.783 9.84017 28.783 15.8108Z\" fill=\"white\"/>\n</svg>\n"}, "capabilities": [{"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}, {"description": "Add text to nodes body", "displayName": "Body Text", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "body-text", "type": "style", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": false, "published": false, "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3"}, "configuration": null, "component": {"version": "apiextensions.k8s.io/v1", "kind": "CustomResourceDefinition", "schema": "{\"description\":\"CustomResourceDefinition represents a resource that should be exposed on the API server.  Its name MUST be in the format \\u003c.spec.name\\u003e.\\u003c.spec.group\\u003e.\",\"properties\":{\"spec\":{\"allOf\":[{\"description\":\"CustomResourceDefinitionSpec describes how a user wants their resource to appear\",\"properties\":{\"conversion\":{\"allOf\":[{\"description\":\"CustomResourceConversion describes how to convert different versions of a CR.\",\"properties\":{\"strategy\":{\"default\":\"\",\"description\":\"strategy specifies how custom resources are converted between versions. Allowed values are: - `\\\"None\\\"`: The converter only change the apiVersion and would not touch any other field in the custom resource. - `\\\"Webhook\\\"`: API Server will call to an external webhook to do the conversion. Additional information\\n  is needed for this option. This requires spec.preserveUnknownFields to be false, and spec.conversion.webhook to be set.\",\"type\":\"string\"},\"webhook\":{\"allOf\":[{\"description\":\"WebhookConversion describes how to call a conversion webhook\",\"properties\":{\"clientConfig\":{\"allOf\":[{\"description\":\"WebhookClientConfig contains the information to make a TLS connection with the webhook.\",\"properties\":{\"caBundle\":{\"description\":\"caBundle is a PEM encoded CA bundle which will be used to validate the webhook's server certificate. If unspecified, system trust roots on the apiserver are used.\",\"format\":\"byte\",\"type\":\"string\"},\"service\":{\"allOf\":[{\"description\":\"ServiceReference holds a reference to Service.legacy.k8s.io\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"name is the name of the service. Required\",\"type\":\"string\"},\"namespace\":{\"default\":\"\",\"description\":\"namespace is the namespace of the service. Required\",\"type\":\"string\"},\"path\":{\"description\":\"path is an optional URL path at which the webhook will be contacted.\",\"type\":\"string\"},\"port\":{\"description\":\"port is an optional service port at which the webhook will be contacted. `port` should be a valid port number (1-65535, inclusive). Defaults to 443 for backward compatibility.\",\"format\":\"int32\",\"type\":\"integer\"}},\"required\":[\"namespace\",\"name\"],\"type\":\"object\"}],\"description\":\"service is a reference to the service for this webhook. Either service or url must be specified.\\n\\nIf the webhook is running within the cluster, then you should use `service`.\"},\"url\":{\"description\":\"url gives the location of the webhook, in standard URL form (`scheme://host:port/path`). Exactly one of `url` or `service` must be specified.\\n\\nThe `host` should not refer to a service running in the cluster; use the `service` field instead. The host might be resolved via external DNS in some apiservers (e.g., `kube-apiserver` cannot resolve in-cluster DNS as that would be a layering violation). `host` may also be an IP address.\\n\\nPlease note that using `localhost` or `127.0.0.1` as a `host` is risky unless you take great care to run this webhook on all hosts which run an apiserver which might need to make calls to this webhook. Such installs are likely to be non-portable, i.e., not easy to turn up in a new cluster.\\n\\nThe scheme must be \\\"https\\\"; the URL must begin with \\\"https://\\\".\\n\\nA path is optional, and if present may be any string permissible in a URL. You may use the path to pass an arbitrary string to the webhook, for example, a cluster identifier.\\n\\nAttempting to use a user or basic auth e.g. \\\"user:password@\\\" is not allowed. Fragments (\\\"#...\\\") and query parameters (\\\"?...\\\") are not allowed, either.\",\"type\":\"string\"}},\"type\":\"object\"}],\"description\":\"clientConfig is the instructions for how to call the webhook if strategy is `Webhook`.\"},\"conversionReviewVersions\":{\"description\":\"conversionReviewVersions is an ordered list of preferred `ConversionReview` versions the Webhook expects. The API server will use the first version in the list which it supports. If none of the versions specified in this list are supported by API server, conversion will fail for the custom resource. If a persisted Webhook configuration specifies allowed versions and does not include any versions known to the API Server, calls to the webhook will fail.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"conversionReviewVersions\"],\"type\":\"object\"}],\"description\":\"webhook describes how to call the conversion webhook. Required when `strategy` is set to `\\\"Webhook\\\"`.\"}},\"required\":[\"strategy\"],\"type\":\"object\"}],\"description\":\"conversion defines conversion settings for the CRD.\"},\"group\":{\"default\":\"\",\"description\":\"group is the API group of the defined custom resource. The custom resources are served under `/apis/\\u003cgroup\\u003e/...`. Must match the name of the CustomResourceDefinition (in the form `\\u003cnames.plural\\u003e.\\u003cgroup\\u003e`).\",\"type\":\"string\"},\"names\":{\"allOf\":[{\"description\":\"CustomResourceDefinitionNames indicates the names to serve this CustomResourceDefinition\",\"properties\":{\"categories\":{\"description\":\"categories is a list of grouped resources this custom resource belongs to (e.g. 'all'). This is published in API discovery documents, and used by clients to support invocations like `kubectl get all`.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"kind\":{\"default\":\"\",\"description\":\"kind is the serialized kind of the resource. It is normally CamelCase and singular. Custom resource instances will use this value as the `kind` attribute in API calls.\",\"type\":\"string\"},\"listKind\":{\"description\":\"listKind is the serialized kind of the list for this resource. Defaults to \\\"`kind`List\\\".\",\"type\":\"string\"},\"plural\":{\"default\":\"\",\"description\":\"plural is the plural name of the resource to serve. The custom resources are served under `/apis/\\u003cgroup\\u003e/\\u003cversion\\u003e/.../\\u003cplural\\u003e`. Must match the name of the CustomResourceDefinition (in the form `\\u003cnames.plural\\u003e.\\u003cgroup\\u003e`). Must be all lowercase.\",\"type\":\"string\"},\"shortNames\":{\"description\":\"shortNames are short names for the resource, exposed in API discovery documents, and used by clients to support invocations like `kubectl get \\u003cshortname\\u003e`. It must be all lowercase.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"singular\":{\"description\":\"singular is the singular name of the resource. It must be all lowercase. Defaults to lowercased `kind`.\",\"type\":\"string\"}},\"required\":[\"plural\",\"kind\"],\"type\":\"object\"}],\"default\":{},\"description\":\"names specify the resource and kind names for the custom resource.\"},\"preserveUnknownFields\":{\"description\":\"preserveUnknownFields indicates that object fields which are not specified in the OpenAPI schema should be preserved when persisting to storage. apiVersion, kind, metadata and known fields inside metadata are always preserved. This field is deprecated in favor of setting `x-preserve-unknown-fields` to true in `spec.versions[*].schema.openAPIV3Schema`. See https://kubernetes.io/docs/tasks/extend-kubernetes/custom-resources/custom-resource-definitions/#field-pruning for details.\",\"type\":\"boolean\"},\"scope\":{\"default\":\"\",\"description\":\"scope indicates whether the defined custom resource is cluster- or namespace-scoped. Allowed values are `Cluster` and `Namespaced`.\",\"type\":\"string\"},\"versions\":{\"description\":\"versions is the list of all API versions of the defined custom resource. Version names are used to compute the order in which served versions are listed in API discovery. If the version string is \\\"kube-like\\\", it will sort above non \\\"kube-like\\\" version strings, which are ordered lexicographically. \\\"Kube-like\\\" versions start with a \\\"v\\\", then are followed by a number (the major version), then optionally the string \\\"alpha\\\" or \\\"beta\\\" and another number (the minor version). These are sorted first by GA \\u003e beta \\u003e alpha (where GA is a version with no suffix such as beta or alpha), and then by comparing major version, then minor version. An example sorted list of versions: v10, v2, v1, v11beta2, v10beta3, v3beta1, v12alpha1, v11alpha2, foo1, foo10.\",\"items\":{\"allOf\":[{\"description\":\"CustomResourceDefinitionVersion describes a version for CRD.\",\"properties\":{\"additionalPrinterColumns\":{\"description\":\"additionalPrinterColumns specifies additional columns returned in Table output. See https://kubernetes.io/docs/reference/using-api/api-concepts/#receiving-resources-as-tables for details. If no columns are specified, a single column displaying the age of the custom resource is used.\",\"items\":{\"allOf\":[{\"description\":\"CustomResourceColumnDefinition specifies a column for server side printing.\",\"properties\":{\"description\":{\"description\":\"description is a human readable description of this column.\",\"type\":\"string\"},\"format\":{\"description\":\"format is an optional OpenAPI type definition for this column. The 'name' format is applied to the primary identifier column to assist in clients identifying column is the resource name. See https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#data-types for details.\",\"type\":\"string\"},\"jsonPath\":{\"default\":\"\",\"description\":\"jsonPath is a simple JSON path (i.e. with array notation) which is evaluated against each custom resource to produce the value for this column.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"name is a human readable name for the column.\",\"type\":\"string\"},\"priority\":{\"description\":\"priority is an integer defining the relative importance of this column compared to others. Lower numbers are considered higher priority. Columns that may be omitted in limited space scenarios should be given a priority greater than 0.\",\"format\":\"int32\",\"type\":\"integer\"},\"type\":{\"default\":\"\",\"description\":\"type is an OpenAPI type definition for this column. See https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#data-types for details.\",\"type\":\"string\"}},\"required\":[\"name\",\"type\",\"jsonPath\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"deprecated\":{\"description\":\"deprecated indicates this version of the custom resource API is deprecated. When set to true, API requests to this version receive a warning header in the server response. Defaults to false.\",\"type\":\"boolean\"},\"deprecationWarning\":{\"description\":\"deprecationWarning overrides the default warning returned to API clients. May only be set when `deprecated` is true. The default warning indicates this version is deprecated and recommends use of the newest served version of equal or greater stability, if one exists.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"name is the version name, e.g. “v1”, “v2beta1”, etc. The custom resources are served under this version at `/apis/\\u003cgroup\\u003e/\\u003cversion\\u003e/...` if `served` is true.\",\"type\":\"string\"},\"schema\":{\"allOf\":[{\"description\":\"CustomResourceValidation is a list of validation methods for CustomResources.\",\"properties\":{\"openAPIV3Schema\":{\"allOf\":[{\"description\":\"JSONSchemaProps is a JSON-Schema following Specification Draft 4 (http://json-schema.org/).\",\"properties\":{\"$ref\":{\"type\":\"string\"},\"$schema\":{\"type\":\"string\"},\"additionalItems\":{\"description\":\"JSONSchemaPropsOrBool represents JSONSchemaProps or a boolean value. Defaults to true for the boolean property.\"},\"additionalProperties\":{\"description\":\"JSONSchemaPropsOrBool represents JSONSchemaProps or a boolean value. Defaults to true for the boolean property.\"},\"allOf\":{\"items\":{\"allOf\":[{\"$ref\":\"string\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"anyOf\":{\"items\":{\"allOf\":[{\"$ref\":\"string\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"default\":{\"allOf\":[{\"description\":\"JSON represents any valid JSON value. These types are supported: bool, int64, float64, string, []interface{}, map[string]interface{} and nil.\"}],\"description\":\"default is a default value for undefined object fields. Defaulting is a beta feature under the CustomResourceDefaulting feature gate. Defaulting requires spec.preserveUnknownFields to be false.\"},\"definitions\":{\"additionalProperties\":{\"allOf\":[{\"$ref\":\"string\"}],\"default\":{}},\"type\":\"object\"},\"dependencies\":{\"additionalProperties\":{\"description\":\"JSONSchemaPropsOrStringArray represents a JSONSchemaProps or a string array.\"},\"type\":\"object\"},\"description\":{\"type\":\"string\"},\"enum\":{\"items\":{\"description\":\"JSON represents any valid JSON value. These types are supported: bool, int64, float64, string, []interface{}, map[string]interface{} and nil.\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"example\":{\"description\":\"JSON represents any valid JSON value. These types are supported: bool, int64, float64, string, []interface{}, map[string]interface{} and nil.\"},\"exclusiveMaximum\":{\"type\":\"boolean\"},\"exclusiveMinimum\":{\"type\":\"boolean\"},\"externalDocs\":{\"description\":\"ExternalDocumentation allows referencing an external resource for extended documentation.\",\"properties\":{\"description\":{\"type\":\"string\"},\"url\":{\"type\":\"string\"}},\"type\":\"object\"},\"format\":\"textarea\",\"id\":{\"type\":\"string\"},\"items\":{\"description\":\"JSONSchemaPropsOrArray represents a value that can either be a JSONSchemaProps or an array of JSONSchemaProps. Mainly here for serialization purposes.\"},\"maxItems\":{\"format\":\"int64\",\"type\":\"integer\"},\"maxLength\":{\"format\":\"int64\",\"type\":\"integer\"},\"maxProperties\":{\"format\":\"int64\",\"type\":\"integer\"},\"maximum\":{\"format\":\"double\",\"type\":\"number\"},\"minItems\":{\"format\":\"int64\",\"type\":\"integer\"},\"minLength\":{\"format\":\"int64\",\"type\":\"integer\"},\"minProperties\":{\"format\":\"int64\",\"type\":\"integer\"},\"minimum\":{\"format\":\"double\",\"type\":\"number\"},\"multipleOf\":{\"format\":\"double\",\"type\":\"number\"},\"not\":{\"$ref\":\"string\"},\"nullable\":{\"type\":\"boolean\"},\"oneOf\":{\"items\":{\"allOf\":[{\"$ref\":\"string\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"pattern\":{\"type\":\"string\"},\"patternProperties\":{\"additionalProperties\":{\"allOf\":[{\"$ref\":\"string\"}],\"default\":{}},\"type\":\"object\"},\"properties\":{\"additionalProperties\":{\"allOf\":[{\"$ref\":\"string\"}],\"default\":{}},\"type\":\"object\"},\"required\":{\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"title\":{\"type\":\"string\"},\"type\":\"string\",\"uniqueItems\":{\"type\":\"boolean\"},\"x-kubernetes-embedded-resource\":{\"description\":\"x-kubernetes-embedded-resource defines that the value is an embedded Kubernetes runtime.Object, with TypeMeta and ObjectMeta. The type must be object. It is allowed to further restrict the embedded object. kind, apiVersion and metadata are validated automatically. x-kubernetes-preserve-unknown-fields is allowed to be true, but does not have to be if the object is fully specified (up to kind, apiVersion, metadata).\",\"type\":\"boolean\"},\"x-kubernetes-int-or-string\":{\"description\":\"x-kubernetes-int-or-string specifies that this value is either an integer or a string. If this is true, an empty type is allowed and type as child of anyOf is permitted if following one of the following patterns:\\n\\n1) anyOf:\\n   - type: integer\\n   - type: string\\n2) allOf:\\n   - anyOf:\\n     - type: integer\\n     - type: string\\n   - ... zero or more\",\"type\":\"boolean\"},\"x-kubernetes-list-map-keys\":{\"description\":\"x-kubernetes-list-map-keys annotates an array with the x-kubernetes-list-type `map` by specifying the keys used as the index of the map.\\n\\nThis tag MUST only be used on lists that have the \\\"x-kubernetes-list-type\\\" extension set to \\\"map\\\". Also, the values specified for this attribute must be a scalar typed field of the child structure (no nesting is supported).\\n\\nThe properties specified must either be required or have a default value, to ensure those properties are present for all list items.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"x-kubernetes-list-type\":{\"description\":\"x-kubernetes-list-type annotates an array to further describe its topology. This extension must only be used on lists and may have 3 possible values:\\n\\n1) `atomic`: the list is treated as a single entity, like a scalar.\\n     Atomic lists will be entirely replaced when updated. This extension\\n     may be used on any type of list (struct, scalar, ...).\\n2) `set`:\\n     Sets are lists that must not have multiple items with the same value. Each\\n     value must be a scalar, an object with x-kubernetes-map-type `atomic` or an\\n     array with x-kubernetes-list-type `atomic`.\\n3) `map`:\\n     These lists are like maps in that their elements have a non-index key\\n     used to identify them. Order is preserved upon merge. The map tag\\n     must only be used on a list with elements of type object.\\nDefaults to atomic for arrays.\",\"type\":\"string\"},\"x-kubernetes-map-type\":{\"description\":\"x-kubernetes-map-type annotates an object to further describe its topology. This extension must only be used when type is object and may have 2 possible values:\\n\\n1) `granular`:\\n     These maps are actual maps (key-value pairs) and each fields are independent\\n     from each other (they can each be manipulated by separate actors). This is\\n     the default behaviour for all maps.\\n2) `atomic`: the list is treated as a single entity, like a scalar.\\n     Atomic maps will be entirely replaced when updated.\",\"type\":\"string\"},\"x-kubernetes-validations\":{\"description\":\"x-kubernetes-validations describes a list of validation rules written in the CEL expression language.\",\"items\":{\"allOf\":[{\"description\":\"ValidationRule describes a validation rule written in the CEL expression language.\",\"properties\":{\"fieldPath\":{\"description\":\"fieldPath represents the field path returned when the validation fails. It must be a relative JSON path (i.e. with array notation) scoped to the location of this x-kubernetes-validations extension in the schema and refer to an existing field. e.g. when validation checks if a specific attribute `foo` under a map `testMap`, the fieldPath could be set to `.testMap.foo` If the validation checks two lists must have unique attributes, the fieldPath could be set to either of the list: e.g. `.testList` It does not support list numeric index. It supports child operation to refer to an existing field currently. Refer to [JSONPath support in Kubernetes](https://kubernetes.io/docs/reference/kubectl/jsonpath/) for more info. Numeric index of array is not supported. For field name which contains special characters, use `['specialName']` to refer the field name. e.g. for attribute `foo.34$` appears in a list `testList`, the fieldPath could be set to `.testList['foo.34$']`\",\"type\":\"string\"},\"message\":{\"description\":\"Message represents the message displayed when validation fails. The message is required if the Rule contains line breaks. The message must not contain line breaks. If unset, the message is \\\"failed rule: {Rule}\\\". e.g. \\\"must be a URL with the host matching spec.host\\\"\",\"type\":\"string\"},\"messageExpression\":{\"description\":\"MessageExpression declares a CEL expression that evaluates to the validation failure message that is returned when this rule fails. Since messageExpression is used as a failure message, it must evaluate to a string. If both message and messageExpression are present on a rule, then messageExpression will be used if validation fails. If messageExpression results in a runtime error, the runtime error is logged, and the validation failure message is produced as if the messageExpression field were unset. If messageExpression evaluates to an empty string, a string with only spaces, or a string that contains line breaks, then the validation failure message will also be produced as if the messageExpression field were unset, and the fact that messageExpression produced an empty string/string with only spaces/string with line breaks will be logged. messageExpression has access to all the same variables as the rule; the only difference is the return type. Example: \\\"x must be less than max (\\\"+string(self.max)+\\\")\\\"\",\"type\":\"string\"},\"optionalOldSelf\":{\"description\":\"optionalOldSelf is used to opt a transition rule into evaluation even when the object is first created, or if the old object is missing the value.\\n\\nWhen enabled `oldSelf` will be a CEL optional whose value will be `None` if there is no old value, or when the object is initially created.\\n\\nYou may check for presence of oldSelf using `oldSelf.hasValue()` and unwrap it after checking using `oldSelf.value()`. Check the CEL documentation for Optional types for more information: https://pkg.go.dev/github.com/google/cel-go/cel#OptionalTypes\\n\\nMay not be set unless `oldSelf` is used in `rule`.\",\"type\":\"boolean\"},\"reason\":{\"description\":\"reason provides a machine-readable validation failure reason that is returned to the caller when a request fails this validation rule. The HTTP status code returned to the caller will match the reason of the reason of the first failed validation rule. The currently supported reasons are: \\\"FieldValueInvalid\\\", \\\"FieldValueForbidden\\\", \\\"FieldValueRequired\\\", \\\"FieldValueDuplicate\\\". If not set, default to use \\\"FieldValueInvalid\\\". All future added reasons must be accepted by clients when reading this value and unknown reasons should be treated as FieldValueInvalid.\",\"type\":\"string\"},\"rule\":{\"default\":\"\",\"description\":\"Rule represents the expression which will be evaluated by CEL. ref: https://github.com/google/cel-spec The Rule is scoped to the location of the x-kubernetes-validations extension in the schema. The `self` variable in the CEL expression is bound to the scoped value. Example: - Rule scoped to the root of a resource with a status subresource: {\\\"rule\\\": \\\"self.status.actual \\u003c= self.spec.maxDesired\\\"}\\n\\nIf the Rule is scoped to an object with properties, the accessible properties of the object are field selectable via `self.field` and field presence can be checked via `has(self.field)`. Null valued fields are treated as absent fields in CEL expressions. If the Rule is scoped to an object with additionalProperties (i.e. a map) the value of the map are accessible via `self[mapKey]`, map containment can be checked via `mapKey in self` and all entries of the map are accessible via CEL macros and functions such as `self.all(...)`. If the Rule is scoped to an array, the elements of the array are accessible via `self[i]` and also by macros and functions. If the Rule is scoped to a scalar, `self` is bound to the scalar value. Examples: - Rule scoped to a map of objects: {\\\"rule\\\": \\\"self.components['Widget'].priority \\u003c 10\\\"} - Rule scoped to a list of integers: {\\\"rule\\\": \\\"self.values.all(value, value \\u003e= 0 \\u0026\\u0026 value \\u003c 100)\\\"} - Rule scoped to a string value: {\\\"rule\\\": \\\"self.startsWith('kube')\\\"}\\n\\nThe `apiVersion`, `kind`, `metadata.name` and `metadata.generateName` are always accessible from the root of the object and from any x-kubernetes-embedded-resource annotated objects. No other metadata properties are accessible.\\n\\nUnknown data preserved in custom resources via x-kubernetes-preserve-unknown-fields is not accessible in CEL expressions. This includes: - Unknown field values that are preserved by object schemas with x-kubernetes-preserve-unknown-fields. - Object properties where the property schema is of an \\\"unknown type\\\". An \\\"unknown type\\\" is recursively defined as:\\n  - A schema with no type and x-kubernetes-preserve-unknown-fields set to true\\n  - An array where the items schema is of an \\\"unknown type\\\"\\n  - An object where the additionalProperties schema is of an \\\"unknown type\\\"\\n\\nOnly property names of the form `[a-zA-Z_.-/][a-zA-Z0-9_.-/]*` are accessible. Accessible property names are escaped according to the following rules when accessed in the expression: - '__' escapes to '__underscores__' - '.' escapes to '__dot__' - '-' escapes to '__dash__' - '/' escapes to '__slash__' - Property names that exactly match a CEL RESERVED keyword escape to '__{keyword}__'. The keywords are:\\n\\t  \\\"true\\\", \\\"false\\\", \\\"null\\\", \\\"in\\\", \\\"as\\\", \\\"break\\\", \\\"const\\\", \\\"continue\\\", \\\"else\\\", \\\"for\\\", \\\"function\\\", \\\"if\\\",\\n\\t  \\\"import\\\", \\\"let\\\", \\\"loop\\\", \\\"package\\\", \\\"namespace\\\", \\\"return\\\".\\nExamples:\\n  - Rule accessing a property named \\\"namespace\\\": {\\\"rule\\\": \\\"self.__namespace__ \\u003e 0\\\"}\\n  - Rule accessing a property named \\\"x-prop\\\": {\\\"rule\\\": \\\"self.x__dash__prop \\u003e 0\\\"}\\n  - Rule accessing a property named \\\"redact__d\\\": {\\\"rule\\\": \\\"self.redact__underscores__d \\u003e 0\\\"}\\n\\nEquality on arrays with x-kubernetes-list-type of 'set' or 'map' ignores element order, i.e. [1, 2] == [2, 1]. Concatenation on arrays with x-kubernetes-list-type use the semantics of the list type:\\n  - 'set': `X + Y` performs a union where the array positions of all elements in `X` are preserved and\\n    non-intersecting elements in `Y` are appended, retaining their partial order.\\n  - 'map': `X + Y` performs a merge where the array positions of all keys in `X` are preserved but the values\\n    are overwritten by values in `Y` when the key sets of `X` and `Y` intersect. Elements in `Y` with\\n    non-intersecting keys are appended, retaining their partial order.\\n\\nIf `rule` makes use of the `oldSelf` variable it is implicitly a `transition rule`.\\n\\nBy default, the `oldSelf` variable is the same type as `self`. When `optionalOldSelf` is true, the `oldSelf` variable is a CEL optional\\n variable whose value() is the same type as `self`.\\nSee the documentation for the `optionalOldSelf` field for details.\\n\\nTransition rules by default are applied only on UPDATE requests and are skipped if an old value could not be found. You can opt a transition rule into unconditional evaluation by setting `optionalOldSelf` to true.\",\"type\":\"string\"}},\"required\":[\"rule\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"rule\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"rule\",\"x-kubernetes-patch-strategy\":\"merge\"}},\"type\":\"object\"}],\"description\":\"openAPIV3Schema is the OpenAPI v3 schema to use for validation and pruning.\"}},\"type\":\"object\"}],\"description\":\"schema describes the schema used for validation, pruning, and defaulting of this version of the custom resource.\"},\"selectableFields\":{\"description\":\"selectableFields specifies paths to fields that may be used as field selectors. A maximum of 8 selectable fields are allowed. See https://kubernetes.io/docs/concepts/overview/working-with-objects/field-selectors\",\"items\":{\"allOf\":[{\"description\":\"SelectableField specifies the JSON path of a field that may be used with field selectors.\",\"properties\":{\"jsonPath\":{\"default\":\"\",\"description\":\"jsonPath is a simple JSON path which is evaluated against each custom resource to produce a field selector value. Only JSON paths without the array notation are allowed. Must point to a field of type string, boolean or integer. Types with enum values and strings with formats are allowed. If jsonPath refers to absent field in a resource, the jsonPath evaluates to an empty string. Must not point to metdata fields. Required.\",\"type\":\"string\"}},\"required\":[\"jsonPath\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"served\":{\"default\":false,\"description\":\"served is a flag enabling/disabling this version from being served via REST APIs\",\"type\":\"boolean\"},\"storage\":{\"default\":false,\"description\":\"storage indicates this version should be used when persisting custom resources to storage. There must be exactly one version with storage=true.\",\"type\":\"boolean\"},\"subresources\":{\"allOf\":[{\"description\":\"CustomResourceSubresources defines the status and scale subresources for CustomResources.\",\"properties\":{\"scale\":{\"allOf\":[{\"description\":\"CustomResourceSubresourceScale defines how to serve the scale subresource for CustomResources.\",\"properties\":{\"labelSelectorPath\":{\"description\":\"labelSelectorPath defines the JSON path inside of a custom resource that corresponds to Scale `status.selector`. Only JSON paths without the array notation are allowed. Must be a JSON Path under `.status` or `.spec`. Must be set to work with HorizontalPodAutoscaler. The field pointed by this JSON path must be a string field (not a complex selector struct) which contains a serialized label selector in string form. More info: https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions#scale-subresource If there is no value under the given path in the custom resource, the `status.selector` value in the `/scale` subresource will default to the empty string.\",\"type\":\"string\"},\"specReplicasPath\":{\"default\":\"\",\"description\":\"specReplicasPath defines the JSON path inside of a custom resource that corresponds to Scale `spec.replicas`. Only JSON paths without the array notation are allowed. Must be a JSON Path under `.spec`. If there is no value under the given path in the custom resource, the `/scale` subresource will return an error on GET.\",\"type\":\"string\"},\"statusReplicasPath\":{\"default\":\"\",\"description\":\"statusReplicasPath defines the JSON path inside of a custom resource that corresponds to Scale `status.replicas`. Only JSON paths without the array notation are allowed. Must be a JSON Path under `.status`. If there is no value under the given path in the custom resource, the `status.replicas` value in the `/scale` subresource will default to 0.\",\"type\":\"string\"}},\"required\":[\"specReplicasPath\",\"statusReplicasPath\"],\"type\":\"object\"}],\"description\":\"scale indicates the custom resource should serve a `/scale` subresource that returns an `autoscaling/v1` Scale object.\"},\"status\":{\"allOf\":[{\"description\":\"CustomResourceSubresourceStatus defines how to serve the status subresource for CustomResources. Status is represented by the `.status` JSON path inside of a CustomResource. When set, * exposes a /status subresource for the custom resource * PUT requests to the /status subresource take a custom resource object, and ignore changes to anything except the status stanza * PUT/POST/PATCH requests to the custom resource ignore changes to the status stanza\",\"type\":\"object\"}],\"description\":\"status indicates the custom resource should serve a `/status` subresource. When enabled: 1. requests to the custom resource primary endpoint ignore changes to the `status` stanza of the object. 2. requests to the custom resource `/status` subresource ignore changes to anything other than the `status` stanza of the object.\"}},\"type\":\"object\"}],\"description\":\"subresources specify what subresources this version of the defined custom resource have.\"}},\"required\":[\"name\",\"served\",\"storage\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"group\",\"names\",\"scope\",\"versions\"],\"type\":\"object\"}],\"default\":{},\"description\":\"spec describes how the user wants the resources to appear\"}},\"required\":[\"spec\"],\"type\":\"object\",\"x-kubernetes-group-version-kind\":[{\"group\":\"apiextensions.k8s.io\",\"kind\":\"CustomResourceDefinition\",\"version\":\"v1\"}]}"}}