{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Pod Monitor", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kube-prometheus-stack", "displayName": "Kube Prometheus Stack", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Observability and Analysis"}, "subCategory": "Monitoring", "metadata": {"isAnnotation": false, "primaryColor": "#e75225", "secondaryColor": "#ec7551", "shape": "circle", "source_uri": "https://github.com/prometheus-community/helm-charts/releases/download/kube-prometheus-stack-75.8.0/kube-prometheus-stack-75.8.0.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-3.94 -1.44 438.62 432.87\" height=\"20\" width=\"20\"><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"#E75225\" d=\"M215.926 7.068c115.684.024 210.638 93.784 210.493 207.844-.148 115.793-94.713 208.252-212.912 208.169C97.95 423 4.52 329.143 4.601 213.221 4.68 99.867 99.833 7.044 215.926 7.068zm-63.947 73.001c2.652 12.978.076 25.082-3.846 36.988-2.716 8.244-6.47 16.183-8.711 24.539-3.694 13.769-7.885 27.619-9.422 41.701-2.21 20.25 5.795 38.086 19.493 55.822L86.527 225.94c.11 1.978-.007 2.727.21 3.361 5.968 17.43 16.471 32.115 28.243 45.957 1.246 1.465 4.082 2.217 6.182 2.221 62.782.115 125.565.109 188.347.028 1.948-.003 4.546-.369 5.741-1.618 13.456-14.063 23.746-30.079 30.179-50.257l-66.658 12.976c4.397-8.567 9.417-16.1 12.302-24.377 9.869-28.315 5.779-55.69-8.387-81.509-11.368-20.72-21.854-41.349-16.183-66.32-12.005 11.786-16.615 26.79-19.541 42.253-2.882 15.23-4.58 30.684-6.811 46.136-.317-.467-.728-.811-.792-1.212-.258-1.621-.499-3.255-.587-4.893-1.355-25.31-6.328-49.696-16.823-72.987-6.178-13.71-12.99-27.727-6.622-44.081-4.31 2.259-8.205 4.505-10.997 7.711-8.333 9.569-11.779 21.062-12.666 33.645-.757 10.75-1.796 21.552-3.801 32.123-2.107 11.109-5.448 21.998-12.956 32.209-3.033-21.81-3.37-43.38-22.928-57.237zm161.877 216.523H116.942v34.007h196.914v-34.007zm-157.871 51.575c-.163 28.317 28.851 49.414 64.709 47.883 29.716-1.269 56.016-24.51 53.755-47.883H155.985z\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.61 2.89 434.72 428.97\" height=\"20\" width=\"20\"><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"#FFF\" d=\"M216.412 11.432c114.637.024 208.732 92.935 208.588 205.963-.146 114.745-93.856 206.367-210.985 206.285C99.504 423.599 6.92 330.592 7 215.719c.079-112.328 94.369-204.311 209.412-204.287zm-63.368 72.341c2.628 12.861.075 24.855-3.811 36.653-2.691 8.17-6.411 16.036-8.632 24.317-3.66 13.644-7.813 27.369-9.336 41.324-2.19 20.067 5.743 37.741 19.317 55.316l-62.396-13.06c.109 1.96-.007 2.702.208 3.331 5.914 17.272 16.322 31.824 27.988 45.541 1.234 1.451 4.045 2.197 6.126 2.201 62.214.114 124.428.108 186.642.028 1.93-.002 4.505-.365 5.689-1.603 13.335-13.936 23.531-29.806 29.906-49.802l-66.055 12.859c4.357-8.489 9.331-15.954 12.19-24.156 9.78-28.058 5.726-55.186-8.311-80.771-11.266-20.532-21.657-40.975-16.037-65.72-11.896 11.679-16.465 26.548-19.364 41.871-2.856 15.092-4.539 30.406-6.75 45.718-.314-.462-.722-.804-.785-1.201-.256-1.607-.494-3.226-.581-4.848-1.343-25.081-6.271-49.246-16.671-72.326-6.122-13.586-12.873-27.476-6.562-43.682-4.271 2.239-8.13 4.464-10.897 7.641-8.258 9.482-11.673 20.871-12.551 33.341-.751 10.653-1.779 21.357-3.766 31.833-2.088 11.008-5.399 21.799-12.838 31.917-3.009-21.616-3.342-42.991-22.723-56.722zm160.411 214.562H118.323v33.699h195.132v-33.699zm-156.441 51.108c-.161 28.061 28.59 48.967 64.123 47.45 29.447-1.257 55.509-24.289 53.268-47.45H157.014z\"></path></svg>"}, "model": {"version": "75.8.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#00B39F", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-3.94 -1.44 438.62 432.87\"><path fill=\"#E75225\" d=\"M215.926 7.068c115.684.024 210.638 93.784 210.493 207.844-.148 115.793-94.713 208.252-212.912 208.169C97.95 423 4.52 329.143 4.601 213.221 4.68 99.867 99.833 7.044 215.926 7.068zm-63.947 73.001c2.652 12.978.076 25.082-3.846 36.988-2.716 8.244-6.47 16.183-8.711 24.539-3.694 13.769-7.885 27.619-9.422 41.701-2.21 20.25 5.795 38.086 19.493 55.822L86.527 225.94c.11 1.978-.007 2.727.21 3.361 5.968 17.43 16.471 32.115 28.243 45.957 1.246 1.465 4.082 2.217 6.182 2.221 62.782.115 125.565.109 188.347.028 1.948-.003 4.546-.369 5.741-1.618 13.456-14.063 23.746-30.079 30.179-50.257l-66.658 12.976c4.397-8.567 9.417-16.1 12.302-24.377 9.869-28.315 5.779-55.69-8.387-81.509-11.368-20.72-21.854-41.349-16.183-66.32-12.005 11.786-16.615 26.79-19.541 42.253-2.882 15.23-4.58 30.684-6.811 46.136-.317-.467-.728-.811-.792-1.212-.258-1.621-.499-3.255-.587-4.893-1.355-25.31-6.328-49.696-16.823-72.987-6.178-13.71-12.99-27.727-6.622-44.081-4.31 2.259-8.205 4.505-10.997 7.711-8.333 9.569-11.779 21.062-12.666 33.645-.757 10.75-1.796 21.552-3.801 32.123-2.107 11.109-5.448 21.998-12.956 32.209-3.033-21.81-3.37-43.38-22.928-57.237zm161.877 216.523H116.942v34.007h196.914v-34.007zm-157.871 51.575c-.163 28.317 28.851 49.414 64.709 47.883 29.716-1.269 56.016-24.51 53.755-47.883H155.985z\"/></svg>", "svgComplete": "", "svgWhite": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-3.94 -1.44 438.62 432.87\"><path fill=\"#E75225\" d=\"M215.926 7.068c115.684.024 210.638 93.784 210.493 207.844-.148 115.793-94.713 208.252-212.912 208.169C97.95 423 4.52 329.143 4.601 213.221 4.68 99.867 99.833 7.044 215.926 7.068zm-63.947 73.001c2.652 12.978.076 25.082-3.846 36.988-2.716 8.244-6.47 16.183-8.711 24.539-3.694 13.769-7.885 27.619-9.422 41.701-2.21 20.25 5.795 38.086 19.493 55.822L86.527 225.94c.11 1.978-.007 2.727.21 3.361 5.968 17.43 16.471 32.115 28.243 45.957 1.246 1.465 4.082 2.217 6.182 2.221 62.782.115 125.565.109 188.347.028 1.948-.003 4.546-.369 5.741-1.618 13.456-14.063 23.746-30.079 30.179-50.257l-66.658 12.976c4.397-8.567 9.417-16.1 12.302-24.377 9.869-28.315 5.779-55.69-8.387-81.509-11.368-20.72-21.854-41.349-16.183-66.32-12.005 11.786-16.615 26.79-19.541 42.253-2.882 15.23-4.58 30.684-6.811 46.136-.317-.467-.728-.811-.792-1.212-.258-1.621-.499-3.255-.587-4.893-1.355-25.31-6.328-49.696-16.823-72.987-6.178-13.71-12.99-27.727-6.622-44.081-4.31 2.259-8.205 4.505-10.997 7.711-8.333 9.569-11.779 21.062-12.666 33.645-.757 10.75-1.796 21.552-3.801 32.123-2.107 11.109-5.448 21.998-12.956 32.209-3.033-21.81-3.37-43.38-22.928-57.237zm161.877 216.523H116.942v34.007h196.914v-34.007zm-157.871 51.575c-.163 28.317 28.851 49.414 64.709 47.883 29.716-1.269 56.016-24.51 53.755-47.883H155.985z\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/prometheus-community/helm-charts/releases/download/kube-prometheus-stack-75.8.0/kube-prometheus-stack-75.8.0.tgz"}, "configuration": null, "component": {"version": "monitoring.coreos.com/v1", "kind": "PodMonitor", "schema": "{\n \"description\": \"The `PodMonitor` custom resource definition (CRD) defines how `Prometheus` and `PrometheusAgent` can scrape metrics from a group of pods.\\nAmong other things, it allows to specify:\\n* The pods to scrape via label selectors.\\n* The container ports to scrape.\\n* Authentication credentials to use.\\n* Target and metric relabeling.\\n\\n`Prometheus` and `PrometheusAgent` objects select `PodMonitor` objects using label and namespace selectors.\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Specification of desired Pod selection for target discovery by Prometheus.\",\n   \"properties\": {\n    \"attachMetadata\": {\n     \"description\": \"`attachMetadata` defines additional metadata which is added to the\\ndiscovered targets.\\n\\nIt requires Prometheus \\u003e= v2.35.0.\",\n     \"properties\": {\n      \"node\": {\n       \"description\": \"When set to true, Prometheus attaches node metadata to the discovered\\ntargets.\\n\\nThe Prometheus service account must have the `list` and `watch`\\npermissions on the `Nodes` objects.\",\n       \"type\": \"boolean\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"bodySizeLimit\": {\n     \"description\": \"When defined, bodySizeLimit specifies a job level limit on the size\\nof uncompressed response body that will be accepted by Prometheus.\\n\\nIt requires Prometheus \\u003e= v2.28.0.\",\n     \"pattern\": \"(^0|([0-9]*[.])?[0-9]+((K|M|G|T|E|P)i?)?B)$\",\n     \"type\": \"string\"\n    },\n    \"convertClassicHistogramsToNHCB\": {\n     \"description\": \"Whether to convert all scraped classic histograms into a native histogram with custom buckets.\\nIt requires Prometheus \\u003e= v3.0.0.\",\n     \"type\": \"boolean\"\n    },\n    \"fallbackScrapeProtocol\": {\n     \"description\": \"The protocol to use if a scrape returns blank, unparseable, or otherwise invalid Content-Type.\\n\\nIt requires Prometheus \\u003e= v3.0.0.\",\n     \"enum\": [\n      \"PrometheusProto\",\n      \"OpenMetricsText0.0.1\",\n      \"OpenMetricsText1.0.0\",\n      \"PrometheusText0.0.4\",\n      \"PrometheusText1.0.0\"\n     ],\n     \"type\": \"string\"\n    },\n    \"jobLabel\": {\n     \"description\": \"The label to use to retrieve the job name from.\\n`jobLabel` selects the label from the associated Kubernetes `Pod`\\nobject which will be used as the `job` label for all metrics.\\n\\nFor example if `jobLabel` is set to `foo` and the Kubernetes `Pod`\\nobject is labeled with `foo: bar`, then Prometheus adds the `job=\\\"bar\\\"`\\nlabel to all ingested metrics.\\n\\nIf the value of this field is empty, the `job` label of the metrics\\ndefaults to the namespace and name of the PodMonitor object (e.g. `\\u003cnamespace\\u003e/\\u003cname\\u003e`).\",\n     \"type\": \"string\"\n    },\n    \"keepDroppedTargets\": {\n     \"description\": \"Per-scrape limit on the number of targets dropped by relabeling\\nthat will be kept in memory. 0 means no limit.\\n\\nIt requires Prometheus \\u003e= v2.47.0.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"labelLimit\": {\n     \"description\": \"Per-scrape limit on number of labels that will be accepted for a sample.\\n\\nIt requires Prometheus \\u003e= v2.27.0.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"labelNameLengthLimit\": {\n     \"description\": \"Per-scrape limit on length of labels name that will be accepted for a sample.\\n\\nIt requires Prometheus \\u003e= v2.27.0.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"labelValueLengthLimit\": {\n     \"description\": \"Per-scrape limit on length of labels value that will be accepted for a sample.\\n\\nIt requires Prometheus \\u003e= v2.27.0.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"namespaceSelector\": {\n     \"description\": \"`namespaceSelector` defines in which namespace(s) Prometheus should discover the pods.\\nBy default, the pods are discovered in the same namespace as the `PodMonitor` object but it is possible to select pods across different/all namespaces.\",\n     \"properties\": {\n      \"any\": {\n       \"description\": \"Boolean describing whether all namespaces are selected in contrast to a\\nlist restricting them.\",\n       \"type\": \"boolean\"\n      },\n      \"matchNames\": {\n       \"description\": \"List of namespace names to select from.\",\n       \"items\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"nativeHistogramBucketLimit\": {\n     \"description\": \"If there are more than this many buckets in a native histogram,\\nbuckets will be merged to stay within the limit.\\nIt requires Prometheus \\u003e= v2.45.0.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"nativeHistogramMinBucketFactor\": {\n     \"anyOf\": [\n      {\n       \"type\": \"integer\"\n      },\n      {\n       \"type\": \"string\"\n      }\n     ],\n     \"description\": \"If the growth factor of one bucket to the next is smaller than this,\\nbuckets will be merged to increase the factor sufficiently.\\nIt requires Prometheus \\u003e= v2.50.0.\",\n     \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n     \"x-kubernetes-int-or-string\": true\n    },\n    \"podMetricsEndpoints\": {\n     \"description\": \"Defines how to scrape metrics from the selected pods.\",\n     \"items\": {\n      \"description\": \"PodMetricsEndpoint defines an endpoint serving Prometheus metrics to be scraped by\\nPrometheus.\",\n      \"properties\": {\n       \"authorization\": {\n        \"description\": \"`authorization` configures the Authorization header credentials to use when\\nscraping the target.\\n\\nCannot be set at the same time as `basicAuth`, or `oauth2`.\",\n        \"properties\": {\n         \"credentials\": {\n          \"description\": \"Selects a key of a Secret in the namespace that contains the credentials for authentication.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"type\": {\n          \"description\": \"Defines the authentication type. The value is case-insensitive.\\n\\n\\\"Basic\\\" is not a supported value.\\n\\nDefault: \\\"Bearer\\\"\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"basicAuth\": {\n        \"description\": \"`basicAuth` configures the Basic Authentication credentials to use when\\nscraping the target.\\n\\nCannot be set at the same time as `authorization`, or `oauth2`.\",\n        \"properties\": {\n         \"password\": {\n          \"description\": \"`password` specifies a key of a Secret containing the password for\\nauthentication.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"username\": {\n          \"description\": \"`username` specifies a key of a Secret containing the username for\\nauthentication.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"bearerTokenSecret\": {\n        \"description\": \"`bearerTokenSecret` specifies a key of a Secret containing the bearer\\ntoken for scraping targets. The secret needs to be in the same namespace\\nas the PodMonitor object and readable by the Prometheus Operator.\\n\\nDeprecated: use `authorization` instead.\",\n        \"properties\": {\n         \"key\": {\n          \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"default\": \"\",\n          \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n          \"type\": \"string\"\n         },\n         \"optional\": {\n          \"description\": \"Specify whether the Secret or its key must be defined\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"key\"\n        ],\n        \"type\": \"object\",\n        \"x-kubernetes-map-type\": \"atomic\"\n       },\n       \"enableHttp2\": {\n        \"description\": \"`enableHttp2` can be used to disable HTTP2 when scraping the target.\",\n        \"type\": \"boolean\"\n       },\n       \"filterRunning\": {\n        \"description\": \"When true, the pods which are not running (e.g. either in Failed or\\nSucceeded state) are dropped during the target discovery.\\n\\nIf unset, the filtering is enabled.\\n\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#pod-phase\",\n        \"type\": \"boolean\"\n       },\n       \"followRedirects\": {\n        \"description\": \"`followRedirects` defines whether the scrape requests should follow HTTP\\n3xx redirects.\",\n        \"type\": \"boolean\"\n       },\n       \"honorLabels\": {\n        \"description\": \"When true, `honorLabels` preserves the metric's labels when they collide\\nwith the target's labels.\",\n        \"type\": \"boolean\"\n       },\n       \"honorTimestamps\": {\n        \"description\": \"`honorTimestamps` controls whether Prometheus preserves the timestamps\\nwhen exposed by the target.\",\n        \"type\": \"boolean\"\n       },\n       \"interval\": {\n        \"description\": \"Interval at which Prometheus scrapes the metrics from the target.\\n\\nIf empty, Prometheus uses the global scrape interval.\",\n        \"pattern\": \"^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n        \"type\": \"string\"\n       },\n       \"metricRelabelings\": {\n        \"description\": \"`metricRelabelings` configures the relabeling rules to apply to the\\nsamples before ingestion.\",\n        \"items\": {\n         \"description\": \"RelabelConfig allows dynamic rewriting of the label set for targets, alerts,\\nscraped samples and remote write samples.\\n\\nMore info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config\",\n         \"properties\": {\n          \"action\": {\n           \"default\": \"replace\",\n           \"description\": \"Action to perform based on the regex matching.\\n\\n`Uppercase` and `Lowercase` actions require Prometheus \\u003e= v2.36.0.\\n`DropEqual` and `KeepEqual` actions require Prometheus \\u003e= v2.41.0.\\n\\nDefault: \\\"Replace\\\"\",\n           \"enum\": [\n            \"replace\",\n            \"Replace\",\n            \"keep\",\n            \"Keep\",\n            \"drop\",\n            \"Drop\",\n            \"hashmod\",\n            \"HashMod\",\n            \"labelmap\",\n            \"LabelMap\",\n            \"labeldrop\",\n            \"LabelDrop\",\n            \"labelkeep\",\n            \"LabelKeep\",\n            \"lowercase\",\n            \"Lowercase\",\n            \"uppercase\",\n            \"Uppercase\",\n            \"keepequal\",\n            \"KeepEqual\",\n            \"dropequal\",\n            \"DropEqual\"\n           ],\n           \"type\": \"string\"\n          },\n          \"modulus\": {\n           \"description\": \"Modulus to take of the hash of the source label values.\\n\\nOnly applicable when the action is `HashMod`.\",\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"regex\": {\n           \"description\": \"Regular expression against which the extracted value is matched.\",\n           \"type\": \"string\"\n          },\n          \"replacement\": {\n           \"description\": \"Replacement value against which a Replace action is performed if the\\nregular expression matches.\\n\\nRegex capture groups are available.\",\n           \"type\": \"string\"\n          },\n          \"separator\": {\n           \"description\": \"Separator is the string between concatenated SourceLabels.\",\n           \"type\": \"string\"\n          },\n          \"sourceLabels\": {\n           \"description\": \"The source labels select values from existing labels. Their content is\\nconcatenated using the configured Separator and matched against the\\nconfigured regular expression.\",\n           \"items\": {\n            \"description\": \"LabelName is a valid Prometheus label name which may only contain ASCII\\nletters, numbers, as well as underscores.\",\n            \"pattern\": \"^[a-zA-Z_][a-zA-Z0-9_]*$\",\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"targetLabel\": {\n           \"description\": \"Label to which the resulting string is written in a replacement.\\n\\nIt is mandatory for `Replace`, `HashMod`, `Lowercase`, `Uppercase`,\\n`KeepEqual` and `DropEqual` actions.\\n\\nRegex capture groups are available.\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"oauth2\": {\n        \"description\": \"`oauth2` configures the OAuth2 settings to use when scraping the target.\\n\\nIt requires Prometheus \\u003e= 2.27.0.\\n\\nCannot be set at the same time as `authorization`, or `basicAuth`.\",\n        \"properties\": {\n         \"clientId\": {\n          \"description\": \"`clientId` specifies a key of a Secret or ConfigMap containing the\\nOAuth2 client's ID.\",\n          \"properties\": {\n           \"configMap\": {\n            \"description\": \"ConfigMap containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key to select.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"default\": \"\",\n              \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"secret\": {\n            \"description\": \"Secret containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"default\": \"\",\n              \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"clientSecret\": {\n          \"description\": \"`clientSecret` specifies a key of a Secret containing the OAuth2\\nclient's secret.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"endpointParams\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"`endpointParams` configures the HTTP parameters to append to the token\\nURL.\",\n          \"type\": \"object\"\n         },\n         \"noProxy\": {\n          \"description\": \"`noProxy` is a comma-separated string that can contain IPs, CIDR notation, domain names\\nthat should be excluded from proxying. IP and domain names can\\ncontain port numbers.\\n\\nIt requires Prometheus \\u003e= v2.43.0, Alertmanager \\u003e= v0.25.0 or Thanos \\u003e= v0.32.0.\",\n          \"type\": \"string\"\n         },\n         \"proxyConnectHeader\": {\n          \"additionalProperties\": {\n           \"items\": {\n            \"description\": \"SecretKeySelector selects a key of a Secret.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"default\": \"\",\n              \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"type\": \"array\"\n          },\n          \"description\": \"ProxyConnectHeader optionally specifies headers to send to\\nproxies during CONNECT requests.\\n\\nIt requires Prometheus \\u003e= v2.43.0, Alertmanager \\u003e= v0.25.0 or Thanos \\u003e= v0.32.0.\",\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"proxyFromEnvironment\": {\n          \"description\": \"Whether to use the proxy configuration defined by environment variables (HTTP_PROXY, HTTPS_PROXY, and NO_PROXY).\\n\\nIt requires Prometheus \\u003e= v2.43.0, Alertmanager \\u003e= v0.25.0 or Thanos \\u003e= v0.32.0.\",\n          \"type\": \"boolean\"\n         },\n         \"proxyUrl\": {\n          \"description\": \"`proxyURL` defines the HTTP proxy server to use.\",\n          \"pattern\": \"^(http|https|socks5)://.+$\",\n          \"type\": \"string\"\n         },\n         \"scopes\": {\n          \"description\": \"`scopes` defines the OAuth2 scopes used for the token request.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\"\n         },\n         \"tlsConfig\": {\n          \"description\": \"TLS configuration to use when connecting to the OAuth2 server.\\nIt requires Prometheus \\u003e= v2.43.0.\",\n          \"properties\": {\n           \"ca\": {\n            \"description\": \"Certificate authority used when verifying server certificates.\",\n            \"properties\": {\n             \"configMap\": {\n              \"description\": \"ConfigMap containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key to select.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"secret\": {\n              \"description\": \"Secret containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the Secret or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"cert\": {\n            \"description\": \"Client certificate to present when doing client-authentication.\",\n            \"properties\": {\n             \"configMap\": {\n              \"description\": \"ConfigMap containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key to select.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"secret\": {\n              \"description\": \"Secret containing data to use for the targets.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"description\": \"Specify whether the Secret or its key must be defined\",\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"insecureSkipVerify\": {\n            \"description\": \"Disable target certificate validation.\",\n            \"type\": \"boolean\"\n           },\n           \"keySecret\": {\n            \"description\": \"Secret containing the client key file for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"default\": \"\",\n              \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"maxVersion\": {\n            \"description\": \"Maximum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.41.0 or Thanos \\u003e= v0.31.0.\",\n            \"enum\": [\n             \"TLS10\",\n             \"TLS11\",\n             \"TLS12\",\n             \"TLS13\"\n            ],\n            \"type\": \"string\"\n           },\n           \"minVersion\": {\n            \"description\": \"Minimum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.35.0 or Thanos \\u003e= v0.28.0.\",\n            \"enum\": [\n             \"TLS10\",\n             \"TLS11\",\n             \"TLS12\",\n             \"TLS13\"\n            ],\n            \"type\": \"string\"\n           },\n           \"serverName\": {\n            \"description\": \"Used to verify the hostname for the targets.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"tokenUrl\": {\n          \"description\": \"`tokenURL` configures the URL to fetch the token from.\",\n          \"minLength\": 1,\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"clientId\",\n         \"clientSecret\",\n         \"tokenUrl\"\n        ],\n        \"type\": \"object\"\n       },\n       \"params\": {\n        \"additionalProperties\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"description\": \"`params` define optional HTTP URL parameters.\",\n        \"type\": \"object\"\n       },\n       \"path\": {\n        \"description\": \"HTTP path from which to scrape for metrics.\\n\\nIf empty, Prometheus uses the default value (e.g. `/metrics`).\",\n        \"type\": \"string\"\n       },\n       \"port\": {\n        \"description\": \"The `Pod` port name which exposes the endpoint.\\n\\nIt takes precedence over the `portNumber` and `targetPort` fields.\",\n        \"type\": \"string\"\n       },\n       \"portNumber\": {\n        \"description\": \"The `Pod` port number which exposes the endpoint.\",\n        \"format\": \"int32\",\n        \"maximum\": 65535,\n        \"minimum\": 1,\n        \"type\": \"integer\"\n       },\n       \"proxyUrl\": {\n        \"description\": \"`proxyURL` configures the HTTP Proxy URL (e.g.\\n\\\"http://proxyserver:2195\\\") to go through when scraping the target.\",\n        \"type\": \"string\"\n       },\n       \"relabelings\": {\n        \"description\": \"`relabelings` configures the relabeling rules to apply the target's\\nmetadata labels.\\n\\nThe Operator automatically adds relabelings for a few standard Kubernetes fields.\\n\\nThe original scrape job's name is available via the `__tmp_prometheus_job_name` label.\\n\\nMore info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config\",\n        \"items\": {\n         \"description\": \"RelabelConfig allows dynamic rewriting of the label set for targets, alerts,\\nscraped samples and remote write samples.\\n\\nMore info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config\",\n         \"properties\": {\n          \"action\": {\n           \"default\": \"replace\",\n           \"description\": \"Action to perform based on the regex matching.\\n\\n`Uppercase` and `Lowercase` actions require Prometheus \\u003e= v2.36.0.\\n`DropEqual` and `KeepEqual` actions require Prometheus \\u003e= v2.41.0.\\n\\nDefault: \\\"Replace\\\"\",\n           \"enum\": [\n            \"replace\",\n            \"Replace\",\n            \"keep\",\n            \"Keep\",\n            \"drop\",\n            \"Drop\",\n            \"hashmod\",\n            \"HashMod\",\n            \"labelmap\",\n            \"LabelMap\",\n            \"labeldrop\",\n            \"LabelDrop\",\n            \"labelkeep\",\n            \"LabelKeep\",\n            \"lowercase\",\n            \"Lowercase\",\n            \"uppercase\",\n            \"Uppercase\",\n            \"keepequal\",\n            \"KeepEqual\",\n            \"dropequal\",\n            \"DropEqual\"\n           ],\n           \"type\": \"string\"\n          },\n          \"modulus\": {\n           \"description\": \"Modulus to take of the hash of the source label values.\\n\\nOnly applicable when the action is `HashMod`.\",\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"regex\": {\n           \"description\": \"Regular expression against which the extracted value is matched.\",\n           \"type\": \"string\"\n          },\n          \"replacement\": {\n           \"description\": \"Replacement value against which a Replace action is performed if the\\nregular expression matches.\\n\\nRegex capture groups are available.\",\n           \"type\": \"string\"\n          },\n          \"separator\": {\n           \"description\": \"Separator is the string between concatenated SourceLabels.\",\n           \"type\": \"string\"\n          },\n          \"sourceLabels\": {\n           \"description\": \"The source labels select values from existing labels. Their content is\\nconcatenated using the configured Separator and matched against the\\nconfigured regular expression.\",\n           \"items\": {\n            \"description\": \"LabelName is a valid Prometheus label name which may only contain ASCII\\nletters, numbers, as well as underscores.\",\n            \"pattern\": \"^[a-zA-Z_][a-zA-Z0-9_]*$\",\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"targetLabel\": {\n           \"description\": \"Label to which the resulting string is written in a replacement.\\n\\nIt is mandatory for `Replace`, `HashMod`, `Lowercase`, `Uppercase`,\\n`KeepEqual` and `DropEqual` actions.\\n\\nRegex capture groups are available.\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\"\n       },\n       \"scheme\": {\n        \"description\": \"HTTP scheme to use for scraping.\\n\\n`http` and `https` are the expected values unless you rewrite the\\n`__scheme__` label via relabeling.\\n\\nIf empty, Prometheus uses the default value `http`.\",\n        \"enum\": [\n         \"http\",\n         \"https\"\n        ],\n        \"type\": \"string\"\n       },\n       \"scrapeTimeout\": {\n        \"description\": \"Timeout after which Prometheus considers the scrape to be failed.\\n\\nIf empty, Prometheus uses the global scrape timeout unless it is less\\nthan the target's scrape interval value in which the latter is used.\\nThe value cannot be greater than the scrape interval otherwise the operator will reject the resource.\",\n        \"pattern\": \"^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n        \"type\": \"string\"\n       },\n       \"targetPort\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"description\": \"Name or number of the target port of the `Pod` object behind the Service, the\\nport must be specified with container port property.\\n\\nDeprecated: use 'port' or 'portNumber' instead.\",\n        \"x-kubernetes-int-or-string\": true\n       },\n       \"tlsConfig\": {\n        \"description\": \"TLS configuration to use when scraping the target.\",\n        \"properties\": {\n         \"ca\": {\n          \"description\": \"Certificate authority used when verifying server certificates.\",\n          \"properties\": {\n           \"configMap\": {\n            \"description\": \"ConfigMap containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key to select.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"default\": \"\",\n              \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"secret\": {\n            \"description\": \"Secret containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"default\": \"\",\n              \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"cert\": {\n          \"description\": \"Client certificate to present when doing client-authentication.\",\n          \"properties\": {\n           \"configMap\": {\n            \"description\": \"ConfigMap containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key to select.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"default\": \"\",\n              \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"secret\": {\n            \"description\": \"Secret containing data to use for the targets.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"default\": \"\",\n              \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"insecureSkipVerify\": {\n          \"description\": \"Disable target certificate validation.\",\n          \"type\": \"boolean\"\n         },\n         \"keySecret\": {\n          \"description\": \"Secret containing the client key file for the targets.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"maxVersion\": {\n          \"description\": \"Maximum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.41.0 or Thanos \\u003e= v0.31.0.\",\n          \"enum\": [\n           \"TLS10\",\n           \"TLS11\",\n           \"TLS12\",\n           \"TLS13\"\n          ],\n          \"type\": \"string\"\n         },\n         \"minVersion\": {\n          \"description\": \"Minimum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.35.0 or Thanos \\u003e= v0.28.0.\",\n          \"enum\": [\n           \"TLS10\",\n           \"TLS11\",\n           \"TLS12\",\n           \"TLS13\"\n          ],\n          \"type\": \"string\"\n         },\n         \"serverName\": {\n          \"description\": \"Used to verify the hostname for the targets.\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"trackTimestampsStaleness\": {\n        \"description\": \"`trackTimestampsStaleness` defines whether Prometheus tracks staleness of\\nthe metrics that have an explicit timestamp present in scraped data.\\nHas no effect if `honorTimestamps` is false.\\n\\nIt requires Prometheus \\u003e= v2.48.0.\",\n        \"type\": \"boolean\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"podTargetLabels\": {\n     \"description\": \"`podTargetLabels` defines the labels which are transferred from the\\nassociated Kubernetes `Pod` object onto the ingested metrics.\",\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"sampleLimit\": {\n     \"description\": \"`sampleLimit` defines a per-scrape limit on the number of scraped samples\\nthat will be accepted.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"scrapeClass\": {\n     \"description\": \"The scrape class to apply.\",\n     \"minLength\": 1,\n     \"type\": \"string\"\n    },\n    \"scrapeClassicHistograms\": {\n     \"description\": \"Whether to scrape a classic histogram that is also exposed as a native histogram.\\nIt requires Prometheus \\u003e= v2.45.0.\",\n     \"type\": \"boolean\"\n    },\n    \"scrapeProtocols\": {\n     \"description\": \"`scrapeProtocols` defines the protocols to negotiate during a scrape. It tells clients the\\nprotocols supported by Prometheus in order of preference (from most to least preferred).\\n\\nIf unset, Prometheus uses its default value.\\n\\nIt requires Prometheus \\u003e= v2.49.0.\",\n     \"items\": {\n      \"description\": \"ScrapeProtocol represents a protocol used by Prometheus for scraping metrics.\\nSupported values are:\\n* `OpenMetricsText0.0.1`\\n* `OpenMetricsText1.0.0`\\n* `PrometheusProto`\\n* `PrometheusText0.0.4`\\n* `PrometheusText1.0.0`\",\n      \"enum\": [\n       \"PrometheusProto\",\n       \"OpenMetricsText0.0.1\",\n       \"OpenMetricsText1.0.0\",\n       \"PrometheusText0.0.4\",\n       \"PrometheusText1.0.0\"\n      ],\n      \"type\": \"string\"\n     },\n     \"type\": \"array\",\n     \"x-kubernetes-list-type\": \"set\"\n    },\n    \"selector\": {\n     \"description\": \"Label selector to select the Kubernetes `Pod` objects to scrape metrics from.\",\n     \"properties\": {\n      \"matchExpressions\": {\n       \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n       \"items\": {\n        \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n        \"properties\": {\n         \"key\": {\n          \"description\": \"key is the label key that the selector applies to.\",\n          \"type\": \"string\"\n         },\n         \"operator\": {\n          \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n          \"type\": \"string\"\n         },\n         \"values\": {\n          \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         }\n        },\n        \"required\": [\n         \"key\",\n         \"operator\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-type\": \"atomic\"\n      },\n      \"matchLabels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\",\n     \"x-kubernetes-map-type\": \"atomic\"\n    },\n    \"selectorMechanism\": {\n     \"description\": \"Mechanism used to select the endpoints to scrape.\\nBy default, the selection process relies on relabel configurations to filter the discovered targets.\\nAlternatively, you can opt in for role selectors, which may offer better efficiency in large clusters.\\nWhich strategy is best for your use case needs to be carefully evaluated.\\n\\nIt requires Prometheus \\u003e= v2.17.0.\",\n     \"enum\": [\n      \"RelabelConfig\",\n      \"RoleSelector\"\n     ],\n     \"type\": \"string\"\n    },\n    \"targetLimit\": {\n     \"description\": \"`targetLimit` defines a limit on the number of scraped targets that will\\nbe accepted.\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    }\n   },\n   \"required\": [\n    \"selector\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"required\": [\n  \"spec\"\n ],\n \"title\": \"Pod Monitor\",\n \"type\": \"object\"\n}"}}