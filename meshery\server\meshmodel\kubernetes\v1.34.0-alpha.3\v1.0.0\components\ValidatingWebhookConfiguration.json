{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Validating Webhook Configuration", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubernetes", "displayName": "Kubernetes", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Orchestration & Management"}, "subCategory": "Scheduling & Orchestration", "metadata": {"isAnnotation": false, "primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-0.17 0.08 230.10 223.35\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}.cls-2{fill:#326ce5}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.466a3.59 3.59 0 0 0-.855-.065 3.685 3.685 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53 43.53 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.656 3.656 0 0 0-.733.065l-.037-.045-21.785 3.698a43.695 43.695 0 0 0 17.54 21.946l8.442-20.4-.066-.08a3.683 3.683 0 0 0 .207-2.29zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.2l-10.738-19.406zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.039a3.725 3.725 0 0 0 5.913 2.84l.065.027 18.036-12.788a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.921a3.72 3.72 0 0 0 5.904-2.85l.092-.043 1.253-22.14a44.682 44.682 0 0 0-4.501.776 43.467 43.467 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.729a3.72 3.72 0 0 0 1.462-6.396l.018-.088-16.574-14.824a43.454 43.454 0 0 0-6.168 27.51l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.934 1.514-6.581-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.167l-17.422-75.675a13.602 13.602 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0L38.76 48.437a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.506 13.506 0 0 0 .491 1.718 13.1 13.1 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.423 13.423 0 0 0 2.574 2.133 13.924 13.924 0 0 0 3.857 1.677 13.298 13.298 0 0 0 3.43.473h.759l77.504-.018a12.993 12.993 0 0 0 1.41-.083 13.47 13.47 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.941 13.941 0 0 0 1.479-.822 13.303 13.303 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67 13.67 0 0 0 .5-1.724 13.282 13.282 0 0 0-.004-5.81zm-73.147 29.432a14.516 14.516 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.372 39.372 0 0 0 3.742 6.7 35.06 35.06 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.275 35.275 0 0 1-1.225-3.868 39.305 39.305 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.15-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.73a3.429 3.429 0 0 0-1.819.887 29.517 29.517 0 0 0-3.268 7.582 34.9 34.9 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.177 35.177 0 0 1 2.262-3.373 41.228 41.228 0 0 0 3.82-6.866 4.188 4.188 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68 4.68 0 0 0-2.428-.855 39.524 39.524 0 0 0-7.356 2.165 35.589 35.589 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.605.605 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.388 39.388 0 0 0 7.564-1.242 5.835 5.835 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.366 39.366 0 0 0-6.277-4.41 35.25 35.25 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.253 35.253 0 0 1 2.78 2.95 39.383 39.383 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082A54.289 54.289 0 0 1 102.86 61.89a55.052 55.052 0 0 1 7.63-1.173l.1-1.784a4.6 4.6 0 0 0 1.37-2.184 39.476 39.476 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.08-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.86.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.533 39.533 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.69 4.69 0 0 0 2.56-.278 39.532 39.532 0 0 0 5.69-5.148 35.004 35.004 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.095 35.095 0 0 1-3.507 2.046 39.495 39.495 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.745 4.745 0 0 0 1.82 1.83 39.464 39.464 0 0 0 7.568 1.246 35.64 35.64 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.03-.348-.075-.785-.151-1.09-.231a35.14 35.14 0 0 1-3.785-1.462 39.477 39.477 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.92 54.92 0 0 1-24.328 30.605z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M225.407 135.107L206.4 52.547a14.838 14.838 0 0 0-7.958-9.9l-76.935-36.73a14.825 14.825 0 0 0-12.771 0L31.808 42.669a14.838 14.838 0 0 0-7.961 9.895L4.873 135.129a14.668 14.668 0 0 0 1.995 11.185c.261.4.538.788.838 1.162l53.246 66.205a14.98 14.98 0 0 0 11.499 5.487l85.387-.02a14.986 14.986 0 0 0 11.5-5.48l53.227-66.211a14.72 14.72 0 0 0 2.842-12.347zm-9.197 3.866a13.677 13.677 0 0 1-.498 1.723 13.184 13.184 0 0 1-2.11 3.786l-47.299 58.838-1.486 1.852a13.305 13.305 0 0 1-3.24 2.865 13.945 13.945 0 0 1-1.474.822q-.513.237-1.045.43a13.873 13.873 0 0 1-1.383.445 13.473 13.473 0 0 1-1.989.379 12.988 12.988 0 0 1-1.41.082l-77.504.018h-.76a13.298 13.298 0 0 1-3.429-.472 13.925 13.925 0 0 1-3.855-1.679 13.424 13.424 0 0 1-2.576-2.132c-.233-.246-.468-.487-.68-.75l-48.805-60.679q-.408-.514-.765-1.066a13.102 13.102 0 0 1-1.343-2.726 13.505 13.505 0 0 1-.491-1.719 13.315 13.315 0 0 1 .004-5.809l17.394-75.675a13.598 13.598 0 0 1 7.295-9.07l70.508-33.685a13.589 13.589 0 0 1 11.705 0l70.519 33.67a13.602 13.602 0 0 1 7.293 9.073l17.422 75.674a13.282 13.282 0 0 1 .002 5.807z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M185.814 127.106c-.36-.083-.874-.225-1.227-.29a35.642 35.642 0 0 0-4.046-.326 39.464 39.464 0 0 1-7.57-1.242 4.745 4.745 0 0 1-1.82-1.832l-1.704-.496a54.837 54.837 0 0 0-8.79-37.983c.436-.396 1.262-1.127 1.495-1.342a3.338 3.338 0 0 1 .792-2.39 39.495 39.495 0 0 1 6.277-4.41 35.095 35.095 0 0 0 3.507-2.046c.272-.202.644-.522.929-.75a4.242 4.242 0 1 0-5.256-6.6c-.288.227-.68.525-.936.747a35.004 35.004 0 0 0-2.789 2.95 39.533 39.533 0 0 1-5.69 5.148 4.69 4.69 0 0 1-2.56.278l-1.606 1.147a55.309 55.309 0 0 0-35.032-16.927c-.039-.561-.087-1.577-.102-1.884a3.344 3.344 0 0 1-1.377-2.11 39.533 39.533 0 0 1 .478-7.656 35.112 35.112 0 0 0 .575-4.012c.013-.34-.007-.834-.007-1.201a4.242 4.242 0 1 0-8.441.004c0 .04.009.078.01.118-.004.35-.021.774-.01 1.08a35.476 35.476 0 0 0 .576 4.015 39.475 39.475 0 0 1 .47 7.654 4.601 4.601 0 0 1-1.37 2.182l-.1 1.786a55.052 55.052 0 0 0-7.63 1.173 54.289 54.289 0 0 0-27.574 15.754q-.77-.531-1.526-1.082a3.333 3.333 0 0 1-2.506-.243 39.383 39.383 0 0 1-5.69-5.141 35.255 35.255 0 0 0-2.777-2.95c-.257-.22-.65-.52-.938-.75a5.03 5.03 0 0 0-2.942-1.11 4.066 4.066 0 0 0-3.363 1.49 4.473 4.473 0 0 0 1.038 6.222l.065.046c.276.226.616.515.872.702a35.256 35.256 0 0 0 3.499 2.048 39.367 39.367 0 0 1 6.276 4.412 4.69 4.69 0 0 1 .849 2.434l1.351 1.208a54.613 54.613 0 0 0-8.611 38.073l-1.767.514a5.835 5.835 0 0 1-1.814 1.827 39.39 39.39 0 0 1-7.565 1.247 35.266 35.266 0 0 0-4.049.326c-.324.06-.774.174-1.127.262l-.037.008-.06.018a4.236 4.236 0 1 0 1.875 8.224l.063-.01c.028-.006.052-.02.08-.025.348-.08.786-.163 1.092-.246a35.59 35.59 0 0 0 3.786-1.451 39.527 39.527 0 0 1 7.358-2.165 4.68 4.68 0 0 1 2.426.857l1.84-.315a54.922 54.922 0 0 0 24.34 30.387l-.769 1.84a4.188 4.188 0 0 1 .377 2.387 41.228 41.228 0 0 1-3.82 6.864 35.183 35.183 0 0 0-2.263 3.372c-.168.318-.381.805-.542 1.138a4.238 4.238 0 1 0 7.621 3.616l.007-.008v-.01c.16-.33.387-.763.522-1.072a34.903 34.903 0 0 0 1.218-3.868 29.517 29.517 0 0 1 3.268-7.582 3.43 3.43 0 0 1 1.819-.888l.957-1.73a54.617 54.617 0 0 0 38.946.099c.268.478.768 1.392.9 1.623a3.335 3.335 0 0 1 2.155 1.31 39.306 39.306 0 0 1 2.898 7.096 35.275 35.275 0 0 0 1.225 3.868c.137.312.36.75.522 1.082a4.24 4.24 0 1 0 7.63-3.607c-.161-.333-.383-.82-.55-1.136a35.06 35.06 0 0 0-2.263-3.364 39.372 39.372 0 0 1-3.742-6.7 3.314 3.314 0 0 1 .324-2.49 14.519 14.519 0 0 1-.703-1.703 54.92 54.92 0 0 0 24.328-30.605c.546.087 1.497.253 1.806.316a3.337 3.337 0 0 1 2.36-.877 39.476 39.476 0 0 1 7.36 2.165 35.135 35.135 0 0 0 3.788 1.462c.305.08.74.156 1.09.233.029.008.055.02.083.028l.06.009a4.236 4.236 0 1 0 1.878-8.224zm-40.1-42.987l-18.037 12.787-.063-.03a3.723 3.723 0 0 1-5.913-2.838l-.02-.01-1.253-22.103a43.85 43.85 0 0 1 25.285 12.194zm-33.978 24.228h6.788l4.22 5.276-1.513 6.58-6.096 2.934-6.114-2.94-1.516-6.583zm-6.386-35.648a44.672 44.672 0 0 1 4.503-.774l-1.255 22.137-.092.044a3.72 3.72 0 0 1-5.904 2.852l-.035.02-18.154-12.872a43.467 43.467 0 0 1 20.937-11.407zm-27.52 19.68l16.574 14.824-.018.09a3.72 3.72 0 0 1-1.462 6.395l-.017.072-21.245 6.13a43.454 43.454 0 0 1 6.168-27.51zm22.191 39.38l-8.441 20.397a43.696 43.696 0 0 1-17.536-21.948l21.783-3.7.037.049a3.655 3.655 0 0 1 .73-.065 3.72 3.72 0 0 1 3.364 5.185zm24.916 26.23a43.637 43.637 0 0 1-23.815-1.223l10.713-19.372h.018a3.725 3.725 0 0 1 6.557-.006h.08l10.74 19.404q-2.091.698-4.293 1.199zm13.841-5.751l-8.528-20.605.026-.037a3.725 3.725 0 0 1 1.803-4.823 3.685 3.685 0 0 1 1.425-.37 3.59 3.59 0 0 1 .855.063l.037-.046 21.977 3.714a43.53 43.53 0 0 1-17.595 22.105zm19.903-32.42l-21.352-6.15-.02-.09a3.725 3.725 0 0 1-1.46-6.395l-.008-.043 16.482-14.751a44.279 44.279 0 0 1 6.357 27.43z\" class=\"cls-1\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"9.70 9.20 210.86 204.86\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.46551a3.59023 3.59023 0 0 0-.855-.065 3.68515 3.68515 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53012 43.53012 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.6556 3.6556 0 0 0-.733.065l-.037-.045-21.785 3.698a43.69506 43.69506 0 0 0 17.54 21.946l8.442-20.399-.066-.08a3.68318 3.68318 0 0 0 .207-2.291zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.199l-10.738-19.407zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.0385a3.725 3.725 0 0 0 5.913 2.84l.065.028 18.036-12.789a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.922a3.72 3.72 0 0 0 5.904-2.85l.092-.044 1.253-22.139a44.68209 44.68209 0 0 0-4.501.775 43.4669 43.4669 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.728a3.72 3.72 0 0 0 1.462-6.396l.018-.087-16.574-14.825a43.454 43.454 0 0 0-6.168 27.511l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.933 1.514-6.582-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.16651l-17.422-75.675a13.60207 13.60207 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0l-70.507 33.688a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.50607 13.50607 0 0 0 .491 1.718 13.0998 13.0998 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.42334 13.42334 0 0 0 2.574 2.133 13.9237 13.9237 0 0 0 3.857 1.677 13.29785 13.29785 0 0 0 3.43.473h.759l77.504-.018a12.99345 12.99345 0 0 0 1.41-.083 13.46921 13.46921 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.94127 13.94127 0 0 0 1.479-.822 13.30347 13.30347 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67036 13.67036 0 0 0 .5-1.724 13.28215 13.28215 0 0 0-.004-5.809zm-73.147 29.432a14.51575 14.51575 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.37244 39.37244 0 0 0 3.742 6.7 35.06044 35.06044 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.27528 35.27528 0 0 1-1.225-3.868 39.3046 39.3046 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.149-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.731a3.429 3.429 0 0 0-1.819.886 29.51728 29.51728 0 0 0-3.268 7.582 34.89931 34.89931 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.17706 35.17706 0 0 1 2.262-3.373 41.22786 41.22786 0 0 0 3.82-6.866 4.18792 4.18792 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68007 4.68007 0 0 0-2.428-.855 39.52352 39.52352 0 0 0-7.356 2.165 35.58886 35.58886 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.60518.60518 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.38835 39.38835 0 0 0 7.564-1.242 5.83506 5.83506 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.36558 39.36558 0 0 0-6.277-4.41 35.2503 35.2503 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.25285 35.25285 0 0 1 2.78 2.95 39.38314 39.38314 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082a54.28892 54.28892 0 0 1 27.577-15.754 55.05181 55.05181 0 0 1 7.63-1.173l.1-1.784a4.6001 4.6001 0 0 0 1.37-2.184 39.47551 39.47551 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.079-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.861.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.53346 39.53346 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.6901 4.6901 0 0 0 2.56-.278 39.53152 39.53152 0 0 0 5.69-5.148 35.00382 35.00382 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.09523 35.09523 0 0 1-3.507 2.046 39.49476 39.49476 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.7449 4.7449 0 0 0 1.82 1.831 39.46448 39.46448 0 0 0 7.568 1.245 35.64041 35.64041 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.029-.348-.076-.785-.152-1.09-.232a35.1407 35.1407 0 0 1-3.785-1.462 39.47672 39.47672 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.91994 54.91994 0 0 1-24.328 30.605z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "heptagon", "svgColor": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.2421 57.4458C24.2499 57.4595 24.2577 57.4733 24.2654 57.4871C26.1188 60.8013 24.9378 64.9922 21.6276 66.8479C18.3174 68.7035 14.1315 67.521 12.2781 64.2069C10.4247 60.8927 11.6057 56.7017 14.9159 54.8461C16.2327 54.108 17.688 53.8506 19.0799 54.0168L26.2057 42.2736C23.9974 40.6159 22.1947 38.4641 20.9478 35.9804C19.4279 32.953 18.7961 29.5563 19.1254 26.1841C19.4547 22.8118 20.7318 19.602 22.8086 16.9267C24.8853 14.2513 27.6768 12.2199 30.8595 11.068C34.0422 9.916 37.4858 9.69059 40.7912 10.4179C44.0966 11.1451 47.1285 12.7953 49.5355 15.1771C51.9426 17.559 53.6262 20.575 54.3913 23.8756C55.1563 27.1762 54.9716 30.6263 53.8584 33.8261L47.5941 31.6416C48.295 29.6271 48.4113 27.455 47.9296 25.377C47.4479 23.299 46.388 21.4002 44.8726 19.9007C43.3572 18.4012 41.4484 17.3623 39.3674 16.9044C37.2864 16.4465 35.1185 16.5885 33.1147 17.3137C31.111 18.0389 29.3536 19.3178 28.0461 21.0022C26.7386 22.6865 25.9346 24.7073 25.7273 26.8303C25.5199 28.9534 25.9177 31.0919 26.8746 32.9978C27.6628 34.5679 28.8037 35.9272 30.2018 36.9725L34.8919 39.8253L24.2216 57.41C24.2285 57.4219 24.2353 57.4338 24.2421 57.4458ZM37.0697 35.1098L37.1087 35.1115L46.2619 53.5389L51.1782 51.091C51.8603 50.8284 52.565 50.6334 53.282 50.5079V43.8036C51.7627 43.9695 50.2682 44.3298 48.8358 44.8775L42.7263 32.5777C43.6128 31.4873 44.1746 30.1157 44.2546 28.6041C44.4554 24.8111 41.547 21.5733 37.7586 21.3723C33.9702 21.1712 30.7363 24.0831 30.5355 27.876C30.3347 31.669 33.243 34.9068 37.0314 35.1078L37.0697 35.1098ZM65.8142 76.084L65.6845 76.0145C63.5807 74.8879 61.6948 73.4676 60.0663 71.8197C59.3357 72.1676 58.5665 72.4374 57.771 72.6222C55.6954 73.1041 53.5259 72.9873 51.514 72.2853C49.502 71.5833 47.7299 70.3249 46.403 68.6558L41.213 72.7918C43.3205 75.4429 46.1353 77.4417 49.3311 78.5568C52.5269 79.6718 55.9728 79.8573 59.2696 79.0918C61.6416 78.541 63.8664 77.5142 65.8142 76.084ZM56.8323 67.7221C55.3372 67.9724 53.7498 67.7312 52.3269 66.927C51.0114 66.1834 50.0348 65.0708 49.4535 63.7921L35.7316 63.7995C35.4653 66.5519 34.5657 69.2142 33.0932 71.5731C31.2996 74.4465 28.7295 76.7521 25.6807 78.2229C22.6318 79.6937 19.2291 80.2693 15.8669 79.8832C12.5047 79.497 9.32057 78.1649 6.68358 76.0411C4.0466 73.9173 2.06471 71.0888 0.967585 67.8834C-0.129543 64.6781 -0.296985 61.2271 0.484658 57.9304C1.2663 54.6336 2.96504 51.6261 5.38399 49.2564C7.80295 46.8867 10.8431 45.2519 14.1521 44.5412L15.5433 51.0349C13.46 51.4823 11.5461 52.5115 10.0231 54.0034C8.50025 55.4953 7.43077 57.3887 6.93867 59.4643C6.44657 61.5398 6.55199 63.7124 7.24271 65.7304C7.93343 67.7484 9.18117 69.5292 10.8413 70.8662C12.5015 72.2033 14.5061 73.042 16.6229 73.2851C18.7396 73.5282 20.8819 73.1658 22.8013 72.2398C24.7208 71.3139 26.3389 69.8623 27.4681 68.0533C28.3975 66.5644 28.9644 64.8835 29.1304 63.1458L29.1274 57.6416L49.6809 57.6306C49.6944 57.6061 49.7081 57.5815 49.7219 57.557C50.5465 56.0945 51.8258 55.0503 53.282 54.5019V55.3037C53.282 59.7758 54.5524 64.0605 56.8323 67.7221Z\" fill=\"#326CE5\"/>\n<path d=\"M73.4813 38.7917L59.3064 41.432C58.3593 41.6084 57.6726 42.4349 57.6726 43.3982V55.5082C57.6726 62.4379 61.4857 68.8055 67.5946 72.0769L72.8919 74.9138C73.4817 75.2296 74.1905 75.2296 74.7803 74.9138L80.0776 72.0769C86.1865 68.8055 89.9996 62.4379 89.9996 55.5082V43.2292C89.9996 42.2574 89.301 41.4262 88.3436 41.259L74.1916 38.7877C73.9565 38.7466 73.7159 38.748 73.4813 38.7917Z\" fill=\"#326CE5\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.2421 57.4458C24.2499 57.4595 24.2577 57.4733 24.2654 57.4871C26.1188 60.8013 24.9378 64.9922 21.6276 66.8479C18.3174 68.7035 14.1315 67.521 12.2781 64.2069C10.4247 60.8927 11.6057 56.7017 14.9159 54.8461C16.2327 54.108 17.688 53.8506 19.0799 54.0168L26.2057 42.2736C23.9974 40.6159 22.1947 38.4641 20.9478 35.9804C19.4279 32.953 18.7961 29.5563 19.1254 26.1841C19.4547 22.8118 20.7318 19.602 22.8086 16.9267C24.8853 14.2513 27.6768 12.2199 30.8595 11.068C34.0422 9.916 37.4858 9.69059 40.7912 10.4179C44.0966 11.1451 47.1285 12.7953 49.5355 15.1771C51.9426 17.559 53.6262 20.575 54.3913 23.8756C55.1563 27.1762 54.9716 30.6263 53.8584 33.8261L47.5941 31.6416C48.295 29.6271 48.4113 27.455 47.9296 25.377C47.4479 23.299 46.388 21.4002 44.8726 19.9007C43.3572 18.4012 41.4484 17.3623 39.3674 16.9044C37.2864 16.4465 35.1185 16.5885 33.1147 17.3137C31.111 18.0389 29.3536 19.3178 28.0461 21.0022C26.7386 22.6865 25.9346 24.7073 25.7273 26.8303C25.5199 28.9534 25.9177 31.0919 26.8746 32.9978C27.6628 34.5679 28.8037 35.9272 30.2018 36.9725L34.8919 39.8253L24.2216 57.41C24.2285 57.4219 24.2353 57.4338 24.2421 57.4458ZM37.0697 35.1098L37.1087 35.1115L46.2619 53.5389L51.1782 51.091C51.8603 50.8284 52.565 50.6334 53.282 50.5079V43.8036C51.7627 43.9695 50.2682 44.3298 48.8358 44.8775L42.7263 32.5777C43.6128 31.4873 44.1746 30.1157 44.2546 28.6041C44.4554 24.8111 41.547 21.5733 37.7586 21.3723C33.9702 21.1712 30.7363 24.0831 30.5355 27.876C30.3347 31.669 33.243 34.9068 37.0314 35.1078L37.0697 35.1098ZM65.8142 76.084L65.6845 76.0145C63.5807 74.8879 61.6948 73.4676 60.0663 71.8197C59.3357 72.1676 58.5665 72.4374 57.771 72.6222C55.6954 73.1041 53.5259 72.9873 51.514 72.2853C49.502 71.5833 47.7299 70.3249 46.403 68.6558L41.213 72.7918C43.3205 75.4429 46.1353 77.4417 49.3311 78.5568C52.5269 79.6718 55.9728 79.8573 59.2696 79.0918C61.6416 78.541 63.8664 77.5142 65.8142 76.084ZM56.8323 67.7221C55.3372 67.9724 53.7498 67.7312 52.3269 66.927C51.0114 66.1834 50.0348 65.0708 49.4535 63.7921L35.7316 63.7995C35.4653 66.5519 34.5657 69.2142 33.0932 71.5731C31.2996 74.4465 28.7295 76.7521 25.6807 78.2229C22.6318 79.6937 19.2291 80.2693 15.8669 79.8832C12.5047 79.497 9.32057 78.1649 6.68358 76.0411C4.0466 73.9173 2.06471 71.0888 0.967585 67.8834C-0.129543 64.6781 -0.296985 61.2271 0.484658 57.9304C1.2663 54.6336 2.96504 51.6261 5.38399 49.2564C7.80295 46.8867 10.8431 45.2519 14.1521 44.5412L15.5433 51.0349C13.46 51.4823 11.5461 52.5115 10.0231 54.0034C8.50025 55.4953 7.43077 57.3887 6.93867 59.4643C6.44657 61.5398 6.55199 63.7124 7.24271 65.7304C7.93343 67.7484 9.18117 69.5292 10.8413 70.8662C12.5015 72.2033 14.5061 73.042 16.6229 73.2851C18.7396 73.5282 20.8819 73.1658 22.8013 72.2398C24.7208 71.3139 26.3389 69.8623 27.4681 68.0533C28.3975 66.5644 28.9644 64.8835 29.1304 63.1458L29.1274 57.6416L49.6809 57.6306C49.6944 57.6061 49.7081 57.5815 49.7219 57.557C50.5465 56.0945 51.8258 55.0503 53.282 54.5019V55.3037C53.282 59.7758 54.5524 64.0605 56.8323 67.7221Z\" fill=\"white\"/>\n<path d=\"M73.4813 38.7917L59.3064 41.432C58.3593 41.6084 57.6726 42.4349 57.6726 43.3982V55.5082C57.6726 62.4379 61.4857 68.8055 67.5946 72.0769L72.8919 74.9138C73.4817 75.2296 74.1905 75.2296 74.7803 74.9138L80.0776 72.0769C86.1865 68.8055 89.9996 62.4379 89.9996 55.5082V43.2292C89.9996 42.2574 89.301 41.4262 88.3436 41.259L74.1916 38.7877C73.9565 38.7466 73.7159 38.748 73.4813 38.7917Z\" fill=\"white\"/>\n</svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": false, "published": false, "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3"}, "configuration": null, "component": {"version": "admissionregistration.k8s.io/v1", "kind": "ValidatingWebhookConfiguration", "schema": "{\"description\":\"ValidatingWebhookConfiguration describes the configuration of and admission webhook that accept or reject and object without changing it.\",\"properties\":{\"webhooks\":{\"description\":\"Webhooks is a list of webhooks and the affected resources and operations.\",\"items\":{\"allOf\":[{\"description\":\"ValidatingWebhook describes an admission webhook and the resources and operations it applies to.\",\"properties\":{\"admissionReviewVersions\":{\"description\":\"AdmissionReviewVersions is an ordered list of preferred `AdmissionReview` versions the Webhook expects. API server will try to use first version in the list which it supports. If none of the versions specified in this list supported by API server, validation will fail for this object. If a persisted webhook configuration specifies allowed versions and does not include any versions known to the API Server, calls to the webhook will fail and be subject to the failure policy.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"clientConfig\":{\"allOf\":[{\"description\":\"WebhookClientConfig contains the information to make a TLS connection with the webhook\",\"properties\":{\"caBundle\":{\"description\":\"`caBundle` is a PEM encoded CA bundle which will be used to validate the webhook's server certificate. If unspecified, system trust roots on the apiserver are used.\",\"format\":\"byte\",\"type\":\"string\"},\"service\":{\"allOf\":[{\"description\":\"ServiceReference holds a reference to Service.legacy.k8s.io\",\"properties\":{\"name\":{\"default\":\"\",\"description\":\"`name` is the name of the service. Required\",\"type\":\"string\"},\"namespace\":{\"default\":\"\",\"description\":\"`namespace` is the namespace of the service. Required\",\"type\":\"string\"},\"path\":{\"description\":\"`path` is an optional URL path which will be sent in any request to this service.\",\"type\":\"string\"},\"port\":{\"description\":\"If specified, the port on the service that hosting webhook. Default to 443 for backward compatibility. `port` should be a valid port number (1-65535, inclusive).\",\"format\":\"int32\",\"type\":\"integer\"}},\"required\":[\"namespace\",\"name\"],\"type\":\"object\"}],\"description\":\"`service` is a reference to the service for this webhook. Either `service` or `url` must be specified.\\n\\nIf the webhook is running within the cluster, then you should use `service`.\"},\"url\":{\"description\":\"`url` gives the location of the webhook, in standard URL form (`scheme://host:port/path`). Exactly one of `url` or `service` must be specified.\\n\\nThe `host` should not refer to a service running in the cluster; use the `service` field instead. The host might be resolved via external DNS in some apiservers (e.g., `kube-apiserver` cannot resolve in-cluster DNS as that would be a layering violation). `host` may also be an IP address.\\n\\nPlease note that using `localhost` or `127.0.0.1` as a `host` is risky unless you take great care to run this webhook on all hosts which run an apiserver which might need to make calls to this webhook. Such installs are likely to be non-portable, i.e., not easy to turn up in a new cluster.\\n\\nThe scheme must be \\\"https\\\"; the URL must begin with \\\"https://\\\".\\n\\nA path is optional, and if present may be any string permissible in a URL. You may use the path to pass an arbitrary string to the webhook, for example, a cluster identifier.\\n\\nAttempting to use a user or basic auth e.g. \\\"user:password@\\\" is not allowed. Fragments (\\\"#...\\\") and query parameters (\\\"?...\\\") are not allowed, either.\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"ClientConfig defines how to communicate with the hook. Required\"},\"failurePolicy\":{\"description\":\"FailurePolicy defines how unrecognized errors from the admission endpoint are handled - allowed values are Ignore or Fail. Defaults to Fail.\",\"type\":\"string\"},\"matchConditions\":{\"description\":\"MatchConditions is a list of conditions that must be met for a request to be sent to this webhook. Match conditions filter requests that have already been matched by the rules, namespaceSelector, and objectSelector. An empty list of matchConditions matches all requests. There are a maximum of 64 match conditions allowed.\\n\\nThe exact matching logic is (in order):\\n  1. If ANY matchCondition evaluates to FALSE, the webhook is skipped.\\n  2. If ALL matchConditions evaluate to TRUE, the webhook is called.\\n  3. If any matchCondition evaluates to an error (but none are FALSE):\\n     - If failurePolicy=Fail, reject the request\\n     - If failurePolicy=Ignore, the error is ignored and the webhook is skipped\",\"items\":{\"allOf\":[{\"description\":\"MatchCondition represents a condition which must by fulfilled for a request to be sent to a webhook.\",\"properties\":{\"expression\":{\"default\":\"\",\"description\":\"Expression represents the expression which will be evaluated by CEL. Must evaluate to bool. CEL expressions have access to the contents of the AdmissionRequest and Authorizer, organized into CEL variables:\\n\\n'object' - The object from the incoming request. The value is null for DELETE requests. 'oldObject' - The existing object. The value is null for CREATE requests. 'request' - Attributes of the admission request(/pkg/apis/admission/types.go#AdmissionRequest). 'authorizer' - A CEL Authorizer. May be used to perform authorization checks for the principal (user or service account) of the request.\\n  See https://pkg.go.dev/k8s.io/apiserver/pkg/cel/library#Authz\\n'authorizer.requestResource' - A CEL ResourceCheck constructed from the 'authorizer' and configured with the\\n  request resource.\\nDocumentation on CEL: https://kubernetes.io/docs/reference/using-api/cel/\\n\\nRequired.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name is an identifier for this match condition, used for strategic merging of MatchConditions, as well as providing an identifier for logging purposes. A good name should be descriptive of the associated expression. Name must be a qualified name consisting of alphanumeric characters, '-', '_' or '.', and must start and end with an alphanumeric character (e.g. 'MyName',  or 'my.name',  or '123-abc', regex used for validation is '([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9]') with an optional DNS subdomain prefix and '/' (e.g. 'example.com/MyName')\\n\\nRequired.\",\"type\":\"string\"}},\"required\":[\"name\",\"expression\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"},\"matchPolicy\":{\"description\":\"matchPolicy defines how the \\\"rules\\\" list is used to match incoming requests. Allowed values are \\\"Exact\\\" or \\\"Equivalent\\\".\\n\\n- Exact: match a request only if it exactly matches a specified rule. For example, if deployments can be modified via apps/v1, apps/v1beta1, and extensions/v1beta1, but \\\"rules\\\" only included `apiGroups:[\\\"apps\\\"], apiVersions:[\\\"v1\\\"], resources: [\\\"deployments\\\"]`, a request to apps/v1beta1 or extensions/v1beta1 would not be sent to the webhook.\\n\\n- Equivalent: match a request if modifies a resource listed in rules, even via another API group or version. For example, if deployments can be modified via apps/v1, apps/v1beta1, and extensions/v1beta1, and \\\"rules\\\" only included `apiGroups:[\\\"apps\\\"], apiVersions:[\\\"v1\\\"], resources: [\\\"deployments\\\"]`, a request to apps/v1beta1 or extensions/v1beta1 would be converted to apps/v1 and sent to the webhook.\\n\\nDefaults to \\\"Equivalent\\\"\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"The name of the admission webhook. Name should be fully qualified, e.g., imagepolicy.kubernetes.io, where \\\"imagepolicy\\\" is the name of the webhook, and kubernetes.io is the name of the organization. Required.\",\"type\":\"string\"},\"namespaceSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"NamespaceSelector decides whether to run the webhook on an object based on whether the namespace for that object matches the selector. If the object itself is a namespace, the matching is performed on object.metadata.labels. If the object is another cluster scoped resource, it never skips the webhook.\\n\\nFor example, to run the webhook on any objects whose namespace is not associated with \\\"runlevel\\\" of \\\"0\\\" or \\\"1\\\";  you will set the selector as follows: \\\"namespaceSelector\\\": {\\n  \\\"matchExpressions\\\": [\\n    {\\n      \\\"key\\\": \\\"runlevel\\\",\\n      \\\"operator\\\": \\\"NotIn\\\",\\n      \\\"values\\\": [\\n        \\\"0\\\",\\n        \\\"1\\\"\\n      ]\\n    }\\n  ]\\n}\\n\\nIf instead you want to only run the webhook on any objects whose namespace is associated with the \\\"environment\\\" of \\\"prod\\\" or \\\"staging\\\"; you will set the selector as follows: \\\"namespaceSelector\\\": {\\n  \\\"matchExpressions\\\": [\\n    {\\n      \\\"key\\\": \\\"environment\\\",\\n      \\\"operator\\\": \\\"In\\\",\\n      \\\"values\\\": [\\n        \\\"prod\\\",\\n        \\\"staging\\\"\\n      ]\\n    }\\n  ]\\n}\\n\\nSee https://kubernetes.io/docs/concepts/overview/working-with-objects/labels for more examples of label selectors.\\n\\nDefault to the empty LabelSelector, which matches everything.\"},\"objectSelector\":{\"allOf\":[{\"description\":\"A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.\",\"properties\":{\"matchExpressions\":{\"description\":\"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\"items\":{\"allOf\":[{\"description\":\"A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.\",\"properties\":{\"key\":{\"default\":\"\",\"description\":\"key is the label key that the selector applies to.\",\"type\":\"string\"},\"operator\":{\"default\":\"\",\"description\":\"operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.\",\"type\":\"string\"},\"values\":{\"description\":\"values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"key\",\"operator\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"matchLabels\":{\"additionalProperties\":{\"default\":\"\",\"type\":\"string\"},\"description\":\"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels map is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the operator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\"type\":\"object\"}},\"type\":\"object\",\"x-kubernetes-map-type\":\"atomic\"}],\"description\":\"ObjectSelector decides whether to run the webhook based on if the object has matching labels. objectSelector is evaluated against both the oldObject and newObject that would be sent to the webhook, and is considered to match if either object matches the selector. A null object (oldObject in the case of create, or newObject in the case of delete) or an object that cannot have labels (like a DeploymentRollback or a PodProxyOptions object) is not considered to match. Use the object selector only if the webhook is opt-in, because end users may skip the admission webhook by setting the labels. Default to the empty LabelSelector, which matches everything.\"},\"rules\":{\"description\":\"Rules describes what operations on what resources/subresources the webhook cares about. The webhook cares about an operation if it matches _any_ Rule. However, in order to prevent ValidatingAdmissionWebhooks and MutatingAdmissionWebhooks from putting the cluster in a state which cannot be recovered from without completely disabling the plugin, ValidatingAdmissionWebhooks and MutatingAdmissionWebhooks are never called on admission requests for ValidatingWebhookConfiguration and MutatingWebhookConfiguration objects.\",\"items\":{\"allOf\":[{\"description\":\"RuleWithOperations is a tuple of Operations and Resources. It is recommended to make sure that all the tuple expansions are valid.\",\"properties\":{\"apiGroups\":{\"description\":\"APIGroups is the API groups the resources belong to. '*' is all groups. If '*' is present, the length of the slice must be one. Required.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"apiVersions\":{\"description\":\"APIVersions is the API versions the resources belong to. '*' is all versions. If '*' is present, the length of the slice must be one. Required.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"operations\":{\"description\":\"Operations is the operations the admission hook cares about - CREATE, UPDATE, DELETE, CONNECT or * for all of those operations and any future admission operations that are added. If '*' is present, the length of the slice must be one. Required.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"resources\":{\"description\":\"Resources is a list of resources this rule applies to.\\n\\nFor example: 'pods' means pods. 'pods/log' means the log subresource of pods. '*' means all resources, but not subresources. 'pods/*' means all subresources of pods. '*/scale' means all scale subresources. '*/*' means all resources and their subresources.\\n\\nIf wildcard is present, the validation rule will ensure resources do not overlap with each other.\\n\\nDepending on the enclosing object, subresources might not be allowed. Required.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"scope\":{\"description\":\"scope specifies the scope of this rule. Valid values are \\\"Cluster\\\", \\\"Namespaced\\\", and \\\"*\\\" \\\"Cluster\\\" means that only cluster-scoped resources will match this rule. Namespace API objects are cluster-scoped. \\\"Namespaced\\\" means that only namespaced resources will match this rule. \\\"*\\\" means that there are no scope restrictions. Subresources match the scope of their parent resource. Default is \\\"*\\\".\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"sideEffects\":{\"description\":\"SideEffects states whether this webhook has side effects. Acceptable values are: None, NoneOnDryRun (webhooks created via v1beta1 may also specify Some or Unknown). Webhooks with side effects MUST implement a reconciliation system, since a request may be rejected by a future step in the admission chain and the side effects therefore need to be undone. Requests with the dryRun attribute will be auto-rejected if they match a webhook with sideEffects == Unknown or Some.\",\"type\":\"string\"},\"timeoutSeconds\":{\"description\":\"TimeoutSeconds specifies the timeout for this webhook. After the timeout passes, the webhook call will be ignored or the API call will fail based on the failure policy. The timeout value must be between 1 and 30 seconds. Default to 10 seconds.\",\"format\":\"int32\",\"type\":\"integer\"}},\"required\":[\"name\",\"clientConfig\",\"sideEffects\",\"admissionReviewVersions\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-map-keys\":[\"name\"],\"x-kubernetes-list-type\":\"map\",\"x-kubernetes-patch-merge-key\":\"name\",\"x-kubernetes-patch-strategy\":\"merge\"}},\"type\":\"object\",\"x-kubernetes-group-version-kind\":[{\"group\":\"admissionregistration.k8s.io\",\"kind\":\"ValidatingWebhookConfiguration\",\"version\":\"v1\"}]}"}}