{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "GRPC Route", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "linkerd", "displayName": "<PERSON><PERSON>", "description": "git://github.com/meshery/meshery-linkerd/master/templates/meshmodel/components/stable-2.9.5", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Cloud Native Network"}, "subCategory": "Service Mesh", "metadata": {"isAnnotation": false, "primaryColor": "#0DA6E5", "secondaryColor": "#0DA6E5", "shape": "circle", "source_uri": "git://github.com/linkerd/linkerd2/main/charts/linkerd-crds/", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\" height=\"20\" width=\"20\"><style xmlns=\"http://www.w3.org/2000/svg\">svg {enable-background:new 0 0 500 500}</style><style xmlns=\"http://www.w3.org/2000/svg\">.st2{fill:#2beda7}</style><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_1_\" x1=\"477.221\" x2=\"477.221\" y1=\"106.515\" y2=\"308.8\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_1_)\" d=\"M460.4 106.5v182.8l33.7 19.5V126z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_2_\" x1=\"25.459\" x2=\"25.459\" y1=\"106.52\" y2=\"308.812\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_2_)\" d=\"M8.6 308.8l33.7-19.5V106.5L8.6 126z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M173.8 307l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st2\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_3_\" x1=\"477.221\" x2=\"477.221\" y1=\"196.062\" y2=\"382.938\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_3_)\" d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_4_\" x1=\"403.048\" x2=\"403.048\" y1=\"238.884\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_4_)\" d=\"M394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_5_\" x1=\"328.877\" x2=\"328.877\" y1=\"281.704\" y2=\"472.469\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_5_)\" d=\"M312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_6_\" x1=\"173.82\" x2=\"173.82\" y1=\"281.704\" y2=\"472.466\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_6_)\" d=\"M159.6 454.5l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_7_\" x1=\"99.649\" x2=\"99.649\" y1=\"238.883\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_7_)\" d=\"M86.8 412.5l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7z\"></path><linearGradient xmlns=\"http://www.w3.org/2000/svg\" id=\"SVGID_8_\" x1=\"25.478\" x2=\"25.478\" y1=\"196.059\" y2=\"382.936\" gradientUnits=\"userSpaceOnUse\"><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"0\" stop-color=\"#2beda7\"></stop><stop xmlns=\"http://www.w3.org/2000/svg\" offset=\"1\" stop-color=\"#018afd\"></stop></linearGradient><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"url(#SVGID_8_)\" d=\"M12.6 369.7l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st2\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\" height=\"20\" width=\"20\"><style xmlns=\"http://www.w3.org/2000/svg\">svg {enable-background:new 0 0 500 500}</style><style xmlns=\"http://www.w3.org/2000/svg\">.st0{fill:#fff}</style><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M460.4 106.5v182.8l33.7 19.5V126zM8.6 308.8l33.7-19.5V106.5L8.6 126zm165.2-1.8l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st0\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4zM394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5zM312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3zm-152.4-18l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6zm-72.8-42l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7zm-74.2-42.8l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\" class=\"st0\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st0\"></path></svg>"}, "model": {"version": "edge-25.7.5"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#0DA6E5", "secondaryColor": "#0DA6E5", "shape": "circle", "svgColor": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\"><style>svg {enable-background:new 0 0 500 500}</style><style>.st2{fill:#2beda7}</style><linearGradient id=\"SVGID_1_\" x1=\"477.221\" x2=\"477.221\" y1=\"106.515\" y2=\"308.8\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_1_)\" d=\"M460.4 106.5v182.8l33.7 19.5V126z\"/><linearGradient id=\"SVGID_2_\" x1=\"25.459\" x2=\"25.459\" y1=\"106.52\" y2=\"308.812\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_2_)\" d=\"M8.6 308.8l33.7-19.5V106.5L8.6 126z\"/><path d=\"M173.8 307l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st2\"/><linearGradient id=\"SVGID_3_\" x1=\"477.221\" x2=\"477.221\" y1=\"196.062\" y2=\"382.938\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_3_)\" d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4z\"/><linearGradient id=\"SVGID_4_\" x1=\"403.048\" x2=\"403.048\" y1=\"238.884\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_4_)\" d=\"M394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5z\"/><linearGradient id=\"SVGID_5_\" x1=\"328.877\" x2=\"328.877\" y1=\"281.704\" y2=\"472.469\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_5_)\" d=\"M312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3z\"/><linearGradient id=\"SVGID_6_\" x1=\"173.82\" x2=\"173.82\" y1=\"281.704\" y2=\"472.466\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_6_)\" d=\"M159.6 454.5l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6z\"/><linearGradient id=\"SVGID_7_\" x1=\"99.649\" x2=\"99.649\" y1=\"238.883\" y2=\"425.76\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_7_)\" d=\"M86.8 412.5l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7z\"/><linearGradient id=\"SVGID_8_\" x1=\"25.478\" x2=\"25.478\" y1=\"196.059\" y2=\"382.936\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2beda7\"/><stop offset=\"1\" stop-color=\"#018afd\"/></linearGradient><path fill=\"url(#SVGID_8_)\" d=\"M12.6 369.7l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\"/><path d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st2\"/></svg>", "svgComplete": "", "svgWhite": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.21 12.29 504.92 469.42\"><style>svg {enable-background:new 0 0 500 500}</style><style>.st0{fill:#fff}</style><path d=\"M460.4 106.5v182.8l33.7 19.5V126zM8.6 308.8l33.7-19.5V106.5L8.6 126zm165.2-1.8l155.1 89.5v-38.9l-145.2-83.8-9.9 5.7zm164 141.4l-164-94.7v38.9l43.8 25.3-52.7 30.4c-3.5 2-3.5 7.2 0 9.2l25.7 14.9 60.7-35 60.7 35 25.7-14.9c3.6-1.9 3.6-7.1.1-9.1z\" class=\"st0\"/><path d=\"M460.4 215.5v162.1c0 4.1 4.4 6.7 8 4.6l23.1-13.3c1.6-.9 2.7-2.7 2.7-4.6V196.1l-33.8 19.4zM394.2 425l21.7-12.6c2.5-1.4 4-4.1 4-6.9V238.9l-33.7 19.5v162.1c0 4 4.4 6.6 8 4.5zM312 472.5l31.1-17.9c1.6-.9 2.7-2.7 2.7-4.6V281.7L312 301.2v171.3zm-152.4-18l31.1 17.9V301.2L157 281.7v168.2c0 1.9 1 3.7 2.6 4.6zm-72.8-42l21.7 12.6c3.5 2 8-.5 8-4.6V258.3l-33.7-19.5v166.7c0 2.9 1.5 5.6 4 7zm-74.2-42.8l21.7 12.6c3.5 2 8-.5 8-4.6V215.5L8.6 196.1v166.7c0 2.8 1.5 5.4 4 6.9z\" class=\"st0\"/><path d=\"M494.1 126l-33.7-19.5-60.7 35-40.4-23.3L412 87.8c3.5-2 3.5-7.2 0-9.2L390.2 66c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-40.4-23.3L337.8 45c3.5-2 3.5-7.2 0-9.2L316 23.2c-2.5-1.4-5.5-1.4-8 0l-56.7 32.7-56.7-32.7c-2.5-1.4-5.5-1.4-8 0l-21.8 12.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5-60.7-35 60.9-35.1zM112.5 66L90.8 78.6c-3.5 2-3.5 7.2 0 9.2l295.4 170.6 33.7-19.5L120.5 66c-2.5-1.4-5.5-1.4-8 0zM8.6 126l60.7 35-60.7 35.1 33.8 19.4 60.6-35 40.5 23.4-60.7 35 33.7 19.5 60.7-35.1 40.4 23.4-60.7 35 33.8 19.5 60.6-35.1 60.7 35.1 33.7-19.5L42.3 106.5z\" class=\"st0\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/linkerd/linkerd2/main/charts/linkerd-crds/"}, "configuration": null, "component": {"version": "gateway.networking.k8s.io/v1", "kind": "GRPCRoute", "schema": "{\n \"description\": \"GRPCRoute provides a way to route gRPC requests. This includes the capability\\nto match requests by hostname, gRPC service, gRPC method, or HTTP/2 header.\\nFilters can be used to specify additional processing steps. Backends specify\\nwhere matching requests will be routed.\\n\\n\\nGRPCRoute falls under extended support within the Gateway API. Within the\\nfollowing specification, the word \\\"MUST\\\" indicates that an implementation\\nsupporting GRPCRoute must conform to the indicated requirement, but an\\nimplementation not supporting this route type need not follow the requirement\\nunless explicitly indicated.\\n\\n\\nImplementations supporting `G<PERSON><PERSON>oute` with the `HTTPS` `ProtocolType` MUST\\naccept HTTP/2 connections without an initial upgrade from HTTP/1.1, i.e. via\\nALPN. If the implementation does not support this, then it MUST set the\\n\\\"Accepted\\\" condition to \\\"False\\\" for the affected listener with a reason of\\n\\\"UnsupportedProtocol\\\".  Implementations MAY also accept HTTP/2 connections\\nwith an upgrade from HTTP/1.\\n\\n\\nImplementations supporting `G<PERSON><PERSON>oute` with the `HTTP` `ProtocolType` MUST\\nsupport HTTP/2 over cleartext TCP (h2c,\\nhttps://www.rfc-editor.org/rfc/rfc7540#section-3.1) without an initial\\nupgrade from HTTP/1.1, i.e. with prior knowledge\\n(https://www.rfc-editor.org/rfc/rfc7540#section-3.4). If the implementation\\ndoes not support this, then it MUST set the \\\"Accepted\\\" condition to \\\"False\\\"\\nfor the affected listener with a reason of \\\"UnsupportedProtocol\\\".\\nImplementations MAY also accept HTTP/2 connections with an upgrade from\\nHTTP/1, i.e. without prior knowledge.\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Spec defines the desired state of GRPCRoute.\",\n   \"properties\": {\n    \"hostnames\": {\n     \"description\": \"Hostnames defines a set of hostnames to match against the GRPC\\nHost header to select a GRPCRoute to process the request. This matches\\nthe RFC 1123 definition of a hostname with 2 notable exceptions:\\n\\n\\n1. IPs are not allowed.\\n2. A hostname may be prefixed with a wildcard label (`*.`). The wildcard\\n   label MUST appear by itself as the first label.\\n\\n\\nIf a hostname is specified by both the Listener and GRPCRoute, there\\nMUST be at least one intersecting hostname for the GRPCRoute to be\\nattached to the Listener. For example:\\n\\n\\n* A Listener with `test.example.com` as the hostname matches GRPCRoutes\\n  that have either not specified any hostnames, or have specified at\\n  least one of `test.example.com` or `*.example.com`.\\n* A Listener with `*.example.com` as the hostname matches GRPCRoutes\\n  that have either not specified any hostnames or have specified at least\\n  one hostname that matches the Listener hostname. For example,\\n  `test.example.com` and `*.example.com` would both match. On the other\\n  hand, `example.com` and `test.example.net` would not match.\\n\\n\\nHostnames that are prefixed with a wildcard label (`*.`) are interpreted\\nas a suffix match. That means that a match for `*.example.com` would match\\nboth `test.example.com`, and `foo.test.example.com`, but not `example.com`.\\n\\n\\nIf both the Listener and GRPCRoute have specified hostnames, any\\nGRPCRoute hostnames that do not match the Listener hostname MUST be\\nignored. For example, if a Listener specified `*.example.com`, and the\\nGRPCRoute specified `test.example.com` and `test.example.net`,\\n`test.example.net` MUST NOT be considered for a match.\\n\\n\\nIf both the Listener and GRPCRoute have specified hostnames, and none\\nmatch with the criteria above, then the GRPCRoute MUST NOT be accepted by\\nthe implementation. The implementation MUST raise an 'Accepted' Condition\\nwith a status of `False` in the corresponding RouteParentStatus.\\n\\n\\nIf a Route (A) of type HTTPRoute or GRPCRoute is attached to a\\nListener and that listener already has another Route (B) of the other\\ntype attached and the intersection of the hostnames of A and B is\\nnon-empty, then the implementation MUST accept exactly one of these two\\nroutes, determined by the following criteria, in order:\\n\\n\\n* The oldest Route based on creation timestamp.\\n* The Route appearing first in alphabetical order by\\n  \\\"{namespace}/{name}\\\".\\n\\n\\nThe rejected Route MUST raise an 'Accepted' condition with a status of\\n'False' in the corresponding RouteParentStatus.\\n\\n\\nSupport: Core\",\n     \"items\": {\n      \"description\": \"Hostname is the fully qualified domain name of a network host. This matches\\nthe RFC 1123 definition of a hostname with 2 notable exceptions:\\n\\n\\n 1. IPs are not allowed.\\n 2. A hostname may be prefixed with a wildcard label (`*.`). The wildcard\\n    label must appear by itself as the first label.\\n\\n\\nHostname can be \\\"precise\\\" which is a domain name without the terminating\\ndot of a network host (e.g. \\\"foo.example.com\\\") or \\\"wildcard\\\", which is a\\ndomain name prefixed with a single wildcard label (e.g. `*.example.com`).\\n\\n\\nNote that as per RFC1035 and RFC1123, a *label* must consist of lower case\\nalphanumeric characters or '-', and must start and end with an alphanumeric\\ncharacter. No other punctuation is allowed.\",\n      \"maxLength\": 253,\n      \"minLength\": 1,\n      \"pattern\": \"^(\\\\*\\\\.)?[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n      \"type\": \"string\"\n     },\n     \"maxItems\": 16,\n     \"type\": \"array\"\n    },\n    \"parentRefs\": {\n     \"description\": \"ParentRefs references the resources (usually Gateways) that a Route wants\\nto be attached to. Note that the referenced parent resource needs to\\nallow this for the attachment to be complete. For Gateways, that means\\nthe Gateway needs to allow attachment from Routes of this kind and\\nnamespace. For Services, that means the Service must either be in the same\\nnamespace for a \\\"producer\\\" route, or the mesh implementation must support\\nand allow \\\"consumer\\\" routes for the referenced Service. ReferenceGrant is\\nnot applicable for governing ParentRefs to Services - it is not possible to\\ncreate a \\\"producer\\\" route for a Service in a different namespace from the\\nRoute.\\n\\n\\nThere are two kinds of parent resources with \\\"Core\\\" support:\\n\\n\\n* Gateway (Gateway conformance profile)\\n* Service (Mesh conformance profile, ClusterIP Services only)\\n\\n\\nThis API may be extended in the future to support additional kinds of parent\\nresources.\\n\\n\\nParentRefs must be _distinct_. This means either that:\\n\\n\\n* They select different objects.  If this is the case, then parentRef\\n  entries are distinct. In terms of fields, this means that the\\n  multi-part key defined by `group`, `kind`, `namespace`, and `name` must\\n  be unique across all parentRef entries in the Route.\\n* They do not select different objects, but for each optional field used,\\n  each ParentRef that selects the same object must set the same set of\\n  optional fields to different values. If one ParentRef sets a\\n  combination of optional fields, all must set the same combination.\\n\\n\\nSome examples:\\n\\n\\n* If one ParentRef sets `sectionName`, all ParentRefs referencing the\\n  same object must also set `sectionName`.\\n* If one ParentRef sets `port`, all ParentRefs referencing the same\\n  object must also set `port`.\\n* If one ParentRef sets `sectionName` and `port`, all ParentRefs\\n  referencing the same object must also set `sectionName` and `port`.\\n\\n\\nIt is possible to separately reference multiple distinct objects that may\\nbe collapsed by an implementation. For example, some implementations may\\nchoose to merge compatible Gateway Listeners together. If that is the\\ncase, the list of routes attached to those resources should also be\\nmerged.\\n\\n\\nNote that for ParentRefs that cross namespace boundaries, there are specific\\nrules. Cross-namespace references are only valid if they are explicitly\\nallowed by something in the namespace they are referring to. For example,\\nGateway has the AllowedRoutes field, and ReferenceGrant provides a\\ngeneric way to enable other kinds of cross-namespace reference.\\n\\n\\n\\nParentRefs from a Route to a Service in the same namespace are \\\"producer\\\"\\nroutes, which apply default routing rules to inbound connections from\\nany namespace to the Service.\\n\\n\\nParentRefs from a Route to a Service in a different namespace are\\n\\\"consumer\\\" routes, and these routing rules are only applied to outbound\\nconnections originating from the same namespace as the Route, for which\\nthe intended destination of the connections are a Service targeted as a\\nParentRef of the Route.\\n\\n\\n\\n\\n\\n\\n\",\n     \"items\": {\n      \"description\": \"ParentReference identifies an API object (usually a Gateway) that can be considered\\na parent of this resource (usually a route). There are two kinds of parent resources\\nwith \\\"Core\\\" support:\\n\\n\\n* Gateway (Gateway conformance profile)\\n* Service (Mesh conformance profile, ClusterIP Services only)\\n\\n\\nThis API may be extended in the future to support additional kinds of parent\\nresources.\\n\\n\\nThe API object must be valid in the cluster; the Group and Kind must\\nbe registered in the cluster for this reference to be valid.\",\n      \"properties\": {\n       \"group\": {\n        \"default\": \"gateway.networking.k8s.io\",\n        \"description\": \"Group is the group of the referent.\\nWhen unspecified, \\\"gateway.networking.k8s.io\\\" is inferred.\\nTo set the core API group (such as for a \\\"Service\\\" kind referent),\\nGroup must be explicitly set to \\\"\\\" (empty string).\\n\\n\\nSupport: Core\",\n        \"maxLength\": 253,\n        \"pattern\": \"^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n        \"type\": \"string\"\n       },\n       \"kind\": {\n        \"default\": \"Gateway\",\n        \"description\": \"Kind is kind of the referent.\\n\\n\\nThere are two kinds of parent resources with \\\"Core\\\" support:\\n\\n\\n* Gateway (Gateway conformance profile)\\n* Service (Mesh conformance profile, ClusterIP Services only)\\n\\n\\nSupport for other resources is Implementation-Specific.\",\n        \"maxLength\": 63,\n        \"minLength\": 1,\n        \"pattern\": \"^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$\",\n        \"type\": \"string\"\n       },\n       \"name\": {\n        \"description\": \"Name is the name of the referent.\\n\\n\\nSupport: Core\",\n        \"maxLength\": 253,\n        \"minLength\": 1,\n        \"type\": \"string\"\n       },\n       \"namespace\": {\n        \"description\": \"Namespace is the namespace of the referent. When unspecified, this refers\\nto the local namespace of the Route.\\n\\n\\nNote that there are specific rules for ParentRefs which cross namespace\\nboundaries. Cross-namespace references are only valid if they are explicitly\\nallowed by something in the namespace they are referring to. For example:\\nGateway has the AllowedRoutes field, and ReferenceGrant provides a\\ngeneric way to enable any other kind of cross-namespace reference.\\n\\n\\n\\nParentRefs from a Route to a Service in the same namespace are \\\"producer\\\"\\nroutes, which apply default routing rules to inbound connections from\\nany namespace to the Service.\\n\\n\\nParentRefs from a Route to a Service in a different namespace are\\n\\\"consumer\\\" routes, and these routing rules are only applied to outbound\\nconnections originating from the same namespace as the Route, for which\\nthe intended destination of the connections are a Service targeted as a\\nParentRef of the Route.\\n\\n\\n\\nSupport: Core\",\n        \"maxLength\": 63,\n        \"minLength\": 1,\n        \"pattern\": \"^[a-z0-9]([-a-z0-9]*[a-z0-9])?$\",\n        \"type\": \"string\"\n       },\n       \"port\": {\n        \"description\": \"Port is the network port this Route targets. It can be interpreted\\ndifferently based on the type of parent resource.\\n\\n\\nWhen the parent resource is a Gateway, this targets all listeners\\nlistening on the specified port that also support this kind of Route(and\\nselect this Route). It's not recommended to set `Port` unless the\\nnetworking behaviors specified in a Route must apply to a specific port\\nas opposed to a listener(s) whose port(s) may be changed. When both Port\\nand SectionName are specified, the name and port of the selected listener\\nmust match both specified values.\\n\\n\\n\\nWhen the parent resource is a Service, this targets a specific port in the\\nService spec. When both Port (experimental) and SectionName are specified,\\nthe name and port of the selected port must match both specified values.\\n\\n\\n\\nImplementations MAY choose to support other parent resources.\\nImplementations supporting other types of parent resources MUST clearly\\ndocument how/if Port is interpreted.\\n\\n\\nFor the purpose of status, an attachment is considered successful as\\nlong as the parent resource accepts it partially. For example, Gateway\\nlisteners can restrict which Routes can attach to them by Route kind,\\nnamespace, or hostname. If 1 of 2 Gateway listeners accept attachment\\nfrom the referencing Route, the Route MUST be considered successfully\\nattached. If no Gateway listeners accept attachment from this Route,\\nthe Route MUST be considered detached from the Gateway.\\n\\n\\nSupport: Extended\",\n        \"format\": \"int32\",\n        \"maximum\": 65535,\n        \"minimum\": 1,\n        \"type\": \"integer\"\n       },\n       \"sectionName\": {\n        \"description\": \"SectionName is the name of a section within the target resource. In the\\nfollowing resources, SectionName is interpreted as the following:\\n\\n\\n* Gateway: Listener name. When both Port (experimental) and SectionName\\nare specified, the name and port of the selected listener must match\\nboth specified values.\\n* Service: Port name. When both Port (experimental) and SectionName\\nare specified, the name and port of the selected listener must match\\nboth specified values.\\n\\n\\nImplementations MAY choose to support attaching Routes to other resources.\\nIf that is the case, they MUST clearly document how SectionName is\\ninterpreted.\\n\\n\\nWhen unspecified (empty string), this will reference the entire resource.\\nFor the purpose of status, an attachment is considered successful if at\\nleast one section in the parent resource accepts it. For example, Gateway\\nlisteners can restrict which Routes can attach to them by Route kind,\\nnamespace, or hostname. If 1 of 2 Gateway listeners accept attachment from\\nthe referencing Route, the Route MUST be considered successfully\\nattached. If no Gateway listeners accept attachment from this Route, the\\nRoute MUST be considered detached from the Gateway.\\n\\n\\nSupport: Core\",\n        \"maxLength\": 253,\n        \"minLength\": 1,\n        \"pattern\": \"^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"maxItems\": 32,\n     \"type\": \"array\",\n     \"x-kubernetes-validations\": [\n      {\n       \"message\": \"sectionName or port must be specified when parentRefs includes 2 or more references to the same parent\",\n       \"rule\": \"self.all(p1, self.all(p2, p1.group == p2.group \\u0026\\u0026 p1.kind == p2.kind \\u0026\\u0026 p1.name == p2.name \\u0026\\u0026 (((!has(p1.__namespace__) || p1.__namespace__ == '') \\u0026\\u0026 (!has(p2.__namespace__) || p2.__namespace__ == '')) || (has(p1.__namespace__) \\u0026\\u0026 has(p2.__namespace__) \\u0026\\u0026 p1.__namespace__ == p2.__namespace__)) ? ((!has(p1.sectionName) || p1.sectionName == '') == (!has(p2.sectionName) || p2.sectionName == '') \\u0026\\u0026 (!has(p1.port) || p1.port == 0) == (!has(p2.port) || p2.port == 0)): true))\"\n      },\n      {\n       \"message\": \"sectionName or port must be unique when parentRefs includes 2 or more references to the same parent\",\n       \"rule\": \"self.all(p1, self.exists_one(p2, p1.group == p2.group \\u0026\\u0026 p1.kind == p2.kind \\u0026\\u0026 p1.name == p2.name \\u0026\\u0026 (((!has(p1.__namespace__) || p1.__namespace__ == '') \\u0026\\u0026 (!has(p2.__namespace__) || p2.__namespace__ == '')) || (has(p1.__namespace__) \\u0026\\u0026 has(p2.__namespace__) \\u0026\\u0026 p1.__namespace__ == p2.__namespace__ )) \\u0026\\u0026 (((!has(p1.sectionName) || p1.sectionName == '') \\u0026\\u0026 (!has(p2.sectionName) || p2.sectionName == '')) || ( has(p1.sectionName) \\u0026\\u0026 has(p2.sectionName) \\u0026\\u0026 p1.sectionName == p2.sectionName)) \\u0026\\u0026 (((!has(p1.port) || p1.port == 0) \\u0026\\u0026 (!has(p2.port) || p2.port == 0)) || (has(p1.port) \\u0026\\u0026 has(p2.port) \\u0026\\u0026 p1.port == p2.port))))\"\n      }\n     ]\n    },\n    \"rules\": {\n     \"description\": \"Rules are a list of GRPC matchers, filters and actions.\",\n     \"items\": {\n      \"description\": \"GRPCRouteRule defines the semantics for matching a gRPC request based on\\nconditions (matches), processing it (filters), and forwarding the request to\\nan API object (backendRefs).\",\n      \"properties\": {\n       \"backendRefs\": {\n        \"description\": \"BackendRefs defines the backend(s) where matching requests should be\\nsent.\\n\\n\\nFailure behavior here depends on how many BackendRefs are specified and\\nhow many are invalid.\\n\\n\\nIf *all* entries in BackendRefs are invalid, and there are also no filters\\nspecified in this route rule, *all* traffic which matches this rule MUST\\nreceive an `UNAVAILABLE` status.\\n\\n\\nSee the GRPCBackendRef definition for the rules about what makes a single\\nGRPCBackendRef invalid.\\n\\n\\nWhen a GRPCBackendRef is invalid, `UNAVAILABLE` statuses MUST be returned for\\nrequests that would have otherwise been routed to an invalid backend. If\\nmultiple backends are specified, and some are invalid, the proportion of\\nrequests that would otherwise have been routed to an invalid backend\\nMUST receive an `UNAVAILABLE` status.\\n\\n\\nFor example, if two backends are specified with equal weights, and one is\\ninvalid, 50 percent of traffic MUST receive an `UNAVAILABLE` status.\\nImplementations may choose how that 50 percent is determined.\\n\\n\\nSupport: Core for Kubernetes Service\\n\\n\\nSupport: Implementation-specific for any other resource\\n\\n\\nSupport for weight: Core\",\n        \"items\": {\n         \"description\": \"GRPCBackendRef defines how a GRPCRoute forwards a gRPC request.\\n\\n\\nNote that when a namespace different than the local namespace is specified, a\\nReferenceGrant object is required in the referent namespace to allow that\\nnamespace's owner to accept the reference. See the ReferenceGrant\\ndocumentation for details.\\n\\n\\n\\u003cgateway:experimental:description\\u003e\\n\\n\\nWhen the BackendRef points to a Kubernetes Service, implementations SHOULD\\nhonor the appProtocol field if it is set for the target Service Port.\\n\\n\\nImplementations supporting appProtocol SHOULD recognize the Kubernetes\\nStandard Application Protocols defined in KEP-3726.\\n\\n\\nIf a Service appProtocol isn't specified, an implementation MAY infer the\\nbackend protocol through its own means. Implementations MAY infer the\\nprotocol from the Route type referring to the backend Service.\\n\\n\\nIf a Route is not able to send traffic to the backend using the specified\\nprotocol then the backend is considered invalid. Implementations MUST set the\\n\\\"ResolvedRefs\\\" condition to \\\"False\\\" with the \\\"UnsupportedProtocol\\\" reason.\\n\\n\\n\\u003c/gateway:experimental:description\\u003e\",\n         \"properties\": {\n          \"filters\": {\n           \"description\": \"Filters defined at this level MUST be executed if and only if the\\nrequest is being forwarded to the backend defined here.\\n\\n\\nSupport: Implementation-specific (For broader support of filters, use the\\nFilters field in GRPCRouteRule.)\",\n           \"items\": {\n            \"description\": \"GRPCRouteFilter defines processing steps that must be completed during the\\nrequest or response lifecycle. GRPCRouteFilters are meant as an extension\\npoint to express processing that may be done in Gateway implementations. Some\\nexamples include request or response modification, implementing\\nauthentication strategies, rate-limiting, and traffic shaping. API\\nguarantee/conformance is defined based on the type of the filter.\",\n            \"properties\": {\n             \"extensionRef\": {\n              \"description\": \"ExtensionRef is an optional, implementation-specific extension to the\\n\\\"filter\\\" behavior.  For example, resource \\\"myroutefilter\\\" in group\\n\\\"networking.example.net\\\"). ExtensionRef MUST NOT be used for core and\\nextended filters.\\n\\n\\nSupport: Implementation-specific\\n\\n\\nThis filter can be used multiple times within the same rule.\",\n              \"properties\": {\n               \"group\": {\n                \"description\": \"Group is the group of the referent. For example, \\\"gateway.networking.k8s.io\\\".\\nWhen unspecified or empty string, core API group is inferred.\",\n                \"maxLength\": 253,\n                \"pattern\": \"^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind is kind of the referent. For example \\\"HTTPRoute\\\" or \\\"Service\\\".\",\n                \"maxLength\": 63,\n                \"minLength\": 1,\n                \"pattern\": \"^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name is the name of the referent.\",\n                \"maxLength\": 253,\n                \"minLength\": 1,\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"group\",\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"requestHeaderModifier\": {\n              \"description\": \"RequestHeaderModifier defines a schema for a filter that modifies request\\nheaders.\\n\\n\\nSupport: Core\",\n              \"properties\": {\n               \"add\": {\n                \"description\": \"Add adds the given header(s) (name, value) to the request\\nbefore the action. It appends to any existing values associated\\nwith the header name.\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header: foo\\n\\n\\nConfig:\\n  add:\\n  - name: \\\"my-header\\\"\\n    value: \\\"bar,baz\\\"\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header: foo,bar,baz\",\n                \"items\": {\n                 \"description\": \"HTTPHeader represents an HTTP Header name and value as defined by RFC 7230.\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"Name is the name of the HTTP Header to be matched. Name matching MUST be\\ncase insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).\\n\\n\\nIf multiple entries specify equivalent header names, the first entry with\\nan equivalent name MUST be considered for a match. Subsequent entries\\nwith an equivalent header name MUST be ignored. Due to the\\ncase-insensitivity of header names, \\\"foo\\\" and \\\"Foo\\\" are considered\\nequivalent.\",\n                   \"maxLength\": 256,\n                   \"minLength\": 1,\n                   \"pattern\": \"^[A-Za-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"Value is the value of HTTP Header to be matched.\",\n                   \"maxLength\": 4096,\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"maxItems\": 16,\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"name\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"remove\": {\n                \"description\": \"Remove the given header(s) from the HTTP request before the action. The\\nvalue of Remove is a list of HTTP header names. Note that the header\\nnames are case-insensitive (see\\nhttps://datatracker.ietf.org/doc/html/rfc2616#section-4.2).\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header1: foo\\n  my-header2: bar\\n  my-header3: baz\\n\\n\\nConfig:\\n  remove: [\\\"my-header1\\\", \\\"my-header3\\\"]\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header2: bar\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"maxItems\": 16,\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"set\"\n               },\n               \"set\": {\n                \"description\": \"Set overwrites the request with the given header (name, value)\\nbefore the action.\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header: foo\\n\\n\\nConfig:\\n  set:\\n  - name: \\\"my-header\\\"\\n    value: \\\"bar\\\"\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header: bar\",\n                \"items\": {\n                 \"description\": \"HTTPHeader represents an HTTP Header name and value as defined by RFC 7230.\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"Name is the name of the HTTP Header to be matched. Name matching MUST be\\ncase insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).\\n\\n\\nIf multiple entries specify equivalent header names, the first entry with\\nan equivalent name MUST be considered for a match. Subsequent entries\\nwith an equivalent header name MUST be ignored. Due to the\\ncase-insensitivity of header names, \\\"foo\\\" and \\\"Foo\\\" are considered\\nequivalent.\",\n                   \"maxLength\": 256,\n                   \"minLength\": 1,\n                   \"pattern\": \"^[A-Za-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"Value is the value of HTTP Header to be matched.\",\n                   \"maxLength\": 4096,\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"maxItems\": 16,\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"name\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"requestMirror\": {\n              \"description\": \"RequestMirror defines a schema for a filter that mirrors requests.\\nRequests are sent to the specified destination, but responses from\\nthat destination are ignored.\\n\\n\\nThis filter can be used multiple times within the same rule. Note that\\nnot all implementations will be able to support mirroring to multiple\\nbackends.\\n\\n\\nSupport: Extended\",\n              \"properties\": {\n               \"backendRef\": {\n                \"description\": \"BackendRef references a resource where mirrored requests are sent.\\n\\n\\nMirrored requests must be sent only to a single destination endpoint\\nwithin this BackendRef, irrespective of how many endpoints are present\\nwithin this BackendRef.\\n\\n\\nIf the referent cannot be found, this BackendRef is invalid and must be\\ndropped from the Gateway. The controller must ensure the \\\"ResolvedRefs\\\"\\ncondition on the Route status is set to `status: False` and not configure\\nthis backend in the underlying implementation.\\n\\n\\nIf there is a cross-namespace reference to an *existing* object\\nthat is not allowed by a ReferenceGrant, the controller must ensure the\\n\\\"ResolvedRefs\\\"  condition on the Route is set to `status: False`,\\nwith the \\\"RefNotPermitted\\\" reason and not configure this backend in the\\nunderlying implementation.\\n\\n\\nIn either error case, the Message of the `ResolvedRefs` Condition\\nshould be used to provide more detail about the problem.\\n\\n\\nSupport: Extended for Kubernetes Service\\n\\n\\nSupport: Implementation-specific for any other resource\",\n                \"properties\": {\n                 \"group\": {\n                  \"default\": \"\",\n                  \"description\": \"Group is the group of the referent. For example, \\\"gateway.networking.k8s.io\\\".\\nWhen unspecified or empty string, core API group is inferred.\",\n                  \"maxLength\": 253,\n                  \"pattern\": \"^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n                  \"type\": \"string\"\n                 },\n                 \"kind\": {\n                  \"default\": \"Service\",\n                  \"description\": \"Kind is the Kubernetes resource kind of the referent. For example\\n\\\"Service\\\".\\n\\n\\nDefaults to \\\"Service\\\" when not specified.\\n\\n\\nExternalName services can refer to CNAME DNS records that may live\\noutside of the cluster and as such are difficult to reason about in\\nterms of conformance. They also may not be safe to forward to (see\\nCVE-2021-25740 for more information). Implementations SHOULD NOT\\nsupport ExternalName Services.\\n\\n\\nSupport: Core (Services with a type other than ExternalName)\\n\\n\\nSupport: Implementation-specific (Services with type ExternalName)\",\n                  \"maxLength\": 63,\n                  \"minLength\": 1,\n                  \"pattern\": \"^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$\",\n                  \"type\": \"string\"\n                 },\n                 \"name\": {\n                  \"description\": \"Name is the name of the referent.\",\n                  \"maxLength\": 253,\n                  \"minLength\": 1,\n                  \"type\": \"string\"\n                 },\n                 \"namespace\": {\n                  \"description\": \"Namespace is the namespace of the backend. When unspecified, the local\\nnamespace is inferred.\\n\\n\\nNote that when a namespace different than the local namespace is specified,\\na ReferenceGrant object is required in the referent namespace to allow that\\nnamespace's owner to accept the reference. See the ReferenceGrant\\ndocumentation for details.\\n\\n\\nSupport: Core\",\n                  \"maxLength\": 63,\n                  \"minLength\": 1,\n                  \"pattern\": \"^[a-z0-9]([-a-z0-9]*[a-z0-9])?$\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"description\": \"Port specifies the destination port number to use for this resource.\\nPort is required when the referent is a Kubernetes Service. In this\\ncase, the port number is the service port number, not the target port.\\nFor other resources, destination port might be derived from the referent\\nresource or this field.\",\n                  \"format\": \"int32\",\n                  \"maximum\": 65535,\n                  \"minimum\": 1,\n                  \"type\": \"integer\"\n                 }\n                },\n                \"required\": [\n                 \"name\"\n                ],\n                \"type\": \"object\",\n                \"x-kubernetes-validations\": [\n                 {\n                  \"message\": \"Must have port for Service reference\",\n                  \"rule\": \"(size(self.group) == 0 \\u0026\\u0026 self.kind == 'Service') ? has(self.port) : true\"\n                 }\n                ]\n               }\n              },\n              \"required\": [\n               \"backendRef\"\n              ],\n              \"type\": \"object\"\n             },\n             \"responseHeaderModifier\": {\n              \"description\": \"ResponseHeaderModifier defines a schema for a filter that modifies response\\nheaders.\\n\\n\\nSupport: Extended\",\n              \"properties\": {\n               \"add\": {\n                \"description\": \"Add adds the given header(s) (name, value) to the request\\nbefore the action. It appends to any existing values associated\\nwith the header name.\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header: foo\\n\\n\\nConfig:\\n  add:\\n  - name: \\\"my-header\\\"\\n    value: \\\"bar,baz\\\"\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header: foo,bar,baz\",\n                \"items\": {\n                 \"description\": \"HTTPHeader represents an HTTP Header name and value as defined by RFC 7230.\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"Name is the name of the HTTP Header to be matched. Name matching MUST be\\ncase insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).\\n\\n\\nIf multiple entries specify equivalent header names, the first entry with\\nan equivalent name MUST be considered for a match. Subsequent entries\\nwith an equivalent header name MUST be ignored. Due to the\\ncase-insensitivity of header names, \\\"foo\\\" and \\\"Foo\\\" are considered\\nequivalent.\",\n                   \"maxLength\": 256,\n                   \"minLength\": 1,\n                   \"pattern\": \"^[A-Za-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"Value is the value of HTTP Header to be matched.\",\n                   \"maxLength\": 4096,\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"maxItems\": 16,\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"name\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               },\n               \"remove\": {\n                \"description\": \"Remove the given header(s) from the HTTP request before the action. The\\nvalue of Remove is a list of HTTP header names. Note that the header\\nnames are case-insensitive (see\\nhttps://datatracker.ietf.org/doc/html/rfc2616#section-4.2).\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header1: foo\\n  my-header2: bar\\n  my-header3: baz\\n\\n\\nConfig:\\n  remove: [\\\"my-header1\\\", \\\"my-header3\\\"]\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header2: bar\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"maxItems\": 16,\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"set\"\n               },\n               \"set\": {\n                \"description\": \"Set overwrites the request with the given header (name, value)\\nbefore the action.\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header: foo\\n\\n\\nConfig:\\n  set:\\n  - name: \\\"my-header\\\"\\n    value: \\\"bar\\\"\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header: bar\",\n                \"items\": {\n                 \"description\": \"HTTPHeader represents an HTTP Header name and value as defined by RFC 7230.\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"Name is the name of the HTTP Header to be matched. Name matching MUST be\\ncase insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).\\n\\n\\nIf multiple entries specify equivalent header names, the first entry with\\nan equivalent name MUST be considered for a match. Subsequent entries\\nwith an equivalent header name MUST be ignored. Due to the\\ncase-insensitivity of header names, \\\"foo\\\" and \\\"Foo\\\" are considered\\nequivalent.\",\n                   \"maxLength\": 256,\n                   \"minLength\": 1,\n                   \"pattern\": \"^[A-Za-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"Value is the value of HTTP Header to be matched.\",\n                   \"maxLength\": 4096,\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"maxItems\": 16,\n                \"type\": \"array\",\n                \"x-kubernetes-list-map-keys\": [\n                 \"name\"\n                ],\n                \"x-kubernetes-list-type\": \"map\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"type\": {\n              \"description\": \"Type identifies the type of filter to apply. As with other API fields,\\ntypes are classified into three conformance levels:\\n\\n\\n- Core: Filter types and their corresponding configuration defined by\\n  \\\"Support: Core\\\" in this package, e.g. \\\"RequestHeaderModifier\\\". All\\n  implementations supporting GRPCRoute MUST support core filters.\\n\\n\\n- Extended: Filter types and their corresponding configuration defined by\\n  \\\"Support: Extended\\\" in this package, e.g. \\\"RequestMirror\\\". Implementers\\n  are encouraged to support extended filters.\\n\\n\\n- Implementation-specific: Filters that are defined and supported by specific vendors.\\n  In the future, filters showing convergence in behavior across multiple\\n  implementations will be considered for inclusion in extended or core\\n  conformance levels. Filter-specific configuration for such filters\\n  is specified using the ExtensionRef field. `Type` MUST be set to\\n  \\\"ExtensionRef\\\" for custom filters.\\n\\n\\nImplementers are encouraged to define custom implementation types to\\nextend the core API with implementation-specific behavior.\\n\\n\\nIf a reference to a custom filter type cannot be resolved, the filter\\nMUST NOT be skipped. Instead, requests that would have been processed by\\nthat filter MUST receive a HTTP error response.\\n\\n\\n\",\n              \"enum\": [\n               \"ResponseHeaderModifier\",\n               \"RequestHeaderModifier\",\n               \"RequestMirror\",\n               \"ExtensionRef\"\n              ],\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"type\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-validations\": [\n             {\n              \"message\": \"filter.requestHeaderModifier must be nil if the filter.type is not RequestHeaderModifier\",\n              \"rule\": \"!(has(self.requestHeaderModifier) \\u0026\\u0026 self.type != 'RequestHeaderModifier')\"\n             },\n             {\n              \"message\": \"filter.requestHeaderModifier must be specified for RequestHeaderModifier filter.type\",\n              \"rule\": \"!(!has(self.requestHeaderModifier) \\u0026\\u0026 self.type == 'RequestHeaderModifier')\"\n             },\n             {\n              \"message\": \"filter.responseHeaderModifier must be nil if the filter.type is not ResponseHeaderModifier\",\n              \"rule\": \"!(has(self.responseHeaderModifier) \\u0026\\u0026 self.type != 'ResponseHeaderModifier')\"\n             },\n             {\n              \"message\": \"filter.responseHeaderModifier must be specified for ResponseHeaderModifier filter.type\",\n              \"rule\": \"!(!has(self.responseHeaderModifier) \\u0026\\u0026 self.type == 'ResponseHeaderModifier')\"\n             },\n             {\n              \"message\": \"filter.requestMirror must be nil if the filter.type is not RequestMirror\",\n              \"rule\": \"!(has(self.requestMirror) \\u0026\\u0026 self.type != 'RequestMirror')\"\n             },\n             {\n              \"message\": \"filter.requestMirror must be specified for RequestMirror filter.type\",\n              \"rule\": \"!(!has(self.requestMirror) \\u0026\\u0026 self.type == 'RequestMirror')\"\n             },\n             {\n              \"message\": \"filter.extensionRef must be nil if the filter.type is not ExtensionRef\",\n              \"rule\": \"!(has(self.extensionRef) \\u0026\\u0026 self.type != 'ExtensionRef')\"\n             },\n             {\n              \"message\": \"filter.extensionRef must be specified for ExtensionRef filter.type\",\n              \"rule\": \"!(!has(self.extensionRef) \\u0026\\u0026 self.type == 'ExtensionRef')\"\n             }\n            ]\n           },\n           \"maxItems\": 16,\n           \"type\": \"array\",\n           \"x-kubernetes-validations\": [\n            {\n             \"message\": \"RequestHeaderModifier filter cannot be repeated\",\n             \"rule\": \"self.filter(f, f.type == 'RequestHeaderModifier').size() \\u003c= 1\"\n            },\n            {\n             \"message\": \"ResponseHeaderModifier filter cannot be repeated\",\n             \"rule\": \"self.filter(f, f.type == 'ResponseHeaderModifier').size() \\u003c= 1\"\n            }\n           ]\n          },\n          \"group\": {\n           \"default\": \"\",\n           \"description\": \"Group is the group of the referent. For example, \\\"gateway.networking.k8s.io\\\".\\nWhen unspecified or empty string, core API group is inferred.\",\n           \"maxLength\": 253,\n           \"pattern\": \"^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n           \"type\": \"string\"\n          },\n          \"kind\": {\n           \"default\": \"Service\",\n           \"description\": \"Kind is the Kubernetes resource kind of the referent. For example\\n\\\"Service\\\".\\n\\n\\nDefaults to \\\"Service\\\" when not specified.\\n\\n\\nExternalName services can refer to CNAME DNS records that may live\\noutside of the cluster and as such are difficult to reason about in\\nterms of conformance. They also may not be safe to forward to (see\\nCVE-2021-25740 for more information). Implementations SHOULD NOT\\nsupport ExternalName Services.\\n\\n\\nSupport: Core (Services with a type other than ExternalName)\\n\\n\\nSupport: Implementation-specific (Services with type ExternalName)\",\n           \"maxLength\": 63,\n           \"minLength\": 1,\n           \"pattern\": \"^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"Name is the name of the referent.\",\n           \"maxLength\": 253,\n           \"minLength\": 1,\n           \"type\": \"string\"\n          },\n          \"namespace\": {\n           \"description\": \"Namespace is the namespace of the backend. When unspecified, the local\\nnamespace is inferred.\\n\\n\\nNote that when a namespace different than the local namespace is specified,\\na ReferenceGrant object is required in the referent namespace to allow that\\nnamespace's owner to accept the reference. See the ReferenceGrant\\ndocumentation for details.\\n\\n\\nSupport: Core\",\n           \"maxLength\": 63,\n           \"minLength\": 1,\n           \"pattern\": \"^[a-z0-9]([-a-z0-9]*[a-z0-9])?$\",\n           \"type\": \"string\"\n          },\n          \"port\": {\n           \"description\": \"Port specifies the destination port number to use for this resource.\\nPort is required when the referent is a Kubernetes Service. In this\\ncase, the port number is the service port number, not the target port.\\nFor other resources, destination port might be derived from the referent\\nresource or this field.\",\n           \"format\": \"int32\",\n           \"maximum\": 65535,\n           \"minimum\": 1,\n           \"type\": \"integer\"\n          },\n          \"weight\": {\n           \"default\": 1,\n           \"description\": \"Weight specifies the proportion of requests forwarded to the referenced\\nbackend. This is computed as weight/(sum of all weights in this\\nBackendRefs list). For non-zero values, there may be some epsilon from\\nthe exact proportion defined here depending on the precision an\\nimplementation supports. Weight is not a percentage and the sum of\\nweights does not need to equal 100.\\n\\n\\nIf only one backend is specified and it has a weight greater than 0, 100%\\nof the traffic is forwarded to that backend. If weight is set to 0, no\\ntraffic should be forwarded for this entry. If unspecified, weight\\ndefaults to 1.\\n\\n\\nSupport for this field varies based on the context where used.\",\n           \"format\": \"int32\",\n           \"maximum\": 1000000,\n           \"minimum\": 0,\n           \"type\": \"integer\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-validations\": [\n          {\n           \"message\": \"Must have port for Service reference\",\n           \"rule\": \"(size(self.group) == 0 \\u0026\\u0026 self.kind == 'Service') ? has(self.port) : true\"\n          }\n         ]\n        },\n        \"maxItems\": 16,\n        \"type\": \"array\"\n       },\n       \"filters\": {\n        \"description\": \"Filters define the filters that are applied to requests that match\\nthis rule.\\n\\n\\nThe effects of ordering of multiple behaviors are currently unspecified.\\nThis can change in the future based on feedback during the alpha stage.\\n\\n\\nConformance-levels at this level are defined based on the type of filter:\\n\\n\\n- ALL core filters MUST be supported by all implementations that support\\n  GRPCRoute.\\n- Implementers are encouraged to support extended filters.\\n- Implementation-specific custom filters have no API guarantees across\\n  implementations.\\n\\n\\nSpecifying the same filter multiple times is not supported unless explicitly\\nindicated in the filter.\\n\\n\\nIf an implementation can not support a combination of filters, it must clearly\\ndocument that limitation. In cases where incompatible or unsupported\\nfilters are specified and cause the `Accepted` condition to be set to status\\n`False`, implementations may use the `IncompatibleFilters` reason to specify\\nthis configuration error.\\n\\n\\nSupport: Core\",\n        \"items\": {\n         \"description\": \"GRPCRouteFilter defines processing steps that must be completed during the\\nrequest or response lifecycle. GRPCRouteFilters are meant as an extension\\npoint to express processing that may be done in Gateway implementations. Some\\nexamples include request or response modification, implementing\\nauthentication strategies, rate-limiting, and traffic shaping. API\\nguarantee/conformance is defined based on the type of the filter.\",\n         \"properties\": {\n          \"extensionRef\": {\n           \"description\": \"ExtensionRef is an optional, implementation-specific extension to the\\n\\\"filter\\\" behavior.  For example, resource \\\"myroutefilter\\\" in group\\n\\\"networking.example.net\\\"). ExtensionRef MUST NOT be used for core and\\nextended filters.\\n\\n\\nSupport: Implementation-specific\\n\\n\\nThis filter can be used multiple times within the same rule.\",\n           \"properties\": {\n            \"group\": {\n             \"description\": \"Group is the group of the referent. For example, \\\"gateway.networking.k8s.io\\\".\\nWhen unspecified or empty string, core API group is inferred.\",\n             \"maxLength\": 253,\n             \"pattern\": \"^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind is kind of the referent. For example \\\"HTTPRoute\\\" or \\\"Service\\\".\",\n             \"maxLength\": 63,\n             \"minLength\": 1,\n             \"pattern\": \"^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name is the name of the referent.\",\n             \"maxLength\": 253,\n             \"minLength\": 1,\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"group\",\n            \"kind\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"requestHeaderModifier\": {\n           \"description\": \"RequestHeaderModifier defines a schema for a filter that modifies request\\nheaders.\\n\\n\\nSupport: Core\",\n           \"properties\": {\n            \"add\": {\n             \"description\": \"Add adds the given header(s) (name, value) to the request\\nbefore the action. It appends to any existing values associated\\nwith the header name.\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header: foo\\n\\n\\nConfig:\\n  add:\\n  - name: \\\"my-header\\\"\\n    value: \\\"bar,baz\\\"\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header: foo,bar,baz\",\n             \"items\": {\n              \"description\": \"HTTPHeader represents an HTTP Header name and value as defined by RFC 7230.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name is the name of the HTTP Header to be matched. Name matching MUST be\\ncase insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).\\n\\n\\nIf multiple entries specify equivalent header names, the first entry with\\nan equivalent name MUST be considered for a match. Subsequent entries\\nwith an equivalent header name MUST be ignored. Due to the\\ncase-insensitivity of header names, \\\"foo\\\" and \\\"Foo\\\" are considered\\nequivalent.\",\n                \"maxLength\": 256,\n                \"minLength\": 1,\n                \"pattern\": \"^[A-Za-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                \"type\": \"string\"\n               },\n               \"value\": {\n                \"description\": \"Value is the value of HTTP Header to be matched.\",\n                \"maxLength\": 4096,\n                \"minLength\": 1,\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\",\n               \"value\"\n              ],\n              \"type\": \"object\"\n             },\n             \"maxItems\": 16,\n             \"type\": \"array\",\n             \"x-kubernetes-list-map-keys\": [\n              \"name\"\n             ],\n             \"x-kubernetes-list-type\": \"map\"\n            },\n            \"remove\": {\n             \"description\": \"Remove the given header(s) from the HTTP request before the action. The\\nvalue of Remove is a list of HTTP header names. Note that the header\\nnames are case-insensitive (see\\nhttps://datatracker.ietf.org/doc/html/rfc2616#section-4.2).\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header1: foo\\n  my-header2: bar\\n  my-header3: baz\\n\\n\\nConfig:\\n  remove: [\\\"my-header1\\\", \\\"my-header3\\\"]\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header2: bar\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"maxItems\": 16,\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"set\"\n            },\n            \"set\": {\n             \"description\": \"Set overwrites the request with the given header (name, value)\\nbefore the action.\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header: foo\\n\\n\\nConfig:\\n  set:\\n  - name: \\\"my-header\\\"\\n    value: \\\"bar\\\"\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header: bar\",\n             \"items\": {\n              \"description\": \"HTTPHeader represents an HTTP Header name and value as defined by RFC 7230.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name is the name of the HTTP Header to be matched. Name matching MUST be\\ncase insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).\\n\\n\\nIf multiple entries specify equivalent header names, the first entry with\\nan equivalent name MUST be considered for a match. Subsequent entries\\nwith an equivalent header name MUST be ignored. Due to the\\ncase-insensitivity of header names, \\\"foo\\\" and \\\"Foo\\\" are considered\\nequivalent.\",\n                \"maxLength\": 256,\n                \"minLength\": 1,\n                \"pattern\": \"^[A-Za-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                \"type\": \"string\"\n               },\n               \"value\": {\n                \"description\": \"Value is the value of HTTP Header to be matched.\",\n                \"maxLength\": 4096,\n                \"minLength\": 1,\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\",\n               \"value\"\n              ],\n              \"type\": \"object\"\n             },\n             \"maxItems\": 16,\n             \"type\": \"array\",\n             \"x-kubernetes-list-map-keys\": [\n              \"name\"\n             ],\n             \"x-kubernetes-list-type\": \"map\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"requestMirror\": {\n           \"description\": \"RequestMirror defines a schema for a filter that mirrors requests.\\nRequests are sent to the specified destination, but responses from\\nthat destination are ignored.\\n\\n\\nThis filter can be used multiple times within the same rule. Note that\\nnot all implementations will be able to support mirroring to multiple\\nbackends.\\n\\n\\nSupport: Extended\",\n           \"properties\": {\n            \"backendRef\": {\n             \"description\": \"BackendRef references a resource where mirrored requests are sent.\\n\\n\\nMirrored requests must be sent only to a single destination endpoint\\nwithin this BackendRef, irrespective of how many endpoints are present\\nwithin this BackendRef.\\n\\n\\nIf the referent cannot be found, this BackendRef is invalid and must be\\ndropped from the Gateway. The controller must ensure the \\\"ResolvedRefs\\\"\\ncondition on the Route status is set to `status: False` and not configure\\nthis backend in the underlying implementation.\\n\\n\\nIf there is a cross-namespace reference to an *existing* object\\nthat is not allowed by a ReferenceGrant, the controller must ensure the\\n\\\"ResolvedRefs\\\"  condition on the Route is set to `status: False`,\\nwith the \\\"RefNotPermitted\\\" reason and not configure this backend in the\\nunderlying implementation.\\n\\n\\nIn either error case, the Message of the `ResolvedRefs` Condition\\nshould be used to provide more detail about the problem.\\n\\n\\nSupport: Extended for Kubernetes Service\\n\\n\\nSupport: Implementation-specific for any other resource\",\n             \"properties\": {\n              \"group\": {\n               \"default\": \"\",\n               \"description\": \"Group is the group of the referent. For example, \\\"gateway.networking.k8s.io\\\".\\nWhen unspecified or empty string, core API group is inferred.\",\n               \"maxLength\": 253,\n               \"pattern\": \"^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$\",\n               \"type\": \"string\"\n              },\n              \"kind\": {\n               \"default\": \"Service\",\n               \"description\": \"Kind is the Kubernetes resource kind of the referent. For example\\n\\\"Service\\\".\\n\\n\\nDefaults to \\\"Service\\\" when not specified.\\n\\n\\nExternalName services can refer to CNAME DNS records that may live\\noutside of the cluster and as such are difficult to reason about in\\nterms of conformance. They also may not be safe to forward to (see\\nCVE-2021-25740 for more information). Implementations SHOULD NOT\\nsupport ExternalName Services.\\n\\n\\nSupport: Core (Services with a type other than ExternalName)\\n\\n\\nSupport: Implementation-specific (Services with type ExternalName)\",\n               \"maxLength\": 63,\n               \"minLength\": 1,\n               \"pattern\": \"^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of the referent.\",\n               \"maxLength\": 253,\n               \"minLength\": 1,\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"Namespace is the namespace of the backend. When unspecified, the local\\nnamespace is inferred.\\n\\n\\nNote that when a namespace different than the local namespace is specified,\\na ReferenceGrant object is required in the referent namespace to allow that\\nnamespace's owner to accept the reference. See the ReferenceGrant\\ndocumentation for details.\\n\\n\\nSupport: Core\",\n               \"maxLength\": 63,\n               \"minLength\": 1,\n               \"pattern\": \"^[a-z0-9]([-a-z0-9]*[a-z0-9])?$\",\n               \"type\": \"string\"\n              },\n              \"port\": {\n               \"description\": \"Port specifies the destination port number to use for this resource.\\nPort is required when the referent is a Kubernetes Service. In this\\ncase, the port number is the service port number, not the target port.\\nFor other resources, destination port might be derived from the referent\\nresource or this field.\",\n               \"format\": \"int32\",\n               \"maximum\": 65535,\n               \"minimum\": 1,\n               \"type\": \"integer\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-validations\": [\n              {\n               \"message\": \"Must have port for Service reference\",\n               \"rule\": \"(size(self.group) == 0 \\u0026\\u0026 self.kind == 'Service') ? has(self.port) : true\"\n              }\n             ]\n            }\n           },\n           \"required\": [\n            \"backendRef\"\n           ],\n           \"type\": \"object\"\n          },\n          \"responseHeaderModifier\": {\n           \"description\": \"ResponseHeaderModifier defines a schema for a filter that modifies response\\nheaders.\\n\\n\\nSupport: Extended\",\n           \"properties\": {\n            \"add\": {\n             \"description\": \"Add adds the given header(s) (name, value) to the request\\nbefore the action. It appends to any existing values associated\\nwith the header name.\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header: foo\\n\\n\\nConfig:\\n  add:\\n  - name: \\\"my-header\\\"\\n    value: \\\"bar,baz\\\"\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header: foo,bar,baz\",\n             \"items\": {\n              \"description\": \"HTTPHeader represents an HTTP Header name and value as defined by RFC 7230.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name is the name of the HTTP Header to be matched. Name matching MUST be\\ncase insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).\\n\\n\\nIf multiple entries specify equivalent header names, the first entry with\\nan equivalent name MUST be considered for a match. Subsequent entries\\nwith an equivalent header name MUST be ignored. Due to the\\ncase-insensitivity of header names, \\\"foo\\\" and \\\"Foo\\\" are considered\\nequivalent.\",\n                \"maxLength\": 256,\n                \"minLength\": 1,\n                \"pattern\": \"^[A-Za-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                \"type\": \"string\"\n               },\n               \"value\": {\n                \"description\": \"Value is the value of HTTP Header to be matched.\",\n                \"maxLength\": 4096,\n                \"minLength\": 1,\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\",\n               \"value\"\n              ],\n              \"type\": \"object\"\n             },\n             \"maxItems\": 16,\n             \"type\": \"array\",\n             \"x-kubernetes-list-map-keys\": [\n              \"name\"\n             ],\n             \"x-kubernetes-list-type\": \"map\"\n            },\n            \"remove\": {\n             \"description\": \"Remove the given header(s) from the HTTP request before the action. The\\nvalue of Remove is a list of HTTP header names. Note that the header\\nnames are case-insensitive (see\\nhttps://datatracker.ietf.org/doc/html/rfc2616#section-4.2).\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header1: foo\\n  my-header2: bar\\n  my-header3: baz\\n\\n\\nConfig:\\n  remove: [\\\"my-header1\\\", \\\"my-header3\\\"]\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header2: bar\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"maxItems\": 16,\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"set\"\n            },\n            \"set\": {\n             \"description\": \"Set overwrites the request with the given header (name, value)\\nbefore the action.\\n\\n\\nInput:\\n  GET /foo HTTP/1.1\\n  my-header: foo\\n\\n\\nConfig:\\n  set:\\n  - name: \\\"my-header\\\"\\n    value: \\\"bar\\\"\\n\\n\\nOutput:\\n  GET /foo HTTP/1.1\\n  my-header: bar\",\n             \"items\": {\n              \"description\": \"HTTPHeader represents an HTTP Header name and value as defined by RFC 7230.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name is the name of the HTTP Header to be matched. Name matching MUST be\\ncase insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).\\n\\n\\nIf multiple entries specify equivalent header names, the first entry with\\nan equivalent name MUST be considered for a match. Subsequent entries\\nwith an equivalent header name MUST be ignored. Due to the\\ncase-insensitivity of header names, \\\"foo\\\" and \\\"Foo\\\" are considered\\nequivalent.\",\n                \"maxLength\": 256,\n                \"minLength\": 1,\n                \"pattern\": \"^[A-Za-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n                \"type\": \"string\"\n               },\n               \"value\": {\n                \"description\": \"Value is the value of HTTP Header to be matched.\",\n                \"maxLength\": 4096,\n                \"minLength\": 1,\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"name\",\n               \"value\"\n              ],\n              \"type\": \"object\"\n             },\n             \"maxItems\": 16,\n             \"type\": \"array\",\n             \"x-kubernetes-list-map-keys\": [\n              \"name\"\n             ],\n             \"x-kubernetes-list-type\": \"map\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": {\n           \"description\": \"Type identifies the type of filter to apply. As with other API fields,\\ntypes are classified into three conformance levels:\\n\\n\\n- Core: Filter types and their corresponding configuration defined by\\n  \\\"Support: Core\\\" in this package, e.g. \\\"RequestHeaderModifier\\\". All\\n  implementations supporting GRPCRoute MUST support core filters.\\n\\n\\n- Extended: Filter types and their corresponding configuration defined by\\n  \\\"Support: Extended\\\" in this package, e.g. \\\"RequestMirror\\\". Implementers\\n  are encouraged to support extended filters.\\n\\n\\n- Implementation-specific: Filters that are defined and supported by specific vendors.\\n  In the future, filters showing convergence in behavior across multiple\\n  implementations will be considered for inclusion in extended or core\\n  conformance levels. Filter-specific configuration for such filters\\n  is specified using the ExtensionRef field. `Type` MUST be set to\\n  \\\"ExtensionRef\\\" for custom filters.\\n\\n\\nImplementers are encouraged to define custom implementation types to\\nextend the core API with implementation-specific behavior.\\n\\n\\nIf a reference to a custom filter type cannot be resolved, the filter\\nMUST NOT be skipped. Instead, requests that would have been processed by\\nthat filter MUST receive a HTTP error response.\\n\\n\\n\",\n           \"enum\": [\n            \"ResponseHeaderModifier\",\n            \"RequestHeaderModifier\",\n            \"RequestMirror\",\n            \"ExtensionRef\"\n           ],\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"type\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-validations\": [\n          {\n           \"message\": \"filter.requestHeaderModifier must be nil if the filter.type is not RequestHeaderModifier\",\n           \"rule\": \"!(has(self.requestHeaderModifier) \\u0026\\u0026 self.type != 'RequestHeaderModifier')\"\n          },\n          {\n           \"message\": \"filter.requestHeaderModifier must be specified for RequestHeaderModifier filter.type\",\n           \"rule\": \"!(!has(self.requestHeaderModifier) \\u0026\\u0026 self.type == 'RequestHeaderModifier')\"\n          },\n          {\n           \"message\": \"filter.responseHeaderModifier must be nil if the filter.type is not ResponseHeaderModifier\",\n           \"rule\": \"!(has(self.responseHeaderModifier) \\u0026\\u0026 self.type != 'ResponseHeaderModifier')\"\n          },\n          {\n           \"message\": \"filter.responseHeaderModifier must be specified for ResponseHeaderModifier filter.type\",\n           \"rule\": \"!(!has(self.responseHeaderModifier) \\u0026\\u0026 self.type == 'ResponseHeaderModifier')\"\n          },\n          {\n           \"message\": \"filter.requestMirror must be nil if the filter.type is not RequestMirror\",\n           \"rule\": \"!(has(self.requestMirror) \\u0026\\u0026 self.type != 'RequestMirror')\"\n          },\n          {\n           \"message\": \"filter.requestMirror must be specified for RequestMirror filter.type\",\n           \"rule\": \"!(!has(self.requestMirror) \\u0026\\u0026 self.type == 'RequestMirror')\"\n          },\n          {\n           \"message\": \"filter.extensionRef must be nil if the filter.type is not ExtensionRef\",\n           \"rule\": \"!(has(self.extensionRef) \\u0026\\u0026 self.type != 'ExtensionRef')\"\n          },\n          {\n           \"message\": \"filter.extensionRef must be specified for ExtensionRef filter.type\",\n           \"rule\": \"!(!has(self.extensionRef) \\u0026\\u0026 self.type == 'ExtensionRef')\"\n          }\n         ]\n        },\n        \"maxItems\": 16,\n        \"type\": \"array\",\n        \"x-kubernetes-validations\": [\n         {\n          \"message\": \"RequestHeaderModifier filter cannot be repeated\",\n          \"rule\": \"self.filter(f, f.type == 'RequestHeaderModifier').size() \\u003c= 1\"\n         },\n         {\n          \"message\": \"ResponseHeaderModifier filter cannot be repeated\",\n          \"rule\": \"self.filter(f, f.type == 'ResponseHeaderModifier').size() \\u003c= 1\"\n         }\n        ]\n       },\n       \"matches\": {\n        \"description\": \"Matches define conditions used for matching the rule against incoming\\ngRPC requests. Each match is independent, i.e. this rule will be matched\\nif **any** one of the matches is satisfied.\\n\\n\\nFor example, take the following matches configuration:\\n\\n\\n```\\nmatches:\\n- method:\\n    service: foo.bar\\n  headers:\\n    values:\\n      version: 2\\n- method:\\n    service: foo.bar.v2\\n```\\n\\n\\nFor a request to match against this rule, it MUST satisfy\\nEITHER of the two conditions:\\n\\n\\n- service of foo.bar AND contains the header `version: 2`\\n- service of foo.bar.v2\\n\\n\\nSee the documentation for GRPCRouteMatch on how to specify multiple\\nmatch conditions to be ANDed together.\\n\\n\\nIf no matches are specified, the implementation MUST match every gRPC request.\\n\\n\\nProxy or Load Balancer routing configuration generated from GRPCRoutes\\nMUST prioritize rules based on the following criteria, continuing on\\nties. Merging MUST not be done between GRPCRoutes and HTTPRoutes.\\nPrecedence MUST be given to the rule with the largest number of:\\n\\n\\n* Characters in a matching non-wildcard hostname.\\n* Characters in a matching hostname.\\n* Characters in a matching service.\\n* Characters in a matching method.\\n* Header matches.\\n\\n\\nIf ties still exist across multiple Routes, matching precedence MUST be\\ndetermined in order of the following criteria, continuing on ties:\\n\\n\\n* The oldest Route based on creation timestamp.\\n* The Route appearing first in alphabetical order by\\n  \\\"{namespace}/{name}\\\".\\n\\n\\nIf ties still exist within the Route that has been given precedence,\\nmatching precedence MUST be granted to the first matching rule meeting\\nthe above criteria.\",\n        \"items\": {\n         \"description\": \"GRPCRouteMatch defines the predicate used to match requests to a given\\naction. Multiple match types are ANDed together, i.e. the match will\\nevaluate to true only if all conditions are satisfied.\\n\\n\\nFor example, the match below will match a gRPC request only if its service\\nis `foo` AND it contains the `version: v1` header:\\n\\n\\n```\\nmatches:\\n  - method:\\n    type: Exact\\n    service: \\\"foo\\\"\\n    headers:\\n  - name: \\\"version\\\"\\n    value \\\"v1\\\"\\n\\n\\n```\",\n         \"properties\": {\n          \"headers\": {\n           \"description\": \"Headers specifies gRPC request header matchers. Multiple match values are\\nANDed together, meaning, a request MUST match all the specified headers\\nto select the route.\",\n           \"items\": {\n            \"description\": \"GRPCHeaderMatch describes how to select a gRPC route by matching gRPC request\\nheaders.\",\n            \"properties\": {\n             \"name\": {\n              \"description\": \"Name is the name of the gRPC Header to be matched.\\n\\n\\nIf multiple entries specify equivalent header names, only the first\\nentry with an equivalent name MUST be considered for a match. Subsequent\\nentries with an equivalent header name MUST be ignored. Due to the\\ncase-insensitivity of header names, \\\"foo\\\" and \\\"Foo\\\" are considered\\nequivalent.\",\n              \"maxLength\": 256,\n              \"minLength\": 1,\n              \"pattern\": \"^[A-Za-z0-9!#$%\\u0026'*+\\\\-.^_\\\\x60|~]+$\",\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"default\": \"Exact\",\n              \"description\": \"Type specifies how to match against the value of the header.\",\n              \"enum\": [\n               \"Exact\",\n               \"RegularExpression\"\n              ],\n              \"type\": \"string\"\n             },\n             \"value\": {\n              \"description\": \"Value is the value of the gRPC Header to be matched.\",\n              \"maxLength\": 4096,\n              \"minLength\": 1,\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"name\",\n             \"value\"\n            ],\n            \"type\": \"object\"\n           },\n           \"maxItems\": 16,\n           \"type\": \"array\",\n           \"x-kubernetes-list-map-keys\": [\n            \"name\"\n           ],\n           \"x-kubernetes-list-type\": \"map\"\n          },\n          \"method\": {\n           \"description\": \"Method specifies a gRPC request service/method matcher. If this field is\\nnot specified, all services and methods will match.\",\n           \"properties\": {\n            \"method\": {\n             \"description\": \"Value of the method to match against. If left empty or omitted, will\\nmatch all services.\\n\\n\\nAt least one of Service and Method MUST be a non-empty string.\",\n             \"maxLength\": 1024,\n             \"type\": \"string\"\n            },\n            \"service\": {\n             \"description\": \"Value of the service to match against. If left empty or omitted, will\\nmatch any service.\\n\\n\\nAt least one of Service and Method MUST be a non-empty string.\",\n             \"maxLength\": 1024,\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"default\": \"Exact\",\n             \"description\": \"Type specifies how to match against the service and/or method.\\nSupport: Core (Exact with service and method specified)\\n\\n\\nSupport: Implementation-specific (Exact with method specified but no service specified)\\n\\n\\nSupport: Implementation-specific (RegularExpression)\",\n             \"enum\": [\n              \"Exact\",\n              \"RegularExpression\"\n             ],\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\",\n           \"x-kubernetes-validations\": [\n            {\n             \"message\": \"One or both of 'service' or 'method' must be specified\",\n             \"rule\": \"has(self.type) ? has(self.service) || has(self.method) : true\"\n            },\n            {\n             \"message\": \"service must only contain valid characters (matching ^(?i)\\\\.?[a-z_][a-z_0-9]*(\\\\.[a-z_][a-z_0-9]*)*$)\",\n             \"rule\": \"(!has(self.type) || self.type == 'Exact') \\u0026\\u0026 has(self.service) ? self.service.matches(r\\\"\\\"\\\"^(?i)\\\\.?[a-z_][a-z_0-9]*(\\\\.[a-z_][a-z_0-9]*)*$\\\"\\\"\\\"): true\"\n            },\n            {\n             \"message\": \"method must only contain valid characters (matching ^[A-Za-z_][A-Za-z_0-9]*$)\",\n             \"rule\": \"(!has(self.type) || self.type == 'Exact') \\u0026\\u0026 has(self.method) ? self.method.matches(r\\\"\\\"\\\"^[A-Za-z_][A-Za-z_0-9]*$\\\"\\\"\\\"): true\"\n            }\n           ]\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"maxItems\": 8,\n        \"type\": \"array\"\n       },\n       \"sessionPersistence\": {\n        \"description\": \"SessionPersistence defines and configures session persistence\\nfor the route rule.\\n\\n\\nSupport: Extended\\n\\n\\n\",\n        \"properties\": {\n         \"absoluteTimeout\": {\n          \"description\": \"AbsoluteTimeout defines the absolute timeout of the persistent\\nsession. Once the AbsoluteTimeout duration has elapsed, the\\nsession becomes invalid.\\n\\n\\nSupport: Extended\",\n          \"pattern\": \"^([0-9]{1,5}(h|m|s|ms)){1,4}$\",\n          \"type\": \"string\"\n         },\n         \"cookieConfig\": {\n          \"description\": \"CookieConfig provides configuration settings that are specific\\nto cookie-based session persistence.\\n\\n\\nSupport: Core\",\n          \"properties\": {\n           \"lifetimeType\": {\n            \"default\": \"Session\",\n            \"description\": \"LifetimeType specifies whether the cookie has a permanent or\\nsession-based lifetime. A permanent cookie persists until its\\nspecified expiry time, defined by the Expires or Max-Age cookie\\nattributes, while a session cookie is deleted when the current\\nsession ends.\\n\\n\\nWhen set to \\\"Permanent\\\", AbsoluteTimeout indicates the\\ncookie's lifetime via the Expires or Max-Age cookie attributes\\nand is required.\\n\\n\\nWhen set to \\\"Session\\\", AbsoluteTimeout indicates the\\nabsolute lifetime of the cookie tracked by the gateway and\\nis optional.\\n\\n\\nSupport: Core for \\\"Session\\\" type\\n\\n\\nSupport: Extended for \\\"Permanent\\\" type\",\n            \"enum\": [\n             \"Permanent\",\n             \"Session\"\n            ],\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"idleTimeout\": {\n          \"description\": \"IdleTimeout defines the idle timeout of the persistent session.\\nOnce the session has been idle for more than the specified\\nIdleTimeout duration, the session becomes invalid.\\n\\n\\nSupport: Extended\",\n          \"pattern\": \"^([0-9]{1,5}(h|m|s|ms)){1,4}$\",\n          \"type\": \"string\"\n         },\n         \"sessionName\": {\n          \"description\": \"SessionName defines the name of the persistent session token\\nwhich may be reflected in the cookie or the header. Users\\nshould avoid reusing session names to prevent unintended\\nconsequences, such as rejection or unpredictable behavior.\\n\\n\\nSupport: Implementation-specific\",\n          \"maxLength\": 128,\n          \"type\": \"string\"\n         },\n         \"type\": {\n          \"default\": \"Cookie\",\n          \"description\": \"Type defines the type of session persistence such as through\\nthe use a header or cookie. Defaults to cookie based session\\npersistence.\\n\\n\\nSupport: Core for \\\"Cookie\\\" type\\n\\n\\nSupport: Extended for \\\"Header\\\" type\",\n          \"enum\": [\n           \"Cookie\",\n           \"Header\"\n          ],\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\",\n        \"x-kubernetes-validations\": [\n         {\n          \"message\": \"AbsoluteTimeout must be specified when cookie lifetimeType is Permanent\",\n          \"rule\": \"!has(self.cookieConfig) || !has(self.cookieConfig.lifetimeType) || self.cookieConfig.lifetimeType != 'Permanent' || has(self.absoluteTimeout)\"\n         }\n        ]\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"maxItems\": 16,\n     \"type\": \"array\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"GRPC Route\",\n \"type\": \"object\"\n}"}}