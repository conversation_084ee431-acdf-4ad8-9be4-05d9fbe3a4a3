{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Definition Revision", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubevela", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "App Definition and Development"}, "subCategory": "Application Definition & Image Build", "metadata": {"isAnnotation": false, "primaryColor": "#006fff", "secondaryColor": "#45B4FF\n", "shape": "circle", "source_uri": "git://github.com/kubevela/kubevela/master/charts/vela-core/crds", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"图层_1\" data-name=\"图层 1\" viewBox=\"0 0 122.5 122.5\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#006fff;}</style></defs><title xmlns=\"http://www.w3.org/2000/svg\">KubeVela </title><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" id=\"图层_1\" data-name=\"图层 1\" viewBox=\"0 0 122.5 122.5\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff;}</style></defs><title xmlns=\"http://www.w3.org/2000/svg\">KubeVela </title><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"></path><path xmlns=\"http://www.w3.org/2000/svg\" class=\"cls-1\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"></path></svg>"}, "model": {"version": "v1.10.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#006fff", "secondaryColor": "#45B4FF\n", "shape": "circle", "svgColor": "<svg id=\"图层_1\" data-name=\"图层 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 122.5 122.5\"><defs><style>.cls-1{fill:#006fff;}</style></defs><title><PERSON><PERSON><PERSON><PERSON> </title><path d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"/><path class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"/><path class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"/><path d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"/></svg>", "svgComplete": "", "svgWhite": "<svg id=\"图层_1\" data-name=\"图层 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 122.5 122.5\"><defs><style>.cls-1{fill:#fff;}</style></defs><title><PERSON><PERSON><PERSON><PERSON> </title><path class=\"cls-1\" d=\"M32.05,96.24a11.58,11.58,0,0,0,10,5.79,11.34,11.34,0,0,0,5.62-1.47,6,6,0,0,1,5.89,0A11.33,11.33,0,0,0,59.19,102a11.52,11.52,0,0,0,5.27-1.27,6.17,6.17,0,0,1,5.7,0,11.55,11.55,0,0,0,15.28-4.52Z\"/><path class=\"cls-1\" d=\"M91.52,76.27,91.38,74C90,51.79,81.7,30.91,68.07,15.23h0v60Z\"/><path class=\"cls-1\" d=\"M64.86,75.05V27.8A79.26,79.26,0,0,0,42,74Z\"/><path class=\"cls-1\" d=\"M43.87,78.38,42,74,20.35,73l6,14.12h0a10.16,10.16,0,0,0,8.79,5.08A10,10,0,0,0,40,91a7.32,7.32,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.3,7.3,0,0,1,7.17,0,10,10,0,0,0,9.78,0,7.32,7.32,0,0,1,7.17,0A10,10,0,0,0,86,92.23a10.16,10.16,0,0,0,8.79-5.08l2.46-5L46,79.84A2.38,2.38,0,0,1,43.87,78.38Z\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/kubevela/kubevela/master/charts/vela-core/crds"}, "configuration": null, "component": {"version": "core.oam.dev/v1beta1", "kind": "DefinitionRevision", "schema": "{\n \"description\": \"DefinitionRevision is the Schema for the DefinitionRevision API\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"DefinitionRevisionSpec is the spec of DefinitionRevision\",\n   \"properties\": {\n    \"componentDefinition\": {\n     \"description\": \"ComponentDefinition records the snapshot of the created/modified ComponentDefinition\",\n     \"properties\": {\n      \"apiVersion\": {\n       \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n       \"type\": \"string\"\n      },\n      \"kind\": {\n       \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n       \"type\": \"string\"\n      },\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"finalizers\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"name\": {\n         \"type\": \"string\"\n        },\n        \"namespace\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"description\": \"ComponentDefinitionSpec defines the desired state of ComponentDefinition\",\n       \"properties\": {\n        \"childResourceKinds\": {\n         \"description\": \"ChildResourceKinds are the list of GVK of the child resources this workload generates\",\n         \"items\": {\n          \"description\": \"A ChildResourceKind defines a child Kubernetes resource kind with a selector\",\n          \"properties\": {\n           \"apiVersion\": {\n            \"description\": \"APIVersion of the child resource\",\n            \"type\": \"string\"\n           },\n           \"kind\": {\n            \"description\": \"Kind of the child resource\",\n            \"type\": \"string\"\n           },\n           \"selector\": {\n            \"additionalProperties\": {\n             \"type\": \"string\"\n            },\n            \"description\": \"Selector to select the child resources that the workload wants to expose to traits\",\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"apiVersion\",\n           \"kind\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"extension\": {\n         \"description\": \"Extension is used for extension needs by OAM platform builders\",\n         \"format\": \"textarea\",\n         \"type\": \"string\"\n        },\n        \"podSpecPath\": {\n         \"description\": \"PodSpecPath indicates where/if this workload has K8s podSpec field\\nif one workload has podSpec, trait can do lot's of assumption such as port, env, volume fields.\",\n         \"type\": \"string\"\n        },\n        \"revisionLabel\": {\n         \"description\": \"RevisionLabel indicates which label for underlying resources(e.g. pods) of this workload\\ncan be used by trait to create resource selectors(e.g. label selector for pods).\",\n         \"type\": \"string\"\n        },\n        \"schematic\": {\n         \"description\": \"Schematic defines the data format and template of the encapsulation of the workload\",\n         \"properties\": {\n          \"cue\": {\n           \"description\": \"CUE defines the encapsulation in CUE format\",\n           \"properties\": {\n            \"template\": {\n             \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"template\"\n           ],\n           \"type\": \"object\"\n          },\n          \"terraform\": {\n           \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n           \"properties\": {\n            \"configuration\": {\n             \"description\": \"Configuration is Terraform Configuration\",\n             \"type\": \"string\"\n            },\n            \"customRegion\": {\n             \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n             \"type\": \"string\"\n            },\n            \"deleteResource\": {\n             \"default\": true,\n             \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n             \"type\": \"boolean\"\n            },\n            \"gitCredentialsSecretReference\": {\n             \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"name is unique within a namespace to reference a secret resource.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"namespace defines the space within which the secret name must be unique.\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"path\": {\n             \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n             \"type\": \"string\"\n            },\n            \"providerRef\": {\n             \"description\": \"ProviderReference specifies the reference to Provider\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the referenced object.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"default\": \"default\",\n               \"description\": \"Namespace of the referenced object.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": {\n             \"default\": \"hcl\",\n             \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n             \"enum\": [\n              \"hcl\",\n              \"json\",\n              \"remote\"\n             ],\n             \"type\": \"string\"\n            },\n            \"writeConnectionSecretToRef\": {\n             \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the secret.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"Namespace of the secret.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"configuration\"\n           ],\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"status\": {\n         \"description\": \"Status defines the custom health policy and status message for workload\",\n         \"properties\": {\n          \"customStatus\": {\n           \"description\": \"CustomStatus defines the custom status message that could display to user\",\n           \"type\": \"string\"\n          },\n          \"healthPolicy\": {\n           \"description\": \"HealthPolicy defines the health check policy for the abstraction\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"version\": {\n         \"type\": \"string\"\n        },\n        \"workload\": {\n         \"description\": \"Workload is a workload type descriptor\",\n         \"properties\": {\n          \"definition\": {\n           \"description\": \"Definition mutually exclusive to workload.type, a embedded WorkloadDefinition\",\n           \"properties\": {\n            \"apiVersion\": {\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"apiVersion\",\n            \"kind\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": {\n           \"description\": \"Type ref to a WorkloadDefinition via name\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"required\": [\n        \"workload\"\n       ],\n       \"type\": \"object\"\n      },\n      \"status\": {\n       \"description\": \"ComponentDefinitionStatus is the status of ComponentDefinition\",\n       \"properties\": {\n        \"conditions\": {\n         \"description\": \"Conditions of the resource.\",\n         \"items\": {\n          \"description\": \"A Condition that may apply to a resource.\",\n          \"properties\": {\n           \"lastTransitionTime\": {\n            \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n            \"format\": \"date-time\",\n            \"type\": \"string\"\n           },\n           \"message\": {\n            \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n            \"type\": \"string\"\n           },\n           \"reason\": {\n            \"description\": \"A Reason for this condition's last transition from one status to another.\",\n            \"type\": \"string\"\n           },\n           \"status\": {\n            \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"lastTransitionTime\",\n           \"reason\",\n           \"status\",\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"configMapRef\": {\n         \"description\": \"ConfigMapRef refer to a ConfigMap which contains OpenAPI V3 JSON schema of Component parameters.\",\n         \"type\": \"string\"\n        },\n        \"latestRevision\": {\n         \"description\": \"LatestRevision of the component definition\",\n         \"properties\": {\n          \"name\": {\n           \"type\": \"string\"\n          },\n          \"revision\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"revisionHash\": {\n           \"description\": \"RevisionHash record the hash value of the spec of ApplicationRevision object.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\",\n          \"revision\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"definitionType\": {\n     \"description\": \"DefinitionType\",\n     \"enum\": [\n      \"Component\",\n      \"Trait\",\n      \"Policy\",\n      \"WorkflowStep\"\n     ],\n     \"type\": \"string\"\n    },\n    \"policyDefinition\": {\n     \"description\": \"PolicyDefinition records the snapshot of the created/modified PolicyDefinition\",\n     \"properties\": {\n      \"apiVersion\": {\n       \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n       \"type\": \"string\"\n      },\n      \"kind\": {\n       \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n       \"type\": \"string\"\n      },\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"finalizers\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"name\": {\n         \"type\": \"string\"\n        },\n        \"namespace\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"description\": \"PolicyDefinitionSpec defines the desired state of PolicyDefinition\",\n       \"properties\": {\n        \"definitionRef\": {\n         \"description\": \"Reference to the CustomResourceDefinition that defines this trait kind.\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name of the referenced CustomResourceDefinition.\",\n           \"type\": \"string\"\n          },\n          \"version\": {\n           \"description\": \"Version indicate which version should be used if CRD has multiple versions\\nby default it will use the first one if not specified\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"manageHealthCheck\": {\n         \"description\": \"ManageHealthCheck means the policy will handle health checking and skip application controller\\nbuilt-in health checking.\",\n         \"type\": \"boolean\"\n        },\n        \"schematic\": {\n         \"description\": \"Schematic defines the data format and template of the encapsulation of the policy definition.\\nOnly CUE schematic is supported for now.\",\n         \"properties\": {\n          \"cue\": {\n           \"description\": \"CUE defines the encapsulation in CUE format\",\n           \"properties\": {\n            \"template\": {\n             \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"template\"\n           ],\n           \"type\": \"object\"\n          },\n          \"terraform\": {\n           \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n           \"properties\": {\n            \"configuration\": {\n             \"description\": \"Configuration is Terraform Configuration\",\n             \"type\": \"string\"\n            },\n            \"customRegion\": {\n             \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n             \"type\": \"string\"\n            },\n            \"deleteResource\": {\n             \"default\": true,\n             \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n             \"type\": \"boolean\"\n            },\n            \"gitCredentialsSecretReference\": {\n             \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"name is unique within a namespace to reference a secret resource.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"namespace defines the space within which the secret name must be unique.\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"path\": {\n             \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n             \"type\": \"string\"\n            },\n            \"providerRef\": {\n             \"description\": \"ProviderReference specifies the reference to Provider\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the referenced object.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"default\": \"default\",\n               \"description\": \"Namespace of the referenced object.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": {\n             \"default\": \"hcl\",\n             \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n             \"enum\": [\n              \"hcl\",\n              \"json\",\n              \"remote\"\n             ],\n             \"type\": \"string\"\n            },\n            \"writeConnectionSecretToRef\": {\n             \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the secret.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"Namespace of the secret.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"configuration\"\n           ],\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"version\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"status\": {\n       \"description\": \"PolicyDefinitionStatus is the status of PolicyDefinition\",\n       \"properties\": {\n        \"conditions\": {\n         \"description\": \"Conditions of the resource.\",\n         \"items\": {\n          \"description\": \"A Condition that may apply to a resource.\",\n          \"properties\": {\n           \"lastTransitionTime\": {\n            \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n            \"format\": \"date-time\",\n            \"type\": \"string\"\n           },\n           \"message\": {\n            \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n            \"type\": \"string\"\n           },\n           \"reason\": {\n            \"description\": \"A Reason for this condition's last transition from one status to another.\",\n            \"type\": \"string\"\n           },\n           \"status\": {\n            \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"lastTransitionTime\",\n           \"reason\",\n           \"status\",\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"configMapRef\": {\n         \"description\": \"ConfigMapRef refer to a ConfigMap which contains OpenAPI V3 JSON schema of Component parameters.\",\n         \"type\": \"string\"\n        },\n        \"latestRevision\": {\n         \"description\": \"LatestRevision of the component definition\",\n         \"properties\": {\n          \"name\": {\n           \"type\": \"string\"\n          },\n          \"revision\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"revisionHash\": {\n           \"description\": \"RevisionHash record the hash value of the spec of ApplicationRevision object.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\",\n          \"revision\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"revision\": {\n     \"description\": \"Revision record revision number of DefinitionRevision\",\n     \"format\": \"int64\",\n     \"type\": \"integer\"\n    },\n    \"revisionHash\": {\n     \"description\": \"RevisionHash record the hash value of the spec of DefinitionRevision object.\",\n     \"type\": \"string\"\n    },\n    \"traitDefinition\": {\n     \"description\": \"TraitDefinition records the snapshot of the created/modified TraitDefinition\",\n     \"properties\": {\n      \"apiVersion\": {\n       \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n       \"type\": \"string\"\n      },\n      \"kind\": {\n       \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n       \"type\": \"string\"\n      },\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"finalizers\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"name\": {\n         \"type\": \"string\"\n        },\n        \"namespace\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"description\": \"A TraitDefinitionSpec defines the desired state of a TraitDefinition.\",\n       \"properties\": {\n        \"appliesToWorkloads\": {\n         \"description\": \"AppliesToWorkloads specifies the list of workload kinds this trait\\napplies to. Workload kinds are specified in resource.group/version format,\\ne.g. server.core.oam.dev/v1alpha2. Traits that omit this field apply to\\nall workload kinds.\",\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"conflictsWith\": {\n         \"description\": \"ConflictsWith specifies the list of traits(CRD name, Definition name, CRD group)\\nwhich could not apply to the same workloads with this trait.\\nTraits that omit this field can work with any other traits.\\nExample rules:\\n\\\"service\\\" # Trait definition name\\n\\\"services.k8s.io\\\" # API resource/crd name\\n\\\"*.networking.k8s.io\\\" # API group\\n\\\"labelSelector:foo=bar\\\" # label selector\\nlabelSelector format: https://pkg.go.dev/k8s.io/apimachinery/pkg/labels#Parse\",\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"controlPlaneOnly\": {\n         \"description\": \"ControlPlaneOnly defines which cluster is dispatched to\",\n         \"type\": \"boolean\"\n        },\n        \"definitionRef\": {\n         \"description\": \"Reference to the CustomResourceDefinition that defines this trait kind.\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name of the referenced CustomResourceDefinition.\",\n           \"type\": \"string\"\n          },\n          \"version\": {\n           \"description\": \"Version indicate which version should be used if CRD has multiple versions\\nby default it will use the first one if not specified\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"extension\": {\n         \"description\": \"Extension is used for extension needs by OAM platform builders\",\n         \"format\": \"textarea\",\n         \"type\": \"string\"\n        },\n        \"manageWorkload\": {\n         \"description\": \"ManageWorkload defines the trait would be responsible for creating the workload\",\n         \"type\": \"boolean\"\n        },\n        \"podDisruptive\": {\n         \"description\": \"PodDisruptive specifies whether using the trait will cause the pod to restart or not.\",\n         \"type\": \"boolean\"\n        },\n        \"revisionEnabled\": {\n         \"description\": \"Revision indicates whether a trait is aware of component revision\",\n         \"type\": \"boolean\"\n        },\n        \"schematic\": {\n         \"description\": \"Schematic defines the data format and template of the encapsulation of the trait.\\nOnly CUE and Kube schematic are supported for now.\",\n         \"properties\": {\n          \"cue\": {\n           \"description\": \"CUE defines the encapsulation in CUE format\",\n           \"properties\": {\n            \"template\": {\n             \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"template\"\n           ],\n           \"type\": \"object\"\n          },\n          \"terraform\": {\n           \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n           \"properties\": {\n            \"configuration\": {\n             \"description\": \"Configuration is Terraform Configuration\",\n             \"type\": \"string\"\n            },\n            \"customRegion\": {\n             \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n             \"type\": \"string\"\n            },\n            \"deleteResource\": {\n             \"default\": true,\n             \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n             \"type\": \"boolean\"\n            },\n            \"gitCredentialsSecretReference\": {\n             \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"name is unique within a namespace to reference a secret resource.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"namespace defines the space within which the secret name must be unique.\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"path\": {\n             \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n             \"type\": \"string\"\n            },\n            \"providerRef\": {\n             \"description\": \"ProviderReference specifies the reference to Provider\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the referenced object.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"default\": \"default\",\n               \"description\": \"Namespace of the referenced object.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": {\n             \"default\": \"hcl\",\n             \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n             \"enum\": [\n              \"hcl\",\n              \"json\",\n              \"remote\"\n             ],\n             \"type\": \"string\"\n            },\n            \"writeConnectionSecretToRef\": {\n             \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the secret.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"Namespace of the secret.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"configuration\"\n           ],\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"stage\": {\n         \"description\": \"Stage defines the stage information to which this trait resource processing belongs.\\nCurrently, PreDispatch and PostDispatch are provided, which are used to control resource\\npre-process and post-process respectively.\",\n         \"type\": \"string\"\n        },\n        \"status\": {\n         \"description\": \"Status defines the custom health policy and status message for trait\",\n         \"properties\": {\n          \"customStatus\": {\n           \"description\": \"CustomStatus defines the custom status message that could display to user\",\n           \"type\": \"string\"\n          },\n          \"healthPolicy\": {\n           \"description\": \"HealthPolicy defines the health check policy for the abstraction\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"version\": {\n         \"type\": \"string\"\n        },\n        \"workloadRefPath\": {\n         \"description\": \"WorkloadRefPath indicates where/if a trait accepts a workloadRef object\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"status\": {\n       \"description\": \"TraitDefinitionStatus is the status of TraitDefinition\",\n       \"properties\": {\n        \"conditions\": {\n         \"description\": \"Conditions of the resource.\",\n         \"items\": {\n          \"description\": \"A Condition that may apply to a resource.\",\n          \"properties\": {\n           \"lastTransitionTime\": {\n            \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n            \"format\": \"date-time\",\n            \"type\": \"string\"\n           },\n           \"message\": {\n            \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n            \"type\": \"string\"\n           },\n           \"reason\": {\n            \"description\": \"A Reason for this condition's last transition from one status to another.\",\n            \"type\": \"string\"\n           },\n           \"status\": {\n            \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"lastTransitionTime\",\n           \"reason\",\n           \"status\",\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"configMapRef\": {\n         \"description\": \"ConfigMapRef refer to a ConfigMap which contains OpenAPI V3 JSON schema of Component parameters.\",\n         \"type\": \"string\"\n        },\n        \"latestRevision\": {\n         \"description\": \"LatestRevision of the component definition\",\n         \"properties\": {\n          \"name\": {\n           \"type\": \"string\"\n          },\n          \"revision\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"revisionHash\": {\n           \"description\": \"RevisionHash record the hash value of the spec of ApplicationRevision object.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\",\n          \"revision\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"workflowStepDefinition\": {\n     \"description\": \"WorkflowStepDefinition records the snapshot of the created/modified WorkflowStepDefinition\",\n     \"properties\": {\n      \"apiVersion\": {\n       \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n       \"type\": \"string\"\n      },\n      \"kind\": {\n       \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n       \"type\": \"string\"\n      },\n      \"metadata\": {\n       \"properties\": {\n        \"annotations\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"finalizers\": {\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"labels\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"name\": {\n         \"type\": \"string\"\n        },\n        \"namespace\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"spec\": {\n       \"description\": \"WorkflowStepDefinitionSpec defines the desired state of WorkflowStepDefinition\",\n       \"properties\": {\n        \"definitionRef\": {\n         \"description\": \"Reference to the CustomResourceDefinition that defines this trait kind.\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name of the referenced CustomResourceDefinition.\",\n           \"type\": \"string\"\n          },\n          \"version\": {\n           \"description\": \"Version indicate which version should be used if CRD has multiple versions\\nby default it will use the first one if not specified\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"schematic\": {\n         \"description\": \"Schematic defines the data format and template of the encapsulation of the workflow step definition.\\nOnly CUE schematic is supported for now.\",\n         \"properties\": {\n          \"cue\": {\n           \"description\": \"CUE defines the encapsulation in CUE format\",\n           \"properties\": {\n            \"template\": {\n             \"description\": \"Template defines the abstraction template data of the capability, it will replace the old CUE template in extension field.\\nTemplate is a required field if CUE is defined in Capability Definition.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"template\"\n           ],\n           \"type\": \"object\"\n          },\n          \"terraform\": {\n           \"description\": \"Terraform is the struct to describe cloud resources managed by Hashicorp Terraform\",\n           \"properties\": {\n            \"configuration\": {\n             \"description\": \"Configuration is Terraform Configuration\",\n             \"type\": \"string\"\n            },\n            \"customRegion\": {\n             \"description\": \"Region is cloud provider's region. It will override the region in the region field of ProviderReference\",\n             \"type\": \"string\"\n            },\n            \"deleteResource\": {\n             \"default\": true,\n             \"description\": \"DeleteResource will determine whether provisioned cloud resources will be deleted when CR is deleted\",\n             \"type\": \"boolean\"\n            },\n            \"gitCredentialsSecretReference\": {\n             \"description\": \"GitCredentialsSecretReference specifies the reference to the secret containing the git credentials\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"name is unique within a namespace to reference a secret resource.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"namespace defines the space within which the secret name must be unique.\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"path\": {\n             \"description\": \"Path is the sub-directory of remote git repository. It's valid when remote is set\",\n             \"type\": \"string\"\n            },\n            \"providerRef\": {\n             \"description\": \"ProviderReference specifies the reference to Provider\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the referenced object.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"default\": \"default\",\n               \"description\": \"Namespace of the referenced object.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": {\n             \"default\": \"hcl\",\n             \"description\": \"Type specifies which Terraform configuration it is, HCL or JSON syntax\",\n             \"enum\": [\n              \"hcl\",\n              \"json\",\n              \"remote\"\n             ],\n             \"type\": \"string\"\n            },\n            \"writeConnectionSecretToRef\": {\n             \"description\": \"WriteConnectionSecretToReference specifies the namespace and name of a\\nSecret to which any connection details for this managed resource should\\nbe written. Connection details frequently include the endpoint, username,\\nand password required to connect to the managed resource.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the secret.\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"Namespace of the secret.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"configuration\"\n           ],\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"version\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"status\": {\n       \"description\": \"WorkflowStepDefinitionStatus is the status of WorkflowStepDefinition\",\n       \"properties\": {\n        \"conditions\": {\n         \"description\": \"Conditions of the resource.\",\n         \"items\": {\n          \"description\": \"A Condition that may apply to a resource.\",\n          \"properties\": {\n           \"lastTransitionTime\": {\n            \"description\": \"LastTransitionTime is the last time this condition transitioned from one\\nstatus to another.\",\n            \"format\": \"date-time\",\n            \"type\": \"string\"\n           },\n           \"message\": {\n            \"description\": \"A Message containing details about this condition's last transition from\\none status to another, if any.\",\n            \"type\": \"string\"\n           },\n           \"reason\": {\n            \"description\": \"A Reason for this condition's last transition from one status to another.\",\n            \"type\": \"string\"\n           },\n           \"status\": {\n            \"description\": \"Status of this condition; is it currently True, False, or Unknown?\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type of this condition. At most one of each condition type may apply to\\na resource at any point in time.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"lastTransitionTime\",\n           \"reason\",\n           \"status\",\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"configMapRef\": {\n         \"description\": \"ConfigMapRef refer to a ConfigMap which contains OpenAPI V3 JSON schema of Component parameters.\",\n         \"type\": \"string\"\n        },\n        \"latestRevision\": {\n         \"description\": \"LatestRevision of the component definition\",\n         \"properties\": {\n          \"name\": {\n           \"type\": \"string\"\n          },\n          \"revision\": {\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"revisionHash\": {\n           \"description\": \"RevisionHash record the hash value of the spec of ApplicationRevision object.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\",\n          \"revision\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    }\n   },\n   \"required\": [\n    \"definitionType\",\n    \"revision\",\n    \"revisionHash\"\n   ],\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Definition Revision\",\n \"type\": \"object\"\n}"}}