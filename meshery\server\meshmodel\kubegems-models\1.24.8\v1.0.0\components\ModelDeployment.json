{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Model Deployment", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubegems-models", "displayName": "Kubegems Models", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Provisioning"}, "subCategory": "Automation & Configuration", "metadata": {"isAnnotation": false, "primaryColor": "#1c77c8", "secondaryColor": "#00D3A9", "shape": "circle", "source_uri": "https://charts.kubegems.io/kubegems/charts/kubegems-models-1.24.8.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.76 0.49 364.26 356.51\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#1c77c8}.cls-2{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M99.589 251.108a4.575 4.575 0 1 0 4.72 4.573 4.654 4.654 0 0 0-4.72-4.573zm150.361 9.146a4.575 4.575 0 1 0-4.72-4.573 4.653 4.653 0 0 0 4.72 4.573zm-16.086-37.929c20.693 0 37.567-16.148 37.567-35.987 0-14.965-9.619-27.82-23.127-33.248 0-1.068.095-2.158.095-3.249 0-30-25.37-54.386-56.78-54.386-22.649 0-42.029 12.692-51.17 30.998a27.216 27.216 0 0 0-16.038-5.267c-14.559 0-26.206 11.253-26.206 25.105a24.56 24.56 0 0 0 1.122 7.425 36.546 36.546 0 0 0-18.831 31.718c0 20.116 16.97 35.615 37.853 36.798h113.463c.668 0 1.312.093 2.052.093zM87.688 185.434a29.349 29.349 0 0 1 15.197-25.468l5.035-2.866-1.729-5.53a17.369 17.369 0 0 1-.794-5.279c0-9.846 8.398-17.913 19.014-17.913a20.02 20.02 0 0 1 11.786 3.876l6.878 5.04 3.809-7.628c8.173-16.37 25.37-27.019 44.736-27.019 27.472 0 49.587 21.206 49.587 47.194 0 .315-.01.612-.04 1.317a39.31 39.31 0 0 0-.055 1.932v4.86l4.51 1.813c11.185 4.495 18.617 14.959 18.617 26.575 0 15.79-13.58 28.795-30.375 28.795a8.9 8.9 0 0 1-.56-.029 28.085 28.085 0 0 0-.772-.047c-.333-.013-.333-.013-.72-.016H118.349c-17.206-.987-30.661-13.822-30.661-29.607zm88.275 82.21a4.575 4.575 0 1 0 4.72 4.573 4.654 4.654 0 0 0-4.72-4.573z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M267.594 339.93l81.986-102.808a27.308 27.308 0 0 0 5.273-23.103L325.593 85.82a27.307 27.307 0 0 0-14.775-18.527L192.345 10.24a27.309 27.309 0 0 0-23.697 0L50.174 67.293A27.308 27.308 0 0 0 35.4 85.82L6.14 214.02a27.308 27.308 0 0 0 5.273 23.103L93.399 339.93a27.308 27.308 0 0 0 21.35 10.282h131.496a27.308 27.308 0 0 0 21.35-10.282zm-31.11-89.023a14.174 14.174 0 1 1 0 9.547H209.56v-2.411h-.183v-28.612q-11.455-.005-28.64-.009v29.45a14.32 14.32 0 1 1-9.547-.001v-29.45l-28.64-.005v28.627h-.184v2.411h-29.31a14.174 14.174 0 1 1-.001-9.547h19.948v-21.492l-15.061-.002c-24.922-1.412-44.638-20.22-44.638-43.98a43.463 43.463 0 0 1 17.964-35.046 32.577 32.577 0 0 1-.255-4.096c0-17.923 14.965-32.297 33.398-32.297a34.272 34.272 0 0 1 13.372 2.728 64.37 64.37 0 0 1 53.837-28.459c34.843 0 63.252 26.823 63.958 60.303a43.089 43.089 0 0 1 23.045 37.772c0 23.876-20.157 43.18-44.759 43.18-.523 0-.896-.02-1.547-.064-.111-.007-4.585-.013-13.393-.018v21.471z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M232.532 215.057c.243.011.448.024.773.047a8.9 8.9 0 0 0 .56.03c16.793 0 30.374-13.006 30.374-28.796 0-11.616-7.432-22.08-18.618-26.575l-4.51-1.813v-4.86c0-.586.015-1.008.055-1.932.031-.705.041-1.002.041-1.317 0-25.988-22.115-47.194-49.587-47.194-19.365 0-36.563 10.65-44.736 27.02l-3.81 7.628-6.877-5.041a20.02 20.02 0 0 0-11.786-3.876c-10.616 0-19.014 8.067-19.014 17.913a17.369 17.369 0 0 0 .794 5.28l1.729 5.529-5.035 2.866a29.349 29.349 0 0 0-15.197 25.468c0 15.785 13.455 28.62 30.66 29.607h113.464c.387.003.387.003.72.016z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M218.924 222.61v6.826c8.808.005 13.282.011 13.393.018.651.045 1.024.063 1.547.063 24.602 0 44.758-19.303 44.758-43.179a43.089 43.089 0 0 0-23.045-37.772c-.705-33.48-29.114-60.303-63.957-60.303a64.37 64.37 0 0 0-53.837 28.46 34.272 34.272 0 0 0-13.372-2.729c-18.433 0-33.398 14.374-33.398 32.297a32.577 32.577 0 0 0 .255 4.096 43.463 43.463 0 0 0-17.964 35.047c0 23.759 19.716 42.567 44.638 43.979l15.06.002v-6.805h9.548v6.806l28.64.005v-4.449h9.547v4.45q17.178.004 28.64.01v-6.822zm-100.575-.378c-20.884-1.183-37.853-16.682-37.853-36.798a36.546 36.546 0 0 1 18.83-31.718 24.56 24.56 0 0 1-1.121-7.425c0-13.852 11.647-25.105 26.206-25.105a27.216 27.216 0 0 1 16.038 5.267c9.141-18.306 28.521-30.998 51.17-30.998 31.41 0 56.78 24.386 56.78 54.386 0 1.09-.095 2.181-.095 3.249 13.508 5.429 23.127 18.283 23.127 33.248 0 19.838-16.874 35.987-37.567 35.987-.74 0-1.384-.093-2.052-.093z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M218.924 222.61h-9.547v35.433h.183v2.411h26.924a13.773 13.773 0 0 1 0-9.547h-17.56V222.61zm-47.734 2.362v33.899a14.19 14.19 0 0 1 9.547 0v-33.899zm-38.187-2.362v28.297h-19.948a13.775 13.775 0 0 1 0 9.547h29.311v-2.411h.184V222.61z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M113.055 250.907a14.174 14.174 0 1 0 0 9.547 13.775 13.775 0 0 0 0-9.547zm-13.466 9.347a4.575 4.575 0 1 1 4.72-4.573 4.653 4.653 0 0 1-4.72 4.573zm71.601-1.383a14.32 14.32 0 1 0 9.547 0 14.19 14.19 0 0 0-9.547 0zm4.773 17.919a4.575 4.575 0 1 1 4.72-4.573 4.653 4.653 0 0 1-4.72 4.573zm60.521-16.336a14.174 14.174 0 1 0 0-9.547 13.773 13.773 0 0 0 0 9.547zm13.467-9.346a4.575 4.575 0 1 1-4.72 4.573 4.655 4.655 0 0 1 4.72-4.573z\" class=\"cls-2\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-3.82 -0.07 367.64 360.14\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M98.5 253.002a4.621 4.621 0 1 0 4.767 4.62 4.7 4.7 0 0 0-4.767-4.62zm151.861 9.238a4.621 4.621 0 1 0-4.767-4.618 4.7 4.7 0 0 0 4.767 4.618zm-16.246-38.307c20.899 0 37.94-16.31 37.94-36.346 0-15.115-9.714-28.097-23.357-33.58 0-1.079.096-2.18.096-3.281 0-30.3-25.623-54.93-57.345-54.93-22.876 0-42.449 12.82-51.681 31.308a27.488 27.488 0 0 0-16.199-5.32c-14.704 0-26.467 11.366-26.467 25.356a24.805 24.805 0 0 0 1.133 7.5 36.91 36.91 0 0 0-19.019 32.033c0 20.317 17.139 35.97 38.23 37.166h114.596c.674 0 1.325.094 2.073.094zM86.48 186.673a29.642 29.642 0 0 1 15.349-25.722l5.085-2.894-1.746-5.585a17.543 17.543 0 0 1-.802-5.332c0-9.945 8.482-18.091 19.203-18.091a20.22 20.22 0 0 1 11.904 3.914l6.946 5.091 3.847-7.705c8.256-16.532 25.624-27.288 45.183-27.288 27.746 0 50.082 21.417 50.082 47.665 0 .317-.01.618-.042 1.33-.04.933-.055 1.359-.055 1.95v4.91l4.555 1.83c11.297 4.54 18.803 15.109 18.803 26.84 0 15.948-13.716 29.083-30.677 29.083-.098 0-.225-.006-.565-.03a28.708 28.708 0 0 0-.78-.047c-.337-.013-.337-.013-.729-.017H117.447c-17.378-.996-30.967-13.96-30.967-29.902zm84.389 87.649a4.77 4.77 0 1 0 4.766-4.619 4.7 4.7 0 0 0-4.766 4.62z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M268.18 342.71l82.805-103.833a27.58 27.58 0 0 0 5.326-23.333L326.758 86.066a27.58 27.58 0 0 0-14.922-18.712L192.181 9.731a27.581 27.581 0 0 0-23.934 0L48.592 67.354A27.58 27.58 0 0 0 33.67 86.066L4.118 215.544a27.58 27.58 0 0 0 5.325 23.333L92.247 342.71a27.58 27.58 0 0 0 21.563 10.384h132.808a27.58 27.58 0 0 0 21.563-10.384zm-31.419-89.91a14.315 14.315 0 1 1 0 9.642h-27.193v-2.435h-.185V231.11q-11.57-.005-28.926-.01v29.743a14.462 14.462 0 1 1-9.642 0V231.1l-28.926-.005v28.913h-.185v2.435H112.1a14.315 14.315 0 1 1 0-9.642h20.147v-21.707l-15.211-.002c-25.171-1.426-45.084-20.422-45.084-44.418a43.897 43.897 0 0 1 18.143-35.396 32.899 32.899 0 0 1-.257-4.137c0-18.102 15.114-32.619 33.731-32.619a34.614 34.614 0 0 1 13.506 2.755 65.012 65.012 0 0 1 54.374-28.743c35.19 0 63.883 27.091 64.596 60.904a43.519 43.519 0 0 1 23.274 38.15c0 24.114-20.357 43.61-45.204 43.61-.528 0-.905-.02-1.563-.064-.112-.007-4.63-.013-13.527-.019V252.8z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "1.24.8"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#00B39F", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.76 0.49 364.26 356.51\"><defs><style>.cls-1{fill:#1c77c8}.cls-2{fill:#fff}</style></defs><path d=\"M99.589 251.108a4.575 4.575 0 1 0 4.72 4.573 4.654 4.654 0 0 0-4.72-4.573zm150.361 9.146a4.575 4.575 0 1 0-4.72-4.573 4.653 4.653 0 0 0 4.72 4.573zm-16.086-37.929c20.693 0 37.567-16.148 37.567-35.987 0-14.965-9.619-27.82-23.127-33.248 0-1.068.095-2.158.095-3.249 0-30-25.37-54.386-56.78-54.386-22.649 0-42.029 12.692-51.17 30.998a27.216 27.216 0 0 0-16.038-5.267c-14.559 0-26.206 11.253-26.206 25.105a24.56 24.56 0 0 0 1.122 7.425 36.546 36.546 0 0 0-18.831 31.718c0 20.116 16.97 35.615 37.853 36.798h113.463c.668 0 1.312.093 2.052.093zM87.688 185.434a29.349 29.349 0 0 1 15.197-25.468l5.035-2.866-1.729-5.53a17.369 17.369 0 0 1-.794-5.279c0-9.846 8.398-17.913 19.014-17.913a20.02 20.02 0 0 1 11.786 3.876l6.878 5.04 3.809-7.628c8.173-16.37 25.37-27.019 44.736-27.019 27.472 0 49.587 21.206 49.587 47.194 0 .315-.01.612-.04 1.317a39.31 39.31 0 0 0-.055 1.932v4.86l4.51 1.813c11.185 4.495 18.617 14.959 18.617 26.575 0 15.79-13.58 28.795-30.375 28.795a8.9 8.9 0 0 1-.56-.029 28.085 28.085 0 0 0-.772-.047c-.333-.013-.333-.013-.72-.016H118.349c-17.206-.987-30.661-13.822-30.661-29.607zm88.275 82.21a4.575 4.575 0 1 0 4.72 4.573 4.654 4.654 0 0 0-4.72-4.573z\" class=\"cls-1\"/><path d=\"M267.594 339.93l81.986-102.808a27.308 27.308 0 0 0 5.273-23.103L325.593 85.82a27.307 27.307 0 0 0-14.775-18.527L192.345 10.24a27.309 27.309 0 0 0-23.697 0L50.174 67.293A27.308 27.308 0 0 0 35.4 85.82L6.14 214.02a27.308 27.308 0 0 0 5.273 23.103L93.399 339.93a27.308 27.308 0 0 0 21.35 10.282h131.496a27.308 27.308 0 0 0 21.35-10.282zm-31.11-89.023a14.174 14.174 0 1 1 0 9.547H209.56v-2.411h-.183v-28.612q-11.455-.005-28.64-.009v29.45a14.32 14.32 0 1 1-9.547-.001v-29.45l-28.64-.005v28.627h-.184v2.411h-29.31a14.174 14.174 0 1 1-.001-9.547h19.948v-21.492l-15.061-.002c-24.922-1.412-44.638-20.22-44.638-43.98a43.463 43.463 0 0 1 17.964-35.046 32.577 32.577 0 0 1-.255-4.096c0-17.923 14.965-32.297 33.398-32.297a34.272 34.272 0 0 1 13.372 2.728 64.37 64.37 0 0 1 53.837-28.459c34.843 0 63.252 26.823 63.958 60.303a43.089 43.089 0 0 1 23.045 37.772c0 23.876-20.157 43.18-44.759 43.18-.523 0-.896-.02-1.547-.064-.111-.007-4.585-.013-13.393-.018v21.471z\" class=\"cls-1\"/><path d=\"M232.532 215.057c.243.011.448.024.773.047a8.9 8.9 0 0 0 .56.03c16.793 0 30.374-13.006 30.374-28.796 0-11.616-7.432-22.08-18.618-26.575l-4.51-1.813v-4.86c0-.586.015-1.008.055-1.932.031-.705.041-1.002.041-1.317 0-25.988-22.115-47.194-49.587-47.194-19.365 0-36.563 10.65-44.736 27.02l-3.81 7.628-6.877-5.041a20.02 20.02 0 0 0-11.786-3.876c-10.616 0-19.014 8.067-19.014 17.913a17.369 17.369 0 0 0 .794 5.28l1.729 5.529-5.035 2.866a29.349 29.349 0 0 0-15.197 25.468c0 15.785 13.455 28.62 30.66 29.607h113.464c.387.003.387.003.72.016z\" class=\"cls-2\"/><path d=\"M218.924 222.61v6.826c8.808.005 13.282.011 13.393.018.651.045 1.024.063 1.547.063 24.602 0 44.758-19.303 44.758-43.179a43.089 43.089 0 0 0-23.045-37.772c-.705-33.48-29.114-60.303-63.957-60.303a64.37 64.37 0 0 0-53.837 28.46 34.272 34.272 0 0 0-13.372-2.729c-18.433 0-33.398 14.374-33.398 32.297a32.577 32.577 0 0 0 .255 4.096 43.463 43.463 0 0 0-17.964 35.047c0 23.759 19.716 42.567 44.638 43.979l15.06.002v-6.805h9.548v6.806l28.64.005v-4.449h9.547v4.45q17.178.004 28.64.01v-6.822zm-100.575-.378c-20.884-1.183-37.853-16.682-37.853-36.798a36.546 36.546 0 0 1 18.83-31.718 24.56 24.56 0 0 1-1.121-7.425c0-13.852 11.647-25.105 26.206-25.105a27.216 27.216 0 0 1 16.038 5.267c9.141-18.306 28.521-30.998 51.17-30.998 31.41 0 56.78 24.386 56.78 54.386 0 1.09-.095 2.181-.095 3.249 13.508 5.429 23.127 18.283 23.127 33.248 0 19.838-16.874 35.987-37.567 35.987-.74 0-1.384-.093-2.052-.093z\" class=\"cls-2\"/><path d=\"M218.924 222.61h-9.547v35.433h.183v2.411h26.924a13.773 13.773 0 0 1 0-9.547h-17.56V222.61zm-47.734 2.362v33.899a14.19 14.19 0 0 1 9.547 0v-33.899zm-38.187-2.362v28.297h-19.948a13.775 13.775 0 0 1 0 9.547h29.311v-2.411h.184V222.61z\" class=\"cls-2\"/><path d=\"M113.055 250.907a14.174 14.174 0 1 0 0 9.547 13.775 13.775 0 0 0 0-9.547zm-13.466 9.347a4.575 4.575 0 1 1 4.72-4.573 4.653 4.653 0 0 1-4.72 4.573zm71.601-1.383a14.32 14.32 0 1 0 9.547 0 14.19 14.19 0 0 0-9.547 0zm4.773 17.919a4.575 4.575 0 1 1 4.72-4.573 4.653 4.653 0 0 1-4.72 4.573zm60.521-16.336a14.174 14.174 0 1 0 0-9.547 13.773 13.773 0 0 0 0 9.547zm13.467-9.346a4.575 4.575 0 1 1-4.72 4.573 4.655 4.655 0 0 1 4.72-4.573z\" class=\"cls-2\"/></svg>", "svgComplete": "", "svgWhite": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-3.82 -0.07 367.64 360.14\"><defs><style>.cls-1{fill:#fff}</style></defs><path d=\"M98.5 253.002a4.621 4.621 0 1 0 4.767 4.62 4.7 4.7 0 0 0-4.767-4.62zm151.861 9.238a4.621 4.621 0 1 0-4.767-4.618 4.7 4.7 0 0 0 4.767 4.618zm-16.246-38.307c20.899 0 37.94-16.31 37.94-36.346 0-15.115-9.714-28.097-23.357-33.58 0-1.079.096-2.18.096-3.281 0-30.3-25.623-54.93-57.345-54.93-22.876 0-42.449 12.82-51.681 31.308a27.488 27.488 0 0 0-16.199-5.32c-14.704 0-26.467 11.366-26.467 25.356a24.805 24.805 0 0 0 1.133 7.5 36.91 36.91 0 0 0-19.019 32.033c0 20.317 17.139 35.97 38.23 37.166h114.596c.674 0 1.325.094 2.073.094zM86.48 186.673a29.642 29.642 0 0 1 15.349-25.722l5.085-2.894-1.746-5.585a17.543 17.543 0 0 1-.802-5.332c0-9.945 8.482-18.091 19.203-18.091a20.22 20.22 0 0 1 11.904 3.914l6.946 5.091 3.847-7.705c8.256-16.532 25.624-27.288 45.183-27.288 27.746 0 50.082 21.417 50.082 47.665 0 .317-.01.618-.042 1.33-.04.933-.055 1.359-.055 1.95v4.91l4.555 1.83c11.297 4.54 18.803 15.109 18.803 26.84 0 15.948-13.716 29.083-30.677 29.083-.098 0-.225-.006-.565-.03a28.708 28.708 0 0 0-.78-.047c-.337-.013-.337-.013-.729-.017H117.447c-17.378-.996-30.967-13.96-30.967-29.902zm84.389 87.649a4.77 4.77 0 1 0 4.766-4.619 4.7 4.7 0 0 0-4.766 4.62z\" class=\"cls-1\"/><path d=\"M268.18 342.71l82.805-103.833a27.58 27.58 0 0 0 5.326-23.333L326.758 86.066a27.58 27.58 0 0 0-14.922-18.712L192.181 9.731a27.581 27.581 0 0 0-23.934 0L48.592 67.354A27.58 27.58 0 0 0 33.67 86.066L4.118 215.544a27.58 27.58 0 0 0 5.325 23.333L92.247 342.71a27.58 27.58 0 0 0 21.563 10.384h132.808a27.58 27.58 0 0 0 21.563-10.384zm-31.419-89.91a14.315 14.315 0 1 1 0 9.642h-27.193v-2.435h-.185V231.11q-11.57-.005-28.926-.01v29.743a14.462 14.462 0 1 1-9.642 0V231.1l-28.926-.005v28.913h-.185v2.435H112.1a14.315 14.315 0 1 1 0-9.642h20.147v-21.707l-15.211-.002c-25.171-1.426-45.084-20.422-45.084-44.418a43.897 43.897 0 0 1 18.143-35.396 32.899 32.899 0 0 1-.257-4.137c0-18.102 15.114-32.619 33.731-32.619a34.614 34.614 0 0 1 13.506 2.755 65.012 65.012 0 0 1 54.374-28.743c35.19 0 63.883 27.091 64.596 60.904a43.519 43.519 0 0 1 23.274 38.15c0 24.114-20.357 43.61-45.204 43.61-.528 0-.905-.02-1.563-.064-.112-.007-4.63-.013-13.527-.019V252.8z\" class=\"cls-1\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://charts.kubegems.io/kubegems/charts/kubegems-models-1.24.8.tgz"}, "configuration": null, "component": {"version": "models.kubegems.io/v1beta1", "kind": "ModelDeployment", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"description\": \"ModelDeploymentSpec is the spec for a ModelDeployment\",\n   \"properties\": {\n    \"backend\": {\n     \"type\": \"string\"\n    },\n    \"ingress\": {\n     \"properties\": {\n      \"className\": {\n       \"type\": \"string\"\n      },\n      \"gatewayName\": {\n       \"type\": \"string\"\n      },\n      \"host\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"model\": {\n     \"properties\": {\n      \"name\": {\n       \"type\": \"string\"\n      },\n      \"source\": {\n       \"type\": \"string\"\n      },\n      \"task\": {\n       \"type\": \"string\"\n      },\n      \"token\": {\n       \"type\": \"string\"\n      },\n      \"url\": {\n       \"type\": \"string\"\n      },\n      \"version\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"name\",\n      \"source\",\n      \"version\"\n     ],\n     \"type\": \"object\"\n    },\n    \"replicas\": {\n     \"format\": \"int32\",\n     \"type\": \"integer\"\n    },\n    \"server\": {\n     \"properties\": {\n      \"args\": {\n       \"items\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      },\n      \"command\": {\n       \"items\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      },\n      \"env\": {\n       \"items\": {\n        \"description\": \"EnvVar represents an environment variable present in a Container.\",\n        \"properties\": {\n         \"name\": {\n          \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n          \"type\": \"string\"\n         },\n         \"value\": {\n          \"description\": \"Variable references $(VAR_NAME) are expanded\\nusing the previously defined environment variables in the container and\\nany service environment variables. If a variable cannot be resolved,\\nthe reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.\\n\\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\".\\nEscaped references will never be expanded, regardless of whether the variable\\nexists or not.\\nDefaults to \\\"\\\".\",\n          \"type\": \"string\"\n         },\n         \"valueFrom\": {\n          \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n          \"properties\": {\n           \"configMapKeyRef\": {\n            \"description\": \"Selects a key of a ConfigMap.\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key to select.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"fieldRef\": {\n            \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`,\\nspec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\",\n            \"properties\": {\n             \"apiVersion\": {\n              \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n              \"type\": \"string\"\n             },\n             \"fieldPath\": {\n              \"description\": \"Path of the field to select in the specified API version.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"fieldPath\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"resourceFieldRef\": {\n            \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n            \"properties\": {\n             \"containerName\": {\n              \"description\": \"Container name: required for volumes, optional for env vars\",\n              \"type\": \"string\"\n             },\n             \"divisor\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"resource\": {\n              \"description\": \"Required: resource to select\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"resource\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"secretKeyRef\": {\n            \"description\": \"Selects a key of a secret in the pod's namespace\",\n            \"properties\": {\n             \"key\": {\n              \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n              \"type\": \"string\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its key must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"key\"\n            ],\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"name\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"image\": {\n       \"type\": \"string\"\n      },\n      \"kind\": {\n       \"type\": \"string\"\n      },\n      \"metadata\": {\n       \"format\": \"textarea\",\n       \"type\": \"string\"\n      },\n      \"mounts\": {\n       \"items\": {\n        \"properties\": {\n         \"kind\": {\n          \"enum\": [\n           \"HostPath\",\n           \"EmptyDir\",\n           \"Secret\",\n           \"ConfigMap\",\n           \"PVC\",\n           \"Model\"\n          ],\n          \"type\": \"string\"\n         },\n         \"mountPath\": {\n          \"type\": \"string\"\n         },\n         \"source\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"kind\",\n         \"mountPath\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"parameters\": {\n       \"items\": {\n        \"properties\": {\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"value\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"name\",\n         \"value\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"podSpec\": {\n       \"description\": \"PodSpec is a description of a pod.\",\n       \"properties\": {\n        \"activeDeadlineSeconds\": {\n         \"description\": \"Optional duration in seconds the pod may be active on the node relative to\\nStartTime before the system will actively try to mark it failed and kill associated containers.\\nValue must be a positive integer.\",\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"affinity\": {\n         \"description\": \"If specified, the pod's scheduling constraints\",\n         \"properties\": {\n          \"nodeAffinity\": {\n           \"description\": \"Describes node affinity scheduling rules for the pod.\",\n           \"properties\": {\n            \"preferredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy\\nthe affinity expressions specified by this field, but it may choose\\na node that violates one or more of the expressions. The node that is\\nmost preferred is the one with the greatest sum of weights, i.e.\\nfor each node that meets all of the scheduling requirements (resource\\nrequest, requiredDuringScheduling affinity expressions, etc.),\\ncompute a sum by iterating through the elements of this field and adding\\n\\\"weight\\\" to the sum if the node matches the corresponding matchExpressions; the\\nnode(s) with the highest sum are the most preferred.\",\n             \"items\": {\n              \"description\": \"An empty preferred scheduling term matches all objects with implicit weight 0\\n(i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).\",\n              \"properties\": {\n               \"preference\": {\n                \"description\": \"A node selector term, associated with the corresponding weight.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"A list of node selector requirements by node's labels.\",\n                  \"items\": {\n                   \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"The label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"matchFields\": {\n                  \"description\": \"A list of node selector requirements by node's fields.\",\n                  \"items\": {\n                   \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"The label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"weight\": {\n                \"description\": \"Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"preference\",\n               \"weight\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"requiredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"If the affinity requirements specified by this field are not met at\\nscheduling time, the pod will not be scheduled onto the node.\\nIf the affinity requirements specified by this field cease to be met\\nat some point during pod execution (e.g. due to an update), the system\\nmay or may not try to eventually evict the pod from its node.\",\n             \"properties\": {\n              \"nodeSelectorTerms\": {\n               \"description\": \"Required. A list of node selector terms. The terms are ORed.\",\n               \"items\": {\n                \"description\": \"A null or empty node selector term matches no objects. The requirements of\\nthem are ANDed.\\nThe TopologySelectorTerm type implements a subset of the NodeSelectorTerm.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"A list of node selector requirements by node's labels.\",\n                  \"items\": {\n                   \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"The label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"matchFields\": {\n                  \"description\": \"A list of node selector requirements by node's fields.\",\n                  \"items\": {\n                   \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"The label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"type\": \"array\"\n              }\n             },\n             \"required\": [\n              \"nodeSelectorTerms\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"podAffinity\": {\n           \"description\": \"Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).\",\n           \"properties\": {\n            \"preferredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy\\nthe affinity expressions specified by this field, but it may choose\\na node that violates one or more of the expressions. The node that is\\nmost preferred is the one with the greatest sum of weights, i.e.\\nfor each node that meets all of the scheduling requirements (resource\\nrequest, requiredDuringScheduling affinity expressions, etc.),\\ncompute a sum by iterating through the elements of this field and adding\\n\\\"weight\\\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the\\nnode(s) with the highest sum are the most preferred.\",\n             \"items\": {\n              \"description\": \"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\n              \"properties\": {\n               \"podAffinityTerm\": {\n                \"description\": \"Required. A pod affinity term, associated with the corresponding weight.\",\n                \"properties\": {\n                 \"labelSelector\": {\n                  \"description\": \"A label query over a set of resources, in this case pods.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"namespaceSelector\": {\n                  \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\\nThis field is beta-level and is only honored when PodAffinityNamespaceSelector feature is enabled.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"namespaces\": {\n                  \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\"\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"topologyKey\": {\n                  \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"topologyKey\"\n                ],\n                \"type\": \"object\"\n               },\n               \"weight\": {\n                \"description\": \"weight associated with matching the corresponding podAffinityTerm,\\nin the range 1-100.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"podAffinityTerm\",\n               \"weight\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"requiredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"If the affinity requirements specified by this field are not met at\\nscheduling time, the pod will not be scheduled onto the node.\\nIf the affinity requirements specified by this field cease to be met\\nat some point during pod execution (e.g. due to a pod label update), the\\nsystem may or may not try to eventually evict the pod from its node.\\nWhen there are multiple elements, the lists of nodes corresponding to each\\npodAffinityTerm are intersected, i.e. all terms must be satisfied.\",\n             \"items\": {\n              \"description\": \"Defines a set of pods (namely those matching the labelSelector\\nrelative to the given namespace(s)) that this pod should be\\nco-located (affinity) or not co-located (anti-affinity) with,\\nwhere co-located is defined as running on a node whose value of\\nthe label with key \\u003ctopologyKey\\u003e matches that of any node on which\\na pod of the set of pods is running\",\n              \"properties\": {\n               \"labelSelector\": {\n                \"description\": \"A label query over a set of resources, in this case pods.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                  \"items\": {\n                   \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"key is the label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaceSelector\": {\n                \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\\nThis field is beta-level and is only honored when PodAffinityNamespaceSelector feature is enabled.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                  \"items\": {\n                   \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"key is the label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaces\": {\n                \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\"\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               },\n               \"topologyKey\": {\n                \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"topologyKey\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"podAntiAffinity\": {\n           \"description\": \"Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).\",\n           \"properties\": {\n            \"preferredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy\\nthe anti-affinity expressions specified by this field, but it may choose\\na node that violates one or more of the expressions. The node that is\\nmost preferred is the one with the greatest sum of weights, i.e.\\nfor each node that meets all of the scheduling requirements (resource\\nrequest, requiredDuringScheduling anti-affinity expressions, etc.),\\ncompute a sum by iterating through the elements of this field and adding\\n\\\"weight\\\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the\\nnode(s) with the highest sum are the most preferred.\",\n             \"items\": {\n              \"description\": \"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\n              \"properties\": {\n               \"podAffinityTerm\": {\n                \"description\": \"Required. A pod affinity term, associated with the corresponding weight.\",\n                \"properties\": {\n                 \"labelSelector\": {\n                  \"description\": \"A label query over a set of resources, in this case pods.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"namespaceSelector\": {\n                  \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\\nThis field is beta-level and is only honored when PodAffinityNamespaceSelector feature is enabled.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"namespaces\": {\n                  \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\"\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"topologyKey\": {\n                  \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"topologyKey\"\n                ],\n                \"type\": \"object\"\n               },\n               \"weight\": {\n                \"description\": \"weight associated with matching the corresponding podAffinityTerm,\\nin the range 1-100.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               }\n              },\n              \"required\": [\n               \"podAffinityTerm\",\n               \"weight\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            },\n            \"requiredDuringSchedulingIgnoredDuringExecution\": {\n             \"description\": \"If the anti-affinity requirements specified by this field are not met at\\nscheduling time, the pod will not be scheduled onto the node.\\nIf the anti-affinity requirements specified by this field cease to be met\\nat some point during pod execution (e.g. due to a pod label update), the\\nsystem may or may not try to eventually evict the pod from its node.\\nWhen there are multiple elements, the lists of nodes corresponding to each\\npodAffinityTerm are intersected, i.e. all terms must be satisfied.\",\n             \"items\": {\n              \"description\": \"Defines a set of pods (namely those matching the labelSelector\\nrelative to the given namespace(s)) that this pod should be\\nco-located (affinity) or not co-located (anti-affinity) with,\\nwhere co-located is defined as running on a node whose value of\\nthe label with key \\u003ctopologyKey\\u003e matches that of any node on which\\na pod of the set of pods is running\",\n              \"properties\": {\n               \"labelSelector\": {\n                \"description\": \"A label query over a set of resources, in this case pods.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                  \"items\": {\n                   \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"key is the label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaceSelector\": {\n                \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\\nThis field is beta-level and is only honored when PodAffinityNamespaceSelector feature is enabled.\",\n                \"properties\": {\n                 \"matchExpressions\": {\n                  \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                  \"items\": {\n                   \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                   \"properties\": {\n                    \"key\": {\n                     \"description\": \"key is the label key that the selector applies to.\",\n                     \"type\": \"string\"\n                    },\n                    \"operator\": {\n                     \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                     \"type\": \"string\"\n                    },\n                    \"values\": {\n                     \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                     \"items\": {\n                      \"type\": \"string\"\n                     },\n                     \"type\": \"array\"\n                    }\n                   },\n                   \"required\": [\n                    \"key\",\n                    \"operator\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"matchLabels\": {\n                  \"additionalProperties\": {\n                   \"type\": \"string\"\n                  },\n                  \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                  \"type\": \"object\"\n                 }\n                },\n                \"type\": \"object\",\n                \"x-kubernetes-map-type\": \"atomic\"\n               },\n               \"namespaces\": {\n                \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\"\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               },\n               \"topologyKey\": {\n                \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"topologyKey\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"automountServiceAccountToken\": {\n         \"description\": \"AutomountServiceAccountToken indicates whether a service account token should be automatically mounted.\",\n         \"type\": \"boolean\"\n        },\n        \"containers\": {\n         \"description\": \"List of containers belonging to the pod.\\nContainers cannot currently be added or removed.\\nThere must be at least one container in a Pod.\\nCannot be updated.\",\n         \"items\": {\n          \"description\": \"A single application container that you want to run within a pod.\",\n          \"properties\": {\n           \"args\": {\n            \"description\": \"Arguments to the entrypoint.\\nThe docker image's CMD is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"command\": {\n            \"description\": \"Entrypoint array. Not executed within a shell.\\nThe docker image's ENTRYPOINT is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"env\": {\n            \"description\": \"List of environment variables to set in the container.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"EnvVar represents an environment variable present in a Container.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"Variable references $(VAR_NAME) are expanded\\nusing the previously defined environment variables in the container and\\nany service environment variables. If a variable cannot be resolved,\\nthe reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.\\n\\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\".\\nEscaped references will never be expanded, regardless of whether the variable\\nexists or not.\\nDefaults to \\\"\\\".\",\n               \"type\": \"string\"\n              },\n              \"valueFrom\": {\n               \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n               \"properties\": {\n                \"configMapKeyRef\": {\n                 \"description\": \"Selects a key of a ConfigMap.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key to select.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"fieldRef\": {\n                 \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`,\\nspec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\",\n                 \"properties\": {\n                  \"apiVersion\": {\n                   \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                   \"type\": \"string\"\n                  },\n                  \"fieldPath\": {\n                   \"description\": \"Path of the field to select in the specified API version.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"fieldPath\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"resourceFieldRef\": {\n                 \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n                 \"properties\": {\n                  \"containerName\": {\n                   \"description\": \"Container name: required for volumes, optional for env vars\",\n                   \"type\": \"string\"\n                  },\n                  \"divisor\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"resource\": {\n                   \"description\": \"Required: resource to select\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resource\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"secretKeyRef\": {\n                 \"description\": \"Selects a key of a secret in the pod's namespace\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the Secret or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"envFrom\": {\n            \"description\": \"List of sources to populate environment variables in the container.\\nThe keys defined within a source must be a C_IDENTIFIER. All invalid keys\\nwill be reported as an event when the container is starting. When a key exists in multiple\\nsources, the value associated with the last source will take precedence.\\nValues defined by an Env with a duplicate key will take precedence.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"EnvFromSource represents the source of a set of ConfigMaps\",\n             \"properties\": {\n              \"configMapRef\": {\n               \"description\": \"The ConfigMap to select from\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the ConfigMap must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"prefix\": {\n               \"description\": \"An optional identifier to prepend to each key in the ConfigMap. Must be a C_IDENTIFIER.\",\n               \"type\": \"string\"\n              },\n              \"secretRef\": {\n               \"description\": \"The Secret to select from\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"image\": {\n            \"description\": \"Docker image name.\\nMore info: https://kubernetes.io/docs/concepts/containers/images\\nThis field is optional to allow higher level config management to default or override\\ncontainer images in workload controllers like Deployments and StatefulSets.\",\n            \"type\": \"string\"\n           },\n           \"imagePullPolicy\": {\n            \"description\": \"Image pull policy.\\nOne of Always, Never, IfNotPresent.\\nDefaults to Always if :latest tag is specified, or IfNotPresent otherwise.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\n            \"type\": \"string\"\n           },\n           \"lifecycle\": {\n            \"description\": \"Actions that the management system should take in response to container lifecycle events.\\nCannot be updated.\",\n            \"properties\": {\n             \"postStart\": {\n              \"description\": \"PostStart is called immediately after a container is created. If the handler fails,\\nthe container is terminated and restarted according to its restart policy.\\nOther management of the container blocks until the hook completes.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n              \"properties\": {\n               \"exec\": {\n                \"description\": \"Exec specifies the action to take.\",\n                \"properties\": {\n                 \"command\": {\n                  \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"httpGet\": {\n                \"description\": \"HTTPGet specifies the http request to perform.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                  \"type\": \"string\"\n                 },\n                 \"httpHeaders\": {\n                  \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                  \"items\": {\n                   \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                   \"properties\": {\n                    \"name\": {\n                     \"description\": \"The header field name\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"The header field value\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"path\": {\n                  \"description\": \"Path to access on the HTTP server.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 },\n                 \"scheme\": {\n                  \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               },\n               \"tcpSocket\": {\n                \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor the backward compatibility. There are no validation of this field and\\nlifecycle hooks will fail in runtime when tcp handler is specified.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"preStop\": {\n              \"description\": \"PreStop is called immediately before a container is terminated due to an\\nAPI request or management event such as liveness/startup probe failure,\\npreemption, resource contention, etc. The handler is not called if the\\ncontainer crashes or exits. The Pod's termination grace period countdown begins before the\\nPreStop hook is executed. Regardless of the outcome of the handler, the\\ncontainer will eventually terminate within the Pod's termination grace\\nperiod (unless delayed by finalizers). Other management of the container blocks until the hook completes\\nor until the termination grace period is reached.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n              \"properties\": {\n               \"exec\": {\n                \"description\": \"Exec specifies the action to take.\",\n                \"properties\": {\n                 \"command\": {\n                  \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"httpGet\": {\n                \"description\": \"HTTPGet specifies the http request to perform.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                  \"type\": \"string\"\n                 },\n                 \"httpHeaders\": {\n                  \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                  \"items\": {\n                   \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                   \"properties\": {\n                    \"name\": {\n                     \"description\": \"The header field name\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"The header field value\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"path\": {\n                  \"description\": \"Path to access on the HTTP server.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 },\n                 \"scheme\": {\n                  \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               },\n               \"tcpSocket\": {\n                \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor the backward compatibility. There are no validation of this field and\\nlifecycle hooks will fail in runtime when tcp handler is specified.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"livenessProbe\": {\n            \"description\": \"Periodic probe of container liveness.\\nContainer will be restarted if the probe fails.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n            \"properties\": {\n             \"exec\": {\n              \"description\": \"Exec specifies the action to take.\",\n              \"properties\": {\n               \"command\": {\n                \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"description\": \"GRPC specifies an action involving a GRPC port.\\nThis is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n              \"properties\": {\n               \"port\": {\n                \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"description\": \"HTTPGet specifies the http request to perform.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                \"items\": {\n                 \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The header field name\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"The header field value\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"path\": {\n                \"description\": \"Path to access on the HTTP server.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"name\": {\n            \"description\": \"Name of the container specified as a DNS_LABEL.\\nEach container in a pod must have a unique name (DNS_LABEL).\\nCannot be updated.\",\n            \"type\": \"string\"\n           },\n           \"ports\": {\n            \"description\": \"List of ports to expose from the container. Exposing a port here gives\\nthe system additional information about the network connections a\\ncontainer uses, but is primarily informational. Not specifying a port here\\nDOES NOT prevent that port from being exposed. Any port which is\\nlistening on the default \\\"0.0.0.0\\\" address inside a container will be\\naccessible from the network.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"ContainerPort represents a network port in a single container.\",\n             \"properties\": {\n              \"containerPort\": {\n               \"description\": \"Number of port to expose on the pod's IP address.\\nThis must be a valid port number, 0 \\u003c x \\u003c 65536.\",\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"hostIP\": {\n               \"description\": \"What host IP to bind the external port to.\",\n               \"type\": \"string\"\n              },\n              \"hostPort\": {\n               \"description\": \"Number of port to expose on the host.\\nIf specified, this must be a valid port number, 0 \\u003c x \\u003c 65536.\\nIf HostNetwork is specified, this must match ContainerPort.\\nMost containers do not need this.\",\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"name\": {\n               \"description\": \"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each\\nnamed port in a pod must have a unique name. Name for the port that can be\\nreferred to by services.\",\n               \"type\": \"string\"\n              },\n              \"protocol\": {\n               \"default\": \"TCP\",\n               \"description\": \"Protocol for port. Must be UDP, TCP, or SCTP.\\nDefaults to \\\"TCP\\\".\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"containerPort\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"containerPort\",\n             \"protocol\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"readinessProbe\": {\n            \"description\": \"Periodic probe of container service readiness.\\nContainer will be removed from service endpoints if the probe fails.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n            \"properties\": {\n             \"exec\": {\n              \"description\": \"Exec specifies the action to take.\",\n              \"properties\": {\n               \"command\": {\n                \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"description\": \"GRPC specifies an action involving a GRPC port.\\nThis is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n              \"properties\": {\n               \"port\": {\n                \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"description\": \"HTTPGet specifies the http request to perform.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                \"items\": {\n                 \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The header field name\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"The header field value\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"path\": {\n                \"description\": \"Path to access on the HTTP server.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"resources\": {\n            \"description\": \"Compute Resources required by this container.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n            \"properties\": {\n             \"limits\": {\n              \"additionalProperties\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n              \"type\": \"object\"\n             },\n             \"requests\": {\n              \"additionalProperties\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"securityContext\": {\n            \"description\": \"SecurityContext defines the security options the container should be run with.\\nIf set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.\\nMore info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/\",\n            \"properties\": {\n             \"allowPrivilegeEscalation\": {\n              \"description\": \"AllowPrivilegeEscalation controls whether a process can gain more\\nprivileges than its parent process. This bool directly controls if\\nthe no_new_privs flag will be set on the container process.\\nAllowPrivilegeEscalation is true always when the container is:\\n1) run as Privileged\\n2) has CAP_SYS_ADMIN\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"boolean\"\n             },\n             \"capabilities\": {\n              \"description\": \"The capabilities to add/drop when running containers.\\nDefaults to the default set of capabilities granted by the container runtime.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"properties\": {\n               \"add\": {\n                \"description\": \"Added capabilities\",\n                \"items\": {\n                 \"description\": \"Capability represent POSIX capabilities type\",\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               },\n               \"drop\": {\n                \"description\": \"Removed capabilities\",\n                \"items\": {\n                 \"description\": \"Capability represent POSIX capabilities type\",\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"privileged\": {\n              \"description\": \"Run container in privileged mode.\\nProcesses in privileged containers are essentially equivalent to root on the host.\\nDefaults to false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"boolean\"\n             },\n             \"procMount\": {\n              \"description\": \"procMount denotes the type of proc mount to use for the containers.\\nThe default is DefaultProcMount which uses the container runtime defaults for\\nreadonly paths and masked paths.\\nThis requires the ProcMountType feature flag to be enabled.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"string\"\n             },\n             \"readOnlyRootFilesystem\": {\n              \"description\": \"Whether this container has a read-only root filesystem.\\nDefault is false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"boolean\"\n             },\n             \"runAsGroup\": {\n              \"description\": \"The GID to run the entrypoint of the container process.\\nUses runtime default if unset.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"runAsNonRoot\": {\n              \"description\": \"Indicates that the container must run as a non-root user.\\nIf true, the Kubelet will validate the image at runtime to ensure that it\\ndoes not run as UID 0 (root) and fail to start the container if it does.\\nIf unset or false, no such validation will be performed.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n              \"type\": \"boolean\"\n             },\n             \"runAsUser\": {\n              \"description\": \"The UID to run the entrypoint of the container process.\\nDefaults to user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"seLinuxOptions\": {\n              \"description\": \"The SELinux context to be applied to the container.\\nIf unspecified, the container runtime will allocate a random SELinux context for each\\ncontainer.  May also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"properties\": {\n               \"level\": {\n                \"description\": \"Level is SELinux level label that applies to the container.\",\n                \"type\": \"string\"\n               },\n               \"role\": {\n                \"description\": \"Role is a SELinux role label that applies to the container.\",\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"description\": \"Type is a SELinux type label that applies to the container.\",\n                \"type\": \"string\"\n               },\n               \"user\": {\n                \"description\": \"User is a SELinux user label that applies to the container.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"seccompProfile\": {\n              \"description\": \"The seccomp options to use by this container. If seccomp options are\\nprovided at both the pod \\u0026 container level, the container options\\noverride the pod options.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"properties\": {\n               \"localhostProfile\": {\n                \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used.\\nThe profile must be preconfigured on the node to work.\\nMust be a descending path, relative to the kubelet's configured seccomp profile location.\\nMust only be set if type is \\\"Localhost\\\".\",\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"description\": \"type indicates which kind of seccomp profile will be applied.\\nValid options are:\\n\\n\\nLocalhost - a profile defined in a file on the node should be used.\\nRuntimeDefault - the container runtime default profile should be used.\\nUnconfined - no profile should be applied.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"type\"\n              ],\n              \"type\": \"object\"\n             },\n             \"windowsOptions\": {\n              \"description\": \"The Windows specific settings applied to all containers.\\nIf unspecified, the options from the PodSecurityContext will be used.\\nIf set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is linux.\",\n              \"properties\": {\n               \"gmsaCredentialSpec\": {\n                \"description\": \"GMSACredentialSpec is where the GMSA admission webhook\\n(https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the\\nGMSA credential spec named by the GMSACredentialSpecName field.\",\n                \"type\": \"string\"\n               },\n               \"gmsaCredentialSpecName\": {\n                \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n                \"type\": \"string\"\n               },\n               \"hostProcess\": {\n                \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container.\\nThis field is alpha-level and will only be honored by components that enable the\\nWindowsHostProcessContainers feature flag. Setting this field without the feature\\nflag will result in errors when validating the Pod. All of a Pod's containers must\\nhave the same effective HostProcess value (it is not allowed to have a mix of HostProcess\\ncontainers and non-HostProcess containers).  In addition, if HostProcess is true\\nthen HostNetwork must also be set to true.\",\n                \"type\": \"boolean\"\n               },\n               \"runAsUserName\": {\n                \"description\": \"The UserName in Windows to run the entrypoint of the container process.\\nDefaults to the user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext. If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"startupProbe\": {\n            \"description\": \"StartupProbe indicates that the Pod has successfully initialized.\\nIf specified, no other probes are executed until this completes successfully.\\nIf this probe fails, the Pod will be restarted, just as if the livenessProbe failed.\\nThis can be used to provide different probe parameters at the beginning of a Pod's lifecycle,\\nwhen it might take a long time to load data or warm a cache, than during steady-state operation.\\nThis cannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n            \"properties\": {\n             \"exec\": {\n              \"description\": \"Exec specifies the action to take.\",\n              \"properties\": {\n               \"command\": {\n                \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"description\": \"GRPC specifies an action involving a GRPC port.\\nThis is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n              \"properties\": {\n               \"port\": {\n                \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"description\": \"HTTPGet specifies the http request to perform.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                \"items\": {\n                 \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The header field name\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"The header field value\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"path\": {\n                \"description\": \"Path to access on the HTTP server.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"stdin\": {\n            \"description\": \"Whether this container should allocate a buffer for stdin in the container runtime. If this\\nis not set, reads from stdin in the container will always result in EOF.\\nDefault is false.\",\n            \"type\": \"boolean\"\n           },\n           \"stdinOnce\": {\n            \"description\": \"Whether the container runtime should close the stdin channel after it has been opened by\\na single attach. When stdin is true the stdin stream will remain open across multiple attach\\nsessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the\\nfirst client attaches to stdin, and then remains open and accepts data until the client disconnects,\\nat which time stdin is closed and remains closed until the container is restarted. If this\\nflag is false, a container processes that reads from stdin will never receive an EOF.\\nDefault is false\",\n            \"type\": \"boolean\"\n           },\n           \"terminationMessagePath\": {\n            \"description\": \"Optional: Path at which the file to which the container's termination message\\nwill be written is mounted into the container's filesystem.\\nMessage written is intended to be brief final status, such as an assertion failure message.\\nWill be truncated by the node if greater than 4096 bytes. The total message length across\\nall containers will be limited to 12kb.\\nDefaults to /dev/termination-log.\\nCannot be updated.\",\n            \"type\": \"string\"\n           },\n           \"terminationMessagePolicy\": {\n            \"description\": \"Indicate how the termination message should be populated. File will use the contents of\\nterminationMessagePath to populate the container status message on both success and failure.\\nFallbackToLogsOnError will use the last chunk of container log output if the termination\\nmessage file is empty and the container exited with an error.\\nThe log output is limited to 2048 bytes or 80 lines, whichever is smaller.\\nDefaults to File.\\nCannot be updated.\",\n            \"type\": \"string\"\n           },\n           \"tty\": {\n            \"description\": \"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.\\nDefault is false.\",\n            \"type\": \"boolean\"\n           },\n           \"volumeDevices\": {\n            \"description\": \"volumeDevices is the list of block devices to be used by the container.\",\n            \"items\": {\n             \"description\": \"volumeDevice describes a mapping of a raw block device within a container.\",\n             \"properties\": {\n              \"devicePath\": {\n               \"description\": \"devicePath is the path inside of the container that the device will be mapped to.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"name must match the name of a persistentVolumeClaim in the pod\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"devicePath\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"volumeMounts\": {\n            \"description\": \"Pod volumes to mount into the container's filesystem.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n             \"properties\": {\n              \"mountPath\": {\n               \"description\": \"Path within the container at which the volume should be mounted.  Must\\nnot contain ':'.\",\n               \"type\": \"string\"\n              },\n              \"mountPropagation\": {\n               \"description\": \"mountPropagation determines how mounts are propagated from the host\\nto container and the other way around.\\nWhen not set, MountPropagationNone is used.\\nThis field is beta in 1.10.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"This must match the Name of a Volume.\",\n               \"type\": \"string\"\n              },\n              \"readOnly\": {\n               \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified).\\nDefaults to false.\",\n               \"type\": \"boolean\"\n              },\n              \"subPath\": {\n               \"description\": \"Path within the volume from which the container's volume should be mounted.\\nDefaults to \\\"\\\" (volume's root).\",\n               \"type\": \"string\"\n              },\n              \"subPathExpr\": {\n               \"description\": \"Expanded path within the volume from which the container's volume should be mounted.\\nBehaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment.\\nDefaults to \\\"\\\" (volume's root).\\nSubPathExpr and SubPath are mutually exclusive.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"mountPath\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"workingDir\": {\n            \"description\": \"Container's working directory.\\nIf not specified, the container runtime's default will be used, which\\nmight be configured in the container image.\\nCannot be updated.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"dnsConfig\": {\n         \"description\": \"Specifies the DNS parameters of a pod.\\nParameters specified here will be merged to the generated DNS\\nconfiguration based on DNSPolicy.\",\n         \"properties\": {\n          \"nameservers\": {\n           \"description\": \"A list of DNS name server IP addresses.\\nThis will be appended to the base nameservers generated from DNSPolicy.\\nDuplicated nameservers will be removed.\",\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          },\n          \"options\": {\n           \"description\": \"A list of DNS resolver options.\\nThis will be merged with the base options generated from DNSPolicy.\\nDuplicated entries will be removed. Resolution options given in Options\\nwill override those that appear in the base DNSPolicy.\",\n           \"items\": {\n            \"description\": \"PodDNSConfigOption defines DNS resolver options of a pod.\",\n            \"properties\": {\n             \"name\": {\n              \"description\": \"Required.\",\n              \"type\": \"string\"\n             },\n             \"value\": {\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"type\": \"array\"\n          },\n          \"searches\": {\n           \"description\": \"A list of DNS search domains for host-name lookup.\\nThis will be appended to the base search paths generated from DNSPolicy.\\nDuplicated search paths will be removed.\",\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"dnsPolicy\": {\n         \"description\": \"Set DNS policy for the pod.\\nDefaults to \\\"ClusterFirst\\\".\\nValid values are 'ClusterFirstWithHostNet', 'ClusterFirst', 'Default' or 'None'.\\nDNS parameters given in DNSConfig will be merged with the policy selected with DNSPolicy.\\nTo have DNS options set along with hostNetwork, you have to specify DNS policy\\nexplicitly to 'ClusterFirstWithHostNet'.\",\n         \"type\": \"string\"\n        },\n        \"enableServiceLinks\": {\n         \"description\": \"EnableServiceLinks indicates whether information about services should be injected into pod's\\nenvironment variables, matching the syntax of Docker links.\\nOptional: Defaults to true.\",\n         \"type\": \"boolean\"\n        },\n        \"ephemeralContainers\": {\n         \"description\": \"List of ephemeral containers run in this pod. Ephemeral containers may be run in an existing\\npod to perform user-initiated actions such as debugging. This list cannot be specified when\\ncreating a pod, and it cannot be modified by updating the pod spec. In order to add an\\nephemeral container to an existing pod, use the pod's ephemeralcontainers subresource.\\nThis field is beta-level and available on clusters that haven't disabled the EphemeralContainers feature gate.\",\n         \"items\": {\n          \"description\": \"An EphemeralContainer is a temporary container that you may add to an existing Pod for\\nuser-initiated activities such as debugging. Ephemeral containers have no resource or\\nscheduling guarantees, and they will not be restarted when they exit or when a Pod is\\nremoved or restarted. The kubelet may evict a Pod if an ephemeral container causes the\\nPod to exceed its resource allocation.\\n\\n\\nTo add an ephemeral container, use the ephemeralcontainers subresource of an existing\\nPod. Ephemeral containers may not be removed or restarted.\\n\\n\\nThis is a beta feature available on clusters that haven't disabled the EphemeralContainers feature gate.\",\n          \"properties\": {\n           \"args\": {\n            \"description\": \"Arguments to the entrypoint.\\nThe docker image's CMD is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"command\": {\n            \"description\": \"Entrypoint array. Not executed within a shell.\\nThe docker image's ENTRYPOINT is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"env\": {\n            \"description\": \"List of environment variables to set in the container.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"EnvVar represents an environment variable present in a Container.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"Variable references $(VAR_NAME) are expanded\\nusing the previously defined environment variables in the container and\\nany service environment variables. If a variable cannot be resolved,\\nthe reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.\\n\\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\".\\nEscaped references will never be expanded, regardless of whether the variable\\nexists or not.\\nDefaults to \\\"\\\".\",\n               \"type\": \"string\"\n              },\n              \"valueFrom\": {\n               \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n               \"properties\": {\n                \"configMapKeyRef\": {\n                 \"description\": \"Selects a key of a ConfigMap.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key to select.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"fieldRef\": {\n                 \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`,\\nspec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\",\n                 \"properties\": {\n                  \"apiVersion\": {\n                   \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                   \"type\": \"string\"\n                  },\n                  \"fieldPath\": {\n                   \"description\": \"Path of the field to select in the specified API version.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"fieldPath\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"resourceFieldRef\": {\n                 \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n                 \"properties\": {\n                  \"containerName\": {\n                   \"description\": \"Container name: required for volumes, optional for env vars\",\n                   \"type\": \"string\"\n                  },\n                  \"divisor\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"resource\": {\n                   \"description\": \"Required: resource to select\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resource\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"secretKeyRef\": {\n                 \"description\": \"Selects a key of a secret in the pod's namespace\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the Secret or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"envFrom\": {\n            \"description\": \"List of sources to populate environment variables in the container.\\nThe keys defined within a source must be a C_IDENTIFIER. All invalid keys\\nwill be reported as an event when the container is starting. When a key exists in multiple\\nsources, the value associated with the last source will take precedence.\\nValues defined by an Env with a duplicate key will take precedence.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"EnvFromSource represents the source of a set of ConfigMaps\",\n             \"properties\": {\n              \"configMapRef\": {\n               \"description\": \"The ConfigMap to select from\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the ConfigMap must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"prefix\": {\n               \"description\": \"An optional identifier to prepend to each key in the ConfigMap. Must be a C_IDENTIFIER.\",\n               \"type\": \"string\"\n              },\n              \"secretRef\": {\n               \"description\": \"The Secret to select from\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"image\": {\n            \"description\": \"Docker image name.\\nMore info: https://kubernetes.io/docs/concepts/containers/images\",\n            \"type\": \"string\"\n           },\n           \"imagePullPolicy\": {\n            \"description\": \"Image pull policy.\\nOne of Always, Never, IfNotPresent.\\nDefaults to Always if :latest tag is specified, or IfNotPresent otherwise.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\n            \"type\": \"string\"\n           },\n           \"lifecycle\": {\n            \"description\": \"Lifecycle is not allowed for ephemeral containers.\",\n            \"properties\": {\n             \"postStart\": {\n              \"description\": \"PostStart is called immediately after a container is created. If the handler fails,\\nthe container is terminated and restarted according to its restart policy.\\nOther management of the container blocks until the hook completes.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n              \"properties\": {\n               \"exec\": {\n                \"description\": \"Exec specifies the action to take.\",\n                \"properties\": {\n                 \"command\": {\n                  \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"httpGet\": {\n                \"description\": \"HTTPGet specifies the http request to perform.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                  \"type\": \"string\"\n                 },\n                 \"httpHeaders\": {\n                  \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                  \"items\": {\n                   \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                   \"properties\": {\n                    \"name\": {\n                     \"description\": \"The header field name\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"The header field value\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"path\": {\n                  \"description\": \"Path to access on the HTTP server.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 },\n                 \"scheme\": {\n                  \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               },\n               \"tcpSocket\": {\n                \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor the backward compatibility. There are no validation of this field and\\nlifecycle hooks will fail in runtime when tcp handler is specified.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"preStop\": {\n              \"description\": \"PreStop is called immediately before a container is terminated due to an\\nAPI request or management event such as liveness/startup probe failure,\\npreemption, resource contention, etc. The handler is not called if the\\ncontainer crashes or exits. The Pod's termination grace period countdown begins before the\\nPreStop hook is executed. Regardless of the outcome of the handler, the\\ncontainer will eventually terminate within the Pod's termination grace\\nperiod (unless delayed by finalizers). Other management of the container blocks until the hook completes\\nor until the termination grace period is reached.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n              \"properties\": {\n               \"exec\": {\n                \"description\": \"Exec specifies the action to take.\",\n                \"properties\": {\n                 \"command\": {\n                  \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"httpGet\": {\n                \"description\": \"HTTPGet specifies the http request to perform.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                  \"type\": \"string\"\n                 },\n                 \"httpHeaders\": {\n                  \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                  \"items\": {\n                   \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                   \"properties\": {\n                    \"name\": {\n                     \"description\": \"The header field name\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"The header field value\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"path\": {\n                  \"description\": \"Path to access on the HTTP server.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 },\n                 \"scheme\": {\n                  \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               },\n               \"tcpSocket\": {\n                \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor the backward compatibility. There are no validation of this field and\\nlifecycle hooks will fail in runtime when tcp handler is specified.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"livenessProbe\": {\n            \"description\": \"Probes are not allowed for ephemeral containers.\",\n            \"properties\": {\n             \"exec\": {\n              \"description\": \"Exec specifies the action to take.\",\n              \"properties\": {\n               \"command\": {\n                \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"description\": \"GRPC specifies an action involving a GRPC port.\\nThis is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n              \"properties\": {\n               \"port\": {\n                \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"description\": \"HTTPGet specifies the http request to perform.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                \"items\": {\n                 \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The header field name\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"The header field value\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"path\": {\n                \"description\": \"Path to access on the HTTP server.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"name\": {\n            \"description\": \"Name of the ephemeral container specified as a DNS_LABEL.\\nThis name must be unique among all containers, init containers and ephemeral containers.\",\n            \"type\": \"string\"\n           },\n           \"ports\": {\n            \"description\": \"Ports are not allowed for ephemeral containers.\",\n            \"items\": {\n             \"description\": \"ContainerPort represents a network port in a single container.\",\n             \"properties\": {\n              \"containerPort\": {\n               \"description\": \"Number of port to expose on the pod's IP address.\\nThis must be a valid port number, 0 \\u003c x \\u003c 65536.\",\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"hostIP\": {\n               \"description\": \"What host IP to bind the external port to.\",\n               \"type\": \"string\"\n              },\n              \"hostPort\": {\n               \"description\": \"Number of port to expose on the host.\\nIf specified, this must be a valid port number, 0 \\u003c x \\u003c 65536.\\nIf HostNetwork is specified, this must match ContainerPort.\\nMost containers do not need this.\",\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"name\": {\n               \"description\": \"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each\\nnamed port in a pod must have a unique name. Name for the port that can be\\nreferred to by services.\",\n               \"type\": \"string\"\n              },\n              \"protocol\": {\n               \"default\": \"TCP\",\n               \"description\": \"Protocol for port. Must be UDP, TCP, or SCTP.\\nDefaults to \\\"TCP\\\".\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"containerPort\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"containerPort\",\n             \"protocol\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"readinessProbe\": {\n            \"description\": \"Probes are not allowed for ephemeral containers.\",\n            \"properties\": {\n             \"exec\": {\n              \"description\": \"Exec specifies the action to take.\",\n              \"properties\": {\n               \"command\": {\n                \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"description\": \"GRPC specifies an action involving a GRPC port.\\nThis is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n              \"properties\": {\n               \"port\": {\n                \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"description\": \"HTTPGet specifies the http request to perform.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                \"items\": {\n                 \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The header field name\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"The header field value\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"path\": {\n                \"description\": \"Path to access on the HTTP server.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"resources\": {\n            \"description\": \"Resources are not allowed for ephemeral containers. Ephemeral containers use spare resources\\nalready allocated to the pod.\",\n            \"properties\": {\n             \"limits\": {\n              \"additionalProperties\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n              \"type\": \"object\"\n             },\n             \"requests\": {\n              \"additionalProperties\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"securityContext\": {\n            \"description\": \"Optional: SecurityContext defines the security options the ephemeral container should be run with.\\nIf set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.\",\n            \"properties\": {\n             \"allowPrivilegeEscalation\": {\n              \"description\": \"AllowPrivilegeEscalation controls whether a process can gain more\\nprivileges than its parent process. This bool directly controls if\\nthe no_new_privs flag will be set on the container process.\\nAllowPrivilegeEscalation is true always when the container is:\\n1) run as Privileged\\n2) has CAP_SYS_ADMIN\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"boolean\"\n             },\n             \"capabilities\": {\n              \"description\": \"The capabilities to add/drop when running containers.\\nDefaults to the default set of capabilities granted by the container runtime.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"properties\": {\n               \"add\": {\n                \"description\": \"Added capabilities\",\n                \"items\": {\n                 \"description\": \"Capability represent POSIX capabilities type\",\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               },\n               \"drop\": {\n                \"description\": \"Removed capabilities\",\n                \"items\": {\n                 \"description\": \"Capability represent POSIX capabilities type\",\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"privileged\": {\n              \"description\": \"Run container in privileged mode.\\nProcesses in privileged containers are essentially equivalent to root on the host.\\nDefaults to false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"boolean\"\n             },\n             \"procMount\": {\n              \"description\": \"procMount denotes the type of proc mount to use for the containers.\\nThe default is DefaultProcMount which uses the container runtime defaults for\\nreadonly paths and masked paths.\\nThis requires the ProcMountType feature flag to be enabled.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"string\"\n             },\n             \"readOnlyRootFilesystem\": {\n              \"description\": \"Whether this container has a read-only root filesystem.\\nDefault is false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"boolean\"\n             },\n             \"runAsGroup\": {\n              \"description\": \"The GID to run the entrypoint of the container process.\\nUses runtime default if unset.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"runAsNonRoot\": {\n              \"description\": \"Indicates that the container must run as a non-root user.\\nIf true, the Kubelet will validate the image at runtime to ensure that it\\ndoes not run as UID 0 (root) and fail to start the container if it does.\\nIf unset or false, no such validation will be performed.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n              \"type\": \"boolean\"\n             },\n             \"runAsUser\": {\n              \"description\": \"The UID to run the entrypoint of the container process.\\nDefaults to user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"seLinuxOptions\": {\n              \"description\": \"The SELinux context to be applied to the container.\\nIf unspecified, the container runtime will allocate a random SELinux context for each\\ncontainer.  May also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"properties\": {\n               \"level\": {\n                \"description\": \"Level is SELinux level label that applies to the container.\",\n                \"type\": \"string\"\n               },\n               \"role\": {\n                \"description\": \"Role is a SELinux role label that applies to the container.\",\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"description\": \"Type is a SELinux type label that applies to the container.\",\n                \"type\": \"string\"\n               },\n               \"user\": {\n                \"description\": \"User is a SELinux user label that applies to the container.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"seccompProfile\": {\n              \"description\": \"The seccomp options to use by this container. If seccomp options are\\nprovided at both the pod \\u0026 container level, the container options\\noverride the pod options.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"properties\": {\n               \"localhostProfile\": {\n                \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used.\\nThe profile must be preconfigured on the node to work.\\nMust be a descending path, relative to the kubelet's configured seccomp profile location.\\nMust only be set if type is \\\"Localhost\\\".\",\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"description\": \"type indicates which kind of seccomp profile will be applied.\\nValid options are:\\n\\n\\nLocalhost - a profile defined in a file on the node should be used.\\nRuntimeDefault - the container runtime default profile should be used.\\nUnconfined - no profile should be applied.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"type\"\n              ],\n              \"type\": \"object\"\n             },\n             \"windowsOptions\": {\n              \"description\": \"The Windows specific settings applied to all containers.\\nIf unspecified, the options from the PodSecurityContext will be used.\\nIf set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is linux.\",\n              \"properties\": {\n               \"gmsaCredentialSpec\": {\n                \"description\": \"GMSACredentialSpec is where the GMSA admission webhook\\n(https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the\\nGMSA credential spec named by the GMSACredentialSpecName field.\",\n                \"type\": \"string\"\n               },\n               \"gmsaCredentialSpecName\": {\n                \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n                \"type\": \"string\"\n               },\n               \"hostProcess\": {\n                \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container.\\nThis field is alpha-level and will only be honored by components that enable the\\nWindowsHostProcessContainers feature flag. Setting this field without the feature\\nflag will result in errors when validating the Pod. All of a Pod's containers must\\nhave the same effective HostProcess value (it is not allowed to have a mix of HostProcess\\ncontainers and non-HostProcess containers).  In addition, if HostProcess is true\\nthen HostNetwork must also be set to true.\",\n                \"type\": \"boolean\"\n               },\n               \"runAsUserName\": {\n                \"description\": \"The UserName in Windows to run the entrypoint of the container process.\\nDefaults to the user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext. If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"startupProbe\": {\n            \"description\": \"Probes are not allowed for ephemeral containers.\",\n            \"properties\": {\n             \"exec\": {\n              \"description\": \"Exec specifies the action to take.\",\n              \"properties\": {\n               \"command\": {\n                \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"description\": \"GRPC specifies an action involving a GRPC port.\\nThis is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n              \"properties\": {\n               \"port\": {\n                \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"description\": \"HTTPGet specifies the http request to perform.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                \"items\": {\n                 \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The header field name\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"The header field value\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"path\": {\n                \"description\": \"Path to access on the HTTP server.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"stdin\": {\n            \"description\": \"Whether this container should allocate a buffer for stdin in the container runtime. If this\\nis not set, reads from stdin in the container will always result in EOF.\\nDefault is false.\",\n            \"type\": \"boolean\"\n           },\n           \"stdinOnce\": {\n            \"description\": \"Whether the container runtime should close the stdin channel after it has been opened by\\na single attach. When stdin is true the stdin stream will remain open across multiple attach\\nsessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the\\nfirst client attaches to stdin, and then remains open and accepts data until the client disconnects,\\nat which time stdin is closed and remains closed until the container is restarted. If this\\nflag is false, a container processes that reads from stdin will never receive an EOF.\\nDefault is false\",\n            \"type\": \"boolean\"\n           },\n           \"targetContainerName\": {\n            \"description\": \"If set, the name of the container from PodSpec that this ephemeral container targets.\\nThe ephemeral container will be run in the namespaces (IPC, PID, etc) of this container.\\nIf not set then the ephemeral container uses the namespaces configured in the Pod spec.\\n\\n\\nThe container runtime must implement support for this feature. If the runtime does not\\nsupport namespace targeting then the result of setting this field is undefined.\",\n            \"type\": \"string\"\n           },\n           \"terminationMessagePath\": {\n            \"description\": \"Optional: Path at which the file to which the container's termination message\\nwill be written is mounted into the container's filesystem.\\nMessage written is intended to be brief final status, such as an assertion failure message.\\nWill be truncated by the node if greater than 4096 bytes. The total message length across\\nall containers will be limited to 12kb.\\nDefaults to /dev/termination-log.\\nCannot be updated.\",\n            \"type\": \"string\"\n           },\n           \"terminationMessagePolicy\": {\n            \"description\": \"Indicate how the termination message should be populated. File will use the contents of\\nterminationMessagePath to populate the container status message on both success and failure.\\nFallbackToLogsOnError will use the last chunk of container log output if the termination\\nmessage file is empty and the container exited with an error.\\nThe log output is limited to 2048 bytes or 80 lines, whichever is smaller.\\nDefaults to File.\\nCannot be updated.\",\n            \"type\": \"string\"\n           },\n           \"tty\": {\n            \"description\": \"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.\\nDefault is false.\",\n            \"type\": \"boolean\"\n           },\n           \"volumeDevices\": {\n            \"description\": \"volumeDevices is the list of block devices to be used by the container.\",\n            \"items\": {\n             \"description\": \"volumeDevice describes a mapping of a raw block device within a container.\",\n             \"properties\": {\n              \"devicePath\": {\n               \"description\": \"devicePath is the path inside of the container that the device will be mapped to.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"name must match the name of a persistentVolumeClaim in the pod\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"devicePath\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"volumeMounts\": {\n            \"description\": \"Pod volumes to mount into the container's filesystem. Subpath mounts are not allowed for ephemeral containers.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n             \"properties\": {\n              \"mountPath\": {\n               \"description\": \"Path within the container at which the volume should be mounted.  Must\\nnot contain ':'.\",\n               \"type\": \"string\"\n              },\n              \"mountPropagation\": {\n               \"description\": \"mountPropagation determines how mounts are propagated from the host\\nto container and the other way around.\\nWhen not set, MountPropagationNone is used.\\nThis field is beta in 1.10.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"This must match the Name of a Volume.\",\n               \"type\": \"string\"\n              },\n              \"readOnly\": {\n               \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified).\\nDefaults to false.\",\n               \"type\": \"boolean\"\n              },\n              \"subPath\": {\n               \"description\": \"Path within the volume from which the container's volume should be mounted.\\nDefaults to \\\"\\\" (volume's root).\",\n               \"type\": \"string\"\n              },\n              \"subPathExpr\": {\n               \"description\": \"Expanded path within the volume from which the container's volume should be mounted.\\nBehaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment.\\nDefaults to \\\"\\\" (volume's root).\\nSubPathExpr and SubPath are mutually exclusive.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"mountPath\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"workingDir\": {\n            \"description\": \"Container's working directory.\\nIf not specified, the container runtime's default will be used, which\\nmight be configured in the container image.\\nCannot be updated.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"hostAliases\": {\n         \"description\": \"HostAliases is an optional list of hosts and IPs that will be injected into the pod's hosts\\nfile if specified. This is only valid for non-hostNetwork pods.\",\n         \"items\": {\n          \"description\": \"HostAlias holds the mapping between IP and hostnames that will be injected as an entry in the\\npod's hosts file.\",\n          \"properties\": {\n           \"hostnames\": {\n            \"description\": \"Hostnames for the above IP address.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"ip\": {\n            \"description\": \"IP address of the host file entry.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"hostIPC\": {\n         \"description\": \"Use the host's ipc namespace.\\nOptional: Default to false.\",\n         \"type\": \"boolean\"\n        },\n        \"hostNetwork\": {\n         \"description\": \"Host networking requested for this pod. Use the host's network namespace.\\nIf this option is set, the ports that will be used must be specified.\\nDefault to false.\",\n         \"type\": \"boolean\"\n        },\n        \"hostPID\": {\n         \"description\": \"Use the host's pid namespace.\\nOptional: Default to false.\",\n         \"type\": \"boolean\"\n        },\n        \"hostname\": {\n         \"description\": \"Specifies the hostname of the Pod\\nIf not specified, the pod's hostname will be set to a system-defined value.\",\n         \"type\": \"string\"\n        },\n        \"imagePullSecrets\": {\n         \"description\": \"ImagePullSecrets is an optional list of references to secrets in the same namespace to use for pulling any of the images used by this PodSpec.\\nIf specified, these secrets will be passed to individual puller implementations for them to use. For example,\\nin the case of docker, only DockerConfig type secrets are honored.\\nMore info: https://kubernetes.io/docs/concepts/containers/images#specifying-imagepullsecrets-on-a-pod\",\n         \"items\": {\n          \"description\": \"LocalObjectReference contains enough information to let you locate the\\nreferenced object inside the same namespace.\",\n          \"properties\": {\n           \"name\": {\n            \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"type\": \"array\"\n        },\n        \"initContainers\": {\n         \"description\": \"List of initialization containers belonging to the pod.\\nInit containers are executed in order prior to containers being started. If any\\ninit container fails, the pod is considered to have failed and is handled according\\nto its restartPolicy. The name for an init container or normal container must be\\nunique among all containers.\\nInit containers may not have Lifecycle actions, Readiness probes, Liveness probes, or Startup probes.\\nThe resourceRequirements of an init container are taken into account during scheduling\\nby finding the highest request/limit for each resource type, and then using the max of\\nof that value or the sum of the normal containers. Limits are applied to init containers\\nin a similar fashion.\\nInit containers cannot currently be added or removed.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/\",\n         \"items\": {\n          \"description\": \"A single application container that you want to run within a pod.\",\n          \"properties\": {\n           \"args\": {\n            \"description\": \"Arguments to the entrypoint.\\nThe docker image's CMD is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"command\": {\n            \"description\": \"Entrypoint array. Not executed within a shell.\\nThe docker image's ENTRYPOINT is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\"\n           },\n           \"env\": {\n            \"description\": \"List of environment variables to set in the container.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"EnvVar represents an environment variable present in a Container.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"Variable references $(VAR_NAME) are expanded\\nusing the previously defined environment variables in the container and\\nany service environment variables. If a variable cannot be resolved,\\nthe reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.\\n\\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\".\\nEscaped references will never be expanded, regardless of whether the variable\\nexists or not.\\nDefaults to \\\"\\\".\",\n               \"type\": \"string\"\n              },\n              \"valueFrom\": {\n               \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n               \"properties\": {\n                \"configMapKeyRef\": {\n                 \"description\": \"Selects a key of a ConfigMap.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key to select.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"fieldRef\": {\n                 \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`,\\nspec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\",\n                 \"properties\": {\n                  \"apiVersion\": {\n                   \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                   \"type\": \"string\"\n                  },\n                  \"fieldPath\": {\n                   \"description\": \"Path of the field to select in the specified API version.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"fieldPath\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"resourceFieldRef\": {\n                 \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n                 \"properties\": {\n                  \"containerName\": {\n                   \"description\": \"Container name: required for volumes, optional for env vars\",\n                   \"type\": \"string\"\n                  },\n                  \"divisor\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"resource\": {\n                   \"description\": \"Required: resource to select\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resource\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"secretKeyRef\": {\n                 \"description\": \"Selects a key of a secret in the pod's namespace\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the Secret or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"envFrom\": {\n            \"description\": \"List of sources to populate environment variables in the container.\\nThe keys defined within a source must be a C_IDENTIFIER. All invalid keys\\nwill be reported as an event when the container is starting. When a key exists in multiple\\nsources, the value associated with the last source will take precedence.\\nValues defined by an Env with a duplicate key will take precedence.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"EnvFromSource represents the source of a set of ConfigMaps\",\n             \"properties\": {\n              \"configMapRef\": {\n               \"description\": \"The ConfigMap to select from\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the ConfigMap must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"prefix\": {\n               \"description\": \"An optional identifier to prepend to each key in the ConfigMap. Must be a C_IDENTIFIER.\",\n               \"type\": \"string\"\n              },\n              \"secretRef\": {\n               \"description\": \"The Secret to select from\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"image\": {\n            \"description\": \"Docker image name.\\nMore info: https://kubernetes.io/docs/concepts/containers/images\\nThis field is optional to allow higher level config management to default or override\\ncontainer images in workload controllers like Deployments and StatefulSets.\",\n            \"type\": \"string\"\n           },\n           \"imagePullPolicy\": {\n            \"description\": \"Image pull policy.\\nOne of Always, Never, IfNotPresent.\\nDefaults to Always if :latest tag is specified, or IfNotPresent otherwise.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\n            \"type\": \"string\"\n           },\n           \"lifecycle\": {\n            \"description\": \"Actions that the management system should take in response to container lifecycle events.\\nCannot be updated.\",\n            \"properties\": {\n             \"postStart\": {\n              \"description\": \"PostStart is called immediately after a container is created. If the handler fails,\\nthe container is terminated and restarted according to its restart policy.\\nOther management of the container blocks until the hook completes.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n              \"properties\": {\n               \"exec\": {\n                \"description\": \"Exec specifies the action to take.\",\n                \"properties\": {\n                 \"command\": {\n                  \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"httpGet\": {\n                \"description\": \"HTTPGet specifies the http request to perform.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                  \"type\": \"string\"\n                 },\n                 \"httpHeaders\": {\n                  \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                  \"items\": {\n                   \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                   \"properties\": {\n                    \"name\": {\n                     \"description\": \"The header field name\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"The header field value\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"path\": {\n                  \"description\": \"Path to access on the HTTP server.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 },\n                 \"scheme\": {\n                  \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               },\n               \"tcpSocket\": {\n                \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor the backward compatibility. There are no validation of this field and\\nlifecycle hooks will fail in runtime when tcp handler is specified.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"preStop\": {\n              \"description\": \"PreStop is called immediately before a container is terminated due to an\\nAPI request or management event such as liveness/startup probe failure,\\npreemption, resource contention, etc. The handler is not called if the\\ncontainer crashes or exits. The Pod's termination grace period countdown begins before the\\nPreStop hook is executed. Regardless of the outcome of the handler, the\\ncontainer will eventually terminate within the Pod's termination grace\\nperiod (unless delayed by finalizers). Other management of the container blocks until the hook completes\\nor until the termination grace period is reached.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n              \"properties\": {\n               \"exec\": {\n                \"description\": \"Exec specifies the action to take.\",\n                \"properties\": {\n                 \"command\": {\n                  \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 }\n                },\n                \"type\": \"object\"\n               },\n               \"httpGet\": {\n                \"description\": \"HTTPGet specifies the http request to perform.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                  \"type\": \"string\"\n                 },\n                 \"httpHeaders\": {\n                  \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                  \"items\": {\n                   \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                   \"properties\": {\n                    \"name\": {\n                     \"description\": \"The header field name\",\n                     \"type\": \"string\"\n                    },\n                    \"value\": {\n                     \"description\": \"The header field value\",\n                     \"type\": \"string\"\n                    }\n                   },\n                   \"required\": [\n                    \"name\",\n                    \"value\"\n                   ],\n                   \"type\": \"object\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"path\": {\n                  \"description\": \"Path to access on the HTTP server.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 },\n                 \"scheme\": {\n                  \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               },\n               \"tcpSocket\": {\n                \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor the backward compatibility. There are no validation of this field and\\nlifecycle hooks will fail in runtime when tcp handler is specified.\",\n                \"properties\": {\n                 \"host\": {\n                  \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                  \"type\": \"string\"\n                 },\n                 \"port\": {\n                  \"anyOf\": [\n                   {\n                    \"type\": \"integer\"\n                   },\n                   {\n                    \"type\": \"string\"\n                   }\n                  ],\n                  \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                  \"x-kubernetes-int-or-string\": true\n                 }\n                },\n                \"required\": [\n                 \"port\"\n                ],\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"livenessProbe\": {\n            \"description\": \"Periodic probe of container liveness.\\nContainer will be restarted if the probe fails.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n            \"properties\": {\n             \"exec\": {\n              \"description\": \"Exec specifies the action to take.\",\n              \"properties\": {\n               \"command\": {\n                \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"description\": \"GRPC specifies an action involving a GRPC port.\\nThis is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n              \"properties\": {\n               \"port\": {\n                \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"description\": \"HTTPGet specifies the http request to perform.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                \"items\": {\n                 \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The header field name\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"The header field value\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"path\": {\n                \"description\": \"Path to access on the HTTP server.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"name\": {\n            \"description\": \"Name of the container specified as a DNS_LABEL.\\nEach container in a pod must have a unique name (DNS_LABEL).\\nCannot be updated.\",\n            \"type\": \"string\"\n           },\n           \"ports\": {\n            \"description\": \"List of ports to expose from the container. Exposing a port here gives\\nthe system additional information about the network connections a\\ncontainer uses, but is primarily informational. Not specifying a port here\\nDOES NOT prevent that port from being exposed. Any port which is\\nlistening on the default \\\"0.0.0.0\\\" address inside a container will be\\naccessible from the network.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"ContainerPort represents a network port in a single container.\",\n             \"properties\": {\n              \"containerPort\": {\n               \"description\": \"Number of port to expose on the pod's IP address.\\nThis must be a valid port number, 0 \\u003c x \\u003c 65536.\",\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"hostIP\": {\n               \"description\": \"What host IP to bind the external port to.\",\n               \"type\": \"string\"\n              },\n              \"hostPort\": {\n               \"description\": \"Number of port to expose on the host.\\nIf specified, this must be a valid port number, 0 \\u003c x \\u003c 65536.\\nIf HostNetwork is specified, this must match ContainerPort.\\nMost containers do not need this.\",\n               \"format\": \"int32\",\n               \"type\": \"integer\"\n              },\n              \"name\": {\n               \"description\": \"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each\\nnamed port in a pod must have a unique name. Name for the port that can be\\nreferred to by services.\",\n               \"type\": \"string\"\n              },\n              \"protocol\": {\n               \"default\": \"TCP\",\n               \"description\": \"Protocol for port. Must be UDP, TCP, or SCTP.\\nDefaults to \\\"TCP\\\".\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"containerPort\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-map-keys\": [\n             \"containerPort\",\n             \"protocol\"\n            ],\n            \"x-kubernetes-list-type\": \"map\"\n           },\n           \"readinessProbe\": {\n            \"description\": \"Periodic probe of container service readiness.\\nContainer will be removed from service endpoints if the probe fails.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n            \"properties\": {\n             \"exec\": {\n              \"description\": \"Exec specifies the action to take.\",\n              \"properties\": {\n               \"command\": {\n                \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"description\": \"GRPC specifies an action involving a GRPC port.\\nThis is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n              \"properties\": {\n               \"port\": {\n                \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"description\": \"HTTPGet specifies the http request to perform.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                \"items\": {\n                 \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The header field name\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"The header field value\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"path\": {\n                \"description\": \"Path to access on the HTTP server.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"resources\": {\n            \"description\": \"Compute Resources required by this container.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n            \"properties\": {\n             \"limits\": {\n              \"additionalProperties\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n              \"type\": \"object\"\n             },\n             \"requests\": {\n              \"additionalProperties\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"securityContext\": {\n            \"description\": \"SecurityContext defines the security options the container should be run with.\\nIf set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.\\nMore info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/\",\n            \"properties\": {\n             \"allowPrivilegeEscalation\": {\n              \"description\": \"AllowPrivilegeEscalation controls whether a process can gain more\\nprivileges than its parent process. This bool directly controls if\\nthe no_new_privs flag will be set on the container process.\\nAllowPrivilegeEscalation is true always when the container is:\\n1) run as Privileged\\n2) has CAP_SYS_ADMIN\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"boolean\"\n             },\n             \"capabilities\": {\n              \"description\": \"The capabilities to add/drop when running containers.\\nDefaults to the default set of capabilities granted by the container runtime.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"properties\": {\n               \"add\": {\n                \"description\": \"Added capabilities\",\n                \"items\": {\n                 \"description\": \"Capability represent POSIX capabilities type\",\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               },\n               \"drop\": {\n                \"description\": \"Removed capabilities\",\n                \"items\": {\n                 \"description\": \"Capability represent POSIX capabilities type\",\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"privileged\": {\n              \"description\": \"Run container in privileged mode.\\nProcesses in privileged containers are essentially equivalent to root on the host.\\nDefaults to false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"boolean\"\n             },\n             \"procMount\": {\n              \"description\": \"procMount denotes the type of proc mount to use for the containers.\\nThe default is DefaultProcMount which uses the container runtime defaults for\\nreadonly paths and masked paths.\\nThis requires the ProcMountType feature flag to be enabled.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"string\"\n             },\n             \"readOnlyRootFilesystem\": {\n              \"description\": \"Whether this container has a read-only root filesystem.\\nDefault is false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"type\": \"boolean\"\n             },\n             \"runAsGroup\": {\n              \"description\": \"The GID to run the entrypoint of the container process.\\nUses runtime default if unset.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"runAsNonRoot\": {\n              \"description\": \"Indicates that the container must run as a non-root user.\\nIf true, the Kubelet will validate the image at runtime to ensure that it\\ndoes not run as UID 0 (root) and fail to start the container if it does.\\nIf unset or false, no such validation will be performed.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n              \"type\": \"boolean\"\n             },\n             \"runAsUser\": {\n              \"description\": \"The UID to run the entrypoint of the container process.\\nDefaults to user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"seLinuxOptions\": {\n              \"description\": \"The SELinux context to be applied to the container.\\nIf unspecified, the container runtime will allocate a random SELinux context for each\\ncontainer.  May also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"properties\": {\n               \"level\": {\n                \"description\": \"Level is SELinux level label that applies to the container.\",\n                \"type\": \"string\"\n               },\n               \"role\": {\n                \"description\": \"Role is a SELinux role label that applies to the container.\",\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"description\": \"Type is a SELinux type label that applies to the container.\",\n                \"type\": \"string\"\n               },\n               \"user\": {\n                \"description\": \"User is a SELinux user label that applies to the container.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"seccompProfile\": {\n              \"description\": \"The seccomp options to use by this container. If seccomp options are\\nprovided at both the pod \\u0026 container level, the container options\\noverride the pod options.\\nNote that this field cannot be set when spec.os.name is windows.\",\n              \"properties\": {\n               \"localhostProfile\": {\n                \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used.\\nThe profile must be preconfigured on the node to work.\\nMust be a descending path, relative to the kubelet's configured seccomp profile location.\\nMust only be set if type is \\\"Localhost\\\".\",\n                \"type\": \"string\"\n               },\n               \"type\": {\n                \"description\": \"type indicates which kind of seccomp profile will be applied.\\nValid options are:\\n\\n\\nLocalhost - a profile defined in a file on the node should be used.\\nRuntimeDefault - the container runtime default profile should be used.\\nUnconfined - no profile should be applied.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"type\"\n              ],\n              \"type\": \"object\"\n             },\n             \"windowsOptions\": {\n              \"description\": \"The Windows specific settings applied to all containers.\\nIf unspecified, the options from the PodSecurityContext will be used.\\nIf set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is linux.\",\n              \"properties\": {\n               \"gmsaCredentialSpec\": {\n                \"description\": \"GMSACredentialSpec is where the GMSA admission webhook\\n(https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the\\nGMSA credential spec named by the GMSACredentialSpecName field.\",\n                \"type\": \"string\"\n               },\n               \"gmsaCredentialSpecName\": {\n                \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n                \"type\": \"string\"\n               },\n               \"hostProcess\": {\n                \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container.\\nThis field is alpha-level and will only be honored by components that enable the\\nWindowsHostProcessContainers feature flag. Setting this field without the feature\\nflag will result in errors when validating the Pod. All of a Pod's containers must\\nhave the same effective HostProcess value (it is not allowed to have a mix of HostProcess\\ncontainers and non-HostProcess containers).  In addition, if HostProcess is true\\nthen HostNetwork must also be set to true.\",\n                \"type\": \"boolean\"\n               },\n               \"runAsUserName\": {\n                \"description\": \"The UserName in Windows to run the entrypoint of the container process.\\nDefaults to the user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext. If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"startupProbe\": {\n            \"description\": \"StartupProbe indicates that the Pod has successfully initialized.\\nIf specified, no other probes are executed until this completes successfully.\\nIf this probe fails, the Pod will be restarted, just as if the livenessProbe failed.\\nThis can be used to provide different probe parameters at the beginning of a Pod's lifecycle,\\nwhen it might take a long time to load data or warm a cache, than during steady-state operation.\\nThis cannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n            \"properties\": {\n             \"exec\": {\n              \"description\": \"Exec specifies the action to take.\",\n              \"properties\": {\n               \"command\": {\n                \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"failureThreshold\": {\n              \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"grpc\": {\n              \"description\": \"GRPC specifies an action involving a GRPC port.\\nThis is an alpha field and requires enabling GRPCContainerProbe feature gate.\",\n              \"properties\": {\n               \"port\": {\n                \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n                \"format\": \"int32\",\n                \"type\": \"integer\"\n               },\n               \"service\": {\n                \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"httpGet\": {\n              \"description\": \"HTTPGet specifies the http request to perform.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n                \"type\": \"string\"\n               },\n               \"httpHeaders\": {\n                \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n                \"items\": {\n                 \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n                 \"properties\": {\n                  \"name\": {\n                   \"description\": \"The header field name\",\n                   \"type\": \"string\"\n                  },\n                  \"value\": {\n                   \"description\": \"The header field value\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"name\",\n                  \"value\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\"\n               },\n               \"path\": {\n                \"description\": \"Path to access on the HTTP server.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"scheme\": {\n                \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"initialDelaySeconds\": {\n              \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"periodSeconds\": {\n              \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"successThreshold\": {\n              \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"tcpSocket\": {\n              \"description\": \"TCPSocket specifies an action involving a TCP port.\",\n              \"properties\": {\n               \"host\": {\n                \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n                \"type\": \"string\"\n               },\n               \"port\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n                \"x-kubernetes-int-or-string\": true\n               }\n              },\n              \"required\": [\n               \"port\"\n              ],\n              \"type\": \"object\"\n             },\n             \"terminationGracePeriodSeconds\": {\n              \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             },\n             \"timeoutSeconds\": {\n              \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"stdin\": {\n            \"description\": \"Whether this container should allocate a buffer for stdin in the container runtime. If this\\nis not set, reads from stdin in the container will always result in EOF.\\nDefault is false.\",\n            \"type\": \"boolean\"\n           },\n           \"stdinOnce\": {\n            \"description\": \"Whether the container runtime should close the stdin channel after it has been opened by\\na single attach. When stdin is true the stdin stream will remain open across multiple attach\\nsessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the\\nfirst client attaches to stdin, and then remains open and accepts data until the client disconnects,\\nat which time stdin is closed and remains closed until the container is restarted. If this\\nflag is false, a container processes that reads from stdin will never receive an EOF.\\nDefault is false\",\n            \"type\": \"boolean\"\n           },\n           \"terminationMessagePath\": {\n            \"description\": \"Optional: Path at which the file to which the container's termination message\\nwill be written is mounted into the container's filesystem.\\nMessage written is intended to be brief final status, such as an assertion failure message.\\nWill be truncated by the node if greater than 4096 bytes. The total message length across\\nall containers will be limited to 12kb.\\nDefaults to /dev/termination-log.\\nCannot be updated.\",\n            \"type\": \"string\"\n           },\n           \"terminationMessagePolicy\": {\n            \"description\": \"Indicate how the termination message should be populated. File will use the contents of\\nterminationMessagePath to populate the container status message on both success and failure.\\nFallbackToLogsOnError will use the last chunk of container log output if the termination\\nmessage file is empty and the container exited with an error.\\nThe log output is limited to 2048 bytes or 80 lines, whichever is smaller.\\nDefaults to File.\\nCannot be updated.\",\n            \"type\": \"string\"\n           },\n           \"tty\": {\n            \"description\": \"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.\\nDefault is false.\",\n            \"type\": \"boolean\"\n           },\n           \"volumeDevices\": {\n            \"description\": \"volumeDevices is the list of block devices to be used by the container.\",\n            \"items\": {\n             \"description\": \"volumeDevice describes a mapping of a raw block device within a container.\",\n             \"properties\": {\n              \"devicePath\": {\n               \"description\": \"devicePath is the path inside of the container that the device will be mapped to.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"name must match the name of a persistentVolumeClaim in the pod\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"devicePath\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"volumeMounts\": {\n            \"description\": \"Pod volumes to mount into the container's filesystem.\\nCannot be updated.\",\n            \"items\": {\n             \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n             \"properties\": {\n              \"mountPath\": {\n               \"description\": \"Path within the container at which the volume should be mounted.  Must\\nnot contain ':'.\",\n               \"type\": \"string\"\n              },\n              \"mountPropagation\": {\n               \"description\": \"mountPropagation determines how mounts are propagated from the host\\nto container and the other way around.\\nWhen not set, MountPropagationNone is used.\\nThis field is beta in 1.10.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"This must match the Name of a Volume.\",\n               \"type\": \"string\"\n              },\n              \"readOnly\": {\n               \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified).\\nDefaults to false.\",\n               \"type\": \"boolean\"\n              },\n              \"subPath\": {\n               \"description\": \"Path within the volume from which the container's volume should be mounted.\\nDefaults to \\\"\\\" (volume's root).\",\n               \"type\": \"string\"\n              },\n              \"subPathExpr\": {\n               \"description\": \"Expanded path within the volume from which the container's volume should be mounted.\\nBehaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment.\\nDefaults to \\\"\\\" (volume's root).\\nSubPathExpr and SubPath are mutually exclusive.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"mountPath\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"workingDir\": {\n            \"description\": \"Container's working directory.\\nIf not specified, the container runtime's default will be used, which\\nmight be configured in the container image.\\nCannot be updated.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"nodeName\": {\n         \"description\": \"NodeName is a request to schedule this pod onto a specific node. If it is non-empty,\\nthe scheduler simply schedules this pod onto that node, assuming that it fits resource\\nrequirements.\",\n         \"type\": \"string\"\n        },\n        \"nodeSelector\": {\n         \"additionalProperties\": {\n          \"type\": \"string\"\n         },\n         \"description\": \"NodeSelector is a selector which must be true for the pod to fit on a node.\\nSelector which must match a node's labels for the pod to be scheduled on that node.\\nMore info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/\",\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"os\": {\n         \"description\": \"Specifies the OS of the containers in the pod.\\nSome pod and container fields are restricted if this is set.\\n\\n\\nIf the OS field is set to linux, the following fields must be unset:\\n-securityContext.windowsOptions\\n\\n\\nIf the OS field is set to windows, following fields must be unset:\\n- spec.hostPID\\n- spec.hostIPC\\n- spec.securityContext.seLinuxOptions\\n- spec.securityContext.seccompProfile\\n- spec.securityContext.fsGroup\\n- spec.securityContext.fsGroupChangePolicy\\n- spec.securityContext.sysctls\\n- spec.shareProcessNamespace\\n- spec.securityContext.runAsUser\\n- spec.securityContext.runAsGroup\\n- spec.securityContext.supplementalGroups\\n- spec.containers[*].securityContext.seLinuxOptions\\n- spec.containers[*].securityContext.seccompProfile\\n- spec.containers[*].securityContext.capabilities\\n- spec.containers[*].securityContext.readOnlyRootFilesystem\\n- spec.containers[*].securityContext.privileged\\n- spec.containers[*].securityContext.allowPrivilegeEscalation\\n- spec.containers[*].securityContext.procMount\\n- spec.containers[*].securityContext.runAsUser\\n- spec.containers[*].securityContext.runAsGroup\\nThis is an alpha field and requires the IdentifyPodOS feature\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name is the name of the operating system. The currently supported values are linux and windows.\\nAdditional value may be defined in future and can be one of:\\nhttps://github.com/opencontainers/runtime-spec/blob/master/config.md#platform-specific-configuration\\nClients should expect to handle additional values and treat unrecognized values in this field as os: null\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"overhead\": {\n         \"additionalProperties\": {\n          \"anyOf\": [\n           {\n            \"type\": \"integer\"\n           },\n           {\n            \"type\": \"string\"\n           }\n          ],\n          \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n          \"x-kubernetes-int-or-string\": true\n         },\n         \"description\": \"Overhead represents the resource overhead associated with running a pod for a given RuntimeClass.\\nThis field will be autopopulated at admission time by the RuntimeClass admission controller. If\\nthe RuntimeClass admission controller is enabled, overhead must not be set in Pod create requests.\\nThe RuntimeClass admission controller will reject Pod create requests which have the overhead already\\nset. If RuntimeClass is configured and selected in the PodSpec, Overhead will be set to the value\\ndefined in the corresponding RuntimeClass, otherwise it will remain unset and treated as zero.\\nMore info: https://git.k8s.io/enhancements/keps/sig-node/688-pod-overhead/README.md\\nThis field is beta-level as of Kubernetes v1.18, and is only honored by servers that enable the PodOverhead feature.\",\n         \"type\": \"object\"\n        },\n        \"preemptionPolicy\": {\n         \"description\": \"PreemptionPolicy is the Policy for preempting pods with lower priority.\\nOne of Never, PreemptLowerPriority.\\nDefaults to PreemptLowerPriority if unset.\\nThis field is beta-level, gated by the NonPreemptingPriority feature-gate.\",\n         \"type\": \"string\"\n        },\n        \"priority\": {\n         \"description\": \"The priority value. Various system components use this field to find the\\npriority of the pod. When Priority Admission Controller is enabled, it\\nprevents users from setting this field. The admission controller populates\\nthis field from PriorityClassName.\\nThe higher the value, the higher the priority.\",\n         \"format\": \"int32\",\n         \"type\": \"integer\"\n        },\n        \"priorityClassName\": {\n         \"description\": \"If specified, indicates the pod's priority. \\\"system-node-critical\\\" and\\n\\\"system-cluster-critical\\\" are two special keywords which indicate the\\nhighest priorities with the former being the highest priority. Any other\\nname must be defined by creating a PriorityClass object with that name.\\nIf not specified, the pod priority will be default or zero if there is no\\ndefault.\",\n         \"type\": \"string\"\n        },\n        \"readinessGates\": {\n         \"description\": \"If specified, all readiness gates will be evaluated for pod readiness.\\nA pod is ready when all its containers are ready AND\\nall conditions specified in the readiness gates have status equal to \\\"True\\\"\\nMore info: https://git.k8s.io/enhancements/keps/sig-network/580-pod-readiness-gates\",\n         \"items\": {\n          \"description\": \"PodReadinessGate contains the reference to a pod condition\",\n          \"properties\": {\n           \"conditionType\": {\n            \"description\": \"ConditionType refers to a condition in the pod's condition list with matching type.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"conditionType\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"restartPolicy\": {\n         \"description\": \"Restart policy for all containers within the pod.\\nOne of Always, OnFailure, Never.\\nDefault to Always.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#restart-policy\",\n         \"type\": \"string\"\n        },\n        \"runtimeClassName\": {\n         \"description\": \"RuntimeClassName refers to a RuntimeClass object in the node.k8s.io group, which should be used\\nto run this pod.  If no RuntimeClass resource matches the named class, the pod will not be run.\\nIf unset or empty, the \\\"legacy\\\" RuntimeClass will be used, which is an implicit class with an\\nempty definition that uses the default runtime handler.\\nMore info: https://git.k8s.io/enhancements/keps/sig-node/585-runtime-class\\nThis is a beta feature as of Kubernetes v1.14.\",\n         \"type\": \"string\"\n        },\n        \"schedulerName\": {\n         \"description\": \"If specified, the pod will be dispatched by specified scheduler.\\nIf not specified, the pod will be dispatched by default scheduler.\",\n         \"type\": \"string\"\n        },\n        \"securityContext\": {\n         \"description\": \"SecurityContext holds pod-level security attributes and common container settings.\\nOptional: Defaults to empty.  See type description for default values of each field.\",\n         \"properties\": {\n          \"fsGroup\": {\n           \"description\": \"A special supplemental group that applies to all containers in a pod.\\nSome volume types allow the Kubelet to change the ownership of that volume\\nto be owned by the pod:\\n\\n\\n1. The owning GID will be the FSGroup\\n2. The setgid bit is set (new files created in the volume will be owned by FSGroup)\\n3. The permission bits are OR'd with rw-rw----\\n\\n\\nIf unset, the Kubelet will not modify the ownership and permissions of any volume.\\nNote that this field cannot be set when spec.os.name is windows.\",\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"fsGroupChangePolicy\": {\n           \"description\": \"fsGroupChangePolicy defines behavior of changing ownership and permission of the volume\\nbefore being exposed inside Pod. This field will only apply to\\nvolume types which support fsGroup based ownership(and permissions).\\nIt will have no effect on ephemeral volume types such as: secret, configmaps\\nand emptydir.\\nValid values are \\\"OnRootMismatch\\\" and \\\"Always\\\". If not specified, \\\"Always\\\" is used.\\nNote that this field cannot be set when spec.os.name is windows.\",\n           \"type\": \"string\"\n          },\n          \"runAsGroup\": {\n           \"description\": \"The GID to run the entrypoint of the container process.\\nUses runtime default if unset.\\nMay also be set in SecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence\\nfor that container.\\nNote that this field cannot be set when spec.os.name is windows.\",\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"runAsNonRoot\": {\n           \"description\": \"Indicates that the container must run as a non-root user.\\nIf true, the Kubelet will validate the image at runtime to ensure that it\\ndoes not run as UID 0 (root) and fail to start the container if it does.\\nIf unset or false, no such validation will be performed.\\nMay also be set in SecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n           \"type\": \"boolean\"\n          },\n          \"runAsUser\": {\n           \"description\": \"The UID to run the entrypoint of the container process.\\nDefaults to user specified in image metadata if unspecified.\\nMay also be set in SecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence\\nfor that container.\\nNote that this field cannot be set when spec.os.name is windows.\",\n           \"format\": \"int64\",\n           \"type\": \"integer\"\n          },\n          \"seLinuxOptions\": {\n           \"description\": \"The SELinux context to be applied to all containers.\\nIf unspecified, the container runtime will allocate a random SELinux context for each\\ncontainer.  May also be set in SecurityContext.  If set in\\nboth SecurityContext and PodSecurityContext, the value specified in SecurityContext\\ntakes precedence for that container.\\nNote that this field cannot be set when spec.os.name is windows.\",\n           \"properties\": {\n            \"level\": {\n             \"description\": \"Level is SELinux level label that applies to the container.\",\n             \"type\": \"string\"\n            },\n            \"role\": {\n             \"description\": \"Role is a SELinux role label that applies to the container.\",\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"description\": \"Type is a SELinux type label that applies to the container.\",\n             \"type\": \"string\"\n            },\n            \"user\": {\n             \"description\": \"User is a SELinux user label that applies to the container.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"seccompProfile\": {\n           \"description\": \"The seccomp options to use by the containers in this pod.\\nNote that this field cannot be set when spec.os.name is windows.\",\n           \"properties\": {\n            \"localhostProfile\": {\n             \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used.\\nThe profile must be preconfigured on the node to work.\\nMust be a descending path, relative to the kubelet's configured seccomp profile location.\\nMust only be set if type is \\\"Localhost\\\".\",\n             \"type\": \"string\"\n            },\n            \"type\": {\n             \"description\": \"type indicates which kind of seccomp profile will be applied.\\nValid options are:\\n\\n\\nLocalhost - a profile defined in a file on the node should be used.\\nRuntimeDefault - the container runtime default profile should be used.\\nUnconfined - no profile should be applied.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"type\"\n           ],\n           \"type\": \"object\"\n          },\n          \"supplementalGroups\": {\n           \"description\": \"A list of groups applied to the first process run in each container, in addition\\nto the container's primary GID.  If unspecified, no groups will be added to\\nany container.\\nNote that this field cannot be set when spec.os.name is windows.\",\n           \"items\": {\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"type\": \"array\"\n          },\n          \"sysctls\": {\n           \"description\": \"Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported\\nsysctls (by the container runtime) might fail to launch.\\nNote that this field cannot be set when spec.os.name is windows.\",\n           \"items\": {\n            \"description\": \"Sysctl defines a kernel parameter to be set\",\n            \"properties\": {\n             \"name\": {\n              \"description\": \"Name of a property to set\",\n              \"type\": \"string\"\n             },\n             \"value\": {\n              \"description\": \"Value of a property to set\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"name\",\n             \"value\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\"\n          },\n          \"windowsOptions\": {\n           \"description\": \"The Windows specific settings applied to all containers.\\nIf unspecified, the options within a container's SecurityContext will be used.\\nIf set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is linux.\",\n           \"properties\": {\n            \"gmsaCredentialSpec\": {\n             \"description\": \"GMSACredentialSpec is where the GMSA admission webhook\\n(https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the\\nGMSA credential spec named by the GMSACredentialSpecName field.\",\n             \"type\": \"string\"\n            },\n            \"gmsaCredentialSpecName\": {\n             \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n             \"type\": \"string\"\n            },\n            \"hostProcess\": {\n             \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container.\\nThis field is alpha-level and will only be honored by components that enable the\\nWindowsHostProcessContainers feature flag. Setting this field without the feature\\nflag will result in errors when validating the Pod. All of a Pod's containers must\\nhave the same effective HostProcess value (it is not allowed to have a mix of HostProcess\\ncontainers and non-HostProcess containers).  In addition, if HostProcess is true\\nthen HostNetwork must also be set to true.\",\n             \"type\": \"boolean\"\n            },\n            \"runAsUserName\": {\n             \"description\": \"The UserName in Windows to run the entrypoint of the container process.\\nDefaults to the user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext. If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"serviceAccount\": {\n         \"description\": \"DeprecatedServiceAccount is a depreciated alias for ServiceAccountName.\\nDeprecated: Use serviceAccountName instead.\",\n         \"type\": \"string\"\n        },\n        \"serviceAccountName\": {\n         \"description\": \"ServiceAccountName is the name of the ServiceAccount to use to run this pod.\\nMore info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/\",\n         \"type\": \"string\"\n        },\n        \"setHostnameAsFQDN\": {\n         \"description\": \"If true the pod's hostname will be configured as the pod's FQDN, rather than the leaf name (the default).\\nIn Linux containers, this means setting the FQDN in the hostname field of the kernel (the nodename field of struct utsname).\\nIn Windows containers, this means setting the registry value of hostname for the registry key HKEY_LOCAL_MACHINE\\\\\\\\SYSTEM\\\\\\\\CurrentControlSet\\\\\\\\Services\\\\\\\\Tcpip\\\\\\\\Parameters to FQDN.\\nIf a pod does not have FQDN, this has no effect.\\nDefault to false.\",\n         \"type\": \"boolean\"\n        },\n        \"shareProcessNamespace\": {\n         \"description\": \"Share a single process namespace between all of the containers in a pod.\\nWhen this is set containers will be able to view and signal processes from other containers\\nin the same pod, and the first process in each container will not be assigned PID 1.\\nHostPID and ShareProcessNamespace cannot both be set.\\nOptional: Default to false.\",\n         \"type\": \"boolean\"\n        },\n        \"subdomain\": {\n         \"description\": \"If specified, the fully qualified Pod hostname will be \\\"\\u003chostname\\u003e.\\u003csubdomain\\u003e.\\u003cpod namespace\\u003e.svc.\\u003ccluster domain\\u003e\\\".\\nIf not specified, the pod will not have a domainname at all.\",\n         \"type\": \"string\"\n        },\n        \"terminationGracePeriodSeconds\": {\n         \"description\": \"Optional duration in seconds the pod needs to terminate gracefully. May be decreased in delete request.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nIf this value is nil, the default grace period will be used instead.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nDefaults to 30 seconds.\",\n         \"format\": \"int64\",\n         \"type\": \"integer\"\n        },\n        \"tolerations\": {\n         \"description\": \"If specified, the pod's tolerations.\",\n         \"items\": {\n          \"description\": \"The pod this Toleration is attached to tolerates any taint that matches\\nthe triple \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\n          \"properties\": {\n           \"effect\": {\n            \"description\": \"Effect indicates the taint effect to match. Empty means match all taint effects.\\nWhen specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.\",\n            \"type\": \"string\"\n           },\n           \"key\": {\n            \"description\": \"Key is the taint key that the toleration applies to. Empty means match all taint keys.\\nIf the key is empty, operator must be Exists; this combination means to match all values and all keys.\",\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"description\": \"Operator represents a key's relationship to the value.\\nValid operators are Exists and Equal. Defaults to Equal.\\nExists is equivalent to wildcard for value, so that a pod can\\ntolerate all taints of a particular category.\",\n            \"type\": \"string\"\n           },\n           \"tolerationSeconds\": {\n            \"description\": \"TolerationSeconds represents the period of time the toleration (which must be\\nof effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,\\nit is not set, which means tolerate the taint forever (do not evict). Zero and\\nnegative values will be treated as 0 (evict immediately) by the system.\",\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"value\": {\n            \"description\": \"Value is the taint value the toleration matches to.\\nIf the operator is Exists, the value should be empty, otherwise just a regular string.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"topologySpreadConstraints\": {\n         \"description\": \"TopologySpreadConstraints describes how a group of pods ought to spread across topology\\ndomains. Scheduler will schedule pods in a way which abides by the constraints.\\nAll topologySpreadConstraints are ANDed.\",\n         \"items\": {\n          \"description\": \"TopologySpreadConstraint specifies how to spread matching pods among the given topology.\",\n          \"properties\": {\n           \"labelSelector\": {\n            \"description\": \"LabelSelector is used to find matching pods.\\nPods that match this label selector are counted to determine the number of pods\\nin their corresponding topology domain.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"maxSkew\": {\n            \"description\": \"MaxSkew describes the degree to which pods may be unevenly distributed.\\nWhen `whenUnsatisfiable=DoNotSchedule`, it is the maximum permitted difference\\nbetween the number of matching pods in the target topology and the global minimum.\\nFor example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same\\nlabelSelector spread as 1/1/0:\\n| zone1 | zone2 | zone3 |\\n|   P   |   P   |       |\\n- if MaxSkew is 1, incoming pod can only be scheduled to zone3 to become 1/1/1;\\nscheduling it onto zone1(zone2) would make the ActualSkew(2-0) on zone1(zone2)\\nviolate MaxSkew(1).\\n- if MaxSkew is 2, incoming pod can be scheduled onto any zone.\\nWhen `whenUnsatisfiable=ScheduleAnyway`, it is used to give higher precedence\\nto topologies that satisfy it.\\nIt's a required field. Default value is 1 and 0 is not allowed.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"topologyKey\": {\n            \"description\": \"TopologyKey is the key of node labels. Nodes that have a label with this key\\nand identical values are considered to be in the same topology.\\nWe consider each \\u003ckey, value\\u003e as a \\\"bucket\\\", and try to put balanced number\\nof pods into each bucket.\\nIt's a required field.\",\n            \"type\": \"string\"\n           },\n           \"whenUnsatisfiable\": {\n            \"description\": \"WhenUnsatisfiable indicates how to deal with a pod if it doesn't satisfy\\nthe spread constraint.\\n- DoNotSchedule (default) tells the scheduler not to schedule it.\\n- ScheduleAnyway tells the scheduler to schedule the pod in any location,\\n  but giving higher precedence to topologies that would help reduce the\\n  skew.\\nA constraint is considered \\\"Unsatisfiable\\\" for an incoming pod\\nif and only if every possible node assignment for that pod would violate\\n\\\"MaxSkew\\\" on some topology.\\nFor example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same\\nlabelSelector spread as 3/1/1:\\n| zone1 | zone2 | zone3 |\\n| P P P |   P   |   P   |\\nIf WhenUnsatisfiable is set to DoNotSchedule, incoming pod can only be scheduled\\nto zone2(zone3) to become 3/2/1(3/1/2) as ActualSkew(2-1) on zone2(zone3) satisfies\\nMaxSkew(1). In other words, the cluster can still be imbalanced, but scheduler\\nwon't make it *more* imbalanced.\\nIt's a required field.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"maxSkew\",\n           \"topologyKey\",\n           \"whenUnsatisfiable\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-map-keys\": [\n          \"topologyKey\",\n          \"whenUnsatisfiable\"\n         ],\n         \"x-kubernetes-list-type\": \"map\"\n        },\n        \"volumes\": {\n         \"description\": \"List of volumes that can be mounted by containers belonging to the pod.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes\",\n         \"items\": {\n          \"description\": \"Volume represents a named volume in a pod that may be accessed by any container in the pod.\",\n          \"properties\": {\n           \"awsElasticBlockStore\": {\n            \"description\": \"AWSElasticBlockStore represents an AWS Disk resource that is attached to a\\nkubelet's host machine and then exposed to the pod.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"Filesystem type of the volume that you want to mount.\\nTip: Ensure that the filesystem type is supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\\nTODO: how do we prevent errors in the filesystem from compromising the machine\",\n              \"type\": \"string\"\n             },\n             \"partition\": {\n              \"description\": \"The partition in the volume that you want to mount.\\nIf omitted, the default is to mount by volume name.\\nExamples: For volume /dev/sda1, you specify the partition as \\\"1\\\".\\nSimilarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty).\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"readOnly\": {\n              \"description\": \"Specify \\\"true\\\" to force and set the ReadOnly property in VolumeMounts to \\\"true\\\".\\nIf omitted, the default is \\\"false\\\".\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n              \"type\": \"boolean\"\n             },\n             \"volumeID\": {\n              \"description\": \"Unique ID of the persistent disk resource in AWS (Amazon EBS volume).\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"volumeID\"\n            ],\n            \"type\": \"object\"\n           },\n           \"azureDisk\": {\n            \"description\": \"AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.\",\n            \"properties\": {\n             \"cachingMode\": {\n              \"description\": \"Host Caching mode: None, Read Only, Read Write.\",\n              \"type\": \"string\"\n             },\n             \"diskName\": {\n              \"description\": \"The Name of the data disk in the blob storage\",\n              \"type\": \"string\"\n             },\n             \"diskURI\": {\n              \"description\": \"The URI the data disk in the blob storage\",\n              \"type\": \"string\"\n             },\n             \"fsType\": {\n              \"description\": \"Filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n              \"type\": \"string\"\n             },\n             \"kind\": {\n              \"description\": \"Expected values Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"diskName\",\n             \"diskURI\"\n            ],\n            \"type\": \"object\"\n           },\n           \"azureFile\": {\n            \"description\": \"AzureFile represents an Azure File Service mount on the host and bind mount to the pod.\",\n            \"properties\": {\n             \"readOnly\": {\n              \"description\": \"Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n              \"type\": \"boolean\"\n             },\n             \"secretName\": {\n              \"description\": \"the name of secret that contains Azure Storage Account Name and Key\",\n              \"type\": \"string\"\n             },\n             \"shareName\": {\n              \"description\": \"Share Name\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"secretName\",\n             \"shareName\"\n            ],\n            \"type\": \"object\"\n           },\n           \"cephfs\": {\n            \"description\": \"CephFS represents a Ceph FS mount on the host that shares a pod's lifetime\",\n            \"properties\": {\n             \"monitors\": {\n              \"description\": \"Required: Monitors is a collection of Ceph monitors\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"path\": {\n              \"description\": \"Optional: Used as the mounted root, rather than the full Ceph tree, default is /\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n              \"type\": \"boolean\"\n             },\n             \"secretFile\": {\n              \"description\": \"Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n              \"type\": \"string\"\n             },\n             \"secretRef\": {\n              \"description\": \"Optional: SecretRef is reference to the authentication secret for User, default is empty.\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"user\": {\n              \"description\": \"Optional: User is the rados user name, default is admin\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"monitors\"\n            ],\n            \"type\": \"object\"\n           },\n           \"cinder\": {\n            \"description\": \"Cinder represents a cinder volume attached and mounted on kubelets host machine.\\nMore info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"Filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\\nMore info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"description\": \"Optional: points to a secret object containing parameters used to connect\\nto OpenStack.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"volumeID\": {\n              \"description\": \"volume id used to identify the volume in cinder.\\nMore info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"volumeID\"\n            ],\n            \"type\": \"object\"\n           },\n           \"configMap\": {\n            \"description\": \"ConfigMap represents a configMap that should populate this volume\",\n            \"properties\": {\n             \"defaultMode\": {\n              \"description\": \"Optional: mode bits used to set permissions on created files by default.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nDefaults to 0644.\\nDirectories within the path are not affected by this setting.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"items\": {\n              \"description\": \"If unspecified, each key-value pair in the Data field of the referenced\\nConfigMap will be projected into the volume as a file whose name is the\\nkey and content is the value. If specified, the listed keys will be\\nprojected into the specified paths, and unlisted keys will not be\\npresent. If a key is specified which is not present in the ConfigMap,\\nthe volume setup will error unless it is marked optional. Paths must be\\nrelative and may not contain the '..' path or start with '..'.\",\n              \"items\": {\n               \"description\": \"Maps a string key to a path within a volume.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key to project.\",\n                 \"type\": \"string\"\n                },\n                \"mode\": {\n                 \"description\": \"Optional: mode bits used to set permissions on this file.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n                 \"format\": \"int32\",\n                 \"type\": \"integer\"\n                },\n                \"path\": {\n                 \"description\": \"The relative path of the file to map the key to.\\nMay not be an absolute path.\\nMay not contain the path element '..'.\\nMay not start with the string '..'.\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"path\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"name\": {\n              \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n              \"type\": \"string\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the ConfigMap or its keys must be defined\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"csi\": {\n            \"description\": \"CSI (Container Storage Interface) represents ephemeral storage that is handled by certain external CSI drivers (Beta feature).\",\n            \"properties\": {\n             \"driver\": {\n              \"description\": \"Driver is the name of the CSI driver that handles this volume.\\nConsult with your admin for the correct name as registered in the cluster.\",\n              \"type\": \"string\"\n             },\n             \"fsType\": {\n              \"description\": \"Filesystem type to mount. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\".\\nIf not provided, the empty value is passed to the associated CSI driver\\nwhich will determine the default filesystem to apply.\",\n              \"type\": \"string\"\n             },\n             \"nodePublishSecretRef\": {\n              \"description\": \"NodePublishSecretRef is a reference to the secret object containing\\nsensitive information to pass to the CSI driver to complete the CSI\\nNodePublishVolume and NodeUnpublishVolume calls.\\nThis field is optional, and  may be empty if no secret is required. If the\\nsecret object contains more than one secret, all secret references are passed.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"readOnly\": {\n              \"description\": \"Specifies a read-only configuration for the volume.\\nDefaults to false (read/write).\",\n              \"type\": \"boolean\"\n             },\n             \"volumeAttributes\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"VolumeAttributes stores driver-specific properties that are passed to the CSI\\ndriver. Consult your driver's documentation for supported values.\",\n              \"type\": \"object\"\n             }\n            },\n            \"required\": [\n             \"driver\"\n            ],\n            \"type\": \"object\"\n           },\n           \"downwardAPI\": {\n            \"description\": \"DownwardAPI represents downward API about the pod that should populate this volume\",\n            \"properties\": {\n             \"defaultMode\": {\n              \"description\": \"Optional: mode bits to use on created files by default. Must be a\\nOptional: mode bits used to set permissions on created files by default.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nDefaults to 0644.\\nDirectories within the path are not affected by this setting.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"items\": {\n              \"description\": \"Items is a list of downward API volume file\",\n              \"items\": {\n               \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n               \"properties\": {\n                \"fieldRef\": {\n                 \"description\": \"Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.\",\n                 \"properties\": {\n                  \"apiVersion\": {\n                   \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                   \"type\": \"string\"\n                  },\n                  \"fieldPath\": {\n                   \"description\": \"Path of the field to select in the specified API version.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"fieldPath\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"mode\": {\n                 \"description\": \"Optional: mode bits used to set permissions on this file, must be an octal value\\nbetween 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n                 \"format\": \"int32\",\n                 \"type\": \"integer\"\n                },\n                \"path\": {\n                 \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n                 \"type\": \"string\"\n                },\n                \"resourceFieldRef\": {\n                 \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n                 \"properties\": {\n                  \"containerName\": {\n                   \"description\": \"Container name: required for volumes, optional for env vars\",\n                   \"type\": \"string\"\n                  },\n                  \"divisor\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"resource\": {\n                   \"description\": \"Required: resource to select\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resource\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"path\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"emptyDir\": {\n            \"description\": \"EmptyDir represents a temporary directory that shares a pod's lifetime.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n            \"properties\": {\n             \"medium\": {\n              \"description\": \"What type of storage medium should back this directory.\\nThe default is \\\"\\\" which means to use the node's default medium.\\nMust be an empty string (default) or Memory.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n              \"type\": \"string\"\n             },\n             \"sizeLimit\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Total amount of local storage required for this EmptyDir volume.\\nThe size limit is also applicable for memory medium.\\nThe maximum usage on memory medium EmptyDir would be the minimum value between\\nthe SizeLimit specified here and the sum of memory limits of all containers in a pod.\\nThe default is nil which means that the limit is undefined.\\nMore info: http://kubernetes.io/docs/user-guide/volumes#emptydir\",\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"ephemeral\": {\n            \"description\": \"Ephemeral represents a volume that is handled by a cluster storage driver.\\nThe volume's lifecycle is tied to the pod that defines it - it will be created before the pod starts,\\nand deleted when the pod is removed.\\n\\n\\nUse this if:\\na) the volume is only needed while the pod runs,\\nb) features of normal volumes like restoring from snapshot or capacity\\n   tracking are needed,\\nc) the storage driver is specified through a storage class, and\\nd) the storage driver supports dynamic volume provisioning through\\n   a PersistentVolumeClaim (see EphemeralVolumeSource for more\\n   information on the connection between this volume type\\n   and PersistentVolumeClaim).\\n\\n\\nUse PersistentVolumeClaim or one of the vendor-specific\\nAPIs for volumes that persist for longer than the lifecycle\\nof an individual pod.\\n\\n\\nUse CSI for light-weight local ephemeral volumes if the CSI driver is meant to\\nbe used that way - see the documentation of the driver for\\nmore information.\\n\\n\\nA pod can use both types of ephemeral volumes and\\npersistent volumes at the same time.\",\n            \"properties\": {\n             \"volumeClaimTemplate\": {\n              \"description\": \"Will be used to create a stand-alone PVC to provision the volume.\\nThe pod in which this EphemeralVolumeSource is embedded will be the\\nowner of the PVC, i.e. the PVC will be deleted together with the\\npod.  The name of the PVC will be `\\u003cpod name\\u003e-\\u003cvolume name\\u003e` where\\n`\\u003cvolume name\\u003e` is the name from the `PodSpec.Volumes` array\\nentry. Pod validation will reject the pod if the concatenated name\\nis not valid for a PVC (for example, too long).\\n\\n\\nAn existing PVC with that name that is not owned by the pod\\nwill *not* be used for the pod to avoid using an unrelated\\nvolume by mistake. Starting the pod is then blocked until\\nthe unrelated PVC is removed. If such a pre-created PVC is\\nmeant to be used by the pod, the PVC has to updated with an\\nowner reference to the pod once the pod exists. Normally\\nthis should not be necessary, but it may be useful when\\nmanually reconstructing a broken cluster.\\n\\n\\nThis field is read-only and no changes will be made by Kubernetes\\nto the PVC after it has been created.\\n\\n\\nRequired, must not be nil.\",\n              \"properties\": {\n               \"metadata\": {\n                \"description\": \"May contain labels and annotations that will be copied into the PVC\\nwhen creating it. No other fields are allowed and will be rejected during\\nvalidation.\",\n                \"type\": \"object\"\n               },\n               \"spec\": {\n                \"description\": \"The specification for the PersistentVolumeClaim. The entire content is\\ncopied unchanged into the PVC that gets created from this\\ntemplate. The same fields as in a PersistentVolumeClaim\\nare also valid here.\",\n                \"properties\": {\n                 \"accessModes\": {\n                  \"description\": \"AccessModes contains the desired access modes the volume should have.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\"\n                 },\n                 \"dataSource\": {\n                  \"description\": \"This field can be used to specify either:\\n* An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)\\n* An existing PVC (PersistentVolumeClaim)\\nIf the provisioner or an external controller can support the specified data source,\\nit will create a new volume based on the contents of the specified data source.\\nIf the AnyVolumeDataSource feature gate is enabled, this field will always have\\nthe same contents as the DataSourceRef field.\",\n                  \"properties\": {\n                   \"apiGroup\": {\n                    \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n                    \"type\": \"string\"\n                   },\n                   \"kind\": {\n                    \"description\": \"Kind is the type of resource being referenced\",\n                    \"type\": \"string\"\n                   },\n                   \"name\": {\n                    \"description\": \"Name is the name of resource being referenced\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"kind\",\n                   \"name\"\n                  ],\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"dataSourceRef\": {\n                  \"description\": \"Specifies the object from which to populate the volume with data, if a non-empty\\nvolume is desired. This may be any local object from a non-empty API group (non\\ncore object) or a PersistentVolumeClaim object.\\nWhen this field is specified, volume binding will only succeed if the type of\\nthe specified object matches some installed volume populator or dynamic\\nprovisioner.\\nThis field will replace the functionality of the DataSource field and as such\\nif both fields are non-empty, they must have the same value. For backwards\\ncompatibility, both fields (DataSource and DataSourceRef) will be set to the same\\nvalue automatically if one of them is empty and the other is non-empty.\\nThere are two important differences between DataSource and DataSourceRef:\\n* While DataSource only allows two specific types of objects, DataSourceRef\\n  allows any non-core object, as well as PersistentVolumeClaim objects.\\n* While DataSource ignores disallowed values (dropping them), DataSourceRef\\n  preserves all values, and generates an error if a disallowed value is\\n  specified.\\n(Alpha) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\",\n                  \"properties\": {\n                   \"apiGroup\": {\n                    \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n                    \"type\": \"string\"\n                   },\n                   \"kind\": {\n                    \"description\": \"Kind is the type of resource being referenced\",\n                    \"type\": \"string\"\n                   },\n                   \"name\": {\n                    \"description\": \"Name is the name of resource being referenced\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"kind\",\n                   \"name\"\n                  ],\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"resources\": {\n                  \"description\": \"Resources represents the minimum resources the volume should have.\\nIf RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements\\nthat are lower than previous value but must still be higher than capacity recorded in the\\nstatus field of the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n                  \"properties\": {\n                   \"limits\": {\n                    \"additionalProperties\": {\n                     \"anyOf\": [\n                      {\n                       \"type\": \"integer\"\n                      },\n                      {\n                       \"type\": \"string\"\n                      }\n                     ],\n                     \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                     \"x-kubernetes-int-or-string\": true\n                    },\n                    \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                    \"type\": \"object\"\n                   },\n                   \"requests\": {\n                    \"additionalProperties\": {\n                     \"anyOf\": [\n                      {\n                       \"type\": \"integer\"\n                      },\n                      {\n                       \"type\": \"string\"\n                      }\n                     ],\n                     \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                     \"x-kubernetes-int-or-string\": true\n                    },\n                    \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"selector\": {\n                  \"description\": \"A label query over volumes to consider for binding.\",\n                  \"properties\": {\n                   \"matchExpressions\": {\n                    \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                    \"items\": {\n                     \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                     \"properties\": {\n                      \"key\": {\n                       \"description\": \"key is the label key that the selector applies to.\",\n                       \"type\": \"string\"\n                      },\n                      \"operator\": {\n                       \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                       \"type\": \"string\"\n                      },\n                      \"values\": {\n                       \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                       \"items\": {\n                        \"type\": \"string\"\n                       },\n                       \"type\": \"array\"\n                      }\n                     },\n                     \"required\": [\n                      \"key\",\n                      \"operator\"\n                     ],\n                     \"type\": \"object\"\n                    },\n                    \"type\": \"array\"\n                   },\n                   \"matchLabels\": {\n                    \"additionalProperties\": {\n                     \"type\": \"string\"\n                    },\n                    \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"storageClassName\": {\n                  \"description\": \"Name of the StorageClass required by the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n                  \"type\": \"string\"\n                 },\n                 \"volumeMode\": {\n                  \"description\": \"volumeMode defines what type of volume is required by the claim.\\nValue of Filesystem is implied when not included in claim spec.\",\n                  \"type\": \"string\"\n                 },\n                 \"volumeName\": {\n                  \"description\": \"VolumeName is the binding reference to the PersistentVolume backing this claim.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"type\": \"object\"\n               }\n              },\n              \"required\": [\n               \"spec\"\n              ],\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"fc\": {\n            \"description\": \"FC represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"Filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nTODO: how do we prevent errors in the filesystem from compromising the machine\",\n              \"type\": \"string\"\n             },\n             \"lun\": {\n              \"description\": \"Optional: FC target lun number\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"readOnly\": {\n              \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n              \"type\": \"boolean\"\n             },\n             \"targetWWNs\": {\n              \"description\": \"Optional: FC target worldwide names (WWNs)\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"wwids\": {\n              \"description\": \"Optional: FC volume world wide identifiers (wwids)\\nEither wwids or combination of targetWWNs and lun must be set, but not both simultaneously.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"flexVolume\": {\n            \"description\": \"FlexVolume represents a generic volume resource that is\\nprovisioned/attached using an exec based plugin.\",\n            \"properties\": {\n             \"driver\": {\n              \"description\": \"Driver is the name of the driver to use for this volume.\",\n              \"type\": \"string\"\n             },\n             \"fsType\": {\n              \"description\": \"Filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". The default filesystem depends on FlexVolume script.\",\n              \"type\": \"string\"\n             },\n             \"options\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"Optional: Extra command options if any.\",\n              \"type\": \"object\"\n             },\n             \"readOnly\": {\n              \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"description\": \"Optional: SecretRef is reference to the secret object containing\\nsensitive information to pass to the plugin scripts. This may be\\nempty if no secret object is specified. If the secret object\\ncontains more than one secret, all secrets are passed to the plugin\\nscripts.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"required\": [\n             \"driver\"\n            ],\n            \"type\": \"object\"\n           },\n           \"flocker\": {\n            \"description\": \"Flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running\",\n            \"properties\": {\n             \"datasetName\": {\n              \"description\": \"Name of the dataset stored as metadata -\\u003e name on the dataset for Flocker\\nshould be considered as deprecated\",\n              \"type\": \"string\"\n             },\n             \"datasetUUID\": {\n              \"description\": \"UUID of the dataset. This is unique identifier of a Flocker dataset\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"gcePersistentDisk\": {\n            \"description\": \"GCEPersistentDisk represents a GCE Disk resource that is attached to a\\nkubelet's host machine and then exposed to the pod.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"Filesystem type of the volume that you want to mount.\\nTip: Ensure that the filesystem type is supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\\nTODO: how do we prevent errors in the filesystem from compromising the machine\",\n              \"type\": \"string\"\n             },\n             \"partition\": {\n              \"description\": \"The partition in the volume that you want to mount.\\nIf omitted, the default is to mount by volume name.\\nExamples: For volume /dev/sda1, you specify the partition as \\\"1\\\".\\nSimilarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty).\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"pdName\": {\n              \"description\": \"Unique name of the PD resource in GCE. Used to identify the disk in GCE.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts.\\nDefaults to false.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"pdName\"\n            ],\n            \"type\": \"object\"\n           },\n           \"gitRepo\": {\n            \"description\": \"GitRepo represents a git repository at a particular revision.\\nDEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an\\nEmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir\\ninto the Pod's container.\",\n            \"properties\": {\n             \"directory\": {\n              \"description\": \"Target directory name.\\nMust not contain or start with '..'.  If '.' is supplied, the volume directory will be the\\ngit repository.  Otherwise, if specified, the volume will contain the git repository in\\nthe subdirectory with the given name.\",\n              \"type\": \"string\"\n             },\n             \"repository\": {\n              \"description\": \"Repository URL\",\n              \"type\": \"string\"\n             },\n             \"revision\": {\n              \"description\": \"Commit hash for the specified revision.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"repository\"\n            ],\n            \"type\": \"object\"\n           },\n           \"glusterfs\": {\n            \"description\": \"Glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime.\\nMore info: https://examples.k8s.io/volumes/glusterfs/README.md\",\n            \"properties\": {\n             \"endpoints\": {\n              \"description\": \"EndpointsName is the endpoint name that details Glusterfs topology.\\nMore info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n              \"type\": \"string\"\n             },\n             \"path\": {\n              \"description\": \"Path is the Glusterfs volume path.\\nMore info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"ReadOnly here will force the Glusterfs volume to be mounted with read-only permissions.\\nDefaults to false.\\nMore info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"endpoints\",\n             \"path\"\n            ],\n            \"type\": \"object\"\n           },\n           \"hostPath\": {\n            \"description\": \"HostPath represents a pre-existing file or directory on the host\\nmachine that is directly exposed to the container. This is generally\\nused for system agents or other privileged things that are allowed\\nto see the host machine. Most containers will NOT need this.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\\n---\\nTODO(jonesdl) We need to restrict who can use host directory mounts and who can/can not\\nmount host directories as read/write.\",\n            \"properties\": {\n             \"path\": {\n              \"description\": \"Path of the directory on the host.\\nIf the path is a symlink, it will follow the link to the real path.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"description\": \"Type for HostPath Volume\\nDefaults to \\\"\\\"\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"path\"\n            ],\n            \"type\": \"object\"\n           },\n           \"iscsi\": {\n            \"description\": \"ISCSI represents an ISCSI Disk resource that is attached to a\\nkubelet's host machine and then exposed to the pod.\\nMore info: https://examples.k8s.io/volumes/iscsi/README.md\",\n            \"properties\": {\n             \"chapAuthDiscovery\": {\n              \"description\": \"whether support iSCSI Discovery CHAP authentication\",\n              \"type\": \"boolean\"\n             },\n             \"chapAuthSession\": {\n              \"description\": \"whether support iSCSI Session CHAP authentication\",\n              \"type\": \"boolean\"\n             },\n             \"fsType\": {\n              \"description\": \"Filesystem type of the volume that you want to mount.\\nTip: Ensure that the filesystem type is supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi\\nTODO: how do we prevent errors in the filesystem from compromising the machine\",\n              \"type\": \"string\"\n             },\n             \"initiatorName\": {\n              \"description\": \"Custom iSCSI Initiator Name.\\nIf initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface\\n\\u003ctarget portal\\u003e:\\u003cvolume name\\u003e will be created for the connection.\",\n              \"type\": \"string\"\n             },\n             \"iqn\": {\n              \"description\": \"Target iSCSI Qualified Name.\",\n              \"type\": \"string\"\n             },\n             \"iscsiInterface\": {\n              \"description\": \"iSCSI Interface Name that uses an iSCSI transport.\\nDefaults to 'default' (tcp).\",\n              \"type\": \"string\"\n             },\n             \"lun\": {\n              \"description\": \"iSCSI Target Lun number.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"portals\": {\n              \"description\": \"iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port\\nis other than default (typically TCP ports 860 and 3260).\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"readOnly\": {\n              \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts.\\nDefaults to false.\",\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"description\": \"CHAP Secret for iSCSI target and initiator authentication\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"targetPortal\": {\n              \"description\": \"iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port\\nis other than default (typically TCP ports 860 and 3260).\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"iqn\",\n             \"lun\",\n             \"targetPortal\"\n            ],\n            \"type\": \"object\"\n           },\n           \"name\": {\n            \"description\": \"Volume's name.\\nMust be a DNS_LABEL and unique within the pod.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"nfs\": {\n            \"description\": \"NFS represents an NFS mount on the host that shares a pod's lifetime\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n            \"properties\": {\n             \"path\": {\n              \"description\": \"Path that is exported by the NFS server.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"ReadOnly here will force\\nthe NFS export to be mounted with read-only permissions.\\nDefaults to false.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n              \"type\": \"boolean\"\n             },\n             \"server\": {\n              \"description\": \"Server is the hostname or IP address of the NFS server.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"path\",\n             \"server\"\n            ],\n            \"type\": \"object\"\n           },\n           \"persistentVolumeClaim\": {\n            \"description\": \"PersistentVolumeClaimVolumeSource represents a reference to a\\nPersistentVolumeClaim in the same namespace.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n            \"properties\": {\n             \"claimName\": {\n              \"description\": \"ClaimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"Will force the ReadOnly setting in VolumeMounts.\\nDefault false.\",\n              \"type\": \"boolean\"\n             }\n            },\n            \"required\": [\n             \"claimName\"\n            ],\n            \"type\": \"object\"\n           },\n           \"photonPersistentDisk\": {\n            \"description\": \"PhotonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"Filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n              \"type\": \"string\"\n             },\n             \"pdID\": {\n              \"description\": \"ID that identifies Photon Controller persistent disk\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"pdID\"\n            ],\n            \"type\": \"object\"\n           },\n           \"portworxVolume\": {\n            \"description\": \"PortworxVolume represents a portworx volume attached and mounted on kubelets host machine\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"FSType represents the filesystem type to mount\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n              \"type\": \"boolean\"\n             },\n             \"volumeID\": {\n              \"description\": \"VolumeID uniquely identifies a Portworx volume\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"volumeID\"\n            ],\n            \"type\": \"object\"\n           },\n           \"projected\": {\n            \"description\": \"Items for all in one resources secrets, configmaps, and downward API\",\n            \"properties\": {\n             \"defaultMode\": {\n              \"description\": \"Mode bits used to set permissions on created files by default.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nDirectories within the path are not affected by this setting.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"sources\": {\n              \"description\": \"list of volume projections\",\n              \"items\": {\n               \"description\": \"Projection that may be projected along with other supported volume types\",\n               \"properties\": {\n                \"configMap\": {\n                 \"description\": \"information about the configMap data to project\",\n                 \"properties\": {\n                  \"items\": {\n                   \"description\": \"If unspecified, each key-value pair in the Data field of the referenced\\nConfigMap will be projected into the volume as a file whose name is the\\nkey and content is the value. If specified, the listed keys will be\\nprojected into the specified paths, and unlisted keys will not be\\npresent. If a key is specified which is not present in the ConfigMap,\\nthe volume setup will error unless it is marked optional. Paths must be\\nrelative and may not contain the '..' path or start with '..'.\",\n                   \"items\": {\n                    \"description\": \"Maps a string key to a path within a volume.\",\n                    \"properties\": {\n                     \"key\": {\n                      \"description\": \"The key to project.\",\n                      \"type\": \"string\"\n                     },\n                     \"mode\": {\n                      \"description\": \"Optional: mode bits used to set permissions on this file.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"path\": {\n                      \"description\": \"The relative path of the file to map the key to.\\nMay not be an absolute path.\\nMay not contain the path element '..'.\\nMay not start with the string '..'.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"key\",\n                     \"path\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"type\": \"array\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the ConfigMap or its keys must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"downwardAPI\": {\n                 \"description\": \"information about the downwardAPI data to project\",\n                 \"properties\": {\n                  \"items\": {\n                   \"description\": \"Items is a list of DownwardAPIVolume file\",\n                   \"items\": {\n                    \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n                    \"properties\": {\n                     \"fieldRef\": {\n                      \"description\": \"Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.\",\n                      \"properties\": {\n                       \"apiVersion\": {\n                        \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                        \"type\": \"string\"\n                       },\n                       \"fieldPath\": {\n                        \"description\": \"Path of the field to select in the specified API version.\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"required\": [\n                       \"fieldPath\"\n                      ],\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     },\n                     \"mode\": {\n                      \"description\": \"Optional: mode bits used to set permissions on this file, must be an octal value\\nbetween 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"path\": {\n                      \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n                      \"type\": \"string\"\n                     },\n                     \"resourceFieldRef\": {\n                      \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n                      \"properties\": {\n                       \"containerName\": {\n                        \"description\": \"Container name: required for volumes, optional for env vars\",\n                        \"type\": \"string\"\n                       },\n                       \"divisor\": {\n                        \"anyOf\": [\n                         {\n                          \"type\": \"integer\"\n                         },\n                         {\n                          \"type\": \"string\"\n                         }\n                        ],\n                        \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                        \"x-kubernetes-int-or-string\": true\n                       },\n                       \"resource\": {\n                        \"description\": \"Required: resource to select\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"required\": [\n                       \"resource\"\n                      ],\n                      \"type\": \"object\",\n                      \"x-kubernetes-map-type\": \"atomic\"\n                     }\n                    },\n                    \"required\": [\n                     \"path\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"type\": \"array\"\n                  }\n                 },\n                 \"type\": \"object\"\n                },\n                \"secret\": {\n                 \"description\": \"information about the secret data to project\",\n                 \"properties\": {\n                  \"items\": {\n                   \"description\": \"If unspecified, each key-value pair in the Data field of the referenced\\nSecret will be projected into the volume as a file whose name is the\\nkey and content is the value. If specified, the listed keys will be\\nprojected into the specified paths, and unlisted keys will not be\\npresent. If a key is specified which is not present in the Secret,\\nthe volume setup will error unless it is marked optional. Paths must be\\nrelative and may not contain the '..' path or start with '..'.\",\n                   \"items\": {\n                    \"description\": \"Maps a string key to a path within a volume.\",\n                    \"properties\": {\n                     \"key\": {\n                      \"description\": \"The key to project.\",\n                      \"type\": \"string\"\n                     },\n                     \"mode\": {\n                      \"description\": \"Optional: mode bits used to set permissions on this file.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"path\": {\n                      \"description\": \"The relative path of the file to map the key to.\\nMay not be an absolute path.\\nMay not contain the path element '..'.\\nMay not start with the string '..'.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"key\",\n                     \"path\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"type\": \"array\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the Secret or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"serviceAccountToken\": {\n                 \"description\": \"information about the serviceAccountToken data to project\",\n                 \"properties\": {\n                  \"audience\": {\n                   \"description\": \"Audience is the intended audience of the token. A recipient of a token\\nmust identify itself with an identifier specified in the audience of the\\ntoken, and otherwise should reject the token. The audience defaults to the\\nidentifier of the apiserver.\",\n                   \"type\": \"string\"\n                  },\n                  \"expirationSeconds\": {\n                   \"description\": \"ExpirationSeconds is the requested duration of validity of the service\\naccount token. As the token approaches expiration, the kubelet volume\\nplugin will proactively rotate the service account token. The kubelet will\\nstart trying to rotate the token if the token is older than 80 percent of\\nits time to live or if the token is older than 24 hours.Defaults to 1 hour\\nand must be at least 10 minutes.\",\n                   \"format\": \"int64\",\n                   \"type\": \"integer\"\n                  },\n                  \"path\": {\n                   \"description\": \"Path is the path relative to the mount point of the file to project the\\ntoken into.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"path\"\n                 ],\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"quobyte\": {\n            \"description\": \"Quobyte represents a Quobyte mount on the host that shares a pod's lifetime\",\n            \"properties\": {\n             \"group\": {\n              \"description\": \"Group to map volume access to\\nDefault is no group\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"ReadOnly here will force the Quobyte volume to be mounted with read-only permissions.\\nDefaults to false.\",\n              \"type\": \"boolean\"\n             },\n             \"registry\": {\n              \"description\": \"Registry represents a single or multiple Quobyte Registry services\\nspecified as a string as host:port pair (multiple entries are separated with commas)\\nwhich acts as the central registry for volumes\",\n              \"type\": \"string\"\n             },\n             \"tenant\": {\n              \"description\": \"Tenant owning the given Quobyte volume in the Backend\\nUsed with dynamically provisioned Quobyte volumes, value is set by the plugin\",\n              \"type\": \"string\"\n             },\n             \"user\": {\n              \"description\": \"User to map volume access to\\nDefaults to serivceaccount user\",\n              \"type\": \"string\"\n             },\n             \"volume\": {\n              \"description\": \"Volume is a string that references an already created Quobyte volume by name.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"registry\",\n             \"volume\"\n            ],\n            \"type\": \"object\"\n           },\n           \"rbd\": {\n            \"description\": \"RBD represents a Rados Block Device mount on the host that shares a pod's lifetime.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"Filesystem type of the volume that you want to mount.\\nTip: Ensure that the filesystem type is supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#rbd\\nTODO: how do we prevent errors in the filesystem from compromising the machine\",\n              \"type\": \"string\"\n             },\n             \"image\": {\n              \"description\": \"The rados image name.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n              \"type\": \"string\"\n             },\n             \"keyring\": {\n              \"description\": \"Keyring is the path to key ring for RBDUser.\\nDefault is /etc/ceph/keyring.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n              \"type\": \"string\"\n             },\n             \"monitors\": {\n              \"description\": \"A collection of Ceph monitors.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\"\n             },\n             \"pool\": {\n              \"description\": \"The rados pool name.\\nDefault is rbd.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts.\\nDefaults to false.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"description\": \"SecretRef is name of the authentication secret for RBDUser. If provided\\noverrides keyring.\\nDefault is nil.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"user\": {\n              \"description\": \"The rados user name.\\nDefault is admin.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"image\",\n             \"monitors\"\n            ],\n            \"type\": \"object\"\n           },\n           \"scaleIO\": {\n            \"description\": \"ScaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"Filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\".\\nDefault is \\\"xfs\\\".\",\n              \"type\": \"string\"\n             },\n             \"gateway\": {\n              \"description\": \"The host address of the ScaleIO API Gateway.\",\n              \"type\": \"string\"\n             },\n             \"protectionDomain\": {\n              \"description\": \"The name of the ScaleIO Protection Domain for the configured storage.\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"description\": \"SecretRef references to the secret for ScaleIO user and other\\nsensitive information. If this is not provided, Login operation will fail.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"sslEnabled\": {\n              \"description\": \"Flag to enable/disable SSL communication with Gateway, default false\",\n              \"type\": \"boolean\"\n             },\n             \"storageMode\": {\n              \"description\": \"Indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned.\\nDefault is ThinProvisioned.\",\n              \"type\": \"string\"\n             },\n             \"storagePool\": {\n              \"description\": \"The ScaleIO Storage Pool associated with the protection domain.\",\n              \"type\": \"string\"\n             },\n             \"system\": {\n              \"description\": \"The name of the storage system as configured in ScaleIO.\",\n              \"type\": \"string\"\n             },\n             \"volumeName\": {\n              \"description\": \"The name of a volume already created in the ScaleIO system\\nthat is associated with this volume source.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"gateway\",\n             \"secretRef\",\n             \"system\"\n            ],\n            \"type\": \"object\"\n           },\n           \"secret\": {\n            \"description\": \"Secret represents a secret that should populate this volume.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n            \"properties\": {\n             \"defaultMode\": {\n              \"description\": \"Optional: mode bits used to set permissions on created files by default.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values\\nfor mode bits. Defaults to 0644.\\nDirectories within the path are not affected by this setting.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n              \"format\": \"int32\",\n              \"type\": \"integer\"\n             },\n             \"items\": {\n              \"description\": \"If unspecified, each key-value pair in the Data field of the referenced\\nSecret will be projected into the volume as a file whose name is the\\nkey and content is the value. If specified, the listed keys will be\\nprojected into the specified paths, and unlisted keys will not be\\npresent. If a key is specified which is not present in the Secret,\\nthe volume setup will error unless it is marked optional. Paths must be\\nrelative and may not contain the '..' path or start with '..'.\",\n              \"items\": {\n               \"description\": \"Maps a string key to a path within a volume.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key to project.\",\n                 \"type\": \"string\"\n                },\n                \"mode\": {\n                 \"description\": \"Optional: mode bits used to set permissions on this file.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n                 \"format\": \"int32\",\n                 \"type\": \"integer\"\n                },\n                \"path\": {\n                 \"description\": \"The relative path of the file to map the key to.\\nMay not be an absolute path.\\nMay not contain the path element '..'.\\nMay not start with the string '..'.\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"path\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\"\n             },\n             \"optional\": {\n              \"description\": \"Specify whether the Secret or its keys must be defined\",\n              \"type\": \"boolean\"\n             },\n             \"secretName\": {\n              \"description\": \"Name of the secret in the pod's namespace to use.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"storageos\": {\n            \"description\": \"StorageOS represents a StorageOS volume attached and mounted on Kubernetes nodes.\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"Filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n              \"type\": \"string\"\n             },\n             \"readOnly\": {\n              \"description\": \"Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n              \"type\": \"boolean\"\n             },\n             \"secretRef\": {\n              \"description\": \"SecretRef specifies the secret to use for obtaining the StorageOS API\\ncredentials.  If not specified, default values will be attempted.\",\n              \"properties\": {\n               \"name\": {\n                \"description\": \"Name of the referent.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\\nTODO: Add other useful fields. apiVersion, kind, uid?\",\n                \"type\": \"string\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"volumeName\": {\n              \"description\": \"VolumeName is the human-readable name of the StorageOS volume.  Volume\\nnames are only unique within a namespace.\",\n              \"type\": \"string\"\n             },\n             \"volumeNamespace\": {\n              \"description\": \"VolumeNamespace specifies the scope of the volume within StorageOS.  If no\\nnamespace is specified then the Pod's namespace will be used.  This allows the\\nKubernetes name scoping to be mirrored within StorageOS for tighter integration.\\nSet VolumeName to any name to override the default behaviour.\\nSet to \\\"default\\\" if you are not using namespaces within StorageOS.\\nNamespaces that do not pre-exist within StorageOS will be created.\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"vsphereVolume\": {\n            \"description\": \"VsphereVolume represents a vSphere volume attached and mounted on kubelets host machine\",\n            \"properties\": {\n             \"fsType\": {\n              \"description\": \"Filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n              \"type\": \"string\"\n             },\n             \"storagePolicyID\": {\n              \"description\": \"Storage Policy Based Management (SPBM) profile ID associated with the StoragePolicyName.\",\n              \"type\": \"string\"\n             },\n             \"storagePolicyName\": {\n              \"description\": \"Storage Policy Based Management (SPBM) profile name.\",\n              \"type\": \"string\"\n             },\n             \"volumePath\": {\n              \"description\": \"Path that identifies vSphere volume vmdk\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"volumePath\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"name\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        }\n       },\n       \"required\": [\n        \"containers\"\n       ],\n       \"type\": \"object\"\n      },\n      \"ports\": {\n       \"items\": {\n        \"description\": \"ContainerPort represents a network port in a single container.\",\n        \"properties\": {\n         \"containerPort\": {\n          \"description\": \"Number of port to expose on the pod's IP address.\\nThis must be a valid port number, 0 \\u003c x \\u003c 65536.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"hostIP\": {\n          \"description\": \"What host IP to bind the external port to.\",\n          \"type\": \"string\"\n         },\n         \"hostPort\": {\n          \"description\": \"Number of port to expose on the host.\\nIf specified, this must be a valid port number, 0 \\u003c x \\u003c 65536.\\nIf HostNetwork is specified, this must match ContainerPort.\\nMost containers do not need this.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"name\": {\n          \"description\": \"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each\\nnamed port in a pod must have a unique name. Name for the port that can be\\nreferred to by services.\",\n          \"type\": \"string\"\n         },\n         \"protocol\": {\n          \"default\": \"TCP\",\n          \"description\": \"Protocol for port. Must be UDP, TCP, or SCTP.\\nDefaults to \\\"TCP\\\".\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"containerPort\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"privileged\": {\n       \"type\": \"boolean\"\n      },\n      \"protocol\": {\n       \"type\": \"string\"\n      },\n      \"resources\": {\n       \"description\": \"ResourceRequirements describes the compute resource requirements.\",\n       \"properties\": {\n        \"limits\": {\n         \"additionalProperties\": {\n          \"anyOf\": [\n           {\n            \"type\": \"integer\"\n           },\n           {\n            \"type\": \"string\"\n           }\n          ],\n          \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n          \"x-kubernetes-int-or-string\": true\n         },\n         \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n         \"type\": \"object\"\n        },\n        \"requests\": {\n         \"additionalProperties\": {\n          \"anyOf\": [\n           {\n            \"type\": \"integer\"\n           },\n           {\n            \"type\": \"string\"\n           }\n          ],\n          \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n          \"x-kubernetes-int-or-string\": true\n         },\n         \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"storageInitializerImage\": {\n       \"type\": \"string\"\n      },\n      \"upgradeStrategy\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Model Deployment\",\n \"type\": \"object\"\n}"}}