{"id": "00000000-0000-0000-0000-000000000000", "evaluationQuery": "", "kind": "edge", "metadata": {"description": "A relationship that represents volume mounts between components", "styles": {"primaryColor": "", "svgColor": "", "svgWhite": ""}, "isAnnotation": false}, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "schemaVersion": "relationships.meshery.io/v1alpha3", "selectors": [{"allow": {"from": [{"id": null, "kind": "Pod", "match": {"from": [{"id": null, "kind": "self", "mutatedRef": [["configuration", "spec", "volumes", "_", "persistentVolumeClaim", "claim<PERSON>ame"]]}], "to": [{"id": null, "kind": "PersistentVolumeClaim", "mutatorRef": [["displayName"]]}]}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}], "to": [{"id": null, "kind": "PersistentVolume", "match": {"from": [{"id": null, "kind": "PersistentVolumeClaim", "mutatedRef": [["configuration", "spec", "volumeName"], ["configuration", "spec", "selector", "matchLabels"]]}], "to": [{"id": null, "kind": "self", "mutatorRef": [["displayName"], ["configuration", "metadata", "labels"]]}]}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}]}, "deny": {"from": [{"id": null, "kind": "Pod", "match": {}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}], "to": [{"id": null, "kind": "Pod", "match": {}, "match_strategy_matrix": null, "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}]}}], "subType": "mount", "status": "enabled", "type": "binding", "version": "v1.0.0"}