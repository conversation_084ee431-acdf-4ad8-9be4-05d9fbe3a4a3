{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Chaos Engine", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "litmus-core", "displayName": "Litmus Chaos", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Observability and Analysis"}, "subCategory": "Chaos Engineering", "metadata": {"isAnnotation": false, "primaryColor": "#878ede", "secondaryColor": "#CCD3FF", "shape": "circle", "source_uri": "https://github.com/litmuschaos/litmus-helm/releases/download/litmus-core-3.19.0/litmus-core-3.19.0.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 116 146\" fill=\"none\"><path xmlns=\"http://www.w3.org/2000/svg\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M115.657 0H0v135.05C0 141.098 4.855 146 10.843 146h93.971c5.989 0 10.843-4.902 10.843-10.95V0Z\" fill=\"#878EDE\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M17.794 17.804h17.793v40.95H17.793v-40.95Z\" fill=\"#4028A0\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M17.794 55.194h17.793v73H17.793v-73Zm26.69 55.197h53.38v17.805h-53.38v-17.805Z\" fill=\"#fff\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 109 147\" fill=\"none\"><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M31.654 60.497h-16.05V16.03h16.05v44.468Z\" fill=\"#D1D2D9\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M93.832 130.115H46.156v-15.961h47.676v15.961Zm-62.178 0h-16.05V16.029h16.05v114.086ZM.649.035v133.62c0 6.838 5.577 12.379 12.449 12.379h83.384c6.805 0 12.306-5.474 12.306-12.236V.034H.648Z\" fill=\"#fff\"></path></svg>"}, "model": {"version": "3.19.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#878ede", "secondaryColor": "#CCD3FF", "shape": "circle", "svgColor": "<svg width=\"116\" height=\"146\" viewBox=\"0 0 116 146\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M115.657 0H0v135.05C0 141.098 4.855 146 10.843 146h93.971c5.989 0 10.843-4.902 10.843-10.95V0Z\" fill=\"#878EDE\"/><path d=\"M17.794 17.804h17.793v40.95H17.793v-40.95Z\" fill=\"#4028A0\"/><path d=\"M17.794 55.194h17.793v73H17.793v-73Zm26.69 55.197h53.38v17.805h-53.38v-17.805Z\" fill=\"#fff\"/></svg>", "svgComplete": "", "svgWhite": "<svg width=\"109\" height=\"147\" viewBox=\"0 0 109 147\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M31.654 60.497h-16.05V16.03h16.05v44.468Z\" fill=\"#D1D2D9\"/><path d=\"M93.832 130.115H46.156v-15.961h47.676v15.961Zm-62.178 0h-16.05V16.029h16.05v114.086ZM.649.035v133.62c0 6.838 5.577 12.379 12.449 12.379h83.384c6.805 0 12.306-5.474 12.306-12.236V.034H.648Z\" fill=\"#fff\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/litmuschaos/litmus-helm/releases/download/litmus-core-3.19.0/litmus-core-3.19.0.tgz"}, "configuration": null, "component": {"version": "litmuschaos.io/v1alpha1", "kind": "ChaosEngine", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"format\": \"textarea\",\n   \"properties\": {\n    \"appinfo\": {\n     \"properties\": {\n      \"appkind\": {\n       \"pattern\": \"^(^$|deployment|statefulset|daemonset|deploymentconfig|rollout)$\",\n       \"type\": \"string\"\n      },\n      \"applabel\": {\n       \"type\": \"string\"\n      },\n      \"appns\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"auxiliaryAppInfo\": {\n     \"type\": \"string\"\n    },\n    \"chaosServiceAccount\": {\n     \"type\": \"string\"\n    },\n    \"components\": {\n     \"properties\": {\n      \"runner\": {\n       \"format\": \"textarea\",\n       \"properties\": {\n        \"image\": {\n         \"type\": \"string\"\n        },\n        \"runnerAnnotations\": {\n         \"type\": \"object\"\n        },\n        \"runnerLabels\": {\n         \"additionalProperties\": {\n          \"properties\": {\n           \"key\": {\n            \"minLength\": 1,\n            \"type\": \"string\"\n           },\n           \"value\": {\n            \"minLength\": 1,\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"string\"\n         },\n         \"type\": \"object\"\n        },\n        \"tolerations\": {\n         \"description\": \"<PERSON><PERSON>'s tolerations.\",\n         \"items\": {\n          \"description\": \"The pod with this Toleration tolerates any taint matches the \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\n          \"properties\": {\n           \"effect\": {\n            \"description\": \"Effect to match. Empty means all effects.\",\n            \"type\": \"string\"\n           },\n           \"key\": {\n            \"description\": \"Taint key the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists.\",\n            \"type\": \"string\"\n           },\n           \"operator\": {\n            \"description\": \"Operators are Exists or Equal. Defaults to Equal.\",\n            \"type\": \"string\"\n           },\n           \"tolerationSeconds\": {\n            \"description\": \"Period of time the toleration tolerates the taint.\",\n            \"format\": \"int64\",\n            \"type\": \"integer\"\n           },\n           \"value\": {\n            \"description\": \"If the operator is Exists, the value should be empty, otherwise just a regular string.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"type\": \"array\"\n        },\n        \"type\": {\n         \"pattern\": \"^(go)$\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"string\"\n      },\n      \"sidecar\": {\n       \"items\": {\n        \"properties\": {\n         \"env\": {\n          \"description\": \"ENV contains ENV passed to the sidecar container\",\n          \"items\": {\n           \"description\": \"EnvVar represents an environment variable present in a Container.\",\n           \"properties\": {\n            \"name\": {\n             \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n             \"type\": \"string\"\n            },\n            \"value\": {\n             \"description\": \"Variable references $(VAR_NAME) are expanded using the previous defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. The $(VAR_NAME) syntax can be escaped with a double $$, ie: $$(VAR_NAME). Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to \\\"\\\".\",\n             \"type\": \"string\"\n            },\n            \"valueFrom\": {\n             \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n             \"properties\": {\n              \"configMapKeyRef\": {\n               \"description\": \"Selects a key of a ConfigMap.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key to select.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\"\n              },\n              \"fieldRef\": {\n               \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\",\n               \"properties\": {\n                \"apiVersion\": {\n                 \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                 \"type\": \"string\"\n                },\n                \"fieldPath\": {\n                 \"description\": \"Path of the field to select in the specified API version.\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"fieldPath\"\n               ],\n               \"type\": \"object\"\n              },\n              \"resourceFieldRef\": {\n               \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n               \"properties\": {\n                \"containerName\": {\n                 \"description\": \"Container name: required for volumes, optional for env vars\",\n                 \"type\": \"string\"\n                },\n                \"divisor\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"resource\": {\n                 \"description\": \"Required: resource to select\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"resource\"\n               ],\n               \"type\": \"object\"\n              },\n              \"secretKeyRef\": {\n               \"description\": \"Selects a key of a secret in the pod's namespace\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"required\": [\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"envFrom\": {\n          \"description\": \"EnvFrom for the sidecar container\",\n          \"items\": {\n           \"description\": \"EnvFromSource represents the source of a set of ConfigMaps\",\n           \"properties\": {\n            \"configMapRef\": {\n             \"description\": \"The ConfigMap to select from\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the ConfigMap must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"prefix\": {\n             \"description\": \"An optional identifier to prepend to each key in the ConfigMap. Must be a C_IDENTIFIER.\",\n             \"type\": \"string\"\n            },\n            \"secretRef\": {\n             \"description\": \"The Secret to select from\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         },\n         \"image\": {\n          \"type\": \"string\"\n         },\n         \"imagePullPolicy\": {\n          \"type\": \"string\"\n         },\n         \"secrets\": {\n          \"items\": {\n           \"properties\": {\n            \"mountPath\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"mountPath\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"defaultHealthCheck\": {\n     \"type\": \"boolean\"\n    },\n    \"engineState\": {\n     \"pattern\": \"^(active|stop)$\",\n     \"type\": \"string\"\n    },\n    \"experiments\": {\n     \"items\": {\n      \"properties\": {\n       \"name\": {\n        \"type\": \"string\"\n       },\n       \"spec\": {\n        \"properties\": {\n         \"components\": {\n          \"format\": \"textarea\",\n          \"properties\": {\n           \"configMaps\": {\n            \"items\": {\n             \"properties\": {\n              \"mountPath\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"env\": {\n            \"items\": {\n             \"description\": \"EnvVar represents an environment variable present in a Container.\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"Variable references $(VAR_NAME) are expanded using the previous defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. The $(VAR_NAME) syntax can be escaped with a double $$, ie: $$(VAR_NAME). Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to \\\"\\\".\",\n               \"type\": \"string\"\n              },\n              \"valueFrom\": {\n               \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n               \"properties\": {\n                \"configMapKeyRef\": {\n                 \"description\": \"Selects a key of a ConfigMap.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key to select.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"fieldRef\": {\n                 \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, metadata.labels, metadata.annotations, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP.\",\n                 \"properties\": {\n                  \"apiVersion\": {\n                   \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                   \"type\": \"string\"\n                  },\n                  \"fieldPath\": {\n                   \"description\": \"Path of the field to select in the specified API version.\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"fieldPath\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"resourceFieldRef\": {\n                 \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n                 \"properties\": {\n                  \"containerName\": {\n                   \"description\": \"Container name: required for volumes, optional for env vars\",\n                   \"type\": \"string\"\n                  },\n                  \"divisor\": {\n                   \"anyOf\": [\n                    {\n                     \"type\": \"integer\"\n                    },\n                    {\n                     \"type\": \"string\"\n                    }\n                   ],\n                   \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                   \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                   \"x-kubernetes-int-or-string\": true\n                  },\n                  \"resource\": {\n                   \"description\": \"Required: resource to select\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"resource\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"secretKeyRef\": {\n                 \"description\": \"Selects a key of a secret in the pod's namespace\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the Secret or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"experimentAnnotations\": {\n            \"additionalProperties\": {\n             \"properties\": {\n              \"key\": {\n               \"allowEmptyValue\": false,\n               \"minLength\": 1,\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"allowEmptyValue\": false,\n               \"minLength\": 1,\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"string\"\n            },\n            \"type\": \"object\"\n           },\n           \"experimentImage\": {\n            \"type\": \"string\"\n           },\n           \"nodeSelector\": {\n            \"additionalProperties\": {\n             \"properties\": {\n              \"key\": {\n               \"allowEmptyValue\": false,\n               \"minLength\": 1,\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"allowEmptyValue\": false,\n               \"minLength\": 1,\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"string\"\n            },\n            \"type\": \"object\"\n           },\n           \"secrets\": {\n            \"items\": {\n             \"properties\": {\n              \"mountPath\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           },\n           \"statusCheckTimeouts\": {\n            \"properties\": {\n             \"delay\": {\n              \"type\": \"integer\"\n             },\n             \"timeout\": {\n              \"type\": \"integer\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"tolerations\": {\n            \"description\": \"Pod's tolerations.\",\n            \"items\": {\n             \"description\": \"The pod with this Toleration tolerates any taint matches the \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\n             \"properties\": {\n              \"effect\": {\n               \"description\": \"Effect to match. Empty means all effects.\",\n               \"type\": \"string\"\n              },\n              \"key\": {\n               \"description\": \"Taint key the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists.\",\n               \"type\": \"string\"\n              },\n              \"operator\": {\n               \"description\": \"Operators are Exists or Equal. Defaults to Equal.\",\n               \"type\": \"string\"\n              },\n              \"tolerationSeconds\": {\n               \"description\": \"Period of time the toleration tolerates the taint.\",\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              },\n              \"value\": {\n               \"description\": \"If the operator is Exists, the value should be empty, otherwise just a regular string.\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"type\": \"array\"\n           }\n          },\n          \"type\": \"string\"\n         },\n         \"probe\": {\n          \"items\": {\n           \"properties\": {\n            \"cmdProbe/inputs\": {\n             \"properties\": {\n              \"command\": {\n               \"minLength\": 1,\n               \"type\": \"string\"\n              },\n              \"comparator\": {\n               \"properties\": {\n                \"criteria\": {\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"minLength\": 1,\n                 \"pattern\": \"^(int|float|string)$\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"type\",\n                \"criteria\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"source\": {\n               \"description\": \"The external pod where we have to run the probe commands. It will run the commands inside the experiment pod itself(inline mode) if source contains a nil value\",\n               \"properties\": {\n                \"annotations\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"Annotations for the source pod\",\n                 \"type\": \"object\"\n                },\n                \"args\": {\n                 \"description\": \"Args for the source pod\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"command\": {\n                 \"description\": \"Command for the source pod\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"env\": {\n                 \"description\": \"ENVList contains ENV passed to the source pod\",\n                 \"items\": {\n                  \"description\": \"EnvVar represents an environment variable present in a Container.\",\n                  \"properties\": {\n                   \"name\": {\n                    \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n                    \"type\": \"string\"\n                   },\n                   \"value\": {\n                    \"description\": \"Variable references $(VAR_NAME) are expanded using the previous defined environment variables in the container and any service environment variables. If a variable cannot be resolved, the reference in the input string will be unchanged. The $(VAR_NAME) syntax can be escaped with a double $$, ie: $$(VAR_NAME). Escaped references will never be expanded, regardless of whether the variable exists or not. Defaults to \\\"\\\".\",\n                    \"type\": \"string\"\n                   },\n                   \"valueFrom\": {\n                    \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n                    \"properties\": {\n                     \"configMapKeyRef\": {\n                      \"description\": \"Selects a key of a ConfigMap.\",\n                      \"properties\": {\n                       \"key\": {\n                        \"description\": \"The key to select.\",\n                        \"type\": \"string\"\n                       },\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       },\n                       \"optional\": {\n                        \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                        \"type\": \"boolean\"\n                       }\n                      },\n                      \"required\": [\n                       \"key\"\n                      ],\n                      \"type\": \"object\"\n                     },\n                     \"fieldRef\": {\n                      \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, metadata.labels, metadata.annotations, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP.\",\n                      \"properties\": {\n                       \"apiVersion\": {\n                        \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                        \"type\": \"string\"\n                       },\n                       \"fieldPath\": {\n                        \"description\": \"Path of the field to select in the specified API version.\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"required\": [\n                       \"fieldPath\"\n                      ],\n                      \"type\": \"object\"\n                     },\n                     \"resourceFieldRef\": {\n                      \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n                      \"properties\": {\n                       \"containerName\": {\n                        \"description\": \"Container name: required for volumes, optional for env vars\",\n                        \"type\": \"string\"\n                       },\n                       \"divisor\": {\n                        \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                        \"type\": \"string\"\n                       },\n                       \"resource\": {\n                        \"description\": \"Required: resource to select\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"required\": [\n                       \"resource\"\n                      ],\n                      \"type\": \"object\"\n                     },\n                     \"secretKeyRef\": {\n                      \"description\": \"Selects a key of a secret in the pod's namespace\",\n                      \"properties\": {\n                       \"key\": {\n                        \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                        \"type\": \"string\"\n                       },\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       },\n                       \"optional\": {\n                        \"description\": \"Specify whether the Secret or its key must be defined\",\n                        \"type\": \"boolean\"\n                       }\n                      },\n                      \"required\": [\n                       \"key\"\n                      ],\n                      \"type\": \"object\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"required\": [\n                   \"name\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"hostNetwork\": {\n                 \"description\": \"HostNetwork define the hostNetwork of the external pod it supports boolean values and default value is false\",\n                 \"type\": \"boolean\"\n                },\n                \"image\": {\n                 \"description\": \"Image for the source pod\",\n                 \"type\": \"string\"\n                },\n                \"imagePullPolicy\": {\n                 \"description\": \"ImagePullPolicy for the source pod\",\n                 \"type\": \"string\"\n                },\n                \"imagePullSecrets\": {\n                 \"description\": \"ImagePullSecrets for source pod\",\n                 \"items\": {\n                  \"description\": \"LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.\",\n                  \"properties\": {\n                   \"name\": {\n                    \"description\": \"Name of the referent\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"inheritInputs\": {\n                 \"description\": \"InheritInputs define to inherit experiment details in probe pod it supports boolean values and default value is false.\",\n                 \"type\": \"boolean\"\n                },\n                \"labels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"Labels for the source pod\",\n                 \"type\": \"object\"\n                },\n                \"nodeSelector\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"NodeSelector for the source pod\",\n                 \"type\": \"object\"\n                },\n                \"privileged\": {\n                 \"description\": \"Privileged for the source pod\",\n                 \"type\": \"boolean\"\n                },\n                \"tolerations\": {\n                 \"description\": \"Tolerations for the source pod\",\n                 \"items\": {\n                  \"description\": \"The pod with this Toleration tolerates any taint matches the \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\n                  \"properties\": {\n                   \"effect\": {\n                    \"description\": \"Effect to match. Empty means all effects.\",\n                    \"type\": \"string\"\n                   },\n                   \"key\": {\n                    \"description\": \"Taint key the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"Operators are Exists or Equal. Defaults to Equal.\",\n                    \"type\": \"string\"\n                   },\n                   \"tolerationSeconds\": {\n                    \"description\": \"Period of time the toleration tolerates the taint.\",\n                    \"format\": \"int64\",\n                    \"type\": \"integer\"\n                   },\n                   \"value\": {\n                    \"description\": \"If the operator is Exists, the value should be empty, otherwise just a regular string.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"volumeMount\": {\n                 \"description\": \"VolumesMount for the source pod\",\n                 \"items\": {\n                  \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n                  \"properties\": {\n                   \"mountPath\": {\n                    \"description\": \"Path within the container at which the volume should be mounted.  Must not contain ':'.\",\n                    \"type\": \"string\"\n                   },\n                   \"mountPropagation\": {\n                    \"description\": \"mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10.\",\n                    \"type\": \"string\"\n                   },\n                   \"name\": {\n                    \"description\": \"This must match the Name of a Volume.\",\n                    \"type\": \"string\"\n                   },\n                   \"readOnly\": {\n                    \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.\",\n                    \"type\": \"boolean\"\n                   },\n                   \"subPath\": {\n                    \"description\": \"Path within the volume from which the container's volume should be mounted. Defaults to \\\"\\\" (volume's root).\",\n                    \"type\": \"string\"\n                   },\n                   \"subPathExpr\": {\n                    \"description\": \"Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \\\"\\\" (volume's root). SubPathExpr and SubPath are mutually exclusive. This field is beta in 1.15.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"mountPath\",\n                   \"name\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                },\n                \"volumes\": {\n                 \"description\": \"Volumes for the source pod\",\n                 \"items\": {\n                  \"description\": \"Volume represents a named volume in a pod that may be accessed by any container in the pod.\",\n                  \"properties\": {\n                   \"awsElasticBlockStore\": {\n                    \"description\": \"AWSElasticBlockStore represents an AWS Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore TODO: how do we prevent errors in the filesystem from compromising the machine\",\n                      \"type\": \"string\"\n                     },\n                     \"partition\": {\n                      \"description\": \"The partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \\\"1\\\". Similarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty).\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Specify \\\"true\\\" to force and set the ReadOnly property in VolumeMounts to \\\"true\\\". If omitted, the default is \\\"false\\\". More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n                      \"type\": \"boolean\"\n                     },\n                     \"volumeID\": {\n                      \"description\": \"Unique ID of the persistent disk resource in AWS (Amazon EBS volume). More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"volumeID\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"azureDisk\": {\n                    \"description\": \"AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.\",\n                    \"properties\": {\n                     \"cachingMode\": {\n                      \"description\": \"Host Caching mode: None, Read Only, Read Write.\",\n                      \"type\": \"string\"\n                     },\n                     \"diskName\": {\n                      \"description\": \"The Name of the data disk in the blob storage\",\n                      \"type\": \"string\"\n                     },\n                     \"diskURI\": {\n                      \"description\": \"The URI the data disk in the blob storage\",\n                      \"type\": \"string\"\n                     },\n                     \"fsType\": {\n                      \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n                      \"type\": \"string\"\n                     },\n                     \"kind\": {\n                      \"description\": \"Expected values Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n                      \"type\": \"boolean\"\n                     }\n                    },\n                    \"required\": [\n                     \"diskName\",\n                     \"diskURI\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"azureFile\": {\n                    \"description\": \"AzureFile represents an Azure File Service mount on the host and bind mount to the pod.\",\n                    \"properties\": {\n                     \"readOnly\": {\n                      \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"secretName\": {\n                      \"description\": \"the name of secret that contains Azure Storage Account Name and Key\",\n                      \"type\": \"string\"\n                     },\n                     \"shareName\": {\n                      \"description\": \"Share Name\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"secretName\",\n                     \"shareName\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"cephfs\": {\n                    \"description\": \"CephFS represents a Ceph FS mount on the host that shares a pod's lifetime\",\n                    \"properties\": {\n                     \"monitors\": {\n                      \"description\": \"Required: Monitors is a collection of Ceph monitors More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\"\n                     },\n                     \"path\": {\n                      \"description\": \"Optional: Used as the mounted root, rather than the full Ceph tree, default is /\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n                      \"type\": \"boolean\"\n                     },\n                     \"secretFile\": {\n                      \"description\": \"Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n                      \"type\": \"string\"\n                     },\n                     \"secretRef\": {\n                      \"description\": \"Optional: SecretRef is reference to the authentication secret for User, default is empty. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n                      \"properties\": {\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"user\": {\n                      \"description\": \"Optional: User is the rados user name, default is admin More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"monitors\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"cinder\": {\n                    \"description\": \"Cinder represents a cinder volume attached and mounted on kubelets host machine. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n                      \"type\": \"boolean\"\n                     },\n                     \"secretRef\": {\n                      \"description\": \"Optional: points to a secret object containing parameters used to connect to OpenStack.\",\n                      \"properties\": {\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"volumeID\": {\n                      \"description\": \"volume id used to identify the volume in cinder. More info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"volumeID\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"configMap\": {\n                    \"description\": \"ConfigMap represents a configMap that should populate this volume\",\n                    \"properties\": {\n                     \"defaultMode\": {\n                      \"description\": \"Optional: mode bits to use on created files by default. Must be a value between 0 and 0777. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"items\": {\n                      \"description\": \"If unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n                      \"items\": {\n                       \"description\": \"Maps a string key to a path within a volume.\",\n                       \"properties\": {\n                        \"key\": {\n                         \"description\": \"The key to project.\",\n                         \"type\": \"string\"\n                        },\n                        \"mode\": {\n                         \"description\": \"Optional: mode bits to use on this file, must be a value between 0 and 0777. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                         \"format\": \"int32\",\n                         \"type\": \"integer\"\n                        },\n                        \"path\": {\n                         \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n                         \"type\": \"string\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"path\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\"\n                     },\n                     \"name\": {\n                      \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                      \"type\": \"string\"\n                     },\n                     \"optional\": {\n                      \"description\": \"Specify whether the ConfigMap or its keys must be defined\",\n                      \"type\": \"boolean\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"csi\": {\n                    \"description\": \"CSI (Container Storage Interface) represents storage that is handled by an external CSI driver (Alpha feature).\",\n                    \"properties\": {\n                     \"driver\": {\n                      \"description\": \"Driver is the name of the CSI driver that handles this volume. Consult with your admin for the correct name as registered in the cluster.\",\n                      \"type\": \"string\"\n                     },\n                     \"fsType\": {\n                      \"description\": \"Filesystem type to mount. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". If not provided, the empty value is passed to the associated CSI driver which will determine the default filesystem to apply.\",\n                      \"type\": \"string\"\n                     },\n                     \"nodePublishSecretRef\": {\n                      \"description\": \"NodePublishSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI NodePublishVolume and NodeUnpublishVolume calls. This field is optional, and  may be empty if no secret is required. If the secret object contains more than one secret, all secret references are passed.\",\n                      \"properties\": {\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Specifies a read-only configuration for the volume. Defaults to false (read/write).\",\n                      \"type\": \"boolean\"\n                     },\n                     \"volumeAttributes\": {\n                      \"additionalProperties\": {\n                       \"type\": \"string\"\n                      },\n                      \"description\": \"VolumeAttributes stores driver-specific properties that are passed to the CSI driver. Consult your driver's documentation for supported values.\",\n                      \"type\": \"object\"\n                     }\n                    },\n                    \"required\": [\n                     \"driver\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"downwardAPI\": {\n                    \"description\": \"DownwardAPI represents downward API about the pod that should populate this volume\",\n                    \"properties\": {\n                     \"defaultMode\": {\n                      \"description\": \"Optional: mode bits to use on created files by default. Must be a value between 0 and 0777. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"items\": {\n                      \"description\": \"Items is a list of downward API volume file\",\n                      \"items\": {\n                       \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n                       \"properties\": {\n                        \"fieldRef\": {\n                         \"description\": \"Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.\",\n                         \"properties\": {\n                          \"apiVersion\": {\n                           \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                           \"type\": \"string\"\n                          },\n                          \"fieldPath\": {\n                           \"description\": \"Path of the field to select in the specified API version.\",\n                           \"type\": \"string\"\n                          }\n                         },\n                         \"required\": [\n                          \"fieldPath\"\n                         ],\n                         \"type\": \"object\"\n                        },\n                        \"mode\": {\n                         \"description\": \"Optional: mode bits to use on this file, must be a value between 0 and 0777. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                         \"format\": \"int32\",\n                         \"type\": \"integer\"\n                        },\n                        \"path\": {\n                         \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n                         \"type\": \"string\"\n                        },\n                        \"resourceFieldRef\": {\n                         \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n                         \"properties\": {\n                          \"containerName\": {\n                           \"description\": \"Container name: required for volumes, optional for env vars\",\n                           \"type\": \"string\"\n                          },\n                          \"divisor\": {\n                           \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                           \"type\": \"string\"\n                          },\n                          \"resource\": {\n                           \"description\": \"Required: resource to select\",\n                           \"type\": \"string\"\n                          }\n                         },\n                         \"required\": [\n                          \"resource\"\n                         ],\n                         \"type\": \"object\"\n                        }\n                       },\n                       \"required\": [\n                        \"path\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"emptyDir\": {\n                    \"description\": \"EmptyDir represents a temporary directory that shares a pod's lifetime. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n                    \"properties\": {\n                     \"medium\": {\n                      \"description\": \"What type of storage medium should back this directory. The default is \\\"\\\" which means to use the node's default medium. Must be an empty string (default) or Memory. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n                      \"type\": \"string\"\n                     },\n                     \"sizeLimit\": {\n                      \"description\": \"Total amount of local storage required for this EmptyDir volume. The size limit is also applicable for memory medium. The maximum usage on memory medium EmptyDir would be the minimum value between the SizeLimit specified here and the sum of memory limits of all containers in a pod. The default is nil which means that the limit is undefined. More info: http://kubernetes.io/docs/user-guide/volumes#emptydir\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"fc\": {\n                    \"description\": \"FC represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. TODO: how do we prevent errors in the filesystem from compromising the machine\",\n                      \"type\": \"string\"\n                     },\n                     \"lun\": {\n                      \"description\": \"Optional: FC target lun number\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"targetWWNs\": {\n                      \"description\": \"Optional: FC target worldwide names (WWNs)\",\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\"\n                     },\n                     \"wwids\": {\n                      \"description\": \"Optional: FC volume world wide identifiers (wwids) Either wwids or combination of targetWWNs and lun must be set, but not both simultaneously.\",\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"flexVolume\": {\n                    \"description\": \"FlexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin.\",\n                    \"properties\": {\n                     \"driver\": {\n                      \"description\": \"Driver is the name of the driver to use for this volume.\",\n                      \"type\": \"string\"\n                     },\n                     \"fsType\": {\n                      \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". The default filesystem depends on FlexVolume script.\",\n                      \"type\": \"string\"\n                     },\n                     \"options\": {\n                      \"additionalProperties\": {\n                       \"type\": \"string\"\n                      },\n                      \"description\": \"Optional: Extra command options if any.\",\n                      \"type\": \"object\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"secretRef\": {\n                      \"description\": \"Optional: SecretRef is reference to the secret object containing sensitive information to pass to the plugin scripts. This may be empty if no secret object is specified. If the secret object contains more than one secret, all secrets are passed to the plugin scripts.\",\n                      \"properties\": {\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     }\n                    },\n                    \"required\": [\n                     \"driver\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"flocker\": {\n                    \"description\": \"Flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running\",\n                    \"properties\": {\n                     \"datasetName\": {\n                      \"description\": \"Name of the dataset stored as metadata -\\u003e name on the dataset for Flocker should be considered as deprecated\",\n                      \"type\": \"string\"\n                     },\n                     \"datasetUUID\": {\n                      \"description\": \"UUID of the dataset. This is unique identifier of a Flocker dataset\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"gcePersistentDisk\": {\n                    \"description\": \"GCEPersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk TODO: how do we prevent errors in the filesystem from compromising the machine\",\n                      \"type\": \"string\"\n                     },\n                     \"partition\": {\n                      \"description\": \"The partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \\\"1\\\". Similarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty). More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"pdName\": {\n                      \"description\": \"Unique name of the PD resource in GCE. Used to identify the disk in GCE. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n                      \"type\": \"boolean\"\n                     }\n                    },\n                    \"required\": [\n                     \"pdName\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"gitRepo\": {\n                    \"description\": \"GitRepo represents a git repository at a particular revision. DEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir into the Pod's container.\",\n                    \"properties\": {\n                     \"directory\": {\n                      \"description\": \"Target directory name. Must not contain or start with '..'.  If '.' is supplied, the volume directory will be the git repository.  Otherwise, if specified, the volume will contain the git repository in the subdirectory with the given name.\",\n                      \"type\": \"string\"\n                     },\n                     \"repository\": {\n                      \"description\": \"Repository URL\",\n                      \"type\": \"string\"\n                     },\n                     \"revision\": {\n                      \"description\": \"Commit hash for the specified revision.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"repository\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"glusterfs\": {\n                    \"description\": \"Glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/glusterfs/README.md\",\n                    \"properties\": {\n                     \"endpoints\": {\n                      \"description\": \"EndpointsName is the endpoint name that details Glusterfs topology. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n                      \"type\": \"string\"\n                     },\n                     \"path\": {\n                      \"description\": \"Path is the Glusterfs volume path. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"ReadOnly here will force the Glusterfs volume to be mounted with read-only permissions. Defaults to false. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n                      \"type\": \"boolean\"\n                     }\n                    },\n                    \"required\": [\n                     \"endpoints\",\n                     \"path\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"hostPath\": {\n                    \"description\": \"HostPath represents a pre-existing file or directory on the host machine that is directly exposed to the container. This is generally used for system agents or other privileged things that are allowed to see the host machine. Most containers will NOT need this. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath --- TODO(jonesdl) We need to restrict who can use host directory mounts and who can/can not mount host directories as read/write.\",\n                    \"properties\": {\n                     \"path\": {\n                      \"description\": \"Path of the directory on the host. If the path is a symlink, it will follow the link to the real path. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n                      \"type\": \"string\"\n                     },\n                     \"type\": {\n                      \"description\": \"Type for HostPath Volume Defaults to \\\"\\\" More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"path\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"iscsi\": {\n                    \"description\": \"ISCSI represents an ISCSI Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://examples.k8s.io/volumes/iscsi/README.md\",\n                    \"properties\": {\n                     \"chapAuthDiscovery\": {\n                      \"description\": \"whether support iSCSI Discovery CHAP authentication\",\n                      \"type\": \"boolean\"\n                     },\n                     \"chapAuthSession\": {\n                      \"description\": \"whether support iSCSI Session CHAP authentication\",\n                      \"type\": \"boolean\"\n                     },\n                     \"fsType\": {\n                      \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi TODO: how do we prevent errors in the filesystem from compromising the machine\",\n                      \"type\": \"string\"\n                     },\n                     \"initiatorName\": {\n                      \"description\": \"Custom iSCSI Initiator Name. If initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface \\u003ctarget portal\\u003e:\\u003cvolume name\\u003e will be created for the connection.\",\n                      \"type\": \"string\"\n                     },\n                     \"iqn\": {\n                      \"description\": \"Target iSCSI Qualified Name.\",\n                      \"type\": \"string\"\n                     },\n                     \"iscsiInterface\": {\n                      \"description\": \"iSCSI Interface Name that uses an iSCSI transport. Defaults to 'default' (tcp).\",\n                      \"type\": \"string\"\n                     },\n                     \"lun\": {\n                      \"description\": \"iSCSI Target Lun number.\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"portals\": {\n                      \"description\": \"iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).\",\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"secretRef\": {\n                      \"description\": \"CHAP Secret for iSCSI target and initiator authentication\",\n                      \"properties\": {\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"targetPortal\": {\n                      \"description\": \"iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"iqn\",\n                     \"lun\",\n                     \"targetPortal\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"name\": {\n                    \"description\": \"Volume's name. Must be a DNS_LABEL and unique within the pod. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                    \"type\": \"string\"\n                   },\n                   \"nfs\": {\n                    \"description\": \"NFS represents an NFS mount on the host that shares a pod's lifetime More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n                    \"properties\": {\n                     \"path\": {\n                      \"description\": \"Path that is exported by the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"ReadOnly here will force the NFS export to be mounted with read-only permissions. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n                      \"type\": \"boolean\"\n                     },\n                     \"server\": {\n                      \"description\": \"Server is the hostname or IP address of the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"path\",\n                     \"server\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"persistentVolumeClaim\": {\n                    \"description\": \"PersistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n                    \"properties\": {\n                     \"claimName\": {\n                      \"description\": \"ClaimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Will force the ReadOnly setting in VolumeMounts. Default false.\",\n                      \"type\": \"boolean\"\n                     }\n                    },\n                    \"required\": [\n                     \"claimName\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"photonPersistentDisk\": {\n                    \"description\": \"PhotonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n                      \"type\": \"string\"\n                     },\n                     \"pdID\": {\n                      \"description\": \"ID that identifies Photon Controller persistent disk\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"pdID\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"portworxVolume\": {\n                    \"description\": \"PortworxVolume represents a portworx volume attached and mounted on kubelets host machine\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"FSType represents the filesystem type to mount Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"volumeID\": {\n                      \"description\": \"VolumeID uniquely identifies a Portworx volume\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"volumeID\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"projected\": {\n                    \"description\": \"Items for all in one resources secrets, configmaps, and downward API\",\n                    \"properties\": {\n                     \"defaultMode\": {\n                      \"description\": \"Mode bits to use on created files by default. Must be a value between 0 and 0777. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"sources\": {\n                      \"description\": \"list of volume projections\",\n                      \"items\": {\n                       \"description\": \"Projection that may be projected along with other supported volume types\",\n                       \"properties\": {\n                        \"configMap\": {\n                         \"description\": \"information about the configMap data to project\",\n                         \"properties\": {\n                          \"items\": {\n                           \"description\": \"If unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n                           \"items\": {\n                            \"description\": \"Maps a string key to a path within a volume.\",\n                            \"properties\": {\n                             \"key\": {\n                              \"description\": \"The key to project.\",\n                              \"type\": \"string\"\n                             },\n                             \"mode\": {\n                              \"description\": \"Optional: mode bits to use on this file, must be a value between 0 and 0777. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                              \"format\": \"int32\",\n                              \"type\": \"integer\"\n                             },\n                             \"path\": {\n                              \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n                              \"type\": \"string\"\n                             }\n                            },\n                            \"required\": [\n                             \"key\",\n                             \"path\"\n                            ],\n                            \"type\": \"object\"\n                           },\n                           \"type\": \"array\"\n                          },\n                          \"name\": {\n                           \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                           \"type\": \"string\"\n                          },\n                          \"optional\": {\n                           \"description\": \"Specify whether the ConfigMap or its keys must be defined\",\n                           \"type\": \"boolean\"\n                          }\n                         },\n                         \"type\": \"object\"\n                        },\n                        \"downwardAPI\": {\n                         \"description\": \"information about the downwardAPI data to project\",\n                         \"properties\": {\n                          \"items\": {\n                           \"description\": \"Items is a list of DownwardAPIVolume file\",\n                           \"items\": {\n                            \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n                            \"properties\": {\n                             \"fieldRef\": {\n                              \"description\": \"Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.\",\n                              \"properties\": {\n                               \"apiVersion\": {\n                                \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                                \"type\": \"string\"\n                               },\n                               \"fieldPath\": {\n                                \"description\": \"Path of the field to select in the specified API version.\",\n                                \"type\": \"string\"\n                               }\n                              },\n                              \"required\": [\n                               \"fieldPath\"\n                              ],\n                              \"type\": \"object\"\n                             },\n                             \"mode\": {\n                              \"description\": \"Optional: mode bits to use on this file, must be a value between 0 and 0777. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                              \"format\": \"int32\",\n                              \"type\": \"integer\"\n                             },\n                             \"path\": {\n                              \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n                              \"type\": \"string\"\n                             },\n                             \"resourceFieldRef\": {\n                              \"description\": \"Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n                              \"properties\": {\n                               \"containerName\": {\n                                \"description\": \"Container name: required for volumes, optional for env vars\",\n                                \"type\": \"string\"\n                               },\n                               \"divisor\": {\n                                \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                                \"type\": \"string\"\n                               },\n                               \"resource\": {\n                                \"description\": \"Required: resource to select\",\n                                \"type\": \"string\"\n                               }\n                              },\n                              \"required\": [\n                               \"resource\"\n                              ],\n                              \"type\": \"object\"\n                             }\n                            },\n                            \"required\": [\n                             \"path\"\n                            ],\n                            \"type\": \"object\"\n                           },\n                           \"type\": \"array\"\n                          }\n                         },\n                         \"type\": \"object\"\n                        },\n                        \"secret\": {\n                         \"description\": \"information about the secret data to project\",\n                         \"properties\": {\n                          \"items\": {\n                           \"description\": \"If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n                           \"items\": {\n                            \"description\": \"Maps a string key to a path within a volume.\",\n                            \"properties\": {\n                             \"key\": {\n                              \"description\": \"The key to project.\",\n                              \"type\": \"string\"\n                             },\n                             \"mode\": {\n                              \"description\": \"Optional: mode bits to use on this file, must be a value between 0 and 0777. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                              \"format\": \"int32\",\n                              \"type\": \"integer\"\n                             },\n                             \"path\": {\n                              \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n                              \"type\": \"string\"\n                             }\n                            },\n                            \"required\": [\n                             \"key\",\n                             \"path\"\n                            ],\n                            \"type\": \"object\"\n                           },\n                           \"type\": \"array\"\n                          },\n                          \"name\": {\n                           \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                           \"type\": \"string\"\n                          },\n                          \"optional\": {\n                           \"description\": \"Specify whether the Secret or its key must be defined\",\n                           \"type\": \"boolean\"\n                          }\n                         },\n                         \"type\": \"object\"\n                        },\n                        \"serviceAccountToken\": {\n                         \"description\": \"information about the serviceAccountToken data to project\",\n                         \"properties\": {\n                          \"audience\": {\n                           \"description\": \"Audience is the intended audience of the token. A recipient of a token must identify itself with an identifier specified in the audience of the token, and otherwise should reject the token. The audience defaults to the identifier of the apiserver.\",\n                           \"type\": \"string\"\n                          },\n                          \"expirationSeconds\": {\n                           \"description\": \"ExpirationSeconds is the requested duration of validity of the service account token. As the token approaches expiration, the kubelet volume plugin will proactively rotate the service account token. The kubelet will start trying to rotate the token if the token is older than 80 percent of its time to live or if the token is older than 24 hours.Defaults to 1 hour and must be at least 10 minutes.\",\n                           \"format\": \"int64\",\n                           \"type\": \"integer\"\n                          },\n                          \"path\": {\n                           \"description\": \"Path is the path relative to the mount point of the file to project the token into.\",\n                           \"type\": \"string\"\n                          }\n                         },\n                         \"required\": [\n                          \"path\"\n                         ],\n                         \"type\": \"object\"\n                        }\n                       },\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\"\n                     }\n                    },\n                    \"required\": [\n                     \"sources\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"quobyte\": {\n                    \"description\": \"Quobyte represents a Quobyte mount on the host that shares a pod's lifetime\",\n                    \"properties\": {\n                     \"group\": {\n                      \"description\": \"Group to map volume access to Default is no group\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"ReadOnly here will force the Quobyte volume to be mounted with read-only permissions. Defaults to false.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"registry\": {\n                      \"description\": \"Registry represents a single or multiple Quobyte Registry services specified as a string as host:port pair (multiple entries are separated with commas) which acts as the central registry for volumes\",\n                      \"type\": \"string\"\n                     },\n                     \"tenant\": {\n                      \"description\": \"Tenant owning the given Quobyte volume in the Backend Used with dynamically provisioned Quobyte volumes, value is set by the plugin\",\n                      \"type\": \"string\"\n                     },\n                     \"user\": {\n                      \"description\": \"User to map volume access to Defaults to serivceaccount user\",\n                      \"type\": \"string\"\n                     },\n                     \"volume\": {\n                      \"description\": \"Volume is a string that references an already created Quobyte volume by name.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"registry\",\n                     \"volume\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"rbd\": {\n                    \"description\": \"RBD represents a Rados Block Device mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/rbd/README.md\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"Filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#rbd TODO: how do we prevent errors in the filesystem from compromising the machine\",\n                      \"type\": \"string\"\n                     },\n                     \"image\": {\n                      \"description\": \"The rados image name. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n                      \"type\": \"string\"\n                     },\n                     \"keyring\": {\n                      \"description\": \"Keyring is the path to key ring for RBDUser. Default is /etc/ceph/keyring. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n                      \"type\": \"string\"\n                     },\n                     \"monitors\": {\n                      \"description\": \"A collection of Ceph monitors. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n                      \"items\": {\n                       \"type\": \"string\"\n                      },\n                      \"type\": \"array\"\n                     },\n                     \"pool\": {\n                      \"description\": \"The rados pool name. Default is rbd. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"ReadOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n                      \"type\": \"boolean\"\n                     },\n                     \"secretRef\": {\n                      \"description\": \"SecretRef is name of the authentication secret for RBDUser. If provided overrides keyring. Default is nil. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n                      \"properties\": {\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"user\": {\n                      \"description\": \"The rados user name. Default is admin. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"image\",\n                     \"monitors\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"scaleIO\": {\n                    \"description\": \"ScaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Default is \\\"xfs\\\".\",\n                      \"type\": \"string\"\n                     },\n                     \"gateway\": {\n                      \"description\": \"The host address of the ScaleIO API Gateway.\",\n                      \"type\": \"string\"\n                     },\n                     \"protectionDomain\": {\n                      \"description\": \"The name of the ScaleIO Protection Domain for the configured storage.\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"secretRef\": {\n                      \"description\": \"SecretRef references to the secret for ScaleIO user and other sensitive information. If this is not provided, Login operation will fail.\",\n                      \"properties\": {\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"sslEnabled\": {\n                      \"description\": \"Flag to enable/disable SSL communication with Gateway, default false\",\n                      \"type\": \"boolean\"\n                     },\n                     \"storageMode\": {\n                      \"description\": \"Indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned. Default is ThinProvisioned.\",\n                      \"type\": \"string\"\n                     },\n                     \"storagePool\": {\n                      \"description\": \"The ScaleIO Storage Pool associated with the protection domain.\",\n                      \"type\": \"string\"\n                     },\n                     \"system\": {\n                      \"description\": \"The name of the storage system as configured in ScaleIO.\",\n                      \"type\": \"string\"\n                     },\n                     \"volumeName\": {\n                      \"description\": \"The name of a volume already created in the ScaleIO system that is associated with this volume source.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"gateway\",\n                     \"secretRef\",\n                     \"system\"\n                    ],\n                    \"type\": \"object\"\n                   },\n                   \"secret\": {\n                    \"description\": \"Secret represents a secret that should populate this volume. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n                    \"properties\": {\n                     \"defaultMode\": {\n                      \"description\": \"Optional: mode bits to use on created files by default. Must be a value between 0 and 0777. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                      \"format\": \"int32\",\n                      \"type\": \"integer\"\n                     },\n                     \"items\": {\n                      \"description\": \"If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.\",\n                      \"items\": {\n                       \"description\": \"Maps a string key to a path within a volume.\",\n                       \"properties\": {\n                        \"key\": {\n                         \"description\": \"The key to project.\",\n                         \"type\": \"string\"\n                        },\n                        \"mode\": {\n                         \"description\": \"Optional: mode bits to use on this file, must be a value between 0 and 0777. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.\",\n                         \"format\": \"int32\",\n                         \"type\": \"integer\"\n                        },\n                        \"path\": {\n                         \"description\": \"The relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.\",\n                         \"type\": \"string\"\n                        }\n                       },\n                       \"required\": [\n                        \"key\",\n                        \"path\"\n                       ],\n                       \"type\": \"object\"\n                      },\n                      \"type\": \"array\"\n                     },\n                     \"optional\": {\n                      \"description\": \"Specify whether the Secret or its keys must be defined\",\n                      \"type\": \"boolean\"\n                     },\n                     \"secretName\": {\n                      \"description\": \"Name of the secret in the pod's namespace to use. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"storageos\": {\n                    \"description\": \"StorageOS represents a StorageOS volume attached and mounted on Kubernetes nodes.\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n                      \"type\": \"string\"\n                     },\n                     \"readOnly\": {\n                      \"description\": \"Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.\",\n                      \"type\": \"boolean\"\n                     },\n                     \"secretRef\": {\n                      \"description\": \"SecretRef specifies the secret to use for obtaining the StorageOS API credentials.  If not specified, default values will be attempted.\",\n                      \"properties\": {\n                       \"name\": {\n                        \"description\": \"Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?\",\n                        \"type\": \"string\"\n                       }\n                      },\n                      \"type\": \"object\"\n                     },\n                     \"volumeName\": {\n                      \"description\": \"VolumeName is the human-readable name of the StorageOS volume.  Volume names are only unique within a namespace.\",\n                      \"type\": \"string\"\n                     },\n                     \"volumeNamespace\": {\n                      \"description\": \"VolumeNamespace specifies the scope of the volume within StorageOS.  If no namespace is specified then the Pod's namespace will be used.  This allows the Kubernetes name scoping to be mirrored within StorageOS for tighter integration. Set VolumeName to any name to override the default behaviour. Set to \\\"default\\\" if you are not using namespaces within StorageOS. Namespaces that do not pre-exist within StorageOS will be created.\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"type\": \"object\"\n                   },\n                   \"vsphereVolume\": {\n                    \"description\": \"VsphereVolume represents a vSphere volume attached and mounted on kubelets host machine\",\n                    \"properties\": {\n                     \"fsType\": {\n                      \"description\": \"Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n                      \"type\": \"string\"\n                     },\n                     \"storagePolicyID\": {\n                      \"description\": \"Storage Policy Based Management (SPBM) profile ID associated with the StoragePolicyName.\",\n                      \"type\": \"string\"\n                     },\n                     \"storagePolicyName\": {\n                      \"description\": \"Storage Policy Based Management (SPBM) profile name.\",\n                      \"type\": \"string\"\n                     },\n                     \"volumePath\": {\n                      \"description\": \"Path that identifies vSphere volume vmdk\",\n                      \"type\": \"string\"\n                     }\n                    },\n                    \"required\": [\n                     \"volumePath\"\n                    ],\n                    \"type\": \"object\"\n                   }\n                  },\n                  \"required\": [\n                   \"name\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\"\n                }\n               },\n               \"required\": [\n                \"image\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"command\",\n              \"comparator\"\n             ],\n             \"type\": \"object\"\n            },\n            \"data\": {\n             \"type\": \"string\"\n            },\n            \"httpProbe/inputs\": {\n             \"properties\": {\n              \"insecureSkipVerify\": {\n               \"type\": \"boolean\"\n              },\n              \"method\": {\n               \"minProperties\": 1,\n               \"properties\": {\n                \"get\": {\n                 \"properties\": {\n                  \"criteria\": {\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  },\n                  \"responseCode\": {\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"criteria\",\n                  \"responseCode\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"post\": {\n                 \"properties\": {\n                  \"body\": {\n                   \"type\": \"string\"\n                  },\n                  \"bodyPath\": {\n                   \"type\": \"string\"\n                  },\n                  \"contentType\": {\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  },\n                  \"criteria\": {\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  },\n                  \"responseCode\": {\n                   \"minLength\": 1,\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"criteria\",\n                  \"responseCode\"\n                 ],\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"url\": {\n               \"minLength\": 1,\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"url\",\n              \"method\"\n             ],\n             \"type\": \"object\"\n            },\n            \"k8sProbe/inputs\": {\n             \"properties\": {\n              \"fieldSelector\": {\n               \"type\": \"string\"\n              },\n              \"group\": {\n               \"type\": \"string\"\n              },\n              \"labelSelector\": {\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"type\": \"string\"\n              },\n              \"operation\": {\n               \"minLength\": 1,\n               \"pattern\": \"^(present|absent|create|delete)$\",\n               \"type\": \"string\"\n              },\n              \"resource\": {\n               \"type\": \"string\"\n              },\n              \"resourceNames\": {\n               \"type\": \"string\"\n              },\n              \"version\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"version\",\n              \"resource\",\n              \"operation\"\n             ],\n             \"type\": \"object\"\n            },\n            \"mode\": {\n             \"minLength\": 1,\n             \"pattern\": \"^(SOT|EOT|Edge|Continuous|OnChaos)$\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"type\": \"string\"\n            },\n            \"promProbe/inputs\": {\n             \"properties\": {\n              \"comparator\": {\n               \"properties\": {\n                \"criteria\": {\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"criteria\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"endpoint\": {\n               \"type\": \"string\"\n              },\n              \"query\": {\n               \"type\": \"string\"\n              },\n              \"queryPath\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"endpoint\",\n              \"comparator\"\n             ],\n             \"type\": \"object\"\n            },\n            \"runProperties\": {\n             \"minProperties\": 2,\n             \"properties\": {\n              \"attempt\": {\n               \"type\": \"integer\"\n              },\n              \"evaluationTimeout\": {\n               \"type\": \"string\"\n              },\n              \"initialDelay\": {\n               \"type\": \"string\"\n              },\n              \"initialDelaySeconds\": {\n               \"type\": \"integer\"\n              },\n              \"interval\": {\n               \"type\": \"string\"\n              },\n              \"probePollingInterval\": {\n               \"type\": \"string\"\n              },\n              \"probeTimeout\": {\n               \"type\": \"string\"\n              },\n              \"retry\": {\n               \"type\": \"integer\"\n              },\n              \"stopOnFailure\": {\n               \"type\": \"boolean\"\n              },\n              \"verbosity\": {\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"probeTimeout\",\n              \"interval\"\n             ],\n             \"type\": \"object\"\n            },\n            \"sloProbe/inputs\": {\n             \"description\": \"inputs needed for the SLO probe\",\n             \"properties\": {\n              \"comparator\": {\n               \"description\": \"Comparator check for the correctness of the probe output\",\n               \"properties\": {\n                \"criteria\": {\n                 \"description\": \"Criteria for matching data it supports \\u003e=, \\u003c=, ==, \\u003e, \\u003c, != for int and float it supports equal, notEqual, contains for string\",\n                 \"type\": \"string\"\n                },\n                \"type\": {\n                 \"description\": \"Type of data it can be int, float, string\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"description\": \"Value contains relative value for criteria\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"criteria\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"evaluationWindow\": {\n               \"description\": \"EvaluationWindow is the time period for which the metrics will be evaluated\",\n               \"properties\": {\n                \"evaluationEndTime\": {\n                 \"description\": \"End time of evaluation\",\n                 \"type\": \"integer\"\n                },\n                \"evaluationStartTime\": {\n                 \"description\": \"Start time of evaluation\",\n                 \"type\": \"integer\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"insecureSkipVerify\": {\n               \"description\": \"InsecureSkipVerify flag to skip certificate checks\",\n               \"type\": \"boolean\"\n              },\n              \"platformEndpoint\": {\n               \"description\": \"PlatformEndpoint for the monitoring service endpoint\",\n               \"type\": \"string\"\n              },\n              \"sloIdentifier\": {\n               \"description\": \"SLOIdentifier for fetching the details of the SLO\",\n               \"type\": \"string\"\n              },\n              \"sloSourceMetadata\": {\n               \"description\": \"SLOSourceMetadata consists of required metadata details to fetch metric data\",\n               \"properties\": {\n                \"apiTokenSecret\": {\n                 \"description\": \"APITokenSecret for authenticating with the platform service\",\n                 \"type\": \"string\"\n                },\n                \"scope\": {\n                 \"description\": \"Scope required for fetching details\",\n                 \"properties\": {\n                  \"accountIdentifier\": {\n                   \"description\": \"AccountIdentifier for account ID\",\n                   \"type\": \"string\"\n                  },\n                  \"orgIdentifier\": {\n                   \"description\": \"OrgIdentifier for organization ID\",\n                   \"type\": \"string\"\n                  },\n                  \"projectIdentifier\": {\n                   \"description\": \"ProjectIdentifier for project ID\",\n                   \"type\": \"string\"\n                  }\n                 },\n                 \"required\": [\n                  \"accountIdentifier\",\n                  \"orgIdentifier\",\n                  \"projectIdentifier\"\n                 ],\n                 \"type\": \"object\"\n                }\n               },\n               \"required\": [\n                \"apiTokenSecret\",\n                \"scope\"\n               ],\n               \"type\": \"object\"\n              }\n             },\n             \"required\": [\n              \"platformEndpoint\",\n              \"sloIdentifier\",\n              \"sloSourceMetadata\",\n              \"comparator\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": {\n             \"minLength\": 1,\n             \"pattern\": \"^(k8sProbe|httpProbe|cmdProbe|promProbe|sloProbe)$\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"name\",\n            \"type\",\n            \"mode\",\n            \"runProperties\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\"\n         }\n        },\n        \"type\": \"object\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"jobCleanUpPolicy\": {\n     \"pattern\": \"^(delete|retain)$\",\n     \"type\": \"string\"\n    },\n    \"selectors\": {\n     \"oneOf\": [\n      {\n       \"required\": [\n        \"pods\"\n       ]\n      },\n      {\n       \"required\": [\n        \"workloads\"\n       ]\n      }\n     ],\n     \"properties\": {\n      \"pods\": {\n       \"items\": {\n        \"properties\": {\n         \"names\": {\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"names\",\n         \"namespace\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"workloads\": {\n       \"items\": {\n        \"oneOf\": [\n         {\n          \"required\": [\n           \"names\"\n          ]\n         },\n         {\n          \"required\": [\n           \"labels\"\n          ]\n         }\n        ],\n        \"properties\": {\n         \"kind\": {\n          \"pattern\": \"^(^$|deployment|statefulset|daemonset|deploymentconfig|rollout)$\",\n          \"type\": \"string\"\n         },\n         \"labels\": {\n          \"type\": \"string\"\n         },\n         \"names\": {\n          \"type\": \"string\"\n         },\n         \"namespace\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"kind\",\n         \"namespace\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"terminationGracePeriodSeconds\": {\n     \"type\": \"integer\"\n    }\n   },\n   \"type\": \"string\"\n  }\n },\n \"title\": \"Chaos Engine\",\n \"type\": \"object\"\n}"}}