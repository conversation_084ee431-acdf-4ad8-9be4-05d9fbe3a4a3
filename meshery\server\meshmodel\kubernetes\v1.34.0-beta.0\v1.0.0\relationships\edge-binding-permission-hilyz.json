{"id": "********-0000-0000-0000-************", "evaluationQuery": "", "kind": "edge", "metadata": {"description": "A relationship that represents a set of permissions", "styles": {"primaryColor": "", "svgColor": "", "svgWhite": ""}, "isAnnotation": false}, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "type": "", "sub_type": "", "kind": "", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": "v1.34.0-beta.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "schemaVersion": "relationships.meshery.io/v1alpha3", "selectors": [{"allow": {"from": [{"id": null, "kind": "ClusterRole", "match": {"from": [{"id": null, "kind": "self", "mutatorRef": [["component", "kind"], ["displayName"]]}], "to": [{"id": null, "kind": "ClusterRoleBinding", "mutatedRef": [["configuration", "roleRef", "kind"], ["configuration", "roleRef", "name"]]}]}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "credential_id": "********-0000-0000-0000-************", "type": "", "sub_type": "", "kind": "github", "status": "", "user_id": "********-0000-0000-0000-************", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}, {"id": null, "kind": "Role", "match": {"from": [{"id": null, "kind": "self", "mutatorRef": [["component", "kind"], ["displayName"]]}], "to": [{"id": null, "kind": "RoleBinding", "mutatedRef": [["configuration", "roleRef", "kind"], ["configuration", "roleRef", "name"]]}]}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "credential_id": "********-0000-0000-0000-************", "type": "", "sub_type": "", "kind": "github", "status": "", "user_id": "********-0000-0000-0000-************", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}], "to": [{"id": null, "kind": "ServiceAccount", "match": {"from": [{"id": null, "kind": "ClusterRoleBinding", "mutatedRef": [["configuration", "subjects", "_", "name"]]}], "to": [{"id": null, "kind": "self", "mutatorRef": [["displayName"]]}]}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "credential_id": "********-0000-0000-0000-************", "type": "", "sub_type": "", "kind": "github", "status": "", "user_id": "********-0000-0000-0000-************", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}, {"id": null, "kind": "ServiceAccount", "match": {"from": [{"id": null, "kind": "RoleBinding", "mutatedRef": [["configuration", "subjects", "_", "name"]]}], "to": [{"id": null, "kind": "self", "mutatorRef": [["displayName"]]}]}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "credential_id": "********-0000-0000-0000-************", "type": "", "sub_type": "", "kind": "github", "status": "", "user_id": "********-0000-0000-0000-************", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}]}, "deny": {"from": [{"id": null, "kind": "ClusterRole", "match": {}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}, {"id": null, "kind": "Role", "match": {}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}, {"id": null, "kind": "ClusterRoleBinding", "match": {}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}, {"id": null, "kind": "RoleBinding", "match": {}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}], "to": [{"id": null, "kind": "ClusterRole", "match": {}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}, {"id": null, "kind": "Role", "match": {}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}, {"id": null, "kind": "ClusterRoleBinding", "match": {}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}, {"id": null, "kind": "RoleBinding", "match": {}, "match_strategy_matrix": null, "model": {"id": "********-0000-0000-0000-************", "schemaVersion": "", "version": "", "name": "kubernetes", "displayName": "", "status": "", "registrant": {"id": "********-0000-0000-0000-************", "name": "", "type": "", "sub_type": "", "kind": "github", "status": "", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "********-0000-0000-0000-************", "category": {"id": "********-0000-0000-0000-************", "name": ""}, "subCategory": "", "metadata": null, "model": {"version": ""}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "patch": null}]}}], "subType": "permission", "status": "enabled", "type": "binding", "version": "v1.0.0"}