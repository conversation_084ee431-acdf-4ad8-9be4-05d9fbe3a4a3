{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Resource Claim", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kubernetes", "displayName": "Kubernetes", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "<PERSON><PERSON><PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "github", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Orchestration & Management"}, "subCategory": "Scheduling & Orchestration", "metadata": {"isAnnotation": false, "primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "circle", "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-0.17 0.08 230.10 223.35\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}.cls-2{fill:#326ce5}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.466a3.59 3.59 0 0 0-.855-.065 3.685 3.685 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53 43.53 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.656 3.656 0 0 0-.733.065l-.037-.045-21.785 3.698a43.695 43.695 0 0 0 17.54 21.946l8.442-20.4-.066-.08a3.683 3.683 0 0 0 .207-2.29zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.2l-10.738-19.406zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.039a3.725 3.725 0 0 0 5.913 2.84l.065.027 18.036-12.788a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.921a3.72 3.72 0 0 0 5.904-2.85l.092-.043 1.253-22.14a44.682 44.682 0 0 0-4.501.776 43.467 43.467 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.729a3.72 3.72 0 0 0 1.462-6.396l.018-.088-16.574-14.824a43.454 43.454 0 0 0-6.168 27.51l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.934 1.514-6.581-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.167l-17.422-75.675a13.602 13.602 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0L38.76 48.437a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.506 13.506 0 0 0 .491 1.718 13.1 13.1 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.423 13.423 0 0 0 2.574 2.133 13.924 13.924 0 0 0 3.857 1.677 13.298 13.298 0 0 0 3.43.473h.759l77.504-.018a12.993 12.993 0 0 0 1.41-.083 13.47 13.47 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.941 13.941 0 0 0 1.479-.822 13.303 13.303 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67 13.67 0 0 0 .5-1.724 13.282 13.282 0 0 0-.004-5.81zm-73.147 29.432a14.516 14.516 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.372 39.372 0 0 0 3.742 6.7 35.06 35.06 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.275 35.275 0 0 1-1.225-3.868 39.305 39.305 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.15-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.73a3.429 3.429 0 0 0-1.819.887 29.517 29.517 0 0 0-3.268 7.582 34.9 34.9 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.177 35.177 0 0 1 2.262-3.373 41.228 41.228 0 0 0 3.82-6.866 4.188 4.188 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68 4.68 0 0 0-2.428-.855 39.524 39.524 0 0 0-7.356 2.165 35.589 35.589 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.605.605 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.388 39.388 0 0 0 7.564-1.242 5.835 5.835 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.366 39.366 0 0 0-6.277-4.41 35.25 35.25 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.253 35.253 0 0 1 2.78 2.95 39.383 39.383 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082A54.289 54.289 0 0 1 102.86 61.89a55.052 55.052 0 0 1 7.63-1.173l.1-1.784a4.6 4.6 0 0 0 1.37-2.184 39.476 39.476 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.08-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.86.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.533 39.533 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.69 4.69 0 0 0 2.56-.278 39.532 39.532 0 0 0 5.69-5.148 35.004 35.004 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.095 35.095 0 0 1-3.507 2.046 39.495 39.495 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.745 4.745 0 0 0 1.82 1.83 39.464 39.464 0 0 0 7.568 1.246 35.64 35.64 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.03-.348-.075-.785-.151-1.09-.231a35.14 35.14 0 0 1-3.785-1.462 39.477 39.477 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.92 54.92 0 0 1-24.328 30.605z\" class=\"cls-2\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M225.407 135.107L206.4 52.547a14.838 14.838 0 0 0-7.958-9.9l-76.935-36.73a14.825 14.825 0 0 0-12.771 0L31.808 42.669a14.838 14.838 0 0 0-7.961 9.895L4.873 135.129a14.668 14.668 0 0 0 1.995 11.185c.261.4.538.788.838 1.162l53.246 66.205a14.98 14.98 0 0 0 11.499 5.487l85.387-.02a14.986 14.986 0 0 0 11.5-5.48l53.227-66.211a14.72 14.72 0 0 0 2.842-12.347zm-9.197 3.866a13.677 13.677 0 0 1-.498 1.723 13.184 13.184 0 0 1-2.11 3.786l-47.299 58.838-1.486 1.852a13.305 13.305 0 0 1-3.24 2.865 13.945 13.945 0 0 1-1.474.822q-.513.237-1.045.43a13.873 13.873 0 0 1-1.383.445 13.473 13.473 0 0 1-1.989.379 12.988 12.988 0 0 1-1.41.082l-77.504.018h-.76a13.298 13.298 0 0 1-3.429-.472 13.925 13.925 0 0 1-3.855-1.679 13.424 13.424 0 0 1-2.576-2.132c-.233-.246-.468-.487-.68-.75l-48.805-60.679q-.408-.514-.765-1.066a13.102 13.102 0 0 1-1.343-2.726 13.505 13.505 0 0 1-.491-1.719 13.315 13.315 0 0 1 .004-5.809l17.394-75.675a13.598 13.598 0 0 1 7.295-9.07l70.508-33.685a13.589 13.589 0 0 1 11.705 0l70.519 33.67a13.602 13.602 0 0 1 7.293 9.073l17.422 75.674a13.282 13.282 0 0 1 .002 5.807z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M185.814 127.106c-.36-.083-.874-.225-1.227-.29a35.642 35.642 0 0 0-4.046-.326 39.464 39.464 0 0 1-7.57-1.242 4.745 4.745 0 0 1-1.82-1.832l-1.704-.496a54.837 54.837 0 0 0-8.79-37.983c.436-.396 1.262-1.127 1.495-1.342a3.338 3.338 0 0 1 .792-2.39 39.495 39.495 0 0 1 6.277-4.41 35.095 35.095 0 0 0 3.507-2.046c.272-.202.644-.522.929-.75a4.242 4.242 0 1 0-5.256-6.6c-.288.227-.68.525-.936.747a35.004 35.004 0 0 0-2.789 2.95 39.533 39.533 0 0 1-5.69 5.148 4.69 4.69 0 0 1-2.56.278l-1.606 1.147a55.309 55.309 0 0 0-35.032-16.927c-.039-.561-.087-1.577-.102-1.884a3.344 3.344 0 0 1-1.377-2.11 39.533 39.533 0 0 1 .478-7.656 35.112 35.112 0 0 0 .575-4.012c.013-.34-.007-.834-.007-1.201a4.242 4.242 0 1 0-8.441.004c0 .04.009.078.01.118-.004.35-.021.774-.01 1.08a35.476 35.476 0 0 0 .576 4.015 39.475 39.475 0 0 1 .47 7.654 4.601 4.601 0 0 1-1.37 2.182l-.1 1.786a55.052 55.052 0 0 0-7.63 1.173 54.289 54.289 0 0 0-27.574 15.754q-.77-.531-1.526-1.082a3.333 3.333 0 0 1-2.506-.243 39.383 39.383 0 0 1-5.69-5.141 35.255 35.255 0 0 0-2.777-2.95c-.257-.22-.65-.52-.938-.75a5.03 5.03 0 0 0-2.942-1.11 4.066 4.066 0 0 0-3.363 1.49 4.473 4.473 0 0 0 1.038 6.222l.065.046c.276.226.616.515.872.702a35.256 35.256 0 0 0 3.499 2.048 39.367 39.367 0 0 1 6.276 4.412 4.69 4.69 0 0 1 .849 2.434l1.351 1.208a54.613 54.613 0 0 0-8.611 38.073l-1.767.514a5.835 5.835 0 0 1-1.814 1.827 39.39 39.39 0 0 1-7.565 1.247 35.266 35.266 0 0 0-4.049.326c-.324.06-.774.174-1.127.262l-.037.008-.06.018a4.236 4.236 0 1 0 1.875 8.224l.063-.01c.028-.006.052-.02.08-.025.348-.08.786-.163 1.092-.246a35.59 35.59 0 0 0 3.786-1.451 39.527 39.527 0 0 1 7.358-2.165 4.68 4.68 0 0 1 2.426.857l1.84-.315a54.922 54.922 0 0 0 24.34 30.387l-.769 1.84a4.188 4.188 0 0 1 .377 2.387 41.228 41.228 0 0 1-3.82 6.864 35.183 35.183 0 0 0-2.263 3.372c-.168.318-.381.805-.542 1.138a4.238 4.238 0 1 0 7.621 3.616l.007-.008v-.01c.16-.33.387-.763.522-1.072a34.903 34.903 0 0 0 1.218-3.868 29.517 29.517 0 0 1 3.268-7.582 3.43 3.43 0 0 1 1.819-.888l.957-1.73a54.617 54.617 0 0 0 38.946.099c.268.478.768 1.392.9 1.623a3.335 3.335 0 0 1 2.155 1.31 39.306 39.306 0 0 1 2.898 7.096 35.275 35.275 0 0 0 1.225 3.868c.137.312.36.75.522 1.082a4.24 4.24 0 1 0 7.63-3.607c-.161-.333-.383-.82-.55-1.136a35.06 35.06 0 0 0-2.263-3.364 39.372 39.372 0 0 1-3.742-6.7 3.314 3.314 0 0 1 .324-2.49 14.519 14.519 0 0 1-.703-1.703 54.92 54.92 0 0 0 24.328-30.605c.546.087 1.497.253 1.806.316a3.337 3.337 0 0 1 2.36-.877 39.476 39.476 0 0 1 7.36 2.165 35.135 35.135 0 0 0 3.788 1.462c.305.08.74.156 1.09.233.029.008.055.02.083.028l.06.009a4.236 4.236 0 1 0 1.878-8.224zm-40.1-42.987l-18.037 12.787-.063-.03a3.723 3.723 0 0 1-5.913-2.838l-.02-.01-1.253-22.103a43.85 43.85 0 0 1 25.285 12.194zm-33.978 24.228h6.788l4.22 5.276-1.513 6.58-6.096 2.934-6.114-2.94-1.516-6.583zm-6.386-35.648a44.672 44.672 0 0 1 4.503-.774l-1.255 22.137-.092.044a3.72 3.72 0 0 1-5.904 2.852l-.035.02-18.154-12.872a43.467 43.467 0 0 1 20.937-11.407zm-27.52 19.68l16.574 14.824-.018.09a3.72 3.72 0 0 1-1.462 6.395l-.017.072-21.245 6.13a43.454 43.454 0 0 1 6.168-27.51zm22.191 39.38l-8.441 20.397a43.696 43.696 0 0 1-17.536-21.948l21.783-3.7.037.049a3.655 3.655 0 0 1 .73-.065 3.72 3.72 0 0 1 3.364 5.185zm24.916 26.23a43.637 43.637 0 0 1-23.815-1.223l10.713-19.372h.018a3.725 3.725 0 0 1 6.557-.006h.08l10.74 19.404q-2.091.698-4.293 1.199zm13.841-5.751l-8.528-20.605.026-.037a3.725 3.725 0 0 1 1.803-4.823 3.685 3.685 0 0 1 1.425-.37 3.59 3.59 0 0 1 .855.063l.037-.046 21.977 3.714a43.53 43.53 0 0 1-17.595 22.105zm19.903-32.42l-21.352-6.15-.02-.09a3.725 3.725 0 0 1-1.46-6.395l-.008-.043 16.482-14.751a44.279 44.279 0 0 1 6.357 27.43z\" class=\"cls-1\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"9.70 9.20 210.86 204.86\" height=\"20\" width=\"20\"><defs xmlns=\"http://www.w3.org/2000/svg\"><style xmlns=\"http://www.w3.org/2000/svg\">.cls-1{fill:#fff}</style></defs><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M134.358 126.46551a3.59023 3.59023 0 0 0-.855-.065 3.68515 3.68515 0 0 0-1.425.37 3.725 3.725 0 0 0-1.803 4.825l-.026.037 8.528 20.603a43.53012 43.53012 0 0 0 17.595-22.102l-21.976-3.714zm-34.194 2.92a3.72 3.72 0 0 0-3.568-2.894 3.6556 3.6556 0 0 0-.733.065l-.037-.045-21.785 3.698a43.69506 43.69506 0 0 0 17.54 21.946l8.442-20.399-.066-.08a3.68318 3.68318 0 0 0 .207-2.291zm18.245 8a3.718 3.718 0 0 0-6.557.008h-.018l-10.713 19.372a43.637 43.637 0 0 0 23.815 1.225q2.197-.5 4.292-1.199l-10.738-19.407zm33.914-45l-16.483 14.753.009.047a3.725 3.725 0 0 0 1.46 6.395l.02.089 21.35 6.15a44.278 44.278 0 0 0-6.356-27.432zM121.7 94.0385a3.725 3.725 0 0 0 5.913 2.84l.065.028 18.036-12.789a43.85 43.85 0 0 0-25.287-12.19l1.253 22.105zm-19.1 2.922a3.72 3.72 0 0 0 5.904-2.85l.092-.044 1.253-22.139a44.68209 44.68209 0 0 0-4.501.775 43.4669 43.4669 0 0 0-20.937 11.409l18.154 12.869zm-9.678 16.728a3.72 3.72 0 0 0 1.462-6.396l.018-.087-16.574-14.825a43.454 43.454 0 0 0-6.168 27.511l21.245-6.13zm16.098 6.512l6.114 2.94 6.096-2.933 1.514-6.582-4.219-5.276h-6.79l-4.231 5.268z\" class=\"cls-1\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M216.208 133.16651l-17.422-75.675a13.60207 13.60207 0 0 0-7.293-9.073l-70.521-33.67a13.589 13.589 0 0 0-11.705 0l-70.507 33.688a13.598 13.598 0 0 0-7.295 9.072l-17.394 75.673a13.315 13.315 0 0 0-.004 5.81 13.50607 13.50607 0 0 0 .491 1.718 13.0998 13.0998 0 0 0 1.343 2.726c.239.365.491.72.765 1.064l48.804 60.678c.213.264.448.505.681.75a13.42334 13.42334 0 0 0 2.574 2.133 13.9237 13.9237 0 0 0 3.857 1.677 13.29785 13.29785 0 0 0 3.43.473h.759l77.504-.018a12.99345 12.99345 0 0 0 1.41-.083 13.46921 13.46921 0 0 0 1.989-.378 13.872 13.872 0 0 0 1.381-.442c.353-.135.705-.27 1.045-.433a13.94127 13.94127 0 0 0 1.479-.822 13.30347 13.30347 0 0 0 3.237-2.865l1.488-1.85 47.299-58.84a13.185 13.185 0 0 0 2.108-3.785 13.67036 13.67036 0 0 0 .5-1.724 13.28215 13.28215 0 0 0-.004-5.809zm-73.147 29.432a14.51575 14.51575 0 0 0 .703 1.703 3.314 3.314 0 0 0-.327 2.49 39.37244 39.37244 0 0 0 3.742 6.7 35.06044 35.06044 0 0 1 2.263 3.364c.17.315.392.803.553 1.136a4.24 4.24 0 1 1-7.63 3.607c-.161-.33-.385-.77-.522-1.082a35.27528 35.27528 0 0 1-1.225-3.868 39.3046 39.3046 0 0 0-2.896-7.097 3.335 3.335 0 0 0-2.154-1.307c-.135-.233-.635-1.149-.903-1.623a54.617 54.617 0 0 1-38.948-.1l-.955 1.731a3.429 3.429 0 0 0-1.819.886 29.51728 29.51728 0 0 0-3.268 7.582 34.89931 34.89931 0 0 1-1.218 3.868c-.135.31-.361.744-.522 1.073v.009l-.007.008a4.238 4.238 0 1 1-7.619-3.616c.159-.335.372-.82.54-1.135a35.17706 35.17706 0 0 1 2.262-3.373 41.22786 41.22786 0 0 0 3.82-6.866 4.18792 4.18792 0 0 0-.376-2.387l.768-1.84a54.922 54.922 0 0 1-24.338-30.387l-1.839.313a4.68007 4.68007 0 0 0-2.428-.855 39.52352 39.52352 0 0 0-7.356 2.165 35.58886 35.58886 0 0 1-3.787 1.45c-.305.084-.745.168-1.093.244-.028.01-.052.022-.08.029a.60518.60518 0 0 1-.065.006 4.236 4.236 0 1 1-1.874-8.224l.061-.015.037-.01c.353-.083.805-.2 1.127-.262a35.27 35.27 0 0 1 4.05-.326 39.38835 39.38835 0 0 0 7.564-1.242 5.83506 5.83506 0 0 0 1.814-1.83l1.767-.516a54.613 54.613 0 0 1 8.613-38.073l-1.353-1.206a4.688 4.688 0 0 0-.848-2.436 39.36558 39.36558 0 0 0-6.277-4.41 35.2503 35.2503 0 0 1-3.499-2.046c-.256-.191-.596-.478-.874-.704l-.063-.044a4.473 4.473 0 0 1-1.038-6.222 4.066 4.066 0 0 1 3.363-1.488 5.03 5.03 0 0 1 2.942 1.11c.287.225.68.526.935.745a35.25285 35.25285 0 0 1 2.78 2.95 39.38314 39.38314 0 0 0 5.69 5.142 3.333 3.333 0 0 0 2.507.243q.754.55 1.522 1.082a54.28892 54.28892 0 0 1 27.577-15.754 55.05181 55.05181 0 0 1 7.63-1.173l.1-1.784a4.6001 4.6001 0 0 0 1.37-2.184 39.47551 39.47551 0 0 0-.47-7.654 35.466 35.466 0 0 1-.576-4.014c-.011-.307.006-.731.01-1.081 0-.04-.01-.079-.01-.118a4.242 4.242 0 1 1 8.441-.004c0 .37.022.861.009 1.2a35.109 35.109 0 0 1-.579 4.013 39.53346 39.53346 0 0 0-.478 7.656 3.344 3.344 0 0 0 1.379 2.11c.015.305.065 1.323.102 1.884a55.309 55.309 0 0 1 35.032 16.927l1.606-1.147a4.6901 4.6901 0 0 0 2.56-.278 39.53152 39.53152 0 0 0 5.69-5.148 35.00382 35.00382 0 0 1 2.787-2.95c.259-.222.65-.52.936-.746a4.242 4.242 0 1 1 5.258 6.598c-.283.229-.657.548-.929.75a35.09523 35.09523 0 0 1-3.507 2.046 39.49476 39.49476 0 0 0-6.277 4.41 3.337 3.337 0 0 0-.792 2.39c-.235.216-1.06.947-1.497 1.343a54.837 54.837 0 0 1 8.792 37.983l1.704.496a4.7449 4.7449 0 0 0 1.82 1.831 39.46448 39.46448 0 0 0 7.568 1.245 35.64041 35.64041 0 0 1 4.046.324c.355.065.868.207 1.23.29a4.236 4.236 0 1 1-1.878 8.223l-.061-.008c-.028-.007-.054-.022-.083-.029-.348-.076-.785-.152-1.09-.232a35.1407 35.1407 0 0 1-3.785-1.462 39.47672 39.47672 0 0 0-7.363-2.165 3.337 3.337 0 0 0-2.362.877q-.9-.171-1.804-.316a54.91994 54.91994 0 0 1-24.328 30.605z\" class=\"cls-1\"></path></svg>"}, "model": {"version": "v1.34.0-alpha.3"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#326CE5", "secondaryColor": "#7aa1f0", "shape": "cut-rectangle", "svgColor": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 6C4 4.89543 4.89543 4 6 4H84C85.1046 4 86 4.89543 86 6V84C86 85.1046 85.1046 86 84 86H6C4.89543 86 4 85.1046 4 84V6ZM19.8201 21C18.2626 21 17 22.2626 17 23.8201V60.3345C17 61.892 18.2626 63.1547 19.8201 63.1547H38.0033C36.0523 60.0111 34.9248 56.2957 34.9248 52.315C34.9248 41.0069 44.0241 31.8398 55.2486 31.8398C56.4752 31.8398 57.6765 31.9493 58.8432 32.1591V23.8201C58.8432 22.2626 57.5806 21 56.0231 21H19.8201ZM55.2812 35.4531C55.2704 35.4531 55.2595 35.4531 55.2486 35.4531C46.0049 35.4531 38.5113 43.0024 38.5113 52.315C38.5113 56.4425 39.9834 60.2237 42.4275 63.1547H42.4452C45.5155 66.8366 50.1196 69.1768 55.2663 69.1768C64.5101 69.1768 72.0036 61.6275 72.0036 52.315C72.0036 43.0074 64.5181 35.4612 55.2812 35.4531Z\" fill=\"#326CE5\"/>\n</svg>\n", "svgComplete": "", "svgWhite": "<svg width=\"90\" height=\"90\" viewBox=\"0 0 90 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 6C4 4.89543 4.89543 4 6 4H84C85.1046 4 86 4.89543 86 6V84C86 85.1046 85.1046 86 84 86H6C4.89543 86 4 85.1046 4 84V6ZM19.82 21C18.2626 21 17 22.2626 17 23.82V60.334C17 61.8915 18.2626 63.154 19.82 63.154H38.0041C36.0532 60.0105 34.9257 56.2952 34.9257 52.3147C34.9257 41.0067 44.0247 31.8398 55.2489 31.8398C56.4749 31.8398 57.6756 31.9492 58.8418 32.1588V23.82C58.8418 22.2626 57.5793 21 56.0218 21H19.82ZM56.2455 35.4824C55.9158 35.4629 55.5835 35.453 55.2489 35.453C46.0054 35.453 38.5122 43.0022 38.5122 52.3147C38.5122 56.4421 39.9841 60.2231 42.4281 63.154H42.4452C45.5154 66.8366 50.1198 69.1773 55.2669 69.1773C64.5103 69.1773 72.0036 61.6281 72.0036 52.3157C72.0036 43.3343 65.0335 35.9929 56.2455 35.4824Z\" fill=\"white\"/>\n</svg>\n"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "git://github.com/kubernetes/kubernetes/master/api/openapi-spec/v3"}, "configuration": null, "component": {"version": "resource.k8s.io/v1beta1", "kind": "ResourceClaim", "schema": "{\"description\":\"ResourceClaim describes a request for access to resources in the cluster, for use by workloads. For example, if a workload needs an accelerator device with specific properties, this is how that request is expressed. The status stanza tracks whether this claim has been satisfied and what specific resources have been allocated.\\n\\nThis is an alpha type and requires enabling the DynamicResourceAllocation feature gate.\",\"properties\":{\"spec\":{\"allOf\":[{\"description\":\"ResourceClaimSpec defines what is being requested in a ResourceClaim and how to configure it.\",\"properties\":{\"devices\":{\"allOf\":[{\"description\":\"<PERSON><PERSON>Claim defines how to request devices with a ResourceClaim.\",\"properties\":{\"config\":{\"description\":\"This field holds configuration for multiple potential drivers which could satisfy requests in this claim. It is ignored while allocating the claim.\",\"items\":{\"allOf\":[{\"description\":\"DeviceClaimConfiguration is used for configuration parameters in DeviceClaim.\",\"properties\":{\"opaque\":{\"allOf\":[{\"description\":\"OpaqueDeviceConfiguration contains configuration parameters for a driver in a format defined by the driver vendor.\",\"properties\":{\"driver\":{\"default\":\"\",\"description\":\"Driver is used to determine which kubelet plugin needs to be passed these configuration parameters.\\n\\nAn admission policy provided by the driver developer could use this to decide whether it needs to validate them.\\n\\nMust be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver.\",\"type\":\"string\"},\"parameters\":{\"allOf\":[{\"description\":\"RawExtension is used to hold extensions in external versions.\\n\\nTo use this, make a field which has RawExtension as its type in your external, versioned struct, and Object in your internal struct. You also need to register your various plugin types.\\n\\n// Internal package:\\n\\n\\ttype MyAPIObject struct {\\n\\t\\truntime.TypeMeta `json:\\\",inline\\\"`\\n\\t\\tMyPlugin runtime.Object `json:\\\"myPlugin\\\"`\\n\\t}\\n\\n\\ttype PluginA struct {\\n\\t\\tAOption string `json:\\\"aOption\\\"`\\n\\t}\\n\\n// External package:\\n\\n\\ttype MyAPIObject struct {\\n\\t\\truntime.TypeMeta `json:\\\",inline\\\"`\\n\\t\\tMyPlugin runtime.RawExtension `json:\\\"myPlugin\\\"`\\n\\t}\\n\\n\\ttype PluginA struct {\\n\\t\\tAOption string `json:\\\"aOption\\\"`\\n\\t}\\n\\n// On the wire, the JSON will look something like this:\\n\\n\\t{\\n\\t\\t\\\"kind\\\":\\\"MyAPIObject\\\",\\n\\t\\t\\\"apiVersion\\\":\\\"v1\\\",\\n\\t\\t\\\"myPlugin\\\": {\\n\\t\\t\\t\\\"kind\\\":\\\"PluginA\\\",\\n\\t\\t\\t\\\"aOption\\\":\\\"foo\\\",\\n\\t\\t},\\n\\t}\\n\\nSo what happens? Decode first uses json or yaml to unmarshal the serialized data into your external MyAPIObject. That causes the raw JSON to be stored, but not unpacked. The next step is to copy (using pkg/conversion) into the internal struct. The runtime package's DefaultScheme has conversion functions installed which will unpack the JSON stored in RawExtension, turning it into the correct object type, and storing it in the Object. (TODO: In the case where the object is of an unknown type, a runtime.Unknown object will be created and stored.)\",\"type\":\"object\"}],\"description\":\"Parameters can contain arbitrary data. It is the responsibility of the driver developer to handle validation and versioning. Typically this includes self-identification and a version (\\\"kind\\\" + \\\"apiVersion\\\" for Kubernetes types), with conversion between different versions.\\n\\nThe length of the raw data must be smaller or equal to 10 Ki.\"}},\"required\":[\"driver\",\"parameters\"],\"type\":\"object\"}],\"description\":\"Opaque provides driver-specific configuration parameters.\"},\"requests\":{\"description\":\"Requests lists the names of requests where the configuration applies. If empty, it applies to all requests.\\n\\nReferences to subrequests must include the name of the main request and may include the subrequest using the format \\u003cmain request\\u003e[/\\u003csubrequest\\u003e]. If just the main request is given, the configuration applies to all subrequests.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"constraints\":{\"description\":\"These constraints must be satisfied by the set of devices that get allocated for the claim.\",\"items\":{\"allOf\":[{\"description\":\"DeviceConstraint must have exactly one field set besides Requests.\",\"properties\":{\"matchAttribute\":{\"description\":\"MatchAttribute requires that all devices in question have this attribute and that its type and value are the same across those devices.\\n\\nFor example, if you specified \\\"dra.example.com/numa\\\" (a hypothetical example!), then only devices in the same NUMA node will be chosen. A device which does not have that attribute will not be chosen. All devices should use a value of the same type for this attribute because that is part of its specification, but if one device doesn't, then it also will not be chosen.\\n\\nMust include the domain qualifier.\",\"type\":\"string\"},\"requests\":{\"description\":\"Requests is a list of the one or more requests in this claim which must co-satisfy this constraint. If a request is fulfilled by multiple devices, then all of the devices must satisfy the constraint. If this is not specified, this constraint applies to all requests in this claim.\\n\\nReferences to subrequests must include the name of the main request and may include the subrequest using the format \\u003cmain request\\u003e[/\\u003csubrequest\\u003e]. If just the main request is given, the constraint applies to all subrequests.\",\"items\":{\"default\":\"\",\"type\":\"string\"},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"requests\":{\"description\":\"Requests represent individual requests for distinct devices which must all be satisfied. If empty, nothing needs to be allocated.\",\"items\":{\"allOf\":[{\"description\":\"DeviceRequest is a request for devices required for a claim. This is typically a request for a single resource like a device, but can also ask for several identical devices.\",\"properties\":{\"adminAccess\":{\"description\":\"AdminAccess indicates that this is a claim for administrative access to the device(s). Claims with AdminAccess are expected to be used for monitoring or other management services for a device.  They ignore all ordinary claims to the device with respect to access modes and any resource allocations.\\n\\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.\\n\\nThis is an alpha field and requires enabling the DRAAdminAccess feature gate. Admin access is disabled if this field is unset or set to false, otherwise it is enabled.\",\"type\":\"boolean\"},\"allocationMode\":{\"description\":\"AllocationMode and its related fields define how devices are allocated to satisfy this request. Supported values are:\\n\\n- ExactCount: This request is for a specific number of devices.\\n  This is the default. The exact number is provided in the\\n  count field.\\n\\n- All: This request is for all of the matching devices in a pool.\\n  At least one device must exist on the node for the allocation to succeed.\\n  Allocation will fail if some devices are already allocated,\\n  unless adminAccess is requested.\\n\\nIf AllocationMode is not specified, the default mode is ExactCount. If the mode is ExactCount and count is not specified, the default count is one. Any other requests must specify this field.\\n\\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.\\n\\nMore modes may get added in the future. Clients must refuse to handle requests with unknown modes.\",\"type\":\"string\"},\"count\":{\"description\":\"Count is used only when the count mode is \\\"ExactCount\\\". Must be greater than zero. If AllocationMode is ExactCount and this field is not specified, the default is one.\\n\\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.\",\"format\":\"int64\",\"type\":\"integer\"},\"deviceClassName\":{\"default\":\"\",\"description\":\"DeviceClassName references a specific DeviceClass, which can define additional configuration and selectors to be inherited by this request.\\n\\nA class is required if no subrequests are specified in the firstAvailable list and no class can be set if subrequests are specified in the firstAvailable list. Which classes are available depends on the cluster.\\n\\nAdministrators may use this to restrict which devices may get requested by only installing classes with selectors for permitted devices. If users are free to request anything without restrictions, then administrators can create an empty DeviceClass for users to reference.\",\"type\":\"string\"},\"firstAvailable\":{\"description\":\"FirstAvailable contains subrequests, of which exactly one will be satisfied by the scheduler to satisfy this request. It tries to satisfy them in the order in which they are listed here. So if there are two entries in the list, the scheduler will only check the second one if it determines that the first one cannot be used.\\n\\nThis field may only be set in the entries of DeviceClaim.Requests.\\n\\nDRA does not yet implement scoring, so the scheduler will select the first set of devices that satisfies all the requests in the claim. And if the requirements can be satisfied on more than one node, other scheduling features will determine which node is chosen. This means that the set of devices allocated to a claim might not be the optimal set available to the cluster. Scoring will be implemented later.\",\"items\":{\"allOf\":[{\"description\":\"DeviceSubRequest describes a request for device provided in the claim.spec.devices.requests[].firstAvailable array. Each is typically a request for a single resource like a device, but can also ask for several identical devices.\\n\\nDeviceSubRequest is similar to Request, but doesn't expose the AdminAccess or FirstAvailable fields, as those can only be set on the top-level request. AdminAccess is not supported for requests with a prioritized list, and recursive FirstAvailable fields are not supported.\",\"properties\":{\"allocationMode\":{\"description\":\"AllocationMode and its related fields define how devices are allocated to satisfy this subrequest. Supported values are:\\n\\n- ExactCount: This request is for a specific number of devices.\\n  This is the default. The exact number is provided in the\\n  count field.\\n\\n- All: This subrequest is for all of the matching devices in a pool.\\n  Allocation will fail if some devices are already allocated,\\n  unless adminAccess is requested.\\n\\nIf AllocationMode is not specified, the default mode is ExactCount. If the mode is ExactCount and count is not specified, the default count is one. Any other subrequests must specify this field.\\n\\nMore modes may get added in the future. Clients must refuse to handle requests with unknown modes.\",\"type\":\"string\"},\"count\":{\"description\":\"Count is used only when the count mode is \\\"ExactCount\\\". Must be greater than zero. If AllocationMode is ExactCount and this field is not specified, the default is one.\",\"format\":\"int64\",\"type\":\"integer\"},\"deviceClassName\":{\"default\":\"\",\"description\":\"DeviceClassName references a specific DeviceClass, which can define additional configuration and selectors to be inherited by this subrequest.\\n\\nA class is required. Which classes are available depends on the cluster.\\n\\nAdministrators may use this to restrict which devices may get requested by only installing classes with selectors for permitted devices. If users are free to request anything without restrictions, then administrators can create an empty DeviceClass for users to reference.\",\"type\":\"string\"},\"name\":{\"default\":\"\",\"description\":\"Name can be used to reference this subrequest in the list of constraints or the list of configurations for the claim. References must use the format \\u003cmain request\\u003e/\\u003csubrequest\\u003e.\\n\\nMust be a DNS label.\",\"type\":\"string\"},\"selectors\":{\"description\":\"Selectors define criteria which must be satisfied by a specific device in order for that device to be considered for this subrequest. All selectors must be satisfied for a device to be considered.\",\"items\":{\"allOf\":[{\"description\":\"DeviceSelector must have exactly one field set.\",\"properties\":{\"cel\":{\"allOf\":[{\"description\":\"CELDeviceSelector contains a CEL expression for selecting a device.\",\"properties\":{\"expression\":{\"default\":\"\",\"description\":\"Expression is a CEL expression which evaluates a single device. It must evaluate to true when the device under consideration satisfies the desired criteria, and false when it does not. Any other result is an error and causes allocation of devices to abort.\\n\\nThe expression's input is an object named \\\"device\\\", which carries the following properties:\\n - driver (string): the name of the driver which defines this device.\\n - attributes (map[string]object): the device's attributes, grouped by prefix\\n   (e.g. device.attributes[\\\"dra.example.com\\\"] evaluates to an object with all\\n   of the attributes which were prefixed by \\\"dra.example.com\\\".\\n - capacity (map[string]object): the device's capacities, grouped by prefix.\\n\\nExample: Consider a device with driver=\\\"dra.example.com\\\", which exposes two attributes named \\\"model\\\" and \\\"ext.example.com/family\\\" and which exposes one capacity named \\\"modules\\\". This input to this expression would have the following fields:\\n\\n    device.driver\\n    device.attributes[\\\"dra.example.com\\\"].model\\n    device.attributes[\\\"ext.example.com\\\"].family\\n    device.capacity[\\\"dra.example.com\\\"].modules\\n\\nThe device.driver field can be used to check for a specific driver, either as a high-level precondition (i.e. you only want to consider devices from this driver) or as part of a multi-clause expression that is meant to consider devices from different drivers.\\n\\nThe value type of each attribute is defined by the device definition, and users who write these expressions must consult the documentation for their specific drivers. The value type of each capacity is Quantity.\\n\\nIf an unknown prefix is used as a lookup in either device.attributes or device.capacity, an empty map will be returned. Any reference to an unknown field will cause an evaluation error and allocation to abort.\\n\\nA robust expression should check for the existence of attributes before referencing them.\\n\\nFor ease of use, the cel.bind() function is enabled, and can be used to simplify expressions that access multiple attributes with the same domain. For example:\\n\\n    cel.bind(dra, device.attributes[\\\"dra.example.com\\\"], dra.someBool \\u0026\\u0026 dra.anotherBool)\\n\\nThe length of the expression must be smaller or equal to 10 Ki. The cost of evaluating it is also limited based on the estimated number of logical steps.\",\"type\":\"string\"}},\"required\":[\"expression\"],\"type\":\"object\"}],\"description\":\"CEL contains a CEL expression for selecting a device.\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"tolerations\":{\"description\":\"If specified, the request's tolerations.\\n\\nTolerations for NoSchedule are required to allocate a device which has a taint with that effect. The same applies to NoExecute.\\n\\nIn addition, should any of the allocated devices get tainted with NoExecute after allocation and that effect is not tolerated, then all pods consuming the ResourceClaim get deleted to evict them. The scheduler will not let new pods reserve the claim while it has these tainted devices. Once all pods are evicted, the claim will get deallocated.\\n\\nThe maximum number of tolerations is 16.\\n\\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.\",\"items\":{\"allOf\":[{\"description\":\"The ResourceClaim this DeviceToleration is attached to tolerates any taint that matches the triple \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\"properties\":{\"effect\":{\"description\":\"Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule and NoExecute.\",\"type\":\"string\"},\"key\":{\"description\":\"Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys. Must be a label name.\",\"type\":\"string\"},\"operator\":{\"default\":\"Equal\",\"description\":\"Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a ResourceClaim can tolerate all taints of a particular category.\",\"type\":\"string\"},\"tolerationSeconds\":{\"description\":\"TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system. If larger than zero, the time when the pod needs to be evicted is calculated as \\u003ctime when taint was adedd\\u003e + \\u003ctoleration seconds\\u003e.\",\"format\":\"int64\",\"type\":\"integer\"},\"value\":{\"description\":\"Value is the taint value the toleration matches to. If the operator is Exists, the value must be empty, otherwise just a regular string. Must be a label value.\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"name\",\"deviceClassName\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"name\":{\"default\":\"\",\"description\":\"Name can be used to reference this request in a pod.spec.containers[].resources.claims entry and in a constraint of the claim.\\n\\nMust be a DNS label and unique among all DeviceRequests in a ResourceClaim.\",\"type\":\"string\"},\"selectors\":{\"description\":\"Selectors define criteria which must be satisfied by a specific device in order for that device to be considered for this request. All selectors must be satisfied for a device to be considered.\\n\\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.\",\"items\":{\"allOf\":[{\"description\":\"DeviceSelector must have exactly one field set.\",\"properties\":{\"cel\":{\"allOf\":[{\"description\":\"CELDeviceSelector contains a CEL expression for selecting a device.\",\"properties\":{\"expression\":{\"default\":\"\",\"description\":\"Expression is a CEL expression which evaluates a single device. It must evaluate to true when the device under consideration satisfies the desired criteria, and false when it does not. Any other result is an error and causes allocation of devices to abort.\\n\\nThe expression's input is an object named \\\"device\\\", which carries the following properties:\\n - driver (string): the name of the driver which defines this device.\\n - attributes (map[string]object): the device's attributes, grouped by prefix\\n   (e.g. device.attributes[\\\"dra.example.com\\\"] evaluates to an object with all\\n   of the attributes which were prefixed by \\\"dra.example.com\\\".\\n - capacity (map[string]object): the device's capacities, grouped by prefix.\\n\\nExample: Consider a device with driver=\\\"dra.example.com\\\", which exposes two attributes named \\\"model\\\" and \\\"ext.example.com/family\\\" and which exposes one capacity named \\\"modules\\\". This input to this expression would have the following fields:\\n\\n    device.driver\\n    device.attributes[\\\"dra.example.com\\\"].model\\n    device.attributes[\\\"ext.example.com\\\"].family\\n    device.capacity[\\\"dra.example.com\\\"].modules\\n\\nThe device.driver field can be used to check for a specific driver, either as a high-level precondition (i.e. you only want to consider devices from this driver) or as part of a multi-clause expression that is meant to consider devices from different drivers.\\n\\nThe value type of each attribute is defined by the device definition, and users who write these expressions must consult the documentation for their specific drivers. The value type of each capacity is Quantity.\\n\\nIf an unknown prefix is used as a lookup in either device.attributes or device.capacity, an empty map will be returned. Any reference to an unknown field will cause an evaluation error and allocation to abort.\\n\\nA robust expression should check for the existence of attributes before referencing them.\\n\\nFor ease of use, the cel.bind() function is enabled, and can be used to simplify expressions that access multiple attributes with the same domain. For example:\\n\\n    cel.bind(dra, device.attributes[\\\"dra.example.com\\\"], dra.someBool \\u0026\\u0026 dra.anotherBool)\\n\\nThe length of the expression must be smaller or equal to 10 Ki. The cost of evaluating it is also limited based on the estimated number of logical steps.\",\"type\":\"string\"}},\"required\":[\"expression\"],\"type\":\"object\"}],\"description\":\"CEL contains a CEL expression for selecting a device.\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"},\"tolerations\":{\"description\":\"If specified, the request's tolerations.\\n\\nTolerations for NoSchedule are required to allocate a device which has a taint with that effect. The same applies to NoExecute.\\n\\nIn addition, should any of the allocated devices get tainted with NoExecute after allocation and that effect is not tolerated, then all pods consuming the ResourceClaim get deleted to evict them. The scheduler will not let new pods reserve the claim while it has these tainted devices. Once all pods are evicted, the claim will get deallocated.\\n\\nThe maximum number of tolerations is 16.\\n\\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.\\n\\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.\",\"items\":{\"allOf\":[{\"description\":\"The ResourceClaim this DeviceToleration is attached to tolerates any taint that matches the triple \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\"properties\":{\"effect\":{\"description\":\"Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule and NoExecute.\",\"type\":\"string\"},\"key\":{\"description\":\"Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys. Must be a label name.\",\"type\":\"string\"},\"operator\":{\"default\":\"Equal\",\"description\":\"Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a ResourceClaim can tolerate all taints of a particular category.\",\"type\":\"string\"},\"tolerationSeconds\":{\"description\":\"TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system. If larger than zero, the time when the pod needs to be evicted is calculated as \\u003ctime when taint was adedd\\u003e + \\u003ctoleration seconds\\u003e.\",\"format\":\"int64\",\"type\":\"integer\"},\"value\":{\"description\":\"Value is the taint value the toleration matches to. If the operator is Exists, the value must be empty, otherwise just a regular string. Must be a label value.\",\"type\":\"string\"}},\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"required\":[\"name\"],\"type\":\"object\"}],\"default\":{}},\"type\":\"array\",\"x-kubernetes-list-type\":\"atomic\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"Devices defines how to request devices.\"}},\"type\":\"object\"}],\"default\":{},\"description\":\"Spec describes what is being requested and how to configure it. The spec is immutable.\"}},\"required\":[\"spec\"],\"type\":\"object\",\"x-kubernetes-group-version-kind\":[{\"group\":\"resource.k8s.io\",\"kind\":\"ResourceClaim\",\"version\":\"v1beta1\"}]}"}}