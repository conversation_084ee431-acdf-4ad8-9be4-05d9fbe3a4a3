{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Output", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "lagoon-logging", "displayName": "Lagoon Logging", "status": "ignored", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "credential_id": "00000000-0000-0000-0000-000000000000", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "user_id": "00000000-0000-0000-0000-000000000000", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": "0001-01-01T00:00:00Z", "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Uncategorized"}, "subCategory": "Uncategorized", "metadata": {"isAnnotation": false, "primaryColor": "#00B39F", "secondaryColor": "#00D3A9", "shape": "circle", "source_uri": "https://github.com/uselagoon/lagoon-charts/releases/download/lagoon-logging-0.87.0/lagoon-logging-0.87.0.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 32 32\" fill=\"none\">\n<g xmlns=\"http://www.w3.org/2000/svg\" clip-path=\"url(#clip0_36_80)\">\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.4632 7.69351V15.2015L22.9702 11.4346L16.4632 7.69351Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.4632 16.7705V24.3157L23.0307 20.5607L16.4632 16.7705Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.5274 15.1502V7.75632L9.10194 11.4416L15.5274 15.1502Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M2.63699 24.2878C3.89756 26.3157 5.60178 28.031 7.62134 29.3047V21.4033L2.63699 24.2878Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.5274 24.2785V16.8264L9.08579 20.556L15.5274 24.2785Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.55965 28.8344L15.0829 25.1049L8.55965 21.3335V28.8344Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4753 28.8742V21.3848L16.9615 25.1096L23.4753 28.8742Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M29.852 23.4194C30.9655 21.341 31.5949 19.0378 31.6935 16.6819L24.9119 20.5651L29.852 23.4194Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M24.4136 19.7691L30.96 16.0256L24.4136 12.2634V19.7691Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4755 10.6455V3.20041L16.9919 6.91827L23.4755 10.6455Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4754 19.7364V12.2239L16.9779 15.986L23.4754 19.7364Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.55965 12.2099V19.7784L15.1061 15.9882L8.55965 12.2099Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.5274 0.285706C13.1176 0.353534 10.756 0.977397 8.6271 2.10855L15.5274 6.06621V0.285706Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.55965 3.1492V10.6734L15.1107 6.91597L8.55965 3.1492Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.62134 2.69299C5.60228 3.96735 3.89818 5.6826 2.63699 7.7099L7.62134 10.5873V2.69299Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M23.4335 2.14811C21.2869 0.992986 18.9001 0.355226 16.4632 0.285706V6.14069L23.4335 2.14811Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M0.285713 16.5517C0.367085 18.9754 1.01023 21.3471 2.16447 23.4799L7.21396 20.5559L0.285713 16.5517Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M2.16447 8.51996C1.01384 10.6433 0.370833 13.0043 0.285713 15.4178L7.22097 11.4393L2.16447 8.51996Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.61544 29.8822C10.7469 31.0189 13.1128 31.6461 15.5274 31.7143V25.9291L8.61544 29.8822Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M29.3675 7.73539C28.1143 5.71396 26.4208 4.00147 24.4136 2.72543V10.5987L29.3675 7.73539Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M24.4136 29.2791C26.4312 27.994 28.1314 26.2684 29.3863 24.2321L24.4136 21.3591V29.2791Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M31.7143 15.3738C31.6251 12.9835 30.9879 10.6458 29.8518 8.54102L24.8441 11.4325L31.7143 15.3738Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.4632 31.7143C18.8725 31.6467 21.2333 31.0229 23.3613 29.8914L16.4632 25.8942V31.7143Z\" fill=\"white\"></path>\n<path xmlns=\"http://www.w3.org/2000/svg\" d=\"M7.62141 19.711V12.2892L1.17738 15.9838L7.62141 19.711Z\" fill=\"white\" fill-opacity=\"0.8\"></path>\n</g>\n<defs xmlns=\"http://www.w3.org/2000/svg\">\n<clipPath xmlns=\"http://www.w3.org/2000/svg\" id=\"clip0_36_80\">\n<rect xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" fill=\"white\"></rect>\n</clipPath>\n</defs>\n</svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 32 32\" fill=\"none\"><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.405 8.732v6.57l5.694-3.297-5.694-3.273Zm0 7.942v6.602l5.747-3.285-5.747-3.317Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.586 15.256v-6.47l-5.622 3.225 5.622 3.245ZM4.307 23.252a13.809 13.809 0 0 0 4.362 4.39v-6.914l-4.362 2.524Zm11.279-.008v-6.52L9.95 19.985l5.636 3.258Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"m9.49 27.23 5.707-3.263-5.707-3.3v6.563Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M22.54 27.265v-6.553l-5.699 3.259 5.7 3.294Zm5.58-4.773a13.697 13.697 0 0 0 1.612-5.895l-5.934 3.397 4.323 2.498Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"m23.362 19.298 5.728-3.276-5.728-3.291v6.567Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M22.541 11.315V4.8l-5.673 3.253 5.673 3.262Zm0 7.955v-6.574l-5.685 3.292 5.685 3.281Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.49 12.684v6.622l5.728-3.316-5.728-3.306Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M15.586 2.25a13.69 13.69 0 0 0-6.037 1.595l6.037 3.463V2.25Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.49 4.756v6.583l5.732-3.288L9.49 4.756Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.669 4.356a13.83 13.83 0 0 0-4.362 4.39l4.362 2.518V4.356Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M22.504 3.88a13.695 13.695 0 0 0-6.099-1.63v5.123l6.1-3.493ZM2.25 16.483c.071 2.12.634 4.196 1.644 6.062l4.418-2.559-6.062-3.503Zm1.644-7.028a13.68 13.68 0 0 0-1.644 6.036l6.068-3.482-4.424-2.554Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M9.539 28.147a13.673 13.673 0 0 0 6.047 1.603v-5.062L9.54 28.147Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M27.697 8.768a13.83 13.83 0 0 0-4.335-4.383v6.889l4.335-2.506ZM23.362 27.62a13.851 13.851 0 0 0 4.351-4.417l-4.351-2.514v6.93Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M29.75 15.452a13.659 13.659 0 0 0-1.63-5.979l-4.381 2.53 6.011 3.45Z\" fill=\"#fff\" fill-opacity=\".8\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M16.405 29.75a13.673 13.673 0 0 0 6.036-1.595l-6.036-3.498v5.093Z\" fill=\"#fff\"></path><path xmlns=\"http://www.w3.org/2000/svg\" d=\"M8.669 19.247v-6.494L3.03 15.986l5.639 3.261Z\" fill=\"#fff\" fill-opacity=\".8\"></path></svg>"}, "model": {"version": "0.87.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#00B39F", "secondaryColor": "#00D3A9", "shape": "circle", "svgColor": "<svg width=\"18\" height=\"18\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_36_80)\">\n<path d=\"M16.4632 7.69351V15.2015L22.9702 11.4346L16.4632 7.69351Z\" fill=\"white\"/>\n<path d=\"M16.4632 16.7705V24.3157L23.0307 20.5607L16.4632 16.7705Z\" fill=\"white\"/>\n<path d=\"M15.5274 15.1502V7.75632L9.10194 11.4416L15.5274 15.1502Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M2.63699 24.2878C3.89756 26.3157 5.60178 28.031 7.62134 29.3047V21.4033L2.63699 24.2878Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M15.5274 24.2785V16.8264L9.08579 20.556L15.5274 24.2785Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M8.55965 28.8344L15.0829 25.1049L8.55965 21.3335V28.8344Z\" fill=\"white\"/>\n<path d=\"M23.4753 28.8742V21.3848L16.9615 25.1096L23.4753 28.8742Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M29.852 23.4194C30.9655 21.341 31.5949 19.0378 31.6935 16.6819L24.9119 20.5651L29.852 23.4194Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M24.4136 19.7691L30.96 16.0256L24.4136 12.2634V19.7691Z\" fill=\"white\"/>\n<path d=\"M23.4755 10.6455V3.20041L16.9919 6.91827L23.4755 10.6455Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M23.4754 19.7364V12.2239L16.9779 15.986L23.4754 19.7364Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M8.55965 12.2099V19.7784L15.1061 15.9882L8.55965 12.2099Z\" fill=\"white\"/>\n<path d=\"M15.5274 0.285706C13.1176 0.353534 10.756 0.977397 8.6271 2.10855L15.5274 6.06621V0.285706Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M8.55965 3.1492V10.6734L15.1107 6.91597L8.55965 3.1492Z\" fill=\"white\"/>\n<path d=\"M7.62134 2.69299C5.60228 3.96735 3.89818 5.6826 2.63699 7.7099L7.62134 10.5873V2.69299Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M23.4335 2.14811C21.2869 0.992986 18.9001 0.355226 16.4632 0.285706V6.14069L23.4335 2.14811Z\" fill=\"white\"/>\n<path d=\"M0.285713 16.5517C0.367085 18.9754 1.01023 21.3471 2.16447 23.4799L7.21396 20.5559L0.285713 16.5517Z\" fill=\"white\"/>\n<path d=\"M2.16447 8.51996C1.01384 10.6433 0.370833 13.0043 0.285713 15.4178L7.22097 11.4393L2.16447 8.51996Z\" fill=\"white\"/>\n<path d=\"M8.61544 29.8822C10.7469 31.0189 13.1128 31.6461 15.5274 31.7143V25.9291L8.61544 29.8822Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M29.3675 7.73539C28.1143 5.71396 26.4208 4.00147 24.4136 2.72543V10.5987L29.3675 7.73539Z\" fill=\"white\"/>\n<path d=\"M24.4136 29.2791C26.4312 27.994 28.1314 26.2684 29.3863 24.2321L24.4136 21.3591V29.2791Z\" fill=\"white\"/>\n<path d=\"M31.7143 15.3738C31.6251 12.9835 30.9879 10.6458 29.8518 8.54102L24.8441 11.4325L31.7143 15.3738Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n<path d=\"M16.4632 31.7143C18.8725 31.6467 21.2333 31.0229 23.3613 29.8914L16.4632 25.8942V31.7143Z\" fill=\"white\"/>\n<path d=\"M7.62141 19.711V12.2892L1.17738 15.9838L7.62141 19.711Z\" fill=\"white\" fill-opacity=\"0.8\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_36_80\">\n<rect width=\"32\" height=\"32\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>", "svgComplete": "", "svgWhite": "<svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M16.405 8.732v6.57l5.694-3.297-5.694-3.273Zm0 7.942v6.602l5.747-3.285-5.747-3.317Z\" fill=\"#fff\"/><path d=\"M15.586 15.256v-6.47l-5.622 3.225 5.622 3.245ZM4.307 23.252a13.809 13.809 0 0 0 4.362 4.39v-6.914l-4.362 2.524Zm11.279-.008v-6.52L9.95 19.985l5.636 3.258Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"m9.49 27.23 5.707-3.263-5.707-3.3v6.563Z\" fill=\"#fff\"/><path d=\"M22.54 27.265v-6.553l-5.699 3.259 5.7 3.294Zm5.58-4.773a13.697 13.697 0 0 0 1.612-5.895l-5.934 3.397 4.323 2.498Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"m23.362 19.298 5.728-3.276-5.728-3.291v6.567Z\" fill=\"#fff\"/><path d=\"M22.541 11.315V4.8l-5.673 3.253 5.673 3.262Zm0 7.955v-6.574l-5.685 3.292 5.685 3.281Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M9.49 12.684v6.622l5.728-3.316-5.728-3.306Z\" fill=\"#fff\"/><path d=\"M15.586 2.25a13.69 13.69 0 0 0-6.037 1.595l6.037 3.463V2.25Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M9.49 4.756v6.583l5.732-3.288L9.49 4.756Z\" fill=\"#fff\"/><path d=\"M8.669 4.356a13.83 13.83 0 0 0-4.362 4.39l4.362 2.518V4.356Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M22.504 3.88a13.695 13.695 0 0 0-6.099-1.63v5.123l6.1-3.493ZM2.25 16.483c.071 2.12.634 4.196 1.644 6.062l4.418-2.559-6.062-3.503Zm1.644-7.028a13.68 13.68 0 0 0-1.644 6.036l6.068-3.482-4.424-2.554Z\" fill=\"#fff\"/><path d=\"M9.539 28.147a13.673 13.673 0 0 0 6.047 1.603v-5.062L9.54 28.147Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M27.697 8.768a13.83 13.83 0 0 0-4.335-4.383v6.889l4.335-2.506ZM23.362 27.62a13.851 13.851 0 0 0 4.351-4.417l-4.351-2.514v6.93Z\" fill=\"#fff\"/><path d=\"M29.75 15.452a13.659 13.659 0 0 0-1.63-5.979l-4.381 2.53 6.011 3.45Z\" fill=\"#fff\" fill-opacity=\".8\"/><path d=\"M16.405 29.75a13.673 13.673 0 0 0 6.036-1.595l-6.036-3.498v5.093Z\" fill=\"#fff\"/><path d=\"M8.669 19.247v-6.494L3.03 15.986l5.639 3.261Z\" fill=\"#fff\" fill-opacity=\".8\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/uselagoon/lagoon-charts/releases/download/lagoon-logging-0.87.0/lagoon-logging-0.87.0.tgz"}, "configuration": null, "component": {"version": "logging.banzaicloud.io/v1alpha1", "kind": "Output", "schema": "{\n \"properties\": {\n  \"spec\": {\n   \"properties\": {\n    \"awsElasticsearch\": {\n     \"properties\": {\n      \"api_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"application_name\": {\n       \"type\": \"string\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"bulk_message_request_threshold\": {\n       \"type\": \"string\"\n      },\n      \"ca_file\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_cert\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_key_pass\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"compression_level\": {\n       \"type\": \"string\"\n      },\n      \"content_type\": {\n       \"type\": \"string\"\n      },\n      \"custom_headers\": {\n       \"type\": \"string\"\n      },\n      \"customize_template\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_enable\": {\n       \"type\": \"boolean\"\n      },\n      \"data_stream_ilm_name\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_ilm_policy\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_ilm_policy_overwrite\": {\n       \"type\": \"boolean\"\n      },\n      \"data_stream_name\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_template_name\": {\n       \"type\": \"string\"\n      },\n      \"default_elasticsearch_version\": {\n       \"type\": \"string\"\n      },\n      \"deflector_alias\": {\n       \"type\": \"string\"\n      },\n      \"enable_ilm\": {\n       \"type\": \"boolean\"\n      },\n      \"endpoint\": {\n       \"properties\": {\n        \"access_key_id\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"assume_role_arn\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"assume_role_session_name\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"assume_role_web_identity_token_file\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"ecs_container_credentials_relative_uri\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"region\": {\n         \"type\": \"string\"\n        },\n        \"secret_access_key\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"sts_credentials_region\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"url\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"exception_backup\": {\n       \"type\": \"boolean\"\n      },\n      \"fail_on_detecting_es_version_retry_exceed\": {\n       \"type\": \"boolean\"\n      },\n      \"fail_on_putting_template_retry_exceed\": {\n       \"type\": \"boolean\"\n      },\n      \"flatten_hashes\": {\n       \"type\": \"boolean\"\n      },\n      \"flatten_hashes_separator\": {\n       \"type\": \"string\"\n      },\n      \"flush_interval\": {\n       \"type\": \"string\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"host\": {\n       \"type\": \"string\"\n      },\n      \"hosts\": {\n       \"type\": \"string\"\n      },\n      \"http_backend\": {\n       \"type\": \"string\"\n      },\n      \"id_key\": {\n       \"type\": \"string\"\n      },\n      \"ignore_exceptions\": {\n       \"type\": \"string\"\n      },\n      \"ilm_policy\": {\n       \"type\": \"string\"\n      },\n      \"ilm_policy_id\": {\n       \"type\": \"string\"\n      },\n      \"ilm_policy_overwrite\": {\n       \"type\": \"boolean\"\n      },\n      \"include_index_in_url\": {\n       \"type\": \"boolean\"\n      },\n      \"include_tag_key\": {\n       \"type\": \"boolean\"\n      },\n      \"include_timestamp\": {\n       \"type\": \"boolean\"\n      },\n      \"index_date_pattern\": {\n       \"type\": \"string\"\n      },\n      \"index_name\": {\n       \"type\": \"string\"\n      },\n      \"index_prefix\": {\n       \"type\": \"string\"\n      },\n      \"log_es_400_reason\": {\n       \"type\": \"boolean\"\n      },\n      \"logstash_dateformat\": {\n       \"type\": \"string\"\n      },\n      \"logstash_format\": {\n       \"type\": \"boolean\"\n      },\n      \"logstash_prefix\": {\n       \"type\": \"string\"\n      },\n      \"logstash_prefix_separator\": {\n       \"type\": \"string\"\n      },\n      \"max_retry_get_es_version\": {\n       \"type\": \"string\"\n      },\n      \"max_retry_putting_template\": {\n       \"type\": \"string\"\n      },\n      \"password\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"pipeline\": {\n       \"type\": \"string\"\n      },\n      \"port\": {\n       \"type\": \"integer\"\n      },\n      \"prefer_oj_serializer\": {\n       \"type\": \"boolean\"\n      },\n      \"reconnect_on_error\": {\n       \"type\": \"boolean\"\n      },\n      \"reload_after\": {\n       \"type\": \"string\"\n      },\n      \"reload_connections\": {\n       \"type\": \"boolean\"\n      },\n      \"reload_on_failure\": {\n       \"type\": \"boolean\"\n      },\n      \"remove_keys\": {\n       \"type\": \"string\"\n      },\n      \"remove_keys_on_update\": {\n       \"type\": \"string\"\n      },\n      \"remove_keys_on_update_key\": {\n       \"type\": \"string\"\n      },\n      \"request_timeout\": {\n       \"type\": \"string\"\n      },\n      \"resurrect_after\": {\n       \"type\": \"string\"\n      },\n      \"retry_tag\": {\n       \"type\": \"string\"\n      },\n      \"rollover_index\": {\n       \"type\": \"boolean\"\n      },\n      \"routing_key\": {\n       \"type\": \"string\"\n      },\n      \"scheme\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"sniffer_class_name\": {\n       \"type\": \"string\"\n      },\n      \"ssl_max_version\": {\n       \"type\": \"string\"\n      },\n      \"ssl_min_version\": {\n       \"type\": \"string\"\n      },\n      \"ssl_verify\": {\n       \"type\": \"boolean\"\n      },\n      \"ssl_version\": {\n       \"type\": \"string\"\n      },\n      \"suppress_doc_wrap\": {\n       \"type\": \"boolean\"\n      },\n      \"suppress_type_name\": {\n       \"type\": \"boolean\"\n      },\n      \"tag_key\": {\n       \"type\": \"string\"\n      },\n      \"target_index_key\": {\n       \"type\": \"string\"\n      },\n      \"target_type_key\": {\n       \"type\": \"string\"\n      },\n      \"template_file\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"template_name\": {\n       \"type\": \"string\"\n      },\n      \"template_overwrite\": {\n       \"type\": \"boolean\"\n      },\n      \"templates\": {\n       \"type\": \"string\"\n      },\n      \"time_key\": {\n       \"type\": \"string\"\n      },\n      \"time_key_format\": {\n       \"type\": \"string\"\n      },\n      \"time_parse_error_tag\": {\n       \"type\": \"string\"\n      },\n      \"time_precision\": {\n       \"type\": \"string\"\n      },\n      \"type_name\": {\n       \"type\": \"string\"\n      },\n      \"unrecoverable_error_types\": {\n       \"type\": \"string\"\n      },\n      \"use_legacy_template\": {\n       \"type\": \"boolean\"\n      },\n      \"user\": {\n       \"type\": \"string\"\n      },\n      \"utc_index\": {\n       \"type\": \"boolean\"\n      },\n      \"validate_client_version\": {\n       \"type\": \"boolean\"\n      },\n      \"verify_es_version_at_startup\": {\n       \"type\": \"boolean\"\n      },\n      \"with_transporter_log\": {\n       \"type\": \"boolean\"\n      },\n      \"write_operation\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"azurestorage\": {\n     \"properties\": {\n      \"auto_create_container\": {\n       \"type\": \"boolean\"\n      },\n      \"azure_cloud\": {\n       \"type\": \"string\"\n      },\n      \"azure_container\": {\n       \"type\": \"string\"\n      },\n      \"azure_imds_api_version\": {\n       \"type\": \"string\"\n      },\n      \"azure_object_key_format\": {\n       \"type\": \"string\"\n      },\n      \"azure_storage_access_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"azure_storage_account\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"azure_storage_sas_token\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"format\": {\n       \"type\": \"string\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"azure_container\",\n      \"azure_storage_account\"\n     ],\n     \"type\": \"object\"\n    },\n    \"cloudwatch\": {\n     \"properties\": {\n      \"auto_create_stream\": {\n       \"type\": \"boolean\"\n      },\n      \"aws_instance_profile_credentials_retries\": {\n       \"type\": \"integer\"\n      },\n      \"aws_key_id\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"aws_sec_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"aws_sts_role_arn\": {\n       \"type\": \"string\"\n      },\n      \"aws_sts_session_name\": {\n       \"type\": \"string\"\n      },\n      \"aws_use_sts\": {\n       \"type\": \"boolean\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"concurrency\": {\n       \"type\": \"integer\"\n      },\n      \"endpoint\": {\n       \"type\": \"string\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"http_proxy\": {\n       \"type\": \"string\"\n      },\n      \"include_time_key\": {\n       \"type\": \"boolean\"\n      },\n      \"json_handler\": {\n       \"type\": \"string\"\n      },\n      \"localtime\": {\n       \"type\": \"boolean\"\n      },\n      \"log_group_aws_tags\": {\n       \"type\": \"string\"\n      },\n      \"log_group_aws_tags_key\": {\n       \"type\": \"string\"\n      },\n      \"log_group_name\": {\n       \"type\": \"string\"\n      },\n      \"log_group_name_key\": {\n       \"type\": \"string\"\n      },\n      \"log_rejected_request\": {\n       \"type\": \"string\"\n      },\n      \"log_stream_name\": {\n       \"type\": \"string\"\n      },\n      \"log_stream_name_key\": {\n       \"type\": \"string\"\n      },\n      \"max_events_per_batch\": {\n       \"type\": \"integer\"\n      },\n      \"max_message_length\": {\n       \"type\": \"integer\"\n      },\n      \"message_keys\": {\n       \"type\": \"string\"\n      },\n      \"put_log_events_disable_retry_limit\": {\n       \"type\": \"boolean\"\n      },\n      \"put_log_events_retry_limit\": {\n       \"type\": \"integer\"\n      },\n      \"put_log_events_retry_wait\": {\n       \"type\": \"string\"\n      },\n      \"region\": {\n       \"type\": \"string\"\n      },\n      \"remove_log_group_aws_tags_key\": {\n       \"type\": \"string\"\n      },\n      \"remove_log_group_name_key\": {\n       \"type\": \"string\"\n      },\n      \"remove_log_stream_name_key\": {\n       \"type\": \"string\"\n      },\n      \"remove_retention_in_days\": {\n       \"type\": \"string\"\n      },\n      \"retention_in_days\": {\n       \"type\": \"string\"\n      },\n      \"retention_in_days_key\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"use_tag_as_group\": {\n       \"type\": \"boolean\"\n      },\n      \"use_tag_as_stream\": {\n       \"type\": \"boolean\"\n      }\n     },\n     \"required\": [\n      \"region\"\n     ],\n     \"type\": \"object\"\n    },\n    \"datadog\": {\n     \"properties\": {\n      \"api_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"compression_level\": {\n       \"type\": \"string\"\n      },\n      \"dd_hostname\": {\n       \"type\": \"string\"\n      },\n      \"dd_source\": {\n       \"type\": \"string\"\n      },\n      \"dd_sourcecategory\": {\n       \"type\": \"string\"\n      },\n      \"dd_tags\": {\n       \"type\": \"string\"\n      },\n      \"host\": {\n       \"type\": \"string\"\n      },\n      \"include_tag_key\": {\n       \"type\": \"boolean\"\n      },\n      \"max_backoff\": {\n       \"type\": \"string\"\n      },\n      \"max_retries\": {\n       \"type\": \"string\"\n      },\n      \"no_ssl_validation\": {\n       \"type\": \"boolean\"\n      },\n      \"port\": {\n       \"type\": \"string\"\n      },\n      \"service\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"ssl_port\": {\n       \"type\": \"string\"\n      },\n      \"tag_key\": {\n       \"type\": \"string\"\n      },\n      \"timestamp_key\": {\n       \"type\": \"string\"\n      },\n      \"use_compression\": {\n       \"type\": \"boolean\"\n      },\n      \"use_http\": {\n       \"type\": \"boolean\"\n      },\n      \"use_json\": {\n       \"type\": \"boolean\"\n      },\n      \"use_ssl\": {\n       \"type\": \"boolean\"\n      }\n     },\n     \"required\": [\n      \"api_key\"\n     ],\n     \"type\": \"object\"\n    },\n    \"elasticsearch\": {\n     \"properties\": {\n      \"api_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"application_name\": {\n       \"type\": \"string\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"bulk_message_request_threshold\": {\n       \"type\": \"string\"\n      },\n      \"ca_file\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_cert\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_key_pass\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"compression_level\": {\n       \"type\": \"string\"\n      },\n      \"content_type\": {\n       \"type\": \"string\"\n      },\n      \"custom_headers\": {\n       \"type\": \"string\"\n      },\n      \"customize_template\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_enable\": {\n       \"type\": \"boolean\"\n      },\n      \"data_stream_ilm_name\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_ilm_policy\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_ilm_policy_overwrite\": {\n       \"type\": \"boolean\"\n      },\n      \"data_stream_name\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_template_name\": {\n       \"type\": \"string\"\n      },\n      \"default_elasticsearch_version\": {\n       \"type\": \"string\"\n      },\n      \"deflector_alias\": {\n       \"type\": \"string\"\n      },\n      \"enable_ilm\": {\n       \"type\": \"boolean\"\n      },\n      \"exception_backup\": {\n       \"type\": \"boolean\"\n      },\n      \"fail_on_detecting_es_version_retry_exceed\": {\n       \"type\": \"boolean\"\n      },\n      \"fail_on_putting_template_retry_exceed\": {\n       \"type\": \"boolean\"\n      },\n      \"flatten_hashes\": {\n       \"type\": \"boolean\"\n      },\n      \"flatten_hashes_separator\": {\n       \"type\": \"string\"\n      },\n      \"host\": {\n       \"type\": \"string\"\n      },\n      \"hosts\": {\n       \"type\": \"string\"\n      },\n      \"http_backend\": {\n       \"type\": \"string\"\n      },\n      \"id_key\": {\n       \"type\": \"string\"\n      },\n      \"ignore_exceptions\": {\n       \"type\": \"string\"\n      },\n      \"ilm_policy\": {\n       \"type\": \"string\"\n      },\n      \"ilm_policy_id\": {\n       \"type\": \"string\"\n      },\n      \"ilm_policy_overwrite\": {\n       \"type\": \"boolean\"\n      },\n      \"include_index_in_url\": {\n       \"type\": \"boolean\"\n      },\n      \"include_tag_key\": {\n       \"type\": \"boolean\"\n      },\n      \"include_timestamp\": {\n       \"type\": \"boolean\"\n      },\n      \"index_date_pattern\": {\n       \"type\": \"string\"\n      },\n      \"index_name\": {\n       \"type\": \"string\"\n      },\n      \"index_prefix\": {\n       \"type\": \"string\"\n      },\n      \"log_es_400_reason\": {\n       \"type\": \"boolean\"\n      },\n      \"logstash_dateformat\": {\n       \"type\": \"string\"\n      },\n      \"logstash_format\": {\n       \"type\": \"boolean\"\n      },\n      \"logstash_prefix\": {\n       \"type\": \"string\"\n      },\n      \"logstash_prefix_separator\": {\n       \"type\": \"string\"\n      },\n      \"max_retry_get_es_version\": {\n       \"type\": \"string\"\n      },\n      \"max_retry_putting_template\": {\n       \"type\": \"string\"\n      },\n      \"password\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"pipeline\": {\n       \"type\": \"string\"\n      },\n      \"port\": {\n       \"type\": \"integer\"\n      },\n      \"prefer_oj_serializer\": {\n       \"type\": \"boolean\"\n      },\n      \"reconnect_on_error\": {\n       \"type\": \"boolean\"\n      },\n      \"reload_after\": {\n       \"type\": \"string\"\n      },\n      \"reload_connections\": {\n       \"type\": \"boolean\"\n      },\n      \"reload_on_failure\": {\n       \"type\": \"boolean\"\n      },\n      \"remove_keys\": {\n       \"type\": \"string\"\n      },\n      \"remove_keys_on_update\": {\n       \"type\": \"string\"\n      },\n      \"remove_keys_on_update_key\": {\n       \"type\": \"string\"\n      },\n      \"request_timeout\": {\n       \"type\": \"string\"\n      },\n      \"resurrect_after\": {\n       \"type\": \"string\"\n      },\n      \"retry_tag\": {\n       \"type\": \"string\"\n      },\n      \"rollover_index\": {\n       \"type\": \"boolean\"\n      },\n      \"routing_key\": {\n       \"type\": \"string\"\n      },\n      \"scheme\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"sniffer_class_name\": {\n       \"type\": \"string\"\n      },\n      \"ssl_max_version\": {\n       \"type\": \"string\"\n      },\n      \"ssl_min_version\": {\n       \"type\": \"string\"\n      },\n      \"ssl_verify\": {\n       \"type\": \"boolean\"\n      },\n      \"ssl_version\": {\n       \"type\": \"string\"\n      },\n      \"suppress_doc_wrap\": {\n       \"type\": \"boolean\"\n      },\n      \"suppress_type_name\": {\n       \"type\": \"boolean\"\n      },\n      \"tag_key\": {\n       \"type\": \"string\"\n      },\n      \"target_index_key\": {\n       \"type\": \"string\"\n      },\n      \"target_type_key\": {\n       \"type\": \"string\"\n      },\n      \"template_file\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"template_name\": {\n       \"type\": \"string\"\n      },\n      \"template_overwrite\": {\n       \"type\": \"boolean\"\n      },\n      \"templates\": {\n       \"type\": \"string\"\n      },\n      \"time_key\": {\n       \"type\": \"string\"\n      },\n      \"time_key_format\": {\n       \"type\": \"string\"\n      },\n      \"time_parse_error_tag\": {\n       \"type\": \"string\"\n      },\n      \"time_precision\": {\n       \"type\": \"string\"\n      },\n      \"type_name\": {\n       \"type\": \"string\"\n      },\n      \"unrecoverable_error_types\": {\n       \"type\": \"string\"\n      },\n      \"use_legacy_template\": {\n       \"type\": \"boolean\"\n      },\n      \"user\": {\n       \"type\": \"string\"\n      },\n      \"utc_index\": {\n       \"type\": \"boolean\"\n      },\n      \"validate_client_version\": {\n       \"type\": \"boolean\"\n      },\n      \"verify_es_version_at_startup\": {\n       \"type\": \"boolean\"\n      },\n      \"with_transporter_log\": {\n       \"type\": \"boolean\"\n      },\n      \"write_operation\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"file\": {\n     \"properties\": {\n      \"add_path_suffix\": {\n       \"type\": \"boolean\"\n      },\n      \"append\": {\n       \"type\": \"boolean\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"compress\": {\n       \"type\": \"string\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"path_suffix\": {\n       \"type\": \"string\"\n      },\n      \"recompress\": {\n       \"type\": \"boolean\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"symlink_path\": {\n       \"type\": \"boolean\"\n      }\n     },\n     \"required\": [\n      \"path\"\n     ],\n     \"type\": \"object\"\n    },\n    \"forward\": {\n     \"properties\": {\n      \"ack_response_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"connect_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"dns_round_robin\": {\n       \"type\": \"boolean\"\n      },\n      \"expire_dns_cache\": {\n       \"type\": \"integer\"\n      },\n      \"hard_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"heartbeat_interval\": {\n       \"type\": \"integer\"\n      },\n      \"heartbeat_type\": {\n       \"type\": \"string\"\n      },\n      \"ignore_network_errors_at_startup\": {\n       \"type\": \"boolean\"\n      },\n      \"keepalive\": {\n       \"type\": \"boolean\"\n      },\n      \"keepalive_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"phi_failure_detector\": {\n       \"type\": \"boolean\"\n      },\n      \"phi_threshold\": {\n       \"type\": \"integer\"\n      },\n      \"recover_wait\": {\n       \"type\": \"integer\"\n      },\n      \"require_ack_response\": {\n       \"type\": \"boolean\"\n      },\n      \"security\": {\n       \"properties\": {\n        \"allow_anonymous_source\": {\n         \"type\": \"boolean\"\n        },\n        \"self_hostname\": {\n         \"type\": \"string\"\n        },\n        \"shared_key\": {\n         \"type\": \"string\"\n        },\n        \"user_auth\": {\n         \"type\": \"boolean\"\n        }\n       },\n       \"required\": [\n        \"self_hostname\",\n        \"shared_key\"\n       ],\n       \"type\": \"object\"\n      },\n      \"send_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"servers\": {\n       \"items\": {\n        \"properties\": {\n         \"host\": {\n          \"type\": \"string\"\n         },\n         \"name\": {\n          \"type\": \"string\"\n         },\n         \"password\": {\n          \"properties\": {\n           \"mountFrom\": {\n            \"properties\": {\n             \"secretKeyRef\": {\n              \"properties\": {\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"value\": {\n            \"type\": \"string\"\n           },\n           \"valueFrom\": {\n            \"properties\": {\n             \"secretKeyRef\": {\n              \"properties\": {\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"port\": {\n          \"type\": \"integer\"\n         },\n         \"shared_key\": {\n          \"properties\": {\n           \"mountFrom\": {\n            \"properties\": {\n             \"secretKeyRef\": {\n              \"properties\": {\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"value\": {\n            \"type\": \"string\"\n           },\n           \"valueFrom\": {\n            \"properties\": {\n             \"secretKeyRef\": {\n              \"properties\": {\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"standby\": {\n          \"type\": \"boolean\"\n         },\n         \"username\": {\n          \"properties\": {\n           \"mountFrom\": {\n            \"properties\": {\n             \"secretKeyRef\": {\n              \"properties\": {\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"value\": {\n            \"type\": \"string\"\n           },\n           \"valueFrom\": {\n            \"properties\": {\n             \"secretKeyRef\": {\n              \"properties\": {\n               \"key\": {\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"default\": \"\",\n                \"type\": \"string\"\n               },\n               \"optional\": {\n                \"type\": \"boolean\"\n               }\n              },\n              \"required\": [\n               \"key\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"weight\": {\n          \"type\": \"integer\"\n         }\n        },\n        \"required\": [\n         \"host\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"time_as_integer\": {\n       \"type\": \"boolean\"\n      },\n      \"tls_allow_self_signed_cert\": {\n       \"type\": \"boolean\"\n      },\n      \"tls_cert_logical_store_name\": {\n       \"type\": \"string\"\n      },\n      \"tls_cert_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"tls_cert_thumbprint\": {\n       \"type\": \"string\"\n      },\n      \"tls_cert_use_enterprise_store\": {\n       \"type\": \"boolean\"\n      },\n      \"tls_ciphers\": {\n       \"type\": \"string\"\n      },\n      \"tls_client_cert_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"tls_client_private_key_passphrase\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"tls_client_private_key_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"tls_insecure_mode\": {\n       \"type\": \"boolean\"\n      },\n      \"tls_verify_hostname\": {\n       \"type\": \"boolean\"\n      },\n      \"tls_version\": {\n       \"type\": \"string\"\n      },\n      \"transport\": {\n       \"type\": \"string\"\n      },\n      \"verify_connection_at_startup\": {\n       \"type\": \"boolean\"\n      }\n     },\n     \"required\": [\n      \"servers\"\n     ],\n     \"type\": \"object\"\n    },\n    \"gcs\": {\n     \"properties\": {\n      \"acl\": {\n       \"type\": \"string\"\n      },\n      \"auto_create_bucket\": {\n       \"type\": \"boolean\"\n      },\n      \"bucket\": {\n       \"type\": \"string\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_retries\": {\n       \"type\": \"integer\"\n      },\n      \"client_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"credentials_json\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"encryption_key\": {\n       \"type\": \"string\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"hex_random_length\": {\n       \"type\": \"integer\"\n      },\n      \"keyfile\": {\n       \"type\": \"string\"\n      },\n      \"object_key_format\": {\n       \"type\": \"string\"\n      },\n      \"object_metadata\": {\n       \"items\": {\n        \"properties\": {\n         \"key\": {\n          \"type\": \"string\"\n         },\n         \"value\": {\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"key\",\n         \"value\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      },\n      \"overwrite\": {\n       \"type\": \"boolean\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"project\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"storage_class\": {\n       \"type\": \"string\"\n      },\n      \"store_as\": {\n       \"type\": \"string\"\n      },\n      \"transcoding\": {\n       \"type\": \"boolean\"\n      }\n     },\n     \"required\": [\n      \"bucket\",\n      \"project\"\n     ],\n     \"type\": \"object\"\n    },\n    \"gelf\": {\n     \"properties\": {\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"host\": {\n       \"type\": \"string\"\n      },\n      \"max_bytes\": {\n       \"type\": \"integer\"\n      },\n      \"port\": {\n       \"type\": \"integer\"\n      },\n      \"protocol\": {\n       \"type\": \"string\"\n      },\n      \"tls\": {\n       \"type\": \"boolean\"\n      },\n      \"tls_options\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"required\": [\n      \"host\",\n      \"port\"\n     ],\n     \"type\": \"object\"\n    },\n    \"http\": {\n     \"properties\": {\n      \"auth\": {\n       \"properties\": {\n        \"password\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"username\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"required\": [\n        \"password\",\n        \"username\"\n       ],\n       \"type\": \"object\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"content_type\": {\n       \"type\": \"string\"\n      },\n      \"endpoint\": {\n       \"type\": \"string\"\n      },\n      \"error_response_as_unrecoverable\": {\n       \"type\": \"boolean\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"headers\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"object\"\n      },\n      \"http_method\": {\n       \"type\": \"string\"\n      },\n      \"json_array\": {\n       \"type\": \"boolean\"\n      },\n      \"open_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"proxy\": {\n       \"type\": \"string\"\n      },\n      \"read_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"retryable_response_codes\": {\n       \"items\": {\n        \"type\": \"integer\"\n       },\n       \"type\": \"array\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"ssl_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"tls_ca_cert_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"tls_ciphers\": {\n       \"type\": \"string\"\n      },\n      \"tls_client_cert_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"tls_private_key_passphrase\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"tls_private_key_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"tls_verify_mode\": {\n       \"type\": \"string\"\n      },\n      \"tls_version\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"endpoint\"\n     ],\n     \"type\": \"object\"\n    },\n    \"kafka\": {\n     \"properties\": {\n      \"ack_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"brokers\": {\n       \"type\": \"string\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_id\": {\n       \"type\": \"string\"\n      },\n      \"compression_codec\": {\n       \"type\": \"string\"\n      },\n      \"default_message_key\": {\n       \"type\": \"string\"\n      },\n      \"default_partition_key\": {\n       \"type\": \"string\"\n      },\n      \"default_topic\": {\n       \"type\": \"string\"\n      },\n      \"discard_kafka_delivery_failed\": {\n       \"type\": \"boolean\"\n      },\n      \"exclude_partion_key\": {\n       \"type\": \"boolean\"\n      },\n      \"exclude_topic_key\": {\n       \"type\": \"boolean\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"get_kafka_client_log\": {\n       \"type\": \"boolean\"\n      },\n      \"headers\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"object\"\n      },\n      \"headers_from_record\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"object\"\n      },\n      \"idempotent\": {\n       \"type\": \"boolean\"\n      },\n      \"kafka_agg_max_bytes\": {\n       \"type\": \"integer\"\n      },\n      \"kafka_agg_max_messages\": {\n       \"type\": \"integer\"\n      },\n      \"keytab\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"max_send_limit_bytes\": {\n       \"type\": \"integer\"\n      },\n      \"max_send_retries\": {\n       \"type\": \"integer\"\n      },\n      \"message_key_key\": {\n       \"type\": \"string\"\n      },\n      \"partition_key\": {\n       \"type\": \"string\"\n      },\n      \"partition_key_key\": {\n       \"type\": \"string\"\n      },\n      \"password\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"principal\": {\n       \"type\": \"string\"\n      },\n      \"required_acks\": {\n       \"type\": \"integer\"\n      },\n      \"sasl_over_ssl\": {\n       \"type\": \"boolean\"\n      },\n      \"scram_mechanism\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"ssl_ca_cert\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"ssl_ca_certs_from_system\": {\n       \"type\": \"boolean\"\n      },\n      \"ssl_client_cert\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"ssl_client_cert_chain\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"ssl_client_cert_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"ssl_verify_hostname\": {\n       \"type\": \"boolean\"\n      },\n      \"topic_key\": {\n       \"type\": \"string\"\n      },\n      \"use_default_for_unknown_topic\": {\n       \"type\": \"boolean\"\n      },\n      \"use_rdkafka\": {\n       \"type\": \"boolean\"\n      },\n      \"username\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"required\": [\n      \"brokers\",\n      \"format\"\n     ],\n     \"type\": \"object\"\n    },\n    \"kinesisStream\": {\n     \"properties\": {\n      \"assume_role_credentials\": {\n       \"properties\": {\n        \"duration_seconds\": {\n         \"type\": \"string\"\n        },\n        \"external_id\": {\n         \"type\": \"string\"\n        },\n        \"policy\": {\n         \"type\": \"string\"\n        },\n        \"role_arn\": {\n         \"type\": \"string\"\n        },\n        \"role_session_name\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"role_arn\",\n        \"role_session_name\"\n       ],\n       \"type\": \"object\"\n      },\n      \"aws_iam_retries\": {\n       \"type\": \"integer\"\n      },\n      \"aws_key_id\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"aws_sec_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"aws_ses_token\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"batch_request_max_count\": {\n       \"type\": \"integer\"\n      },\n      \"batch_request_max_size\": {\n       \"type\": \"integer\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"partition_key\": {\n       \"type\": \"string\"\n      },\n      \"process_credentials\": {\n       \"properties\": {\n        \"process\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"process\"\n       ],\n       \"type\": \"object\"\n      },\n      \"region\": {\n       \"type\": \"string\"\n      },\n      \"reset_backoff_if_success\": {\n       \"type\": \"boolean\"\n      },\n      \"retries_on_batch_request\": {\n       \"type\": \"integer\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"stream_name\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"stream_name\"\n     ],\n     \"type\": \"object\"\n    },\n    \"logdna\": {\n     \"properties\": {\n      \"api_key\": {\n       \"type\": \"string\"\n      },\n      \"app\": {\n       \"type\": \"string\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"hostname\": {\n       \"type\": \"string\"\n      },\n      \"ingester_domain\": {\n       \"type\": \"string\"\n      },\n      \"ingester_endpoint\": {\n       \"type\": \"string\"\n      },\n      \"request_timeout\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"tags\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"api_key\",\n      \"hostname\"\n     ],\n     \"type\": \"object\"\n    },\n    \"loggingRef\": {\n     \"type\": \"string\"\n    },\n    \"logz\": {\n     \"properties\": {\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"bulk_limit\": {\n       \"type\": \"integer\"\n      },\n      \"bulk_limit_warning_limit\": {\n       \"type\": \"integer\"\n      },\n      \"endpoint\": {\n       \"properties\": {\n        \"port\": {\n         \"type\": \"integer\"\n        },\n        \"token\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"url\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"gzip\": {\n       \"type\": \"boolean\"\n      },\n      \"http_idle_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"output_include_tags\": {\n       \"type\": \"boolean\"\n      },\n      \"output_include_time\": {\n       \"type\": \"boolean\"\n      },\n      \"retry_count\": {\n       \"type\": \"integer\"\n      },\n      \"retry_sleep\": {\n       \"type\": \"integer\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"endpoint\"\n     ],\n     \"type\": \"object\"\n    },\n    \"loki\": {\n     \"properties\": {\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"ca_cert\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"cert\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"configure_kubernetes_labels\": {\n       \"type\": \"boolean\"\n      },\n      \"drop_single_key\": {\n       \"type\": \"boolean\"\n      },\n      \"extra_labels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"object\"\n      },\n      \"extract_kubernetes_labels\": {\n       \"type\": \"boolean\"\n      },\n      \"include_thread_label\": {\n       \"type\": \"boolean\"\n      },\n      \"insecure_tls\": {\n       \"type\": \"boolean\"\n      },\n      \"key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"labels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"object\"\n      },\n      \"line_format\": {\n       \"type\": \"string\"\n      },\n      \"password\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"remove_keys\": {\n       \"items\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"tenant\": {\n       \"type\": \"string\"\n      },\n      \"url\": {\n       \"type\": \"string\"\n      },\n      \"username\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"newrelic\": {\n     \"properties\": {\n      \"api_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"base_uri\": {\n       \"type\": \"string\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"license_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"nullout\": {\n     \"properties\": {\n      \"never_flush\": {\n       \"type\": \"boolean\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"opensearch\": {\n     \"properties\": {\n      \"application_name\": {\n       \"type\": \"string\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"bulk_message_request_threshold\": {\n       \"type\": \"string\"\n      },\n      \"ca_file\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"catch_transport_exception_on_retry\": {\n       \"type\": \"boolean\"\n      },\n      \"client_cert\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_key_pass\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"compression_level\": {\n       \"type\": \"string\"\n      },\n      \"custom_headers\": {\n       \"type\": \"string\"\n      },\n      \"customize_template\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_enable\": {\n       \"type\": \"boolean\"\n      },\n      \"data_stream_name\": {\n       \"type\": \"string\"\n      },\n      \"data_stream_template_name\": {\n       \"type\": \"string\"\n      },\n      \"default_opensearch_version\": {\n       \"type\": \"integer\"\n      },\n      \"emit_error_for_missing_id\": {\n       \"type\": \"boolean\"\n      },\n      \"emit_error_label_event\": {\n       \"type\": \"boolean\"\n      },\n      \"endpoint\": {\n       \"properties\": {\n        \"access_key_id\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"assume_role_arn\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"assume_role_session_name\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"assume_role_web_identity_token_file\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"ecs_container_credentials_relative_uri\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"region\": {\n         \"type\": \"string\"\n        },\n        \"secret_access_key\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"sts_credentials_region\": {\n         \"properties\": {\n          \"mountFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"value\": {\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"properties\": {\n            \"secretKeyRef\": {\n             \"properties\": {\n              \"key\": {\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"url\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"url\"\n       ],\n       \"type\": \"object\"\n      },\n      \"exception_backup\": {\n       \"type\": \"boolean\"\n      },\n      \"fail_on_detecting_os_version_retry_exceed\": {\n       \"type\": \"boolean\"\n      },\n      \"fail_on_putting_template_retry_exceed\": {\n       \"type\": \"boolean\"\n      },\n      \"flatten_hashes\": {\n       \"type\": \"boolean\"\n      },\n      \"flatten_hashes_separator\": {\n       \"type\": \"string\"\n      },\n      \"host\": {\n       \"type\": \"string\"\n      },\n      \"hosts\": {\n       \"type\": \"string\"\n      },\n      \"http_backend\": {\n       \"type\": \"string\"\n      },\n      \"http_backend_excon_nonblock\": {\n       \"type\": \"boolean\"\n      },\n      \"id_key\": {\n       \"type\": \"string\"\n      },\n      \"ignore_exceptions\": {\n       \"type\": \"string\"\n      },\n      \"include_index_in_url\": {\n       \"type\": \"boolean\"\n      },\n      \"include_tag_key\": {\n       \"type\": \"boolean\"\n      },\n      \"include_timestamp\": {\n       \"type\": \"boolean\"\n      },\n      \"index_date_pattern\": {\n       \"type\": \"string\"\n      },\n      \"index_name\": {\n       \"type\": \"string\"\n      },\n      \"index_separator\": {\n       \"type\": \"string\"\n      },\n      \"log_os_400_reason\": {\n       \"type\": \"boolean\"\n      },\n      \"logstash_dateformat\": {\n       \"type\": \"string\"\n      },\n      \"logstash_format\": {\n       \"type\": \"boolean\"\n      },\n      \"logstash_prefix\": {\n       \"type\": \"string\"\n      },\n      \"logstash_prefix_separator\": {\n       \"type\": \"string\"\n      },\n      \"max_retry_get_os_version\": {\n       \"type\": \"integer\"\n      },\n      \"max_retry_putting_template\": {\n       \"type\": \"string\"\n      },\n      \"parent_key\": {\n       \"type\": \"string\"\n      },\n      \"password\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"pipeline\": {\n       \"type\": \"string\"\n      },\n      \"port\": {\n       \"type\": \"integer\"\n      },\n      \"prefer_oj_serializer\": {\n       \"type\": \"boolean\"\n      },\n      \"reconnect_on_error\": {\n       \"type\": \"boolean\"\n      },\n      \"reload_after\": {\n       \"type\": \"string\"\n      },\n      \"reload_connections\": {\n       \"type\": \"boolean\"\n      },\n      \"reload_on_failure\": {\n       \"type\": \"boolean\"\n      },\n      \"remove_keys\": {\n       \"type\": \"string\"\n      },\n      \"remove_keys_on_update\": {\n       \"type\": \"string\"\n      },\n      \"remove_keys_on_update_key\": {\n       \"type\": \"string\"\n      },\n      \"request_timeout\": {\n       \"type\": \"string\"\n      },\n      \"resurrect_after\": {\n       \"type\": \"string\"\n      },\n      \"retry_tag\": {\n       \"type\": \"string\"\n      },\n      \"routing_key\": {\n       \"type\": \"string\"\n      },\n      \"scheme\": {\n       \"type\": \"string\"\n      },\n      \"selector_class_name\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"sniffer_class_name\": {\n       \"type\": \"string\"\n      },\n      \"ssl_verify\": {\n       \"type\": \"boolean\"\n      },\n      \"ssl_version\": {\n       \"type\": \"string\"\n      },\n      \"suppress_doc_wrap\": {\n       \"type\": \"boolean\"\n      },\n      \"suppress_type_name\": {\n       \"type\": \"boolean\"\n      },\n      \"tag_key\": {\n       \"type\": \"string\"\n      },\n      \"target_index_affinity\": {\n       \"type\": \"boolean\"\n      },\n      \"target_index_key\": {\n       \"type\": \"string\"\n      },\n      \"template_file\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"template_name\": {\n       \"type\": \"string\"\n      },\n      \"template_overwrite\": {\n       \"type\": \"boolean\"\n      },\n      \"templates\": {\n       \"type\": \"string\"\n      },\n      \"time_key\": {\n       \"type\": \"string\"\n      },\n      \"time_key_exclude_timestamp\": {\n       \"type\": \"boolean\"\n      },\n      \"time_key_format\": {\n       \"type\": \"string\"\n      },\n      \"time_parse_error_tag\": {\n       \"type\": \"string\"\n      },\n      \"time_precision\": {\n       \"type\": \"string\"\n      },\n      \"truncate_caches_interval\": {\n       \"type\": \"string\"\n      },\n      \"unrecoverable_error_types\": {\n       \"type\": \"string\"\n      },\n      \"unrecoverable_record_types\": {\n       \"type\": \"string\"\n      },\n      \"use_legacy_template\": {\n       \"type\": \"boolean\"\n      },\n      \"user\": {\n       \"type\": \"string\"\n      },\n      \"utc_index\": {\n       \"type\": \"boolean\"\n      },\n      \"validate_client_version\": {\n       \"type\": \"boolean\"\n      },\n      \"verify_os_version_at_startup\": {\n       \"type\": \"boolean\"\n      },\n      \"with_transporter_log\": {\n       \"type\": \"boolean\"\n      },\n      \"write_operation\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"oss\": {\n     \"properties\": {\n      \"access_key_id\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"access_key_secret\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"auto_create_bucket\": {\n       \"type\": \"boolean\"\n      },\n      \"bucket\": {\n       \"type\": \"string\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"check_bucket\": {\n       \"type\": \"boolean\"\n      },\n      \"check_object\": {\n       \"type\": \"boolean\"\n      },\n      \"download_crc_enable\": {\n       \"type\": \"boolean\"\n      },\n      \"endpoint\": {\n       \"type\": \"string\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"hex_random_length\": {\n       \"type\": \"integer\"\n      },\n      \"index_format\": {\n       \"type\": \"string\"\n      },\n      \"key_format\": {\n       \"type\": \"string\"\n      },\n      \"open_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"oss_sdk_log_dir\": {\n       \"type\": \"string\"\n      },\n      \"overwrite\": {\n       \"type\": \"boolean\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"read_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"store_as\": {\n       \"type\": \"string\"\n      },\n      \"upload_crc_enable\": {\n       \"type\": \"boolean\"\n      },\n      \"warn_for_delay\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"access_key_id\",\n      \"access_key_secret\",\n      \"bucket\",\n      \"endpoint\"\n     ],\n     \"type\": \"object\"\n    },\n    \"redis\": {\n     \"properties\": {\n      \"allow_duplicate_key\": {\n       \"type\": \"boolean\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"db_number\": {\n       \"type\": \"integer\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"host\": {\n       \"type\": \"string\"\n      },\n      \"insert_key_prefix\": {\n       \"type\": \"string\"\n      },\n      \"password\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"port\": {\n       \"type\": \"integer\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"strftime_format\": {\n       \"type\": \"string\"\n      },\n      \"ttl\": {\n       \"type\": \"integer\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"relabel\": {\n     \"properties\": {\n      \"label\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"label\"\n     ],\n     \"type\": \"object\"\n    },\n    \"s3\": {\n     \"properties\": {\n      \"acl\": {\n       \"type\": \"string\"\n      },\n      \"assume_role_credentials\": {\n       \"properties\": {\n        \"duration_seconds\": {\n         \"type\": \"string\"\n        },\n        \"external_id\": {\n         \"type\": \"string\"\n        },\n        \"policy\": {\n         \"type\": \"string\"\n        },\n        \"role_arn\": {\n         \"type\": \"string\"\n        },\n        \"role_session_name\": {\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"role_arn\",\n        \"role_session_name\"\n       ],\n       \"type\": \"object\"\n      },\n      \"auto_create_bucket\": {\n       \"type\": \"string\"\n      },\n      \"aws_iam_retries\": {\n       \"type\": \"string\"\n      },\n      \"aws_key_id\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"aws_sec_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"check_apikey_on_start\": {\n       \"type\": \"string\"\n      },\n      \"check_bucket\": {\n       \"type\": \"string\"\n      },\n      \"check_object\": {\n       \"type\": \"string\"\n      },\n      \"clustername\": {\n       \"type\": \"string\"\n      },\n      \"compress\": {\n       \"properties\": {\n        \"parquet_compression_codec\": {\n         \"type\": \"string\"\n        },\n        \"parquet_page_size\": {\n         \"type\": \"string\"\n        },\n        \"parquet_row_group_size\": {\n         \"type\": \"string\"\n        },\n        \"record_type\": {\n         \"type\": \"string\"\n        },\n        \"schema_file\": {\n         \"type\": \"string\"\n        },\n        \"schema_type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"compute_checksums\": {\n       \"type\": \"string\"\n      },\n      \"enable_transfer_acceleration\": {\n       \"type\": \"string\"\n      },\n      \"force_path_style\": {\n       \"type\": \"string\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"grant_full_control\": {\n       \"type\": \"string\"\n      },\n      \"grant_read\": {\n       \"type\": \"string\"\n      },\n      \"grant_read_acp\": {\n       \"type\": \"string\"\n      },\n      \"grant_write_acp\": {\n       \"type\": \"string\"\n      },\n      \"hex_random_length\": {\n       \"type\": \"string\"\n      },\n      \"index_format\": {\n       \"type\": \"string\"\n      },\n      \"instance_profile_credentials\": {\n       \"properties\": {\n        \"http_open_timeout\": {\n         \"type\": \"string\"\n        },\n        \"http_read_timeout\": {\n         \"type\": \"string\"\n        },\n        \"ip_address\": {\n         \"type\": \"string\"\n        },\n        \"port\": {\n         \"type\": \"string\"\n        },\n        \"retries\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"oneeye_format\": {\n       \"type\": \"boolean\"\n      },\n      \"overwrite\": {\n       \"type\": \"string\"\n      },\n      \"path\": {\n       \"type\": \"string\"\n      },\n      \"proxy_uri\": {\n       \"type\": \"string\"\n      },\n      \"s3_bucket\": {\n       \"type\": \"string\"\n      },\n      \"s3_endpoint\": {\n       \"type\": \"string\"\n      },\n      \"s3_metadata\": {\n       \"type\": \"string\"\n      },\n      \"s3_object_key_format\": {\n       \"type\": \"string\"\n      },\n      \"s3_region\": {\n       \"type\": \"string\"\n      },\n      \"shared_credentials\": {\n       \"properties\": {\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"profile_name\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"signature_version\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"sse_customer_algorithm\": {\n       \"type\": \"string\"\n      },\n      \"sse_customer_key\": {\n       \"type\": \"string\"\n      },\n      \"sse_customer_key_md5\": {\n       \"type\": \"string\"\n      },\n      \"ssekms_key_id\": {\n       \"type\": \"string\"\n      },\n      \"ssl_verify_peer\": {\n       \"type\": \"string\"\n      },\n      \"storage_class\": {\n       \"type\": \"string\"\n      },\n      \"store_as\": {\n       \"type\": \"string\"\n      },\n      \"use_bundled_cert\": {\n       \"type\": \"string\"\n      },\n      \"use_server_side_encryption\": {\n       \"type\": \"string\"\n      },\n      \"warn_for_delay\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"s3_bucket\"\n     ],\n     \"type\": \"object\"\n    },\n    \"splunkHec\": {\n     \"properties\": {\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"ca_file\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"ca_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_cert\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"coerce_to_utf8\": {\n       \"type\": \"boolean\"\n      },\n      \"data_type\": {\n       \"type\": \"string\"\n      },\n      \"fields\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"object\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"add_newline\": {\n         \"type\": \"boolean\"\n        },\n        \"message_key\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"hec_host\": {\n       \"type\": \"string\"\n      },\n      \"hec_port\": {\n       \"type\": \"integer\"\n      },\n      \"hec_token\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"host\": {\n       \"type\": \"string\"\n      },\n      \"host_key\": {\n       \"type\": \"string\"\n      },\n      \"idle_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"index\": {\n       \"type\": \"string\"\n      },\n      \"index_key\": {\n       \"type\": \"string\"\n      },\n      \"insecure_ssl\": {\n       \"type\": \"boolean\"\n      },\n      \"keep_keys\": {\n       \"type\": \"boolean\"\n      },\n      \"metric_name_key\": {\n       \"type\": \"string\"\n      },\n      \"metric_value_key\": {\n       \"type\": \"string\"\n      },\n      \"metrics_from_event\": {\n       \"type\": \"boolean\"\n      },\n      \"non_utf8_replacement_string\": {\n       \"type\": \"string\"\n      },\n      \"open_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"protocol\": {\n       \"type\": \"string\"\n      },\n      \"read_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"source\": {\n       \"type\": \"string\"\n      },\n      \"source_key\": {\n       \"type\": \"string\"\n      },\n      \"sourcetype\": {\n       \"type\": \"string\"\n      },\n      \"sourcetype_key\": {\n       \"type\": \"string\"\n      },\n      \"ssl_ciphers\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"hec_host\",\n      \"hec_token\"\n     ],\n     \"type\": \"object\"\n    },\n    \"sqs\": {\n     \"properties\": {\n      \"aws_key_id\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"aws_sec_key\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"create_queue\": {\n       \"type\": \"boolean\"\n      },\n      \"delay_seconds\": {\n       \"type\": \"integer\"\n      },\n      \"include_tag\": {\n       \"type\": \"boolean\"\n      },\n      \"message_group_id\": {\n       \"type\": \"string\"\n      },\n      \"queue_name\": {\n       \"type\": \"string\"\n      },\n      \"region\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"sqs_url\": {\n       \"type\": \"string\"\n      },\n      \"tag_property_name\": {\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"sumologic\": {\n     \"properties\": {\n      \"add_timestamp\": {\n       \"type\": \"boolean\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"compress\": {\n       \"type\": \"boolean\"\n      },\n      \"compress_encoding\": {\n       \"type\": \"string\"\n      },\n      \"custom_dimensions\": {\n       \"type\": \"string\"\n      },\n      \"custom_fields\": {\n       \"items\": {\n        \"type\": \"string\"\n       },\n       \"type\": \"array\"\n      },\n      \"data_type\": {\n       \"type\": \"string\"\n      },\n      \"delimiter\": {\n       \"type\": \"string\"\n      },\n      \"disable_cookies\": {\n       \"type\": \"boolean\"\n      },\n      \"endpoint\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"log_format\": {\n       \"type\": \"string\"\n      },\n      \"log_key\": {\n       \"type\": \"string\"\n      },\n      \"metric_data_format\": {\n       \"type\": \"string\"\n      },\n      \"open_timeout\": {\n       \"type\": \"integer\"\n      },\n      \"proxy_uri\": {\n       \"type\": \"string\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"source_category\": {\n       \"type\": \"string\"\n      },\n      \"source_host\": {\n       \"type\": \"string\"\n      },\n      \"source_name\": {\n       \"type\": \"string\"\n      },\n      \"source_name_key\": {\n       \"type\": \"string\"\n      },\n      \"sumo_client\": {\n       \"type\": \"string\"\n      },\n      \"timestamp_key\": {\n       \"type\": \"string\"\n      },\n      \"verify_ssl\": {\n       \"type\": \"boolean\"\n      }\n     },\n     \"required\": [\n      \"endpoint\",\n      \"source_name\"\n     ],\n     \"type\": \"object\"\n    },\n    \"syslog\": {\n     \"properties\": {\n      \"allow_self_signed_cert\": {\n       \"type\": \"boolean\"\n      },\n      \"buffer\": {\n       \"properties\": {\n        \"chunk_full_threshold\": {\n         \"type\": \"string\"\n        },\n        \"chunk_limit_records\": {\n         \"type\": \"integer\"\n        },\n        \"chunk_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"compress\": {\n         \"type\": \"string\"\n        },\n        \"delayed_commit_timeout\": {\n         \"type\": \"string\"\n        },\n        \"disable_chunk_backup\": {\n         \"type\": \"boolean\"\n        },\n        \"disabled\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_at_shutdown\": {\n         \"type\": \"boolean\"\n        },\n        \"flush_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_mode\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_burst_interval\": {\n         \"type\": \"string\"\n        },\n        \"flush_thread_count\": {\n         \"type\": \"integer\"\n        },\n        \"flush_thread_interval\": {\n         \"type\": \"string\"\n        },\n        \"overflow_action\": {\n         \"type\": \"string\"\n        },\n        \"path\": {\n         \"type\": \"string\"\n        },\n        \"queue_limit_length\": {\n         \"type\": \"integer\"\n        },\n        \"queued_chunks_limit_size\": {\n         \"type\": \"integer\"\n        },\n        \"retry_exponential_backoff_base\": {\n         \"type\": \"string\"\n        },\n        \"retry_forever\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_max_interval\": {\n         \"type\": \"string\"\n        },\n        \"retry_max_times\": {\n         \"type\": \"integer\"\n        },\n        \"retry_randomize\": {\n         \"type\": \"boolean\"\n        },\n        \"retry_secondary_threshold\": {\n         \"type\": \"string\"\n        },\n        \"retry_timeout\": {\n         \"type\": \"string\"\n        },\n        \"retry_type\": {\n         \"type\": \"string\"\n        },\n        \"retry_wait\": {\n         \"type\": \"string\"\n        },\n        \"tags\": {\n         \"type\": \"string\"\n        },\n        \"timekey\": {\n         \"type\": \"string\"\n        },\n        \"timekey_use_utc\": {\n         \"type\": \"boolean\"\n        },\n        \"timekey_wait\": {\n         \"type\": \"string\"\n        },\n        \"timekey_zone\": {\n         \"type\": \"string\"\n        },\n        \"total_limit_size\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"client_cert_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"enable_system_cert_store\": {\n       \"type\": \"boolean\"\n      },\n      \"format\": {\n       \"properties\": {\n        \"app_name_field\": {\n         \"type\": \"string\"\n        },\n        \"hostname_field\": {\n         \"type\": \"string\"\n        },\n        \"log_field\": {\n         \"type\": \"string\"\n        },\n        \"message_id_field\": {\n         \"type\": \"string\"\n        },\n        \"proc_id_field\": {\n         \"type\": \"string\"\n        },\n        \"rfc6587_message_size\": {\n         \"type\": \"boolean\"\n        },\n        \"structured_data_field\": {\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"enum\": [\n          \"out_file\",\n          \"json\",\n          \"ltsv\",\n          \"csv\",\n          \"msgpack\",\n          \"hash\",\n          \"single_value\"\n         ],\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"fqdn\": {\n       \"type\": \"string\"\n      },\n      \"host\": {\n       \"type\": \"string\"\n      },\n      \"insecure\": {\n       \"type\": \"boolean\"\n      },\n      \"port\": {\n       \"type\": \"integer\"\n      },\n      \"private_key_passphrase\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"private_key_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"slow_flush_log_threshold\": {\n       \"type\": \"string\"\n      },\n      \"transport\": {\n       \"type\": \"string\"\n      },\n      \"trusted_ca_path\": {\n       \"properties\": {\n        \"mountFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"value\": {\n         \"type\": \"string\"\n        },\n        \"valueFrom\": {\n         \"properties\": {\n          \"secretKeyRef\": {\n           \"properties\": {\n            \"key\": {\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"verify_fqdn\": {\n       \"type\": \"boolean\"\n      },\n      \"version\": {\n       \"type\": \"string\"\n      }\n     },\n     \"required\": [\n      \"host\"\n     ],\n     \"type\": \"object\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"title\": \"Output\",\n \"type\": \"object\"\n}"}}