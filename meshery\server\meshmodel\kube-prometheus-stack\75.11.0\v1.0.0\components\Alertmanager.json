{"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "components.meshery.io/v1beta1", "version": "v1.0.0", "displayName": "Alert<PERSON><PERSON>", "description": "", "format": "JSON", "model": {"id": "00000000-0000-0000-0000-000000000000", "schemaVersion": "models.meshery.io/v1beta1", "version": "v1.0.0", "name": "kube-prometheus-stack", "displayName": "Kube Prometheus Stack", "status": "enabled", "registrant": {"id": "00000000-0000-0000-0000-000000000000", "name": "Artifact <PERSON>", "type": "registry", "sub_type": "", "kind": "<PERSON><PERSON><PERSON>", "status": "discovered", "created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z", "deleted_at": null, "schemaVersion": ""}, "connection_id": "00000000-0000-0000-0000-000000000000", "category": {"id": "00000000-0000-0000-0000-000000000000", "name": "Observability and Analysis"}, "subCategory": "Monitoring", "metadata": {"isAnnotation": false, "primaryColor": "#e75225", "secondaryColor": "#ec7551", "shape": "circle", "source_uri": "https://github.com/prometheus-community/helm-charts/releases/download/kube-prometheus-stack-75.11.0/kube-prometheus-stack-75.11.0.tgz", "styleOverrides": "", "svgColor": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-3.94 -1.44 438.62 432.87\" height=\"20\" width=\"20\"><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"#E75225\" d=\"M215.926 7.068c115.684.024 210.638 93.784 210.493 207.844-.148 115.793-94.713 208.252-212.912 208.169C97.95 423 4.52 329.143 4.601 213.221 4.68 99.867 99.833 7.044 215.926 7.068zm-63.947 73.001c2.652 12.978.076 25.082-3.846 36.988-2.716 8.244-6.47 16.183-8.711 24.539-3.694 13.769-7.885 27.619-9.422 41.701-2.21 20.25 5.795 38.086 19.493 55.822L86.527 225.94c.11 1.978-.007 2.727.21 3.361 5.968 17.43 16.471 32.115 28.243 45.957 1.246 1.465 4.082 2.217 6.182 2.221 62.782.115 125.565.109 188.347.028 1.948-.003 4.546-.369 5.741-1.618 13.456-14.063 23.746-30.079 30.179-50.257l-66.658 12.976c4.397-8.567 9.417-16.1 12.302-24.377 9.869-28.315 5.779-55.69-8.387-81.509-11.368-20.72-21.854-41.349-16.183-66.32-12.005 11.786-16.615 26.79-19.541 42.253-2.882 15.23-4.58 30.684-6.811 46.136-.317-.467-.728-.811-.792-1.212-.258-1.621-.499-3.255-.587-4.893-1.355-25.31-6.328-49.696-16.823-72.987-6.178-13.71-12.99-27.727-6.622-44.081-4.31 2.259-8.205 4.505-10.997 7.711-8.333 9.569-11.779 21.062-12.666 33.645-.757 10.75-1.796 21.552-3.801 32.123-2.107 11.109-5.448 21.998-12.956 32.209-3.033-21.81-3.37-43.38-22.928-57.237zm161.877 216.523H116.942v34.007h196.914v-34.007zm-157.871 51.575c-.163 28.317 28.851 49.414 64.709 47.883 29.716-1.269 56.016-24.51 53.755-47.883H155.985z\"></path></svg>", "svgComplete": "", "svgWhite": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE svg><svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.61 2.89 434.72 428.97\" height=\"20\" width=\"20\"><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"#FFF\" d=\"M216.412 11.432c114.637.024 208.732 92.935 208.588 205.963-.146 114.745-93.856 206.367-210.985 206.285C99.504 423.599 6.92 330.592 7 215.719c.079-112.328 94.369-204.311 209.412-204.287zm-63.368 72.341c2.628 12.861.075 24.855-3.811 36.653-2.691 8.17-6.411 16.036-8.632 24.317-3.66 13.644-7.813 27.369-9.336 41.324-2.19 20.067 5.743 37.741 19.317 55.316l-62.396-13.06c.109 1.96-.007 2.702.208 3.331 5.914 17.272 16.322 31.824 27.988 45.541 1.234 1.451 4.045 2.197 6.126 2.201 62.214.114 124.428.108 186.642.028 1.93-.002 4.505-.365 5.689-1.603 13.335-13.936 23.531-29.806 29.906-49.802l-66.055 12.859c4.357-8.489 9.331-15.954 12.19-24.156 9.78-28.058 5.726-55.186-8.311-80.771-11.266-20.532-21.657-40.975-16.037-65.72-11.896 11.679-16.465 26.548-19.364 41.871-2.856 15.092-4.539 30.406-6.75 45.718-.314-.462-.722-.804-.785-1.201-.256-1.607-.494-3.226-.581-4.848-1.343-25.081-6.271-49.246-16.671-72.326-6.122-13.586-12.873-27.476-6.562-43.682-4.271 2.239-8.13 4.464-10.897 7.641-8.258 9.482-11.673 20.871-12.551 33.341-.751 10.653-1.779 21.357-3.766 31.833-2.088 11.008-5.399 21.799-12.838 31.917-3.009-21.616-3.342-42.991-22.723-56.722zm160.411 214.562H118.323v33.699h195.132v-33.699zm-156.441 51.108c-.161 28.061 28.59 48.967 64.123 47.45 29.447-1.257 55.509-24.289 53.268-47.45H157.014z\"></path></svg>"}, "model": {"version": "75.11.0"}, "components_count": 0, "relationships_count": 0, "components": null, "relationships": null}, "styles": {"primaryColor": "#e75225", "secondaryColor": "#ec7551", "shape": "circle", "svgColor": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-3.94 -1.44 438.62 432.87\"><path fill=\"#E75225\" d=\"M215.926 7.068c115.684.024 210.638 93.784 210.493 207.844-.148 115.793-94.713 208.252-212.912 208.169C97.95 423 4.52 329.143 4.601 213.221 4.68 99.867 99.833 7.044 215.926 7.068zm-63.947 73.001c2.652 12.978.076 25.082-3.846 36.988-2.716 8.244-6.47 16.183-8.711 24.539-3.694 13.769-7.885 27.619-9.422 41.701-2.21 20.25 5.795 38.086 19.493 55.822L86.527 225.94c.11 1.978-.007 2.727.21 3.361 5.968 17.43 16.471 32.115 28.243 45.957 1.246 1.465 4.082 2.217 6.182 2.221 62.782.115 125.565.109 188.347.028 1.948-.003 4.546-.369 5.741-1.618 13.456-14.063 23.746-30.079 30.179-50.257l-66.658 12.976c4.397-8.567 9.417-16.1 12.302-24.377 9.869-28.315 5.779-55.69-8.387-81.509-11.368-20.72-21.854-41.349-16.183-66.32-12.005 11.786-16.615 26.79-19.541 42.253-2.882 15.23-4.58 30.684-6.811 46.136-.317-.467-.728-.811-.792-1.212-.258-1.621-.499-3.255-.587-4.893-1.355-25.31-6.328-49.696-16.823-72.987-6.178-13.71-12.99-27.727-6.622-44.081-4.31 2.259-8.205 4.505-10.997 7.711-8.333 9.569-11.779 21.062-12.666 33.645-.757 10.75-1.796 21.552-3.801 32.123-2.107 11.109-5.448 21.998-12.956 32.209-3.033-21.81-3.37-43.38-22.928-57.237zm161.877 216.523H116.942v34.007h196.914v-34.007zm-157.871 51.575c-.163 28.317 28.851 49.414 64.709 47.883 29.716-1.269 56.016-24.51 53.755-47.883H155.985z\"/></svg>", "svgComplete": "", "svgWhite": "<svg xmlns=\"http://www.w3.org/2000/svg\" role=\"img\" viewBox=\"-1.61 2.89 434.72 428.97\" width='434.72' height='428.97'><path fill=\"#FFF\" d=\"M216.412 11.432c114.637.024 208.732 92.935 208.588 205.963-.146 114.745-93.856 206.367-210.985 206.285C99.504 423.599 6.92 330.592 7 215.719c.079-112.328 94.369-204.311 209.412-204.287zm-63.368 72.341c2.628 12.861.075 24.855-3.811 36.653-2.691 8.17-6.411 16.036-8.632 24.317-3.66 13.644-7.813 27.369-9.336 41.324-2.19 20.067 5.743 37.741 19.317 55.316l-62.396-13.06c.109 1.96-.007 2.702.208 3.331 5.914 17.272 16.322 31.824 27.988 45.541 1.234 1.451 4.045 2.197 6.126 2.201 62.214.114 124.428.108 186.642.028 1.93-.002 4.505-.365 5.689-1.603 13.335-13.936 23.531-29.806 29.906-49.802l-66.055 12.859c4.357-8.489 9.331-15.954 12.19-24.156 9.78-28.058 5.726-55.186-8.311-80.771-11.266-20.532-21.657-40.975-16.037-65.72-11.896 11.679-16.465 26.548-19.364 41.871-2.856 15.092-4.539 30.406-6.75 45.718-.314-.462-.722-.804-.785-1.201-.256-1.607-.494-3.226-.581-4.848-1.343-25.081-6.271-49.246-16.671-72.326-6.122-13.586-12.873-27.476-6.562-43.682-4.271 2.239-8.13 4.464-10.897 7.641-8.258 9.482-11.673 20.871-12.551 33.341-.751 10.653-1.779 21.357-3.766 31.833-2.088 11.008-5.399 21.799-12.838 31.917-3.009-21.616-3.342-42.991-22.723-56.722zm160.411 214.562H118.323v33.699h195.132v-33.699zm-156.441 51.108c-.161 28.061 28.59 48.967 64.123 47.45 29.447-1.257 55.509-24.289 53.268-47.45H157.014z\"/></svg>"}, "capabilities": [{"description": "Initiate a performance test. <PERSON><PERSON><PERSON> will execute the load generation, collect metrics, and present the results.", "displayName": "Performance Test", "entityState": ["instance"], "key": "", "kind": "action", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "perf-test", "type": "operator", "version": "0.7.0"}, {"description": "Configure the workload specific setting of a component", "displayName": "Workload Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "config", "type": "configuration", "version": "0.7.0"}, {"description": "Configure Labels And Annotations for  the component ", "displayName": "Labels and Annotations Configuration", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "labels-and-annotations", "type": "configuration", "version": "0.7.0"}, {"description": "View relationships for the component", "displayName": "Relationships", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "relationship", "type": "configuration", "version": "0.7.0"}, {"description": "View Component Definition ", "displayName": "<PERSON><PERSON>", "entityState": ["declaration", "instance"], "key": "", "kind": "view", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "definition", "type": "configuration", "version": "0.7.0"}, {"description": "Configure the visual styles for the component", "displayName": "Styl<PERSON>", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "", "type": "style", "version": "0.7.0"}, {"description": "Change the shape of the component", "displayName": "Change Shape", "entityState": ["declaration"], "key": "", "kind": "mutate", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "shape", "type": "style", "version": "0.7.0"}, {"description": "Drag and Drop a component into a parent component in graph view", "displayName": "Compound Drag And Drop", "entityState": ["declaration"], "key": "", "kind": "interaction", "schemaVersion": "capability.meshery.io/v1alpha1", "status": "enabled", "subType": "compoundDnd", "type": "graph", "version": "0.7.0"}], "status": "enabled", "metadata": {"configurationUISchema": "", "genealogy": "", "instanceDetails": null, "isAnnotation": false, "isNamespaced": true, "published": false, "source_uri": "https://github.com/prometheus-community/helm-charts/releases/download/kube-prometheus-stack-75.11.0/kube-prometheus-stack-75.11.0.tgz"}, "configuration": null, "component": {"version": "monitoring.coreos.com/v1", "kind": "Alert<PERSON><PERSON>", "schema": "{\n \"description\": \"The `Alertmanager` custom resource definition (CRD) defines a desired [Alertmanager](https://prometheus.io/docs/alerting) setup to run in a Kubernetes cluster. It allows to specify many options such as the number of replicas, persistent storage and many more.\\n\\nFor each `Alertmanager` resource, the Operator deploys a `StatefulSet` in the same namespace. When there are two or more configured replicas, the Operator runs the Alertmanager instances in high-availability mode.\\n\\nThe resource defines via label and namespace selectors which `AlertmanagerConfig` objects should be associated to the deployed Alertmanager instances.\",\n \"properties\": {\n  \"spec\": {\n   \"description\": \"Specification of the desired behavior of the Alertmanager cluster. More info:\\nhttps://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md#spec-and-status\",\n   \"properties\": {\n    \"additionalArgs\": {\n     \"description\": \"AdditionalArgs allows setting additional arguments for the 'Alertmanager' container.\\nIt is intended for e.g. activating hidden flags which are not supported by\\nthe dedicated configuration options yet. The arguments are passed as-is to the\\nAlertmanager container which may cause issues if they are invalid or not supported\\nby the given Alertmanager version.\",\n     \"items\": {\n      \"description\": \"Argument as part of the AdditionalArgs list.\",\n      \"properties\": {\n       \"name\": {\n        \"description\": \"Name of the argument, e.g. \\\"scrape.discovery-reload-interval\\\".\",\n        \"minLength\": 1,\n        \"type\": \"string\"\n       },\n       \"value\": {\n        \"description\": \"Argument value, e.g. 30s. Can be empty for name-only arguments (e.g. --storage.tsdb.no-lockfile)\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"additionalPeers\": {\n     \"description\": \"AdditionalPeers allows injecting a set of additional Alertmanagers to peer with to form a highly available cluster.\",\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"affinity\": {\n     \"description\": \"If specified, the pod's scheduling constraints.\",\n     \"properties\": {\n      \"nodeAffinity\": {\n       \"description\": \"Describes node affinity scheduling rules for the pod.\",\n       \"properties\": {\n        \"preferredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy\\nthe affinity expressions specified by this field, but it may choose\\na node that violates one or more of the expressions. The node that is\\nmost preferred is the one with the greatest sum of weights, i.e.\\nfor each node that meets all of the scheduling requirements (resource\\nrequest, requiredDuringScheduling affinity expressions, etc.),\\ncompute a sum by iterating through the elements of this field and adding\\n\\\"weight\\\" to the sum if the node matches the corresponding matchExpressions; the\\nnode(s) with the highest sum are the most preferred.\",\n         \"items\": {\n          \"description\": \"An empty preferred scheduling term matches all objects with implicit weight 0\\n(i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).\",\n          \"properties\": {\n           \"preference\": {\n            \"description\": \"A node selector term, associated with the corresponding weight.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"A list of node selector requirements by node's labels.\",\n              \"items\": {\n               \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchFields\": {\n              \"description\": \"A list of node selector requirements by node's fields.\",\n              \"items\": {\n               \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"weight\": {\n            \"description\": \"Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"required\": [\n           \"preference\",\n           \"weight\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"requiredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"If the affinity requirements specified by this field are not met at\\nscheduling time, the pod will not be scheduled onto the node.\\nIf the affinity requirements specified by this field cease to be met\\nat some point during pod execution (e.g. due to an update), the system\\nmay or may not try to eventually evict the pod from its node.\",\n         \"properties\": {\n          \"nodeSelectorTerms\": {\n           \"description\": \"Required. A list of node selector terms. The terms are ORed.\",\n           \"items\": {\n            \"description\": \"A null or empty node selector term matches no objects. The requirements of\\nthem are ANDed.\\nThe TopologySelectorTerm type implements a subset of the NodeSelectorTerm.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"A list of node selector requirements by node's labels.\",\n              \"items\": {\n               \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchFields\": {\n              \"description\": \"A list of node selector requirements by node's fields.\",\n              \"items\": {\n               \"description\": \"A node selector requirement is a selector that contains values, a key, and an operator\\nthat relates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"Represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"An array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. If the operator is Gt or Lt, the values\\narray must have a single element, which will be interpreted as an integer.\\nThis array is replaced during a strategic merge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          }\n         },\n         \"required\": [\n          \"nodeSelectorTerms\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"podAffinity\": {\n       \"description\": \"Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).\",\n       \"properties\": {\n        \"preferredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy\\nthe affinity expressions specified by this field, but it may choose\\na node that violates one or more of the expressions. The node that is\\nmost preferred is the one with the greatest sum of weights, i.e.\\nfor each node that meets all of the scheduling requirements (resource\\nrequest, requiredDuringScheduling affinity expressions, etc.),\\ncompute a sum by iterating through the elements of this field and adding\\n\\\"weight\\\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the\\nnode(s) with the highest sum are the most preferred.\",\n         \"items\": {\n          \"description\": \"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\n          \"properties\": {\n           \"podAffinityTerm\": {\n            \"description\": \"Required. A pod affinity term, associated with the corresponding weight.\",\n            \"properties\": {\n             \"labelSelector\": {\n              \"description\": \"A label query over a set of resources, in this case pods.\\nIf it's null, this PodAffinityTerm matches with no Pods.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"matchLabelKeys\": {\n              \"description\": \"MatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both matchLabelKeys and labelSelector.\\nAlso, matchLabelKeys cannot be set when labelSelector isn't set.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"mismatchLabelKeys\": {\n              \"description\": \"MismatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both mismatchLabelKeys and labelSelector.\\nAlso, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"namespaceSelector\": {\n              \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"namespaces\": {\n              \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"topologyKey\": {\n              \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"topologyKey\"\n            ],\n            \"type\": \"object\"\n           },\n           \"weight\": {\n            \"description\": \"weight associated with matching the corresponding podAffinityTerm,\\nin the range 1-100.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"required\": [\n           \"podAffinityTerm\",\n           \"weight\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"requiredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"If the affinity requirements specified by this field are not met at\\nscheduling time, the pod will not be scheduled onto the node.\\nIf the affinity requirements specified by this field cease to be met\\nat some point during pod execution (e.g. due to a pod label update), the\\nsystem may or may not try to eventually evict the pod from its node.\\nWhen there are multiple elements, the lists of nodes corresponding to each\\npodAffinityTerm are intersected, i.e. all terms must be satisfied.\",\n         \"items\": {\n          \"description\": \"Defines a set of pods (namely those matching the labelSelector\\nrelative to the given namespace(s)) that this pod should be\\nco-located (affinity) or not co-located (anti-affinity) with,\\nwhere co-located is defined as running on a node whose value of\\nthe label with key \\u003ctopologyKey\\u003e matches that of any node on which\\na pod of the set of pods is running\",\n          \"properties\": {\n           \"labelSelector\": {\n            \"description\": \"A label query over a set of resources, in this case pods.\\nIf it's null, this PodAffinityTerm matches with no Pods.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"matchLabelKeys\": {\n            \"description\": \"MatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both matchLabelKeys and labelSelector.\\nAlso, matchLabelKeys cannot be set when labelSelector isn't set.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"mismatchLabelKeys\": {\n            \"description\": \"MismatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both mismatchLabelKeys and labelSelector.\\nAlso, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"namespaceSelector\": {\n            \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"namespaces\": {\n            \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"topologyKey\": {\n            \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"topologyKey\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"podAntiAffinity\": {\n       \"description\": \"Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).\",\n       \"properties\": {\n        \"preferredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"The scheduler will prefer to schedule pods to nodes that satisfy\\nthe anti-affinity expressions specified by this field, but it may choose\\na node that violates one or more of the expressions. The node that is\\nmost preferred is the one with the greatest sum of weights, i.e.\\nfor each node that meets all of the scheduling requirements (resource\\nrequest, requiredDuringScheduling anti-affinity expressions, etc.),\\ncompute a sum by iterating through the elements of this field and adding\\n\\\"weight\\\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the\\nnode(s) with the highest sum are the most preferred.\",\n         \"items\": {\n          \"description\": \"The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)\",\n          \"properties\": {\n           \"podAffinityTerm\": {\n            \"description\": \"Required. A pod affinity term, associated with the corresponding weight.\",\n            \"properties\": {\n             \"labelSelector\": {\n              \"description\": \"A label query over a set of resources, in this case pods.\\nIf it's null, this PodAffinityTerm matches with no Pods.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"matchLabelKeys\": {\n              \"description\": \"MatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both matchLabelKeys and labelSelector.\\nAlso, matchLabelKeys cannot be set when labelSelector isn't set.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"mismatchLabelKeys\": {\n              \"description\": \"MismatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both mismatchLabelKeys and labelSelector.\\nAlso, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"namespaceSelector\": {\n              \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"namespaces\": {\n              \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"topologyKey\": {\n              \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"topologyKey\"\n            ],\n            \"type\": \"object\"\n           },\n           \"weight\": {\n            \"description\": \"weight associated with matching the corresponding podAffinityTerm,\\nin the range 1-100.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           }\n          },\n          \"required\": [\n           \"podAffinityTerm\",\n           \"weight\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        },\n        \"requiredDuringSchedulingIgnoredDuringExecution\": {\n         \"description\": \"If the anti-affinity requirements specified by this field are not met at\\nscheduling time, the pod will not be scheduled onto the node.\\nIf the anti-affinity requirements specified by this field cease to be met\\nat some point during pod execution (e.g. due to a pod label update), the\\nsystem may or may not try to eventually evict the pod from its node.\\nWhen there are multiple elements, the lists of nodes corresponding to each\\npodAffinityTerm are intersected, i.e. all terms must be satisfied.\",\n         \"items\": {\n          \"description\": \"Defines a set of pods (namely those matching the labelSelector\\nrelative to the given namespace(s)) that this pod should be\\nco-located (affinity) or not co-located (anti-affinity) with,\\nwhere co-located is defined as running on a node whose value of\\nthe label with key \\u003ctopologyKey\\u003e matches that of any node on which\\na pod of the set of pods is running\",\n          \"properties\": {\n           \"labelSelector\": {\n            \"description\": \"A label query over a set of resources, in this case pods.\\nIf it's null, this PodAffinityTerm matches with no Pods.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"matchLabelKeys\": {\n            \"description\": \"MatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both matchLabelKeys and labelSelector.\\nAlso, matchLabelKeys cannot be set when labelSelector isn't set.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"mismatchLabelKeys\": {\n            \"description\": \"MismatchLabelKeys is a set of pod label keys to select which pods will\\nbe taken into consideration. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`\\nto select the group of existing pods which pods will be taken into consideration\\nfor the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming\\npod labels will be ignored. The default value is empty.\\nThe same key is forbidden to exist in both mismatchLabelKeys and labelSelector.\\nAlso, mismatchLabelKeys cannot be set when labelSelector isn't set.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"namespaceSelector\": {\n            \"description\": \"A label query over the set of namespaces that the term applies to.\\nThe term is applied to the union of the namespaces selected by this field\\nand the ones listed in the namespaces field.\\nnull selector and null or empty namespaces list means \\\"this pod's namespace\\\".\\nAn empty selector ({}) matches all namespaces.\",\n            \"properties\": {\n             \"matchExpressions\": {\n              \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n              \"items\": {\n               \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"key is the label key that the selector applies to.\",\n                 \"type\": \"string\"\n                },\n                \"operator\": {\n                 \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                 \"type\": \"string\"\n                },\n                \"values\": {\n                 \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                 \"items\": {\n                  \"type\": \"string\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                }\n               },\n               \"required\": [\n                \"key\",\n                \"operator\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"matchLabels\": {\n              \"additionalProperties\": {\n               \"type\": \"string\"\n              },\n              \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n              \"type\": \"object\"\n             }\n            },\n            \"type\": \"object\",\n            \"x-kubernetes-map-type\": \"atomic\"\n           },\n           \"namespaces\": {\n            \"description\": \"namespaces specifies a static list of namespace names that the term applies to.\\nThe term is applied to the union of the namespaces listed in this field\\nand the ones selected by namespaceSelector.\\nnull or empty namespaces list and null namespaceSelector means \\\"this pod's namespace\\\".\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"topologyKey\": {\n            \"description\": \"This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching\\nthe labelSelector in the specified namespaces, where co-located is defined as running on a node\\nwhose value of the label with key topologyKey matches that of any node on which any of the\\nselected pods is running.\\nEmpty topologyKey is not allowed.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"topologyKey\"\n          ],\n          \"type\": \"object\"\n         },\n         \"type\": \"array\",\n         \"x-kubernetes-list-type\": \"atomic\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"alertmanagerConfigMatcherStrategy\": {\n     \"description\": \"AlertmanagerConfigMatcherStrategy defines how AlertmanagerConfig objects\\nprocess incoming alerts.\",\n     \"properties\": {\n      \"type\": {\n       \"default\": \"OnNamespace\",\n       \"description\": \"AlertmanagerConfigMatcherStrategyType defines the strategy used by\\nAlertmanagerConfig objects to match alerts in the routes and inhibition\\nrules.\\n\\nThe default value is `OnNamespace`.\",\n       \"enum\": [\n        \"OnNamespace\",\n        \"None\"\n       ],\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"alertmanagerConfigNamespaceSelector\": {\n     \"description\": \"Namespaces to be selected for AlertmanagerConfig discovery. If nil, only\\ncheck own namespace.\",\n     \"properties\": {\n      \"matchExpressions\": {\n       \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n       \"items\": {\n        \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n        \"properties\": {\n         \"key\": {\n          \"description\": \"key is the label key that the selector applies to.\",\n          \"type\": \"string\"\n         },\n         \"operator\": {\n          \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n          \"type\": \"string\"\n         },\n         \"values\": {\n          \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         }\n        },\n        \"required\": [\n         \"key\",\n         \"operator\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-type\": \"atomic\"\n      },\n      \"matchLabels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\",\n     \"x-kubernetes-map-type\": \"atomic\"\n    },\n    \"alertmanagerConfigSelector\": {\n     \"description\": \"AlertmanagerConfigs to be selected for to merge and configure Alertmanager with.\",\n     \"properties\": {\n      \"matchExpressions\": {\n       \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n       \"items\": {\n        \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n        \"properties\": {\n         \"key\": {\n          \"description\": \"key is the label key that the selector applies to.\",\n          \"type\": \"string\"\n         },\n         \"operator\": {\n          \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n          \"type\": \"string\"\n         },\n         \"values\": {\n          \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         }\n        },\n        \"required\": [\n         \"key\",\n         \"operator\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-type\": \"atomic\"\n      },\n      \"matchLabels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\",\n     \"x-kubernetes-map-type\": \"atomic\"\n    },\n    \"alertmanagerConfiguration\": {\n     \"description\": \"alertmanagerConfiguration specifies the configuration of Alertmanager.\\n\\nIf defined, it takes precedence over the `configSecret` field.\\n\\nThis is an *experimental feature*, it may change in any upcoming release\\nin a breaking way.\",\n     \"properties\": {\n      \"global\": {\n       \"description\": \"Defines the global parameters of the Alertmanager configuration.\",\n       \"properties\": {\n        \"httpConfig\": {\n         \"description\": \"HTTP client configuration.\",\n         \"properties\": {\n          \"authorization\": {\n           \"description\": \"Authorization header configuration for the client.\\nThis is mutually exclusive with BasicAuth and is only available starting from Alertmanager v0.22+.\",\n           \"properties\": {\n            \"credentials\": {\n             \"description\": \"Selects a key of a Secret in the namespace that contains the credentials for authentication.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"type\": {\n             \"description\": \"Defines the authentication type. The value is case-insensitive.\\n\\n\\\"Basic\\\" is not a supported value.\\n\\nDefault: \\\"Bearer\\\"\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"basicAuth\": {\n           \"description\": \"BasicAuth for the client.\\nThis is mutually exclusive with Authorization. If both are defined, BasicAuth takes precedence.\",\n           \"properties\": {\n            \"password\": {\n             \"description\": \"`password` specifies a key of a Secret containing the password for\\nauthentication.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"username\": {\n             \"description\": \"`username` specifies a key of a Secret containing the username for\\nauthentication.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"bearerTokenSecret\": {\n           \"description\": \"The secret's key that contains the bearer token to be used by the client\\nfor authentication.\\nThe secret needs to be in the same namespace as the Alertmanager\\nobject and accessible by the Prometheus Operator.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"followRedirects\": {\n           \"description\": \"FollowRedirects specifies whether the client should follow HTTP 3xx redirects.\",\n           \"type\": \"boolean\"\n          },\n          \"noProxy\": {\n           \"description\": \"`noProxy` is a comma-separated string that can contain IPs, CIDR notation, domain names\\nthat should be excluded from proxying. IP and domain names can\\ncontain port numbers.\\n\\nIt requires Prometheus \\u003e= v2.43.0, Alertmanager \\u003e= v0.25.0 or Thanos \\u003e= v0.32.0.\",\n           \"type\": \"string\"\n          },\n          \"oauth2\": {\n           \"description\": \"OAuth2 client credentials used to fetch a token for the targets.\",\n           \"properties\": {\n            \"clientId\": {\n             \"description\": \"`clientId` specifies a key of a Secret or ConfigMap containing the\\nOAuth2 client's ID.\",\n             \"properties\": {\n              \"configMap\": {\n               \"description\": \"ConfigMap containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key to select.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"secret\": {\n               \"description\": \"Secret containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"clientSecret\": {\n             \"description\": \"`clientSecret` specifies a key of a Secret containing the OAuth2\\nclient's secret.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"endpointParams\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"description\": \"`endpointParams` configures the HTTP parameters to append to the token\\nURL.\",\n             \"type\": \"object\"\n            },\n            \"noProxy\": {\n             \"description\": \"`noProxy` is a comma-separated string that can contain IPs, CIDR notation, domain names\\nthat should be excluded from proxying. IP and domain names can\\ncontain port numbers.\\n\\nIt requires Prometheus \\u003e= v2.43.0, Alertmanager \\u003e= v0.25.0 or Thanos \\u003e= v0.32.0.\",\n             \"type\": \"string\"\n            },\n            \"proxyConnectHeader\": {\n             \"additionalProperties\": {\n              \"items\": {\n               \"description\": \"SecretKeySelector selects a key of a Secret.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"type\": \"array\"\n             },\n             \"description\": \"ProxyConnectHeader optionally specifies headers to send to\\nproxies during CONNECT requests.\\n\\nIt requires Prometheus \\u003e= v2.43.0, Alertmanager \\u003e= v0.25.0 or Thanos \\u003e= v0.32.0.\",\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"proxyFromEnvironment\": {\n             \"description\": \"Whether to use the proxy configuration defined by environment variables (HTTP_PROXY, HTTPS_PROXY, and NO_PROXY).\\n\\nIt requires Prometheus \\u003e= v2.43.0, Alertmanager \\u003e= v0.25.0 or Thanos \\u003e= v0.32.0.\",\n             \"type\": \"boolean\"\n            },\n            \"proxyUrl\": {\n             \"description\": \"`proxyURL` defines the HTTP proxy server to use.\",\n             \"pattern\": \"^(http|https|socks5)://.+$\",\n             \"type\": \"string\"\n            },\n            \"scopes\": {\n             \"description\": \"`scopes` defines the OAuth2 scopes used for the token request.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\"\n            },\n            \"tlsConfig\": {\n             \"description\": \"TLS configuration to use when connecting to the OAuth2 server.\\nIt requires Prometheus \\u003e= v2.43.0.\",\n             \"properties\": {\n              \"ca\": {\n               \"description\": \"Certificate authority used when verifying server certificates.\",\n               \"properties\": {\n                \"configMap\": {\n                 \"description\": \"ConfigMap containing data to use for the targets.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key to select.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"default\": \"\",\n                   \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"secret\": {\n                 \"description\": \"Secret containing data to use for the targets.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"default\": \"\",\n                   \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the Secret or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"cert\": {\n               \"description\": \"Client certificate to present when doing client-authentication.\",\n               \"properties\": {\n                \"configMap\": {\n                 \"description\": \"ConfigMap containing data to use for the targets.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key to select.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"default\": \"\",\n                   \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                },\n                \"secret\": {\n                 \"description\": \"Secret containing data to use for the targets.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                   \"type\": \"string\"\n                  },\n                  \"name\": {\n                   \"default\": \"\",\n                   \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                   \"type\": \"string\"\n                  },\n                  \"optional\": {\n                   \"description\": \"Specify whether the Secret or its key must be defined\",\n                   \"type\": \"boolean\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\"\n                 ],\n                 \"type\": \"object\",\n                 \"x-kubernetes-map-type\": \"atomic\"\n                }\n               },\n               \"type\": \"object\"\n              },\n              \"insecureSkipVerify\": {\n               \"description\": \"Disable target certificate validation.\",\n               \"type\": \"boolean\"\n              },\n              \"keySecret\": {\n               \"description\": \"Secret containing the client key file for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"maxVersion\": {\n               \"description\": \"Maximum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.41.0 or Thanos \\u003e= v0.31.0.\",\n               \"enum\": [\n                \"TLS10\",\n                \"TLS11\",\n                \"TLS12\",\n                \"TLS13\"\n               ],\n               \"type\": \"string\"\n              },\n              \"minVersion\": {\n               \"description\": \"Minimum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.35.0 or Thanos \\u003e= v0.28.0.\",\n               \"enum\": [\n                \"TLS10\",\n                \"TLS11\",\n                \"TLS12\",\n                \"TLS13\"\n               ],\n               \"type\": \"string\"\n              },\n              \"serverName\": {\n               \"description\": \"Used to verify the hostname for the targets.\",\n               \"type\": \"string\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"tokenUrl\": {\n             \"description\": \"`tokenURL` configures the URL to fetch the token from.\",\n             \"minLength\": 1,\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"clientId\",\n            \"clientSecret\",\n            \"tokenUrl\"\n           ],\n           \"type\": \"object\"\n          },\n          \"proxyConnectHeader\": {\n           \"additionalProperties\": {\n            \"items\": {\n             \"description\": \"SecretKeySelector selects a key of a Secret.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"type\": \"array\"\n           },\n           \"description\": \"ProxyConnectHeader optionally specifies headers to send to\\nproxies during CONNECT requests.\\n\\nIt requires Prometheus \\u003e= v2.43.0, Alertmanager \\u003e= v0.25.0 or Thanos \\u003e= v0.32.0.\",\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"proxyFromEnvironment\": {\n           \"description\": \"Whether to use the proxy configuration defined by environment variables (HTTP_PROXY, HTTPS_PROXY, and NO_PROXY).\\n\\nIt requires Prometheus \\u003e= v2.43.0, Alertmanager \\u003e= v0.25.0 or Thanos \\u003e= v0.32.0.\",\n           \"type\": \"boolean\"\n          },\n          \"proxyUrl\": {\n           \"description\": \"`proxyURL` defines the HTTP proxy server to use.\",\n           \"pattern\": \"^(http|https|socks5)://.+$\",\n           \"type\": \"string\"\n          },\n          \"tlsConfig\": {\n           \"description\": \"TLS configuration for the client.\",\n           \"properties\": {\n            \"ca\": {\n             \"description\": \"Certificate authority used when verifying server certificates.\",\n             \"properties\": {\n              \"configMap\": {\n               \"description\": \"ConfigMap containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key to select.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"secret\": {\n               \"description\": \"Secret containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"cert\": {\n             \"description\": \"Client certificate to present when doing client-authentication.\",\n             \"properties\": {\n              \"configMap\": {\n               \"description\": \"ConfigMap containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key to select.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"secret\": {\n               \"description\": \"Secret containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"insecureSkipVerify\": {\n             \"description\": \"Disable target certificate validation.\",\n             \"type\": \"boolean\"\n            },\n            \"keySecret\": {\n             \"description\": \"Secret containing the client key file for the targets.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"maxVersion\": {\n             \"description\": \"Maximum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.41.0 or Thanos \\u003e= v0.31.0.\",\n             \"enum\": [\n              \"TLS10\",\n              \"TLS11\",\n              \"TLS12\",\n              \"TLS13\"\n             ],\n             \"type\": \"string\"\n            },\n            \"minVersion\": {\n             \"description\": \"Minimum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.35.0 or Thanos \\u003e= v0.28.0.\",\n             \"enum\": [\n              \"TLS10\",\n              \"TLS11\",\n              \"TLS12\",\n              \"TLS13\"\n             ],\n             \"type\": \"string\"\n            },\n            \"serverName\": {\n             \"description\": \"Used to verify the hostname for the targets.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"opsGenieApiKey\": {\n         \"description\": \"The default OpsGenie API Key.\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"default\": \"\",\n           \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"opsGenieApiUrl\": {\n         \"description\": \"The default OpsGenie API URL.\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"default\": \"\",\n           \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"pagerdutyUrl\": {\n         \"description\": \"The default Pagerduty URL.\",\n         \"type\": \"string\"\n        },\n        \"resolveTimeout\": {\n         \"description\": \"ResolveTimeout is the default value used by alertmanager if the alert does\\nnot include EndsAt, after this time passes it can declare the alert as resolved if it has not been updated.\\nThis has no impact on alerts from Prometheus, as they always include EndsAt.\",\n         \"pattern\": \"^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n         \"type\": \"string\"\n        },\n        \"slackApiUrl\": {\n         \"description\": \"The default Slack API URL.\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"default\": \"\",\n           \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"smtp\": {\n         \"description\": \"Configures global SMTP parameters.\",\n         \"properties\": {\n          \"authIdentity\": {\n           \"description\": \"SMTP Auth using PLAIN\",\n           \"type\": \"string\"\n          },\n          \"authPassword\": {\n           \"description\": \"SMTP Auth using LOGIN and PLAIN.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"authSecret\": {\n           \"description\": \"SMTP Auth using CRAM-MD5.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"authUsername\": {\n           \"description\": \"SMTP Auth using CRAM-MD5, LOGIN and PLAIN. If empty, Alertmanager doesn't authenticate to the SMTP server.\",\n           \"type\": \"string\"\n          },\n          \"from\": {\n           \"description\": \"The default SMTP From header field.\",\n           \"type\": \"string\"\n          },\n          \"hello\": {\n           \"description\": \"The default hostname to identify to the SMTP server.\",\n           \"type\": \"string\"\n          },\n          \"requireTLS\": {\n           \"description\": \"The default SMTP TLS requirement.\\nNote that Go does not support unencrypted connections to remote SMTP endpoints.\",\n           \"type\": \"boolean\"\n          },\n          \"smartHost\": {\n           \"description\": \"The default SMTP smarthost used for sending emails.\",\n           \"properties\": {\n            \"host\": {\n             \"description\": \"Defines the host's address, it can be a DNS name or a literal IP address.\",\n             \"minLength\": 1,\n             \"type\": \"string\"\n            },\n            \"port\": {\n             \"description\": \"Defines the host's port, it can be a literal port number or a port name.\",\n             \"minLength\": 1,\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"host\",\n            \"port\"\n           ],\n           \"type\": \"object\"\n          },\n          \"tlsConfig\": {\n           \"description\": \"The default TLS configuration for SMTP receivers\",\n           \"properties\": {\n            \"ca\": {\n             \"description\": \"Certificate authority used when verifying server certificates.\",\n             \"properties\": {\n              \"configMap\": {\n               \"description\": \"ConfigMap containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key to select.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"secret\": {\n               \"description\": \"Secret containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"cert\": {\n             \"description\": \"Client certificate to present when doing client-authentication.\",\n             \"properties\": {\n              \"configMap\": {\n               \"description\": \"ConfigMap containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key to select.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"secret\": {\n               \"description\": \"Secret containing data to use for the targets.\",\n               \"properties\": {\n                \"key\": {\n                 \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n                 \"type\": \"string\"\n                },\n                \"name\": {\n                 \"default\": \"\",\n                 \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n                 \"type\": \"string\"\n                },\n                \"optional\": {\n                 \"description\": \"Specify whether the Secret or its key must be defined\",\n                 \"type\": \"boolean\"\n                }\n               },\n               \"required\": [\n                \"key\"\n               ],\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"insecureSkipVerify\": {\n             \"description\": \"Disable target certificate validation.\",\n             \"type\": \"boolean\"\n            },\n            \"keySecret\": {\n             \"description\": \"Secret containing the client key file for the targets.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"maxVersion\": {\n             \"description\": \"Maximum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.41.0 or Thanos \\u003e= v0.31.0.\",\n             \"enum\": [\n              \"TLS10\",\n              \"TLS11\",\n              \"TLS12\",\n              \"TLS13\"\n             ],\n             \"type\": \"string\"\n            },\n            \"minVersion\": {\n             \"description\": \"Minimum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.35.0 or Thanos \\u003e= v0.28.0.\",\n             \"enum\": [\n              \"TLS10\",\n              \"TLS11\",\n              \"TLS12\",\n              \"TLS13\"\n             ],\n             \"type\": \"string\"\n            },\n            \"serverName\": {\n             \"description\": \"Used to verify the hostname for the targets.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"name\": {\n       \"description\": \"The name of the AlertmanagerConfig resource which is used to generate the Alertmanager configuration.\\nIt must be defined in the same namespace as the Alertmanager object.\\nThe operator will not enforce a `namespace` label for routes and inhibition rules.\",\n       \"minLength\": 1,\n       \"type\": \"string\"\n      },\n      \"templates\": {\n       \"description\": \"Custom notification templates.\",\n       \"items\": {\n        \"description\": \"SecretOrConfigMap allows to specify data as a Secret or ConfigMap. Fields are mutually exclusive.\",\n        \"properties\": {\n         \"configMap\": {\n          \"description\": \"ConfigMap containing data to use for the targets.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key to select.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"secret\": {\n          \"description\": \"Secret containing data to use for the targets.\",\n          \"properties\": {\n           \"key\": {\n            \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n            \"type\": \"string\"\n           },\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           },\n           \"optional\": {\n            \"description\": \"Specify whether the Secret or its key must be defined\",\n            \"type\": \"boolean\"\n           }\n          },\n          \"required\": [\n           \"key\"\n          ],\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"type\": \"array\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"automountServiceAccountToken\": {\n     \"description\": \"AutomountServiceAccountToken indicates whether a service account token should be automatically mounted in the pod.\\nIf the service account has `automountServiceAccountToken: true`, set the field to `false` to opt out of automounting API credentials.\",\n     \"type\": \"boolean\"\n    },\n    \"baseImage\": {\n     \"description\": \"Base image that is used to deploy pods, without tag.\\nDeprecated: use 'image' instead.\",\n     \"type\": \"string\"\n    },\n    \"clusterAdvertiseAddress\": {\n     \"description\": \"ClusterAdvertiseAddress is the explicit address to advertise in cluster.\\nNeeds to be provided for non RFC1918 [1] (public) addresses.\\n[1] RFC1918: https://tools.ietf.org/html/rfc1918\",\n     \"type\": \"string\"\n    },\n    \"clusterGossipInterval\": {\n     \"description\": \"Interval between gossip attempts.\",\n     \"pattern\": \"^(0|(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n     \"type\": \"string\"\n    },\n    \"clusterLabel\": {\n     \"description\": \"Defines the identifier that uniquely identifies the Alertmanager cluster.\\nYou should only set it when the Alertmanager cluster includes Alertmanager instances which are external to this Alertmanager resource. In practice, the addresses of the external instances are provided via the `.spec.additionalPeers` field.\",\n     \"type\": \"string\"\n    },\n    \"clusterPeerTimeout\": {\n     \"description\": \"Timeout for cluster peering.\",\n     \"pattern\": \"^(0|(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n     \"type\": \"string\"\n    },\n    \"clusterPushpullInterval\": {\n     \"description\": \"Interval between pushpull attempts.\",\n     \"pattern\": \"^(0|(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n     \"type\": \"string\"\n    },\n    \"clusterTLS\": {\n     \"description\": \"Configures the mutual TLS configuration for the Alertmanager cluster's gossip protocol.\\n\\nIt requires Alertmanager \\u003e= 0.24.0.\",\n     \"properties\": {\n      \"client\": {\n       \"description\": \"Client-side configuration for mutual TLS.\",\n       \"properties\": {\n        \"ca\": {\n         \"description\": \"Certificate authority used when verifying server certificates.\",\n         \"properties\": {\n          \"configMap\": {\n           \"description\": \"ConfigMap containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to select.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"secret\": {\n           \"description\": \"Secret containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"cert\": {\n         \"description\": \"Client certificate to present when doing client-authentication.\",\n         \"properties\": {\n          \"configMap\": {\n           \"description\": \"ConfigMap containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to select.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"secret\": {\n           \"description\": \"Secret containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"insecureSkipVerify\": {\n         \"description\": \"Disable target certificate validation.\",\n         \"type\": \"boolean\"\n        },\n        \"keySecret\": {\n         \"description\": \"Secret containing the client key file for the targets.\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"default\": \"\",\n           \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"maxVersion\": {\n         \"description\": \"Maximum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.41.0 or Thanos \\u003e= v0.31.0.\",\n         \"enum\": [\n          \"TLS10\",\n          \"TLS11\",\n          \"TLS12\",\n          \"TLS13\"\n         ],\n         \"type\": \"string\"\n        },\n        \"minVersion\": {\n         \"description\": \"Minimum acceptable TLS version.\\n\\nIt requires Prometheus \\u003e= v2.35.0 or Thanos \\u003e= v0.28.0.\",\n         \"enum\": [\n          \"TLS10\",\n          \"TLS11\",\n          \"TLS12\",\n          \"TLS13\"\n         ],\n         \"type\": \"string\"\n        },\n        \"serverName\": {\n         \"description\": \"Used to verify the hostname for the targets.\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"server\": {\n       \"description\": \"Server-side configuration for mutual TLS.\",\n       \"properties\": {\n        \"cert\": {\n         \"description\": \"Secret or ConfigMap containing the TLS certificate for the web server.\\n\\nEither `keySecret` or `keyFile` must be defined.\\n\\nIt is mutually exclusive with `certFile`.\",\n         \"properties\": {\n          \"configMap\": {\n           \"description\": \"ConfigMap containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to select.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"secret\": {\n           \"description\": \"Secret containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"certFile\": {\n         \"description\": \"Path to the TLS certificate file in the container for the web server.\\n\\nEither `keySecret` or `keyFile` must be defined.\\n\\nIt is mutually exclusive with `cert`.\",\n         \"type\": \"string\"\n        },\n        \"cipherSuites\": {\n         \"description\": \"List of supported cipher suites for TLS versions up to TLS 1.2.\\n\\nIf not defined, the Go default cipher suites are used.\\nAvailable cipher suites are documented in the Go documentation:\\nhttps://golang.org/pkg/crypto/tls/#pkg-constants\",\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"clientAuthType\": {\n         \"description\": \"The server policy for client TLS authentication.\\n\\nFor more detail on clientAuth options:\\nhttps://golang.org/pkg/crypto/tls/#ClientAuthType\",\n         \"type\": \"string\"\n        },\n        \"clientCAFile\": {\n         \"description\": \"Path to the CA certificate file for client certificate authentication to\\nthe server.\\n\\nIt is mutually exclusive with `client_ca`.\",\n         \"type\": \"string\"\n        },\n        \"client_ca\": {\n         \"description\": \"Secret or ConfigMap containing the CA certificate for client certificate\\nauthentication to the server.\\n\\nIt is mutually exclusive with `clientCAFile`.\",\n         \"properties\": {\n          \"configMap\": {\n           \"description\": \"ConfigMap containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to select.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"secret\": {\n           \"description\": \"Secret containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"curvePreferences\": {\n         \"description\": \"Elliptic curves that will be used in an ECDHE handshake, in preference\\norder.\\n\\nAvailable curves are documented in the Go documentation:\\nhttps://golang.org/pkg/crypto/tls/#CurveID\",\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"keyFile\": {\n         \"description\": \"Path to the TLS private key file in the container for the web server.\\n\\nIf defined, either `cert` or `certFile` must be defined.\\n\\nIt is mutually exclusive with `keySecret`.\",\n         \"type\": \"string\"\n        },\n        \"keySecret\": {\n         \"description\": \"Secret containing the TLS private key for the web server.\\n\\nEither `cert` or `certFile` must be defined.\\n\\nIt is mutually exclusive with `keyFile`.\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"default\": \"\",\n           \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"maxVersion\": {\n         \"description\": \"Maximum TLS version that is acceptable.\",\n         \"type\": \"string\"\n        },\n        \"minVersion\": {\n         \"description\": \"Minimum TLS version that is acceptable.\",\n         \"type\": \"string\"\n        },\n        \"preferServerCipherSuites\": {\n         \"description\": \"Controls whether the server selects the client's most preferred cipher\\nsuite, or the server's most preferred cipher suite.\\n\\nIf true then the server's preference, as expressed in\\nthe order of elements in cipherSuites, is used.\",\n         \"type\": \"boolean\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"required\": [\n      \"client\",\n      \"server\"\n     ],\n     \"type\": \"object\"\n    },\n    \"configMaps\": {\n     \"description\": \"ConfigMaps is a list of ConfigMaps in the same namespace as the Alertmanager\\nobject, which shall be mounted into the Alertmanager Pods.\\nEach ConfigMap is added to the StatefulSet definition as a volume named `configmap-\\u003cconfigmap-name\\u003e`.\\nThe ConfigMaps are mounted into `/etc/alertmanager/configmaps/\\u003cconfigmap-name\\u003e` in the 'alertmanager' container.\",\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"configSecret\": {\n     \"description\": \"ConfigSecret is the name of a Kubernetes Secret in the same namespace as the\\nAlertmanager object, which contains the configuration for this Alertmanager\\ninstance. If empty, it defaults to `alertmanager-\\u003calertmanager-name\\u003e`.\\n\\nThe Alertmanager configuration should be available under the\\n`alertmanager.yaml` key. Additional keys from the original secret are\\ncopied to the generated secret and mounted into the\\n`/etc/alertmanager/config` directory in the `alertmanager` container.\\n\\nIf either the secret or the `alertmanager.yaml` key is missing, the\\noperator provisions a minimal Alertmanager configuration with one empty\\nreceiver (effectively dropping alert notifications).\",\n     \"type\": \"string\"\n    },\n    \"containers\": {\n     \"description\": \"Containers allows injecting additional containers. This is meant to\\nallow adding an authentication proxy to an Alertmanager pod.\\nContainers described here modify an operator generated container if they\\nshare the same name and modifications are done via a strategic merge\\npatch. The current container names are: `alertmanager` and\\n`config-reloader`. Overriding containers is entirely outside the scope\\nof what the maintainers will support and by doing so, you accept that\\nthis behaviour may break at any time without notice.\",\n     \"items\": {\n      \"description\": \"A single application container that you want to run within a pod.\",\n      \"properties\": {\n       \"args\": {\n        \"description\": \"Arguments to the entrypoint.\\nThe container image's CMD is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-type\": \"atomic\"\n       },\n       \"command\": {\n        \"description\": \"Entrypoint array. Not executed within a shell.\\nThe container image's ENTRYPOINT is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-type\": \"atomic\"\n       },\n       \"env\": {\n        \"description\": \"List of environment variables to set in the container.\\nCannot be updated.\",\n        \"items\": {\n         \"description\": \"EnvVar represents an environment variable present in a Container.\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n           \"type\": \"string\"\n          },\n          \"value\": {\n           \"description\": \"Variable references $(VAR_NAME) are expanded\\nusing the previously defined environment variables in the container and\\nany service environment variables. If a variable cannot be resolved,\\nthe reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.\\n\\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\".\\nEscaped references will never be expanded, regardless of whether the variable\\nexists or not.\\nDefaults to \\\"\\\".\",\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n           \"properties\": {\n            \"configMapKeyRef\": {\n             \"description\": \"Selects a key of a ConfigMap.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key to select.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"fieldRef\": {\n             \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`,\\nspec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\",\n             \"properties\": {\n              \"apiVersion\": {\n               \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n               \"type\": \"string\"\n              },\n              \"fieldPath\": {\n               \"description\": \"Path of the field to select in the specified API version.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"fieldPath\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"resourceFieldRef\": {\n             \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n             \"properties\": {\n              \"containerName\": {\n               \"description\": \"Container name: required for volumes, optional for env vars\",\n               \"type\": \"string\"\n              },\n              \"divisor\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"resource\": {\n               \"description\": \"Required: resource to select\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"resource\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"secretKeyRef\": {\n             \"description\": \"Selects a key of a secret in the pod's namespace\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"name\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"envFrom\": {\n        \"description\": \"List of sources to populate environment variables in the container.\\nThe keys defined within a source must be a C_IDENTIFIER. All invalid keys\\nwill be reported as an event when the container is starting. When a key exists in multiple\\nsources, the value associated with the last source will take precedence.\\nValues defined by an Env with a duplicate key will take precedence.\\nCannot be updated.\",\n        \"items\": {\n         \"description\": \"EnvFromSource represents the source of a set of ConfigMaps or Secrets\",\n         \"properties\": {\n          \"configMapRef\": {\n           \"description\": \"The ConfigMap to select from\",\n           \"properties\": {\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"prefix\": {\n           \"description\": \"Optional text to prepend to the name of each environment variable. Must be a C_IDENTIFIER.\",\n           \"type\": \"string\"\n          },\n          \"secretRef\": {\n           \"description\": \"The Secret to select from\",\n           \"properties\": {\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-type\": \"atomic\"\n       },\n       \"image\": {\n        \"description\": \"Container image name.\\nMore info: https://kubernetes.io/docs/concepts/containers/images\\nThis field is optional to allow higher level config management to default or override\\ncontainer images in workload controllers like Deployments and StatefulSets.\",\n        \"type\": \"string\"\n       },\n       \"imagePullPolicy\": {\n        \"description\": \"Image pull policy.\\nOne of Always, Never, IfNotPresent.\\nDefaults to Always if :latest tag is specified, or IfNotPresent otherwise.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\n        \"type\": \"string\"\n       },\n       \"lifecycle\": {\n        \"description\": \"Actions that the management system should take in response to container lifecycle events.\\nCannot be updated.\",\n        \"properties\": {\n         \"postStart\": {\n          \"description\": \"PostStart is called immediately after a container is created. If the handler fails,\\nthe container is terminated and restarted according to its restart policy.\\nOther management of the container blocks until the hook completes.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n          \"properties\": {\n           \"exec\": {\n            \"description\": \"Exec specifies a command to execute in the container.\",\n            \"properties\": {\n             \"command\": {\n              \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n              \"items\": {\n               \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"description\": \"The header field value\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"description\": \"Path to access on the HTTP server.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"sleep\": {\n            \"description\": \"Sleep represents a duration that the container should sleep.\",\n            \"properties\": {\n             \"seconds\": {\n              \"description\": \"Seconds is the number of seconds to sleep.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             }\n            },\n            \"required\": [\n             \"seconds\"\n            ],\n            \"type\": \"object\"\n           },\n           \"tcpSocket\": {\n            \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor backward compatibility. There is no validation of this field and\\nlifecycle hooks will fail at runtime when it is specified.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"preStop\": {\n          \"description\": \"PreStop is called immediately before a container is terminated due to an\\nAPI request or management event such as liveness/startup probe failure,\\npreemption, resource contention, etc. The handler is not called if the\\ncontainer crashes or exits. The Pod's termination grace period countdown begins before the\\nPreStop hook is executed. Regardless of the outcome of the handler, the\\ncontainer will eventually terminate within the Pod's termination grace\\nperiod (unless delayed by finalizers). Other management of the container blocks until the hook completes\\nor until the termination grace period is reached.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n          \"properties\": {\n           \"exec\": {\n            \"description\": \"Exec specifies a command to execute in the container.\",\n            \"properties\": {\n             \"command\": {\n              \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n              \"items\": {\n               \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"description\": \"The header field value\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"description\": \"Path to access on the HTTP server.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"sleep\": {\n            \"description\": \"Sleep represents a duration that the container should sleep.\",\n            \"properties\": {\n             \"seconds\": {\n              \"description\": \"Seconds is the number of seconds to sleep.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             }\n            },\n            \"required\": [\n             \"seconds\"\n            ],\n            \"type\": \"object\"\n           },\n           \"tcpSocket\": {\n            \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor backward compatibility. There is no validation of this field and\\nlifecycle hooks will fail at runtime when it is specified.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"stopSignal\": {\n          \"description\": \"StopSignal defines which signal will be sent to a container when it is being stopped.\\nIf not specified, the default is defined by the container runtime in use.\\nStopSignal can only be set for Pods with a non-empty .spec.os.name\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"livenessProbe\": {\n        \"description\": \"Periodic probe of container liveness.\\nContainer will be restarted if the probe fails.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies a command to execute in the container.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies a GRPC HealthCheckRequest.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"default\": \"\",\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies a connection to a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"name\": {\n        \"description\": \"Name of the container specified as a DNS_LABEL.\\nEach container in a pod must have a unique name (DNS_LABEL).\\nCannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"ports\": {\n        \"description\": \"List of ports to expose from the container. Not specifying a port here\\nDOES NOT prevent that port from being exposed. Any port which is\\nlistening on the default \\\"0.0.0.0\\\" address inside a container will be\\naccessible from the network.\\nModifying this array with strategic merge patch may corrupt the data.\\nFor more information See https://github.com/kubernetes/kubernetes/issues/108255.\\nCannot be updated.\",\n        \"items\": {\n         \"description\": \"ContainerPort represents a network port in a single container.\",\n         \"properties\": {\n          \"containerPort\": {\n           \"description\": \"Number of port to expose on the pod's IP address.\\nThis must be a valid port number, 0 \\u003c x \\u003c 65536.\",\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"hostIP\": {\n           \"description\": \"What host IP to bind the external port to.\",\n           \"type\": \"string\"\n          },\n          \"hostPort\": {\n           \"description\": \"Number of port to expose on the host.\\nIf specified, this must be a valid port number, 0 \\u003c x \\u003c 65536.\\nIf HostNetwork is specified, this must match ContainerPort.\\nMost containers do not need this.\",\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"name\": {\n           \"description\": \"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each\\nnamed port in a pod must have a unique name. Name for the port that can be\\nreferred to by services.\",\n           \"type\": \"string\"\n          },\n          \"protocol\": {\n           \"default\": \"TCP\",\n           \"description\": \"Protocol for port. Must be UDP, TCP, or SCTP.\\nDefaults to \\\"TCP\\\".\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"containerPort\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"containerPort\",\n         \"protocol\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"readinessProbe\": {\n        \"description\": \"Periodic probe of container service readiness.\\nContainer will be removed from service endpoints if the probe fails.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies a command to execute in the container.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies a GRPC HealthCheckRequest.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"default\": \"\",\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies a connection to a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"resizePolicy\": {\n        \"description\": \"Resources resize policy for the container.\",\n        \"items\": {\n         \"description\": \"ContainerResizePolicy represents resource resize policy for the container.\",\n         \"properties\": {\n          \"resourceName\": {\n           \"description\": \"Name of the resource to which this resource resize policy applies.\\nSupported values: cpu, memory.\",\n           \"type\": \"string\"\n          },\n          \"restartPolicy\": {\n           \"description\": \"Restart policy to apply when specified resource is resized.\\nIf not specified, it defaults to NotRequired.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"resourceName\",\n          \"restartPolicy\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-type\": \"atomic\"\n       },\n       \"resources\": {\n        \"description\": \"Compute Resources required by this container.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n        \"properties\": {\n         \"claims\": {\n          \"description\": \"Claims lists the names of resources, defined in spec.resourceClaims,\\nthat are used by this container.\\n\\nThis is an alpha field and requires enabling the\\nDynamicResourceAllocation feature gate.\\n\\nThis field is immutable. It can only be set for containers.\",\n          \"items\": {\n           \"description\": \"ResourceClaim references one entry in PodSpec.ResourceClaims.\",\n           \"properties\": {\n            \"name\": {\n             \"description\": \"Name must match the name of one entry in pod.spec.resourceClaims of\\nthe Pod where this field is used. It makes that resource available\\ninside a container.\",\n             \"type\": \"string\"\n            },\n            \"request\": {\n             \"description\": \"Request is the name chosen for a request in the referenced claim.\\nIf empty, everything from the claim is made available, otherwise\\nonly the result of this request.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"name\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"limits\": {\n          \"additionalProperties\": {\n           \"anyOf\": [\n            {\n             \"type\": \"integer\"\n            },\n            {\n             \"type\": \"string\"\n            }\n           ],\n           \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n           \"x-kubernetes-int-or-string\": true\n          },\n          \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n          \"type\": \"object\"\n         },\n         \"requests\": {\n          \"additionalProperties\": {\n           \"anyOf\": [\n            {\n             \"type\": \"integer\"\n            },\n            {\n             \"type\": \"string\"\n            }\n           ],\n           \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n           \"x-kubernetes-int-or-string\": true\n          },\n          \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value. Requests cannot exceed Limits.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"restartPolicy\": {\n        \"description\": \"RestartPolicy defines the restart behavior of individual containers in a pod.\\nThis field may only be set for init containers, and the only allowed value is \\\"Always\\\".\\nFor non-init containers or when this field is not specified,\\nthe restart behavior is defined by the Pod's restart policy and the container type.\\nSetting the RestartPolicy as \\\"Always\\\" for the init container will have the following effect:\\nthis init container will be continually restarted on\\nexit until all regular containers have terminated. Once all regular\\ncontainers have completed, all init containers with restartPolicy \\\"Always\\\"\\nwill be shut down. This lifecycle differs from normal init containers and\\nis often referred to as a \\\"sidecar\\\" container. Although this init\\ncontainer still starts in the init container sequence, it does not wait\\nfor the container to complete before proceeding to the next init\\ncontainer. Instead, the next init container starts immediately after this\\ninit container is started, or after any startupProbe has successfully\\ncompleted.\",\n        \"type\": \"string\"\n       },\n       \"securityContext\": {\n        \"description\": \"SecurityContext defines the security options the container should be run with.\\nIf set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.\\nMore info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/\",\n        \"properties\": {\n         \"allowPrivilegeEscalation\": {\n          \"description\": \"AllowPrivilegeEscalation controls whether a process can gain more\\nprivileges than its parent process. This bool directly controls if\\nthe no_new_privs flag will be set on the container process.\\nAllowPrivilegeEscalation is true always when the container is:\\n1) run as Privileged\\n2) has CAP_SYS_ADMIN\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"appArmorProfile\": {\n          \"description\": \"appArmorProfile is the AppArmor options to use by this container. If set, this profile\\noverrides the pod's appArmorProfile.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"localhostProfile\": {\n            \"description\": \"localhostProfile indicates a profile loaded on the node that should be used.\\nThe profile must be preconfigured on the node to work.\\nMust match the loaded name of the profile.\\nMust be set if and only if type is \\\"Localhost\\\".\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"type indicates which kind of AppArmor profile will be applied.\\nValid options are:\\n  Localhost - a profile pre-loaded on the node.\\n  RuntimeDefault - the container runtime's default profile.\\n  Unconfined - no AppArmor enforcement.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"capabilities\": {\n          \"description\": \"The capabilities to add/drop when running containers.\\nDefaults to the default set of capabilities granted by the container runtime.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"add\": {\n            \"description\": \"Added capabilities\",\n            \"items\": {\n             \"description\": \"Capability represent POSIX capabilities type\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"drop\": {\n            \"description\": \"Removed capabilities\",\n            \"items\": {\n             \"description\": \"Capability represent POSIX capabilities type\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"privileged\": {\n          \"description\": \"Run container in privileged mode.\\nProcesses in privileged containers are essentially equivalent to root on the host.\\nDefaults to false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"procMount\": {\n          \"description\": \"procMount denotes the type of proc mount to use for the containers.\\nThe default value is Default which uses the container runtime defaults for\\nreadonly paths and masked paths.\\nThis requires the ProcMountType feature flag to be enabled.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"string\"\n         },\n         \"readOnlyRootFilesystem\": {\n          \"description\": \"Whether this container has a read-only root filesystem.\\nDefault is false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"runAsGroup\": {\n          \"description\": \"The GID to run the entrypoint of the container process.\\nUses runtime default if unset.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"runAsNonRoot\": {\n          \"description\": \"Indicates that the container must run as a non-root user.\\nIf true, the Kubelet will validate the image at runtime to ensure that it\\ndoes not run as UID 0 (root) and fail to start the container if it does.\\nIf unset or false, no such validation will be performed.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n          \"type\": \"boolean\"\n         },\n         \"runAsUser\": {\n          \"description\": \"The UID to run the entrypoint of the container process.\\nDefaults to user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"seLinuxOptions\": {\n          \"description\": \"The SELinux context to be applied to the container.\\nIf unspecified, the container runtime will allocate a random SELinux context for each\\ncontainer.  May also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"level\": {\n            \"description\": \"Level is SELinux level label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"role\": {\n            \"description\": \"Role is a SELinux role label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type is a SELinux type label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"user\": {\n            \"description\": \"User is a SELinux user label that applies to the container.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"seccompProfile\": {\n          \"description\": \"The seccomp options to use by this container. If seccomp options are\\nprovided at both the pod \\u0026 container level, the container options\\noverride the pod options.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"localhostProfile\": {\n            \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used.\\nThe profile must be preconfigured on the node to work.\\nMust be a descending path, relative to the kubelet's configured seccomp profile location.\\nMust be set if type is \\\"Localhost\\\". Must NOT be set for any other type.\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"type indicates which kind of seccomp profile will be applied.\\nValid options are:\\n\\nLocalhost - a profile defined in a file on the node should be used.\\nRuntimeDefault - the container runtime default profile should be used.\\nUnconfined - no profile should be applied.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"windowsOptions\": {\n          \"description\": \"The Windows specific settings applied to all containers.\\nIf unspecified, the options from the PodSecurityContext will be used.\\nIf set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is linux.\",\n          \"properties\": {\n           \"gmsaCredentialSpec\": {\n            \"description\": \"GMSACredentialSpec is where the GMSA admission webhook\\n(https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the\\nGMSA credential spec named by the GMSACredentialSpecName field.\",\n            \"type\": \"string\"\n           },\n           \"gmsaCredentialSpecName\": {\n            \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n            \"type\": \"string\"\n           },\n           \"hostProcess\": {\n            \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container.\\nAll of a Pod's containers must have the same effective HostProcess value\\n(it is not allowed to have a mix of HostProcess containers and non-HostProcess containers).\\nIn addition, if HostProcess is true then HostNetwork must also be set to true.\",\n            \"type\": \"boolean\"\n           },\n           \"runAsUserName\": {\n            \"description\": \"The UserName in Windows to run the entrypoint of the container process.\\nDefaults to the user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext. If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"startupProbe\": {\n        \"description\": \"StartupProbe indicates that the Pod has successfully initialized.\\nIf specified, no other probes are executed until this completes successfully.\\nIf this probe fails, the Pod will be restarted, just as if the livenessProbe failed.\\nThis can be used to provide different probe parameters at the beginning of a Pod's lifecycle,\\nwhen it might take a long time to load data or warm a cache, than during steady-state operation.\\nThis cannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies a command to execute in the container.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies a GRPC HealthCheckRequest.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"default\": \"\",\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies a connection to a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"stdin\": {\n        \"description\": \"Whether this container should allocate a buffer for stdin in the container runtime. If this\\nis not set, reads from stdin in the container will always result in EOF.\\nDefault is false.\",\n        \"type\": \"boolean\"\n       },\n       \"stdinOnce\": {\n        \"description\": \"Whether the container runtime should close the stdin channel after it has been opened by\\na single attach. When stdin is true the stdin stream will remain open across multiple attach\\nsessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the\\nfirst client attaches to stdin, and then remains open and accepts data until the client disconnects,\\nat which time stdin is closed and remains closed until the container is restarted. If this\\nflag is false, a container processes that reads from stdin will never receive an EOF.\\nDefault is false\",\n        \"type\": \"boolean\"\n       },\n       \"terminationMessagePath\": {\n        \"description\": \"Optional: Path at which the file to which the container's termination message\\nwill be written is mounted into the container's filesystem.\\nMessage written is intended to be brief final status, such as an assertion failure message.\\nWill be truncated by the node if greater than 4096 bytes. The total message length across\\nall containers will be limited to 12kb.\\nDefaults to /dev/termination-log.\\nCannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"terminationMessagePolicy\": {\n        \"description\": \"Indicate how the termination message should be populated. File will use the contents of\\nterminationMessagePath to populate the container status message on both success and failure.\\nFallbackToLogsOnError will use the last chunk of container log output if the termination\\nmessage file is empty and the container exited with an error.\\nThe log output is limited to 2048 bytes or 80 lines, whichever is smaller.\\nDefaults to File.\\nCannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"tty\": {\n        \"description\": \"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.\\nDefault is false.\",\n        \"type\": \"boolean\"\n       },\n       \"volumeDevices\": {\n        \"description\": \"volumeDevices is the list of block devices to be used by the container.\",\n        \"items\": {\n         \"description\": \"volumeDevice describes a mapping of a raw block device within a container.\",\n         \"properties\": {\n          \"devicePath\": {\n           \"description\": \"devicePath is the path inside of the container that the device will be mapped to.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"name must match the name of a persistentVolumeClaim in the pod\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"devicePath\",\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"devicePath\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"volumeMounts\": {\n        \"description\": \"Pod volumes to mount into the container's filesystem.\\nCannot be updated.\",\n        \"items\": {\n         \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n         \"properties\": {\n          \"mountPath\": {\n           \"description\": \"Path within the container at which the volume should be mounted.  Must\\nnot contain ':'.\",\n           \"type\": \"string\"\n          },\n          \"mountPropagation\": {\n           \"description\": \"mountPropagation determines how mounts are propagated from the host\\nto container and the other way around.\\nWhen not set, MountPropagationNone is used.\\nThis field is beta in 1.10.\\nWhen RecursiveReadOnly is set to IfPossible or to Enabled, MountPropagation must be None or unspecified\\n(which defaults to None).\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"This must match the Name of a Volume.\",\n           \"type\": \"string\"\n          },\n          \"readOnly\": {\n           \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified).\\nDefaults to false.\",\n           \"type\": \"boolean\"\n          },\n          \"recursiveReadOnly\": {\n           \"description\": \"RecursiveReadOnly specifies whether read-only mounts should be handled\\nrecursively.\\n\\nIf ReadOnly is false, this field has no meaning and must be unspecified.\\n\\nIf ReadOnly is true, and this field is set to Disabled, the mount is not made\\nrecursively read-only.  If this field is set to IfPossible, the mount is made\\nrecursively read-only, if it is supported by the container runtime.  If this\\nfield is set to Enabled, the mount is made recursively read-only if it is\\nsupported by the container runtime, otherwise the pod will not be started and\\nan error will be generated to indicate the reason.\\n\\nIf this field is set to IfPossible or Enabled, MountPropagation must be set to\\nNone (or be unspecified, which defaults to None).\\n\\nIf this field is not specified, it is treated as an equivalent of Disabled.\",\n           \"type\": \"string\"\n          },\n          \"subPath\": {\n           \"description\": \"Path within the volume from which the container's volume should be mounted.\\nDefaults to \\\"\\\" (volume's root).\",\n           \"type\": \"string\"\n          },\n          \"subPathExpr\": {\n           \"description\": \"Expanded path within the volume from which the container's volume should be mounted.\\nBehaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment.\\nDefaults to \\\"\\\" (volume's root).\\nSubPathExpr and SubPath are mutually exclusive.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"mountPath\",\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"mountPath\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"workingDir\": {\n        \"description\": \"Container's working directory.\\nIf not specified, the container runtime's default will be used, which\\nmight be configured in the container image.\\nCannot be updated.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"dnsConfig\": {\n     \"description\": \"Defines the DNS configuration for the pods.\",\n     \"properties\": {\n      \"nameservers\": {\n       \"description\": \"A list of DNS name server IP addresses.\\nThis will be appended to the base nameservers generated from DNSPolicy.\",\n       \"items\": {\n        \"minLength\": 1,\n        \"type\": \"string\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-type\": \"set\"\n      },\n      \"options\": {\n       \"description\": \"A list of DNS resolver options.\\nThis will be merged with the base options generated from DNSPolicy.\\nResolution options given in Options\\nwill override those that appear in the base DNSPolicy.\",\n       \"items\": {\n        \"description\": \"PodDNSConfigOption defines DNS resolver options of a pod.\",\n        \"properties\": {\n         \"name\": {\n          \"description\": \"Name is required and must be unique.\",\n          \"minLength\": 1,\n          \"type\": \"string\"\n         },\n         \"value\": {\n          \"description\": \"Value is optional.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"name\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-map-keys\": [\n        \"name\"\n       ],\n       \"x-kubernetes-list-type\": \"map\"\n      },\n      \"searches\": {\n       \"description\": \"A list of DNS search domains for host-name lookup.\\nThis will be appended to the base search paths generated from DNSPolicy.\",\n       \"items\": {\n        \"minLength\": 1,\n        \"type\": \"string\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-type\": \"set\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"dnsPolicy\": {\n     \"description\": \"Defines the DNS policy for the pods.\",\n     \"enum\": [\n      \"ClusterFirstWithHostNet\",\n      \"ClusterFirst\",\n      \"Default\",\n      \"None\"\n     ],\n     \"type\": \"string\"\n    },\n    \"enableFeatures\": {\n     \"description\": \"Enable access to Alertmanager feature flags. By default, no features are enabled.\\nEnabling features which are disabled by default is entirely outside the\\nscope of what the maintainers will support and by doing so, you accept\\nthat this behaviour may break at any time without notice.\\n\\nIt requires Alertmanager \\u003e= 0.27.0.\",\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"enableServiceLinks\": {\n     \"description\": \"Indicates whether information about services should be injected into pod's environment variables\",\n     \"type\": \"boolean\"\n    },\n    \"externalUrl\": {\n     \"description\": \"The external URL the Alertmanager instances will be available under. This is\\nnecessary to generate correct URLs. This is necessary if Alertmanager is not\\nserved from root of a DNS name.\",\n     \"type\": \"string\"\n    },\n    \"forceEnableClusterMode\": {\n     \"description\": \"ForceEnableClusterMode ensures Alertmanager does not deactivate the cluster mode when running with a single replica.\\nUse case is e.g. spanning an Alertmanager cluster across Kubernetes clusters with a single replica in each.\",\n     \"type\": \"boolean\"\n    },\n    \"hostAliases\": {\n     \"description\": \"Pods' hostAliases configuration\",\n     \"items\": {\n      \"description\": \"HostAlias holds the mapping between IP and hostnames that will be injected as an entry in the\\npod's hosts file.\",\n      \"properties\": {\n       \"hostnames\": {\n        \"description\": \"Hostnames for the above IP address.\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\"\n       },\n       \"ip\": {\n        \"description\": \"IP address of the host file entry.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"hostnames\",\n       \"ip\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\",\n     \"x-kubernetes-list-map-keys\": [\n      \"ip\"\n     ],\n     \"x-kubernetes-list-type\": \"map\"\n    },\n    \"image\": {\n     \"description\": \"Image if specified has precedence over baseImage, tag and sha\\ncombinations. Specifying the version is still necessary to ensure the\\nPrometheus Operator knows what version of Alertmanager is being\\nconfigured.\",\n     \"type\": \"string\"\n    },\n    \"imagePullPolicy\": {\n     \"description\": \"Image pull policy for the 'alertmanager', 'init-config-reloader' and 'config-reloader' containers.\\nSee https://kubernetes.io/docs/concepts/containers/images/#image-pull-policy for more details.\",\n     \"enum\": [\n      \"\",\n      \"Always\",\n      \"Never\",\n      \"IfNotPresent\"\n     ],\n     \"type\": \"string\"\n    },\n    \"imagePullSecrets\": {\n     \"description\": \"An optional list of references to secrets in the same namespace\\nto use for pulling prometheus and alertmanager images from registries\\nsee http://kubernetes.io/docs/user-guide/images#specifying-imagepullsecrets-on-a-pod\",\n     \"items\": {\n      \"description\": \"LocalObjectReference contains enough information to let you locate the\\nreferenced object inside the same namespace.\",\n      \"properties\": {\n       \"name\": {\n        \"default\": \"\",\n        \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n        \"type\": \"string\"\n       }\n      },\n      \"type\": \"object\",\n      \"x-kubernetes-map-type\": \"atomic\"\n     },\n     \"type\": \"array\"\n    },\n    \"initContainers\": {\n     \"description\": \"InitContainers allows adding initContainers to the pod definition. Those can be used to e.g.\\nfetch secrets for injection into the Alertmanager configuration from external sources. Any\\nerrors during the execution of an initContainer will lead to a restart of the Pod. More info: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/\\nInitContainers described here modify an operator\\ngenerated init containers if they share the same name and modifications are\\ndone via a strategic merge patch. The current init container name is:\\n`init-config-reloader`. Overriding init containers is entirely outside the\\nscope of what the maintainers will support and by doing so, you accept that\\nthis behaviour may break at any time without notice.\",\n     \"items\": {\n      \"description\": \"A single application container that you want to run within a pod.\",\n      \"properties\": {\n       \"args\": {\n        \"description\": \"Arguments to the entrypoint.\\nThe container image's CMD is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-type\": \"atomic\"\n       },\n       \"command\": {\n        \"description\": \"Entrypoint array. Not executed within a shell.\\nThe container image's ENTRYPOINT is used if this is not provided.\\nVariable references $(VAR_NAME) are expanded using the container's environment. If a variable\\ncannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. \\\"$$(VAR_NAME)\\\" will\\nproduce the string literal \\\"$(VAR_NAME)\\\". Escaped references will never be expanded, regardless\\nof whether the variable exists or not. Cannot be updated.\\nMore info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-type\": \"atomic\"\n       },\n       \"env\": {\n        \"description\": \"List of environment variables to set in the container.\\nCannot be updated.\",\n        \"items\": {\n         \"description\": \"EnvVar represents an environment variable present in a Container.\",\n         \"properties\": {\n          \"name\": {\n           \"description\": \"Name of the environment variable. Must be a C_IDENTIFIER.\",\n           \"type\": \"string\"\n          },\n          \"value\": {\n           \"description\": \"Variable references $(VAR_NAME) are expanded\\nusing the previously defined environment variables in the container and\\nany service environment variables. If a variable cannot be resolved,\\nthe reference in the input string will be unchanged. Double $$ are reduced\\nto a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.\\n\\\"$$(VAR_NAME)\\\" will produce the string literal \\\"$(VAR_NAME)\\\".\\nEscaped references will never be expanded, regardless of whether the variable\\nexists or not.\\nDefaults to \\\"\\\".\",\n           \"type\": \"string\"\n          },\n          \"valueFrom\": {\n           \"description\": \"Source for the environment variable's value. Cannot be used if value is not empty.\",\n           \"properties\": {\n            \"configMapKeyRef\": {\n             \"description\": \"Selects a key of a ConfigMap.\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key to select.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"fieldRef\": {\n             \"description\": \"Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['\\u003cKEY\\u003e']`, `metadata.annotations['\\u003cKEY\\u003e']`,\\nspec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.\",\n             \"properties\": {\n              \"apiVersion\": {\n               \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n               \"type\": \"string\"\n              },\n              \"fieldPath\": {\n               \"description\": \"Path of the field to select in the specified API version.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"fieldPath\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"resourceFieldRef\": {\n             \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.\",\n             \"properties\": {\n              \"containerName\": {\n               \"description\": \"Container name: required for volumes, optional for env vars\",\n               \"type\": \"string\"\n              },\n              \"divisor\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"resource\": {\n               \"description\": \"Required: resource to select\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"resource\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"secretKeyRef\": {\n             \"description\": \"Selects a key of a secret in the pod's namespace\",\n             \"properties\": {\n              \"key\": {\n               \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"Specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"required\": [\n              \"key\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"required\": [\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"name\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"envFrom\": {\n        \"description\": \"List of sources to populate environment variables in the container.\\nThe keys defined within a source must be a C_IDENTIFIER. All invalid keys\\nwill be reported as an event when the container is starting. When a key exists in multiple\\nsources, the value associated with the last source will take precedence.\\nValues defined by an Env with a duplicate key will take precedence.\\nCannot be updated.\",\n        \"items\": {\n         \"description\": \"EnvFromSource represents the source of a set of ConfigMaps or Secrets\",\n         \"properties\": {\n          \"configMapRef\": {\n           \"description\": \"The ConfigMap to select from\",\n           \"properties\": {\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"prefix\": {\n           \"description\": \"Optional text to prepend to the name of each environment variable. Must be a C_IDENTIFIER.\",\n           \"type\": \"string\"\n          },\n          \"secretRef\": {\n           \"description\": \"The Secret to select from\",\n           \"properties\": {\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-type\": \"atomic\"\n       },\n       \"image\": {\n        \"description\": \"Container image name.\\nMore info: https://kubernetes.io/docs/concepts/containers/images\\nThis field is optional to allow higher level config management to default or override\\ncontainer images in workload controllers like Deployments and StatefulSets.\",\n        \"type\": \"string\"\n       },\n       \"imagePullPolicy\": {\n        \"description\": \"Image pull policy.\\nOne of Always, Never, IfNotPresent.\\nDefaults to Always if :latest tag is specified, or IfNotPresent otherwise.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/containers/images#updating-images\",\n        \"type\": \"string\"\n       },\n       \"lifecycle\": {\n        \"description\": \"Actions that the management system should take in response to container lifecycle events.\\nCannot be updated.\",\n        \"properties\": {\n         \"postStart\": {\n          \"description\": \"PostStart is called immediately after a container is created. If the handler fails,\\nthe container is terminated and restarted according to its restart policy.\\nOther management of the container blocks until the hook completes.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n          \"properties\": {\n           \"exec\": {\n            \"description\": \"Exec specifies a command to execute in the container.\",\n            \"properties\": {\n             \"command\": {\n              \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n              \"items\": {\n               \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"description\": \"The header field value\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"description\": \"Path to access on the HTTP server.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"sleep\": {\n            \"description\": \"Sleep represents a duration that the container should sleep.\",\n            \"properties\": {\n             \"seconds\": {\n              \"description\": \"Seconds is the number of seconds to sleep.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             }\n            },\n            \"required\": [\n             \"seconds\"\n            ],\n            \"type\": \"object\"\n           },\n           \"tcpSocket\": {\n            \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor backward compatibility. There is no validation of this field and\\nlifecycle hooks will fail at runtime when it is specified.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"preStop\": {\n          \"description\": \"PreStop is called immediately before a container is terminated due to an\\nAPI request or management event such as liveness/startup probe failure,\\npreemption, resource contention, etc. The handler is not called if the\\ncontainer crashes or exits. The Pod's termination grace period countdown begins before the\\nPreStop hook is executed. Regardless of the outcome of the handler, the\\ncontainer will eventually terminate within the Pod's termination grace\\nperiod (unless delayed by finalizers). Other management of the container blocks until the hook completes\\nor until the termination grace period is reached.\\nMore info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks\",\n          \"properties\": {\n           \"exec\": {\n            \"description\": \"Exec specifies a command to execute in the container.\",\n            \"properties\": {\n             \"command\": {\n              \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             }\n            },\n            \"type\": \"object\"\n           },\n           \"httpGet\": {\n            \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n              \"type\": \"string\"\n             },\n             \"httpHeaders\": {\n              \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n              \"items\": {\n               \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n               \"properties\": {\n                \"name\": {\n                 \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n                 \"type\": \"string\"\n                },\n                \"value\": {\n                 \"description\": \"The header field value\",\n                 \"type\": \"string\"\n                }\n               },\n               \"required\": [\n                \"name\",\n                \"value\"\n               ],\n               \"type\": \"object\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"path\": {\n              \"description\": \"Path to access on the HTTP server.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"scheme\": {\n              \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           },\n           \"sleep\": {\n            \"description\": \"Sleep represents a duration that the container should sleep.\",\n            \"properties\": {\n             \"seconds\": {\n              \"description\": \"Seconds is the number of seconds to sleep.\",\n              \"format\": \"int64\",\n              \"type\": \"integer\"\n             }\n            },\n            \"required\": [\n             \"seconds\"\n            ],\n            \"type\": \"object\"\n           },\n           \"tcpSocket\": {\n            \"description\": \"Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept\\nfor backward compatibility. There is no validation of this field and\\nlifecycle hooks will fail at runtime when it is specified.\",\n            \"properties\": {\n             \"host\": {\n              \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n              \"type\": \"string\"\n             },\n             \"port\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n              \"x-kubernetes-int-or-string\": true\n             }\n            },\n            \"required\": [\n             \"port\"\n            ],\n            \"type\": \"object\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"stopSignal\": {\n          \"description\": \"StopSignal defines which signal will be sent to a container when it is being stopped.\\nIf not specified, the default is defined by the container runtime in use.\\nStopSignal can only be set for Pods with a non-empty .spec.os.name\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"livenessProbe\": {\n        \"description\": \"Periodic probe of container liveness.\\nContainer will be restarted if the probe fails.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies a command to execute in the container.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies a GRPC HealthCheckRequest.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"default\": \"\",\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies a connection to a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"name\": {\n        \"description\": \"Name of the container specified as a DNS_LABEL.\\nEach container in a pod must have a unique name (DNS_LABEL).\\nCannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"ports\": {\n        \"description\": \"List of ports to expose from the container. Not specifying a port here\\nDOES NOT prevent that port from being exposed. Any port which is\\nlistening on the default \\\"0.0.0.0\\\" address inside a container will be\\naccessible from the network.\\nModifying this array with strategic merge patch may corrupt the data.\\nFor more information See https://github.com/kubernetes/kubernetes/issues/108255.\\nCannot be updated.\",\n        \"items\": {\n         \"description\": \"ContainerPort represents a network port in a single container.\",\n         \"properties\": {\n          \"containerPort\": {\n           \"description\": \"Number of port to expose on the pod's IP address.\\nThis must be a valid port number, 0 \\u003c x \\u003c 65536.\",\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"hostIP\": {\n           \"description\": \"What host IP to bind the external port to.\",\n           \"type\": \"string\"\n          },\n          \"hostPort\": {\n           \"description\": \"Number of port to expose on the host.\\nIf specified, this must be a valid port number, 0 \\u003c x \\u003c 65536.\\nIf HostNetwork is specified, this must match ContainerPort.\\nMost containers do not need this.\",\n           \"format\": \"int32\",\n           \"type\": \"integer\"\n          },\n          \"name\": {\n           \"description\": \"If specified, this must be an IANA_SVC_NAME and unique within the pod. Each\\nnamed port in a pod must have a unique name. Name for the port that can be\\nreferred to by services.\",\n           \"type\": \"string\"\n          },\n          \"protocol\": {\n           \"default\": \"TCP\",\n           \"description\": \"Protocol for port. Must be UDP, TCP, or SCTP.\\nDefaults to \\\"TCP\\\".\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"containerPort\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"containerPort\",\n         \"protocol\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"readinessProbe\": {\n        \"description\": \"Periodic probe of container service readiness.\\nContainer will be removed from service endpoints if the probe fails.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies a command to execute in the container.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies a GRPC HealthCheckRequest.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"default\": \"\",\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies a connection to a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"resizePolicy\": {\n        \"description\": \"Resources resize policy for the container.\",\n        \"items\": {\n         \"description\": \"ContainerResizePolicy represents resource resize policy for the container.\",\n         \"properties\": {\n          \"resourceName\": {\n           \"description\": \"Name of the resource to which this resource resize policy applies.\\nSupported values: cpu, memory.\",\n           \"type\": \"string\"\n          },\n          \"restartPolicy\": {\n           \"description\": \"Restart policy to apply when specified resource is resized.\\nIf not specified, it defaults to NotRequired.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"resourceName\",\n          \"restartPolicy\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-type\": \"atomic\"\n       },\n       \"resources\": {\n        \"description\": \"Compute Resources required by this container.\\nCannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n        \"properties\": {\n         \"claims\": {\n          \"description\": \"Claims lists the names of resources, defined in spec.resourceClaims,\\nthat are used by this container.\\n\\nThis is an alpha field and requires enabling the\\nDynamicResourceAllocation feature gate.\\n\\nThis field is immutable. It can only be set for containers.\",\n          \"items\": {\n           \"description\": \"ResourceClaim references one entry in PodSpec.ResourceClaims.\",\n           \"properties\": {\n            \"name\": {\n             \"description\": \"Name must match the name of one entry in pod.spec.resourceClaims of\\nthe Pod where this field is used. It makes that resource available\\ninside a container.\",\n             \"type\": \"string\"\n            },\n            \"request\": {\n             \"description\": \"Request is the name chosen for a request in the referenced claim.\\nIf empty, everything from the claim is made available, otherwise\\nonly the result of this request.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-map-keys\": [\n           \"name\"\n          ],\n          \"x-kubernetes-list-type\": \"map\"\n         },\n         \"limits\": {\n          \"additionalProperties\": {\n           \"anyOf\": [\n            {\n             \"type\": \"integer\"\n            },\n            {\n             \"type\": \"string\"\n            }\n           ],\n           \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n           \"x-kubernetes-int-or-string\": true\n          },\n          \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n          \"type\": \"object\"\n         },\n         \"requests\": {\n          \"additionalProperties\": {\n           \"anyOf\": [\n            {\n             \"type\": \"integer\"\n            },\n            {\n             \"type\": \"string\"\n            }\n           ],\n           \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n           \"x-kubernetes-int-or-string\": true\n          },\n          \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value. Requests cannot exceed Limits.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"restartPolicy\": {\n        \"description\": \"RestartPolicy defines the restart behavior of individual containers in a pod.\\nThis field may only be set for init containers, and the only allowed value is \\\"Always\\\".\\nFor non-init containers or when this field is not specified,\\nthe restart behavior is defined by the Pod's restart policy and the container type.\\nSetting the RestartPolicy as \\\"Always\\\" for the init container will have the following effect:\\nthis init container will be continually restarted on\\nexit until all regular containers have terminated. Once all regular\\ncontainers have completed, all init containers with restartPolicy \\\"Always\\\"\\nwill be shut down. This lifecycle differs from normal init containers and\\nis often referred to as a \\\"sidecar\\\" container. Although this init\\ncontainer still starts in the init container sequence, it does not wait\\nfor the container to complete before proceeding to the next init\\ncontainer. Instead, the next init container starts immediately after this\\ninit container is started, or after any startupProbe has successfully\\ncompleted.\",\n        \"type\": \"string\"\n       },\n       \"securityContext\": {\n        \"description\": \"SecurityContext defines the security options the container should be run with.\\nIf set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.\\nMore info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/\",\n        \"properties\": {\n         \"allowPrivilegeEscalation\": {\n          \"description\": \"AllowPrivilegeEscalation controls whether a process can gain more\\nprivileges than its parent process. This bool directly controls if\\nthe no_new_privs flag will be set on the container process.\\nAllowPrivilegeEscalation is true always when the container is:\\n1) run as Privileged\\n2) has CAP_SYS_ADMIN\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"appArmorProfile\": {\n          \"description\": \"appArmorProfile is the AppArmor options to use by this container. If set, this profile\\noverrides the pod's appArmorProfile.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"localhostProfile\": {\n            \"description\": \"localhostProfile indicates a profile loaded on the node that should be used.\\nThe profile must be preconfigured on the node to work.\\nMust match the loaded name of the profile.\\nMust be set if and only if type is \\\"Localhost\\\".\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"type indicates which kind of AppArmor profile will be applied.\\nValid options are:\\n  Localhost - a profile pre-loaded on the node.\\n  RuntimeDefault - the container runtime's default profile.\\n  Unconfined - no AppArmor enforcement.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"capabilities\": {\n          \"description\": \"The capabilities to add/drop when running containers.\\nDefaults to the default set of capabilities granted by the container runtime.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"add\": {\n            \"description\": \"Added capabilities\",\n            \"items\": {\n             \"description\": \"Capability represent POSIX capabilities type\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"drop\": {\n            \"description\": \"Removed capabilities\",\n            \"items\": {\n             \"description\": \"Capability represent POSIX capabilities type\",\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"privileged\": {\n          \"description\": \"Run container in privileged mode.\\nProcesses in privileged containers are essentially equivalent to root on the host.\\nDefaults to false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"procMount\": {\n          \"description\": \"procMount denotes the type of proc mount to use for the containers.\\nThe default value is Default which uses the container runtime defaults for\\nreadonly paths and masked paths.\\nThis requires the ProcMountType feature flag to be enabled.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"string\"\n         },\n         \"readOnlyRootFilesystem\": {\n          \"description\": \"Whether this container has a read-only root filesystem.\\nDefault is false.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"type\": \"boolean\"\n         },\n         \"runAsGroup\": {\n          \"description\": \"The GID to run the entrypoint of the container process.\\nUses runtime default if unset.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"runAsNonRoot\": {\n          \"description\": \"Indicates that the container must run as a non-root user.\\nIf true, the Kubelet will validate the image at runtime to ensure that it\\ndoes not run as UID 0 (root) and fail to start the container if it does.\\nIf unset or false, no such validation will be performed.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n          \"type\": \"boolean\"\n         },\n         \"runAsUser\": {\n          \"description\": \"The UID to run the entrypoint of the container process.\\nDefaults to user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"seLinuxOptions\": {\n          \"description\": \"The SELinux context to be applied to the container.\\nIf unspecified, the container runtime will allocate a random SELinux context for each\\ncontainer.  May also be set in PodSecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"level\": {\n            \"description\": \"Level is SELinux level label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"role\": {\n            \"description\": \"Role is a SELinux role label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"Type is a SELinux type label that applies to the container.\",\n            \"type\": \"string\"\n           },\n           \"user\": {\n            \"description\": \"User is a SELinux user label that applies to the container.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"seccompProfile\": {\n          \"description\": \"The seccomp options to use by this container. If seccomp options are\\nprovided at both the pod \\u0026 container level, the container options\\noverride the pod options.\\nNote that this field cannot be set when spec.os.name is windows.\",\n          \"properties\": {\n           \"localhostProfile\": {\n            \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used.\\nThe profile must be preconfigured on the node to work.\\nMust be a descending path, relative to the kubelet's configured seccomp profile location.\\nMust be set if type is \\\"Localhost\\\". Must NOT be set for any other type.\",\n            \"type\": \"string\"\n           },\n           \"type\": {\n            \"description\": \"type indicates which kind of seccomp profile will be applied.\\nValid options are:\\n\\nLocalhost - a profile defined in a file on the node should be used.\\nRuntimeDefault - the container runtime default profile should be used.\\nUnconfined - no profile should be applied.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"type\"\n          ],\n          \"type\": \"object\"\n         },\n         \"windowsOptions\": {\n          \"description\": \"The Windows specific settings applied to all containers.\\nIf unspecified, the options from the PodSecurityContext will be used.\\nIf set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is linux.\",\n          \"properties\": {\n           \"gmsaCredentialSpec\": {\n            \"description\": \"GMSACredentialSpec is where the GMSA admission webhook\\n(https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the\\nGMSA credential spec named by the GMSACredentialSpecName field.\",\n            \"type\": \"string\"\n           },\n           \"gmsaCredentialSpecName\": {\n            \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n            \"type\": \"string\"\n           },\n           \"hostProcess\": {\n            \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container.\\nAll of a Pod's containers must have the same effective HostProcess value\\n(it is not allowed to have a mix of HostProcess containers and non-HostProcess containers).\\nIn addition, if HostProcess is true then HostNetwork must also be set to true.\",\n            \"type\": \"boolean\"\n           },\n           \"runAsUserName\": {\n            \"description\": \"The UserName in Windows to run the entrypoint of the container process.\\nDefaults to the user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext. If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"startupProbe\": {\n        \"description\": \"StartupProbe indicates that the Pod has successfully initialized.\\nIf specified, no other probes are executed until this completes successfully.\\nIf this probe fails, the Pod will be restarted, just as if the livenessProbe failed.\\nThis can be used to provide different probe parameters at the beginning of a Pod's lifecycle,\\nwhen it might take a long time to load data or warm a cache, than during steady-state operation.\\nThis cannot be updated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n        \"properties\": {\n         \"exec\": {\n          \"description\": \"Exec specifies a command to execute in the container.\",\n          \"properties\": {\n           \"command\": {\n            \"description\": \"Command is the command line to execute inside the container, the working directory for the\\ncommand  is root ('/') in the container's filesystem. The command is simply exec'd, it is\\nnot run inside a shell, so traditional shell instructions ('|', etc) won't work. To use\\na shell, you need to explicitly call out to that shell.\\nExit status of 0 is treated as live/healthy and non-zero is unhealthy.\",\n            \"items\": {\n             \"type\": \"string\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           }\n          },\n          \"type\": \"object\"\n         },\n         \"failureThreshold\": {\n          \"description\": \"Minimum consecutive failures for the probe to be considered failed after having succeeded.\\nDefaults to 3. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"grpc\": {\n          \"description\": \"GRPC specifies a GRPC HealthCheckRequest.\",\n          \"properties\": {\n           \"port\": {\n            \"description\": \"Port number of the gRPC service. Number must be in the range 1 to 65535.\",\n            \"format\": \"int32\",\n            \"type\": \"integer\"\n           },\n           \"service\": {\n            \"default\": \"\",\n            \"description\": \"Service is the name of the service to place in the gRPC HealthCheckRequest\\n(see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).\\n\\nIf this is not specified, the default behavior is defined by gRPC.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"httpGet\": {\n          \"description\": \"HTTPGet specifies an HTTP GET request to perform.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Host name to connect to, defaults to the pod IP. You probably want to set\\n\\\"Host\\\" in httpHeaders instead.\",\n            \"type\": \"string\"\n           },\n           \"httpHeaders\": {\n            \"description\": \"Custom headers to set in the request. HTTP allows repeated headers.\",\n            \"items\": {\n             \"description\": \"HTTPHeader describes a custom header to be used in HTTP probes\",\n             \"properties\": {\n              \"name\": {\n               \"description\": \"The header field name.\\nThis will be canonicalized upon output, so case-variant names will be understood as the same header.\",\n               \"type\": \"string\"\n              },\n              \"value\": {\n               \"description\": \"The header field value\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"name\",\n              \"value\"\n             ],\n             \"type\": \"object\"\n            },\n            \"type\": \"array\",\n            \"x-kubernetes-list-type\": \"atomic\"\n           },\n           \"path\": {\n            \"description\": \"Path to access on the HTTP server.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Name or number of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"scheme\": {\n            \"description\": \"Scheme to use for connecting to the host.\\nDefaults to HTTP.\",\n            \"type\": \"string\"\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"initialDelaySeconds\": {\n          \"description\": \"Number of seconds after the container has started before liveness probes are initiated.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"periodSeconds\": {\n          \"description\": \"How often (in seconds) to perform the probe.\\nDefault to 10 seconds. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"successThreshold\": {\n          \"description\": \"Minimum consecutive successes for the probe to be considered successful after having failed.\\nDefaults to 1. Must be 1 for liveness and startup. Minimum value is 1.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"tcpSocket\": {\n          \"description\": \"TCPSocket specifies a connection to a TCP port.\",\n          \"properties\": {\n           \"host\": {\n            \"description\": \"Optional: Host name to connect to, defaults to the pod IP.\",\n            \"type\": \"string\"\n           },\n           \"port\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"description\": \"Number or name of the port to access on the container.\\nNumber must be in the range 1 to 65535.\\nName must be an IANA_SVC_NAME.\",\n            \"x-kubernetes-int-or-string\": true\n           }\n          },\n          \"required\": [\n           \"port\"\n          ],\n          \"type\": \"object\"\n         },\n         \"terminationGracePeriodSeconds\": {\n          \"description\": \"Optional duration in seconds the pod needs to terminate gracefully upon probe failure.\\nThe grace period is the duration in seconds after the processes running in the pod are sent\\na termination signal and the time when the processes are forcibly halted with a kill signal.\\nSet this value longer than the expected cleanup time for your process.\\nIf this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this\\nvalue overrides the value provided by the pod spec.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down).\\nThis is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.\\nMinimum value is 1. spec.terminationGracePeriodSeconds is used if unset.\",\n          \"format\": \"int64\",\n          \"type\": \"integer\"\n         },\n         \"timeoutSeconds\": {\n          \"description\": \"Number of seconds after which the probe times out.\\nDefaults to 1 second. Minimum value is 1.\\nMore info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"stdin\": {\n        \"description\": \"Whether this container should allocate a buffer for stdin in the container runtime. If this\\nis not set, reads from stdin in the container will always result in EOF.\\nDefault is false.\",\n        \"type\": \"boolean\"\n       },\n       \"stdinOnce\": {\n        \"description\": \"Whether the container runtime should close the stdin channel after it has been opened by\\na single attach. When stdin is true the stdin stream will remain open across multiple attach\\nsessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the\\nfirst client attaches to stdin, and then remains open and accepts data until the client disconnects,\\nat which time stdin is closed and remains closed until the container is restarted. If this\\nflag is false, a container processes that reads from stdin will never receive an EOF.\\nDefault is false\",\n        \"type\": \"boolean\"\n       },\n       \"terminationMessagePath\": {\n        \"description\": \"Optional: Path at which the file to which the container's termination message\\nwill be written is mounted into the container's filesystem.\\nMessage written is intended to be brief final status, such as an assertion failure message.\\nWill be truncated by the node if greater than 4096 bytes. The total message length across\\nall containers will be limited to 12kb.\\nDefaults to /dev/termination-log.\\nCannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"terminationMessagePolicy\": {\n        \"description\": \"Indicate how the termination message should be populated. File will use the contents of\\nterminationMessagePath to populate the container status message on both success and failure.\\nFallbackToLogsOnError will use the last chunk of container log output if the termination\\nmessage file is empty and the container exited with an error.\\nThe log output is limited to 2048 bytes or 80 lines, whichever is smaller.\\nDefaults to File.\\nCannot be updated.\",\n        \"type\": \"string\"\n       },\n       \"tty\": {\n        \"description\": \"Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.\\nDefault is false.\",\n        \"type\": \"boolean\"\n       },\n       \"volumeDevices\": {\n        \"description\": \"volumeDevices is the list of block devices to be used by the container.\",\n        \"items\": {\n         \"description\": \"volumeDevice describes a mapping of a raw block device within a container.\",\n         \"properties\": {\n          \"devicePath\": {\n           \"description\": \"devicePath is the path inside of the container that the device will be mapped to.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"name must match the name of a persistentVolumeClaim in the pod\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"devicePath\",\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"devicePath\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"volumeMounts\": {\n        \"description\": \"Pod volumes to mount into the container's filesystem.\\nCannot be updated.\",\n        \"items\": {\n         \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n         \"properties\": {\n          \"mountPath\": {\n           \"description\": \"Path within the container at which the volume should be mounted.  Must\\nnot contain ':'.\",\n           \"type\": \"string\"\n          },\n          \"mountPropagation\": {\n           \"description\": \"mountPropagation determines how mounts are propagated from the host\\nto container and the other way around.\\nWhen not set, MountPropagationNone is used.\\nThis field is beta in 1.10.\\nWhen RecursiveReadOnly is set to IfPossible or to Enabled, MountPropagation must be None or unspecified\\n(which defaults to None).\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"description\": \"This must match the Name of a Volume.\",\n           \"type\": \"string\"\n          },\n          \"readOnly\": {\n           \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified).\\nDefaults to false.\",\n           \"type\": \"boolean\"\n          },\n          \"recursiveReadOnly\": {\n           \"description\": \"RecursiveReadOnly specifies whether read-only mounts should be handled\\nrecursively.\\n\\nIf ReadOnly is false, this field has no meaning and must be unspecified.\\n\\nIf ReadOnly is true, and this field is set to Disabled, the mount is not made\\nrecursively read-only.  If this field is set to IfPossible, the mount is made\\nrecursively read-only, if it is supported by the container runtime.  If this\\nfield is set to Enabled, the mount is made recursively read-only if it is\\nsupported by the container runtime, otherwise the pod will not be started and\\nan error will be generated to indicate the reason.\\n\\nIf this field is set to IfPossible or Enabled, MountPropagation must be set to\\nNone (or be unspecified, which defaults to None).\\n\\nIf this field is not specified, it is treated as an equivalent of Disabled.\",\n           \"type\": \"string\"\n          },\n          \"subPath\": {\n           \"description\": \"Path within the volume from which the container's volume should be mounted.\\nDefaults to \\\"\\\" (volume's root).\",\n           \"type\": \"string\"\n          },\n          \"subPathExpr\": {\n           \"description\": \"Expanded path within the volume from which the container's volume should be mounted.\\nBehaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment.\\nDefaults to \\\"\\\" (volume's root).\\nSubPathExpr and SubPath are mutually exclusive.\",\n           \"type\": \"string\"\n          }\n         },\n         \"required\": [\n          \"mountPath\",\n          \"name\"\n         ],\n         \"type\": \"object\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-map-keys\": [\n         \"mountPath\"\n        ],\n        \"x-kubernetes-list-type\": \"map\"\n       },\n       \"workingDir\": {\n        \"description\": \"Container's working directory.\\nIf not specified, the container runtime's default will be used, which\\nmight be configured in the container image.\\nCannot be updated.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"limits\": {\n     \"description\": \"Defines the limits command line flags when starting Alertmanager.\",\n     \"properties\": {\n      \"maxPerSilenceBytes\": {\n       \"description\": \"The maximum size of an individual silence as stored on disk. This corresponds to the Alertmanager's\\n`--silences.max-per-silence-bytes` flag.\\nIt requires Alertmanager \\u003e= v0.28.0.\",\n       \"pattern\": \"(^0|([0-9]*[.])?[0-9]+((K|M|G|T|E|P)i?)?B)$\",\n       \"type\": \"string\"\n      },\n      \"maxSilences\": {\n       \"description\": \"The maximum number active and pending silences. This corresponds to the\\nAlertmanager's `--silences.max-silences` flag.\\nIt requires Alertmanager \\u003e= v0.28.0.\",\n       \"format\": \"int32\",\n       \"minimum\": 0,\n       \"type\": \"integer\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"listenLocal\": {\n     \"description\": \"ListenLocal makes the Alertmanager server listen on loopback, so that it\\ndoes not bind against the Pod IP. Note this is only for the Alertmanager\\nUI, not the gossip communication.\",\n     \"type\": \"boolean\"\n    },\n    \"logFormat\": {\n     \"description\": \"Log format for Alertmanager to be configured with.\",\n     \"enum\": [\n      \"\",\n      \"logfmt\",\n      \"json\"\n     ],\n     \"type\": \"string\"\n    },\n    \"logLevel\": {\n     \"description\": \"Log level for Alertmanager to be configured with.\",\n     \"enum\": [\n      \"\",\n      \"debug\",\n      \"info\",\n      \"warn\",\n      \"error\"\n     ],\n     \"type\": \"string\"\n    },\n    \"minReadySeconds\": {\n     \"description\": \"Minimum number of seconds for which a newly created pod should be ready\\nwithout any of its container crashing for it to be considered available.\\nDefaults to 0 (pod will be considered available as soon as it is ready)\\nThis is an alpha field from kubernetes 1.22 until 1.24 which requires enabling the StatefulSetMinReadySeconds feature gate.\",\n     \"format\": \"int32\",\n     \"type\": \"integer\"\n    },\n    \"nodeSelector\": {\n     \"additionalProperties\": {\n      \"type\": \"string\"\n     },\n     \"description\": \"Define which Nodes the Pods are scheduled on.\",\n     \"type\": \"object\"\n    },\n    \"paused\": {\n     \"description\": \"If set to true all actions on the underlying managed objects are not\\ngoint to be performed, except for delete actions.\",\n     \"type\": \"boolean\"\n    },\n    \"persistentVolumeClaimRetentionPolicy\": {\n     \"description\": \"The field controls if and how PVCs are deleted during the lifecycle of a StatefulSet.\\nThe default behavior is all PVCs are retained.\\nThis is an alpha field from kubernetes 1.23 until 1.26 and a beta field from 1.26.\\nIt requires enabling the StatefulSetAutoDeletePVC feature gate.\",\n     \"properties\": {\n      \"whenDeleted\": {\n       \"description\": \"WhenDeleted specifies what happens to PVCs created from StatefulSet\\nVolumeClaimTemplates when the StatefulSet is deleted. The default policy\\nof `Retain` causes PVCs to not be affected by StatefulSet deletion. The\\n`Delete` policy causes those PVCs to be deleted.\",\n       \"type\": \"string\"\n      },\n      \"whenScaled\": {\n       \"description\": \"WhenScaled specifies what happens to PVCs created from StatefulSet\\nVolumeClaimTemplates when the StatefulSet is scaled down. The default\\npolicy of `Retain` causes PVCs to not be affected by a scaledown. The\\n`Delete` policy causes the associated PVCs for any excess pods above\\nthe replica count to be deleted.\",\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"podMetadata\": {\n     \"description\": \"PodMetadata configures labels and annotations which are propagated to the Alertmanager pods.\\n\\nThe following items are reserved and cannot be overridden:\\n* \\\"alertmanager\\\" label, set to the name of the Alertmanager instance.\\n* \\\"app.kubernetes.io/instance\\\" label, set to the name of the Alertmanager instance.\\n* \\\"app.kubernetes.io/managed-by\\\" label, set to \\\"prometheus-operator\\\".\\n* \\\"app.kubernetes.io/name\\\" label, set to \\\"alertmanager\\\".\\n* \\\"app.kubernetes.io/version\\\" label, set to the Alertmanager version.\\n* \\\"kubectl.kubernetes.io/default-container\\\" annotation, set to \\\"alertmanager\\\".\",\n     \"properties\": {\n      \"annotations\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Annotations is an unstructured key value map stored with a resource that may be\\nset by external tools to store and retrieve arbitrary metadata. They are not\\nqueryable and should be preserved when modifying objects.\\nMore info: http://kubernetes.io/docs/user-guide/annotations\",\n       \"type\": \"object\"\n      },\n      \"labels\": {\n       \"additionalProperties\": {\n        \"type\": \"string\"\n       },\n       \"description\": \"Map of string keys and values that can be used to organize and categorize\\n(scope and select) objects. May match selectors of replication controllers\\nand services.\\nMore info: http://kubernetes.io/docs/user-guide/labels\",\n       \"type\": \"object\"\n      },\n      \"name\": {\n       \"description\": \"Name must be unique within a namespace. Is required when creating resources, although\\nsome resources may allow a client to request the generation of an appropriate name\\nautomatically. Name is primarily intended for creation idempotence and configuration\\ndefinition.\\nCannot be updated.\\nMore info: http://kubernetes.io/docs/user-guide/identifiers#names\",\n       \"type\": \"string\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"portName\": {\n     \"default\": \"web\",\n     \"description\": \"Port name used for the pods and governing service.\\nDefaults to `web`.\",\n     \"type\": \"string\"\n    },\n    \"priorityClassName\": {\n     \"description\": \"Priority class assigned to the Pods\",\n     \"type\": \"string\"\n    },\n    \"replicas\": {\n     \"description\": \"Size is the expected size of the alertmanager cluster. The controller will\\neventually make the size of the running cluster equal to the expected\\nsize.\",\n     \"format\": \"int32\",\n     \"type\": \"integer\"\n    },\n    \"resources\": {\n     \"description\": \"Define resources requests and limits for single Pods.\",\n     \"properties\": {\n      \"claims\": {\n       \"description\": \"Claims lists the names of resources, defined in spec.resourceClaims,\\nthat are used by this container.\\n\\nThis is an alpha field and requires enabling the\\nDynamicResourceAllocation feature gate.\\n\\nThis field is immutable. It can only be set for containers.\",\n       \"items\": {\n        \"description\": \"ResourceClaim references one entry in PodSpec.ResourceClaims.\",\n        \"properties\": {\n         \"name\": {\n          \"description\": \"Name must match the name of one entry in pod.spec.resourceClaims of\\nthe Pod where this field is used. It makes that resource available\\ninside a container.\",\n          \"type\": \"string\"\n         },\n         \"request\": {\n          \"description\": \"Request is the name chosen for a request in the referenced claim.\\nIf empty, everything from the claim is made available, otherwise\\nonly the result of this request.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"name\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-map-keys\": [\n        \"name\"\n       ],\n       \"x-kubernetes-list-type\": \"map\"\n      },\n      \"limits\": {\n       \"additionalProperties\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n        \"x-kubernetes-int-or-string\": true\n       },\n       \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n       \"type\": \"object\"\n      },\n      \"requests\": {\n       \"additionalProperties\": {\n        \"anyOf\": [\n         {\n          \"type\": \"integer\"\n         },\n         {\n          \"type\": \"string\"\n         }\n        ],\n        \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n        \"x-kubernetes-int-or-string\": true\n       },\n       \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value. Requests cannot exceed Limits.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"retention\": {\n     \"default\": \"120h\",\n     \"description\": \"Time duration Alertmanager shall retain data for. Default is '120h',\\nand must match the regular expression `[0-9]+(ms|s|m|h)` (milliseconds seconds minutes hours).\",\n     \"pattern\": \"^(0|(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$\",\n     \"type\": \"string\"\n    },\n    \"routePrefix\": {\n     \"description\": \"The route prefix Alertmanager registers HTTP handlers for. This is useful,\\nif using ExternalURL and a proxy is rewriting HTTP routes of a request,\\nand the actual ExternalURL is still true, but the server serves requests\\nunder a different route prefix. For example for use with `kubectl proxy`.\",\n     \"type\": \"string\"\n    },\n    \"secrets\": {\n     \"description\": \"Secrets is a list of Secrets in the same namespace as the Alertmanager\\nobject, which shall be mounted into the Alertmanager Pods.\\nEach Secret is added to the StatefulSet definition as a volume named `secret-\\u003csecret-name\\u003e`.\\nThe Secrets are mounted into `/etc/alertmanager/secrets/\\u003csecret-name\\u003e` in the 'alertmanager' container.\",\n     \"items\": {\n      \"type\": \"string\"\n     },\n     \"type\": \"array\"\n    },\n    \"securityContext\": {\n     \"description\": \"SecurityContext holds pod-level security attributes and common container settings.\\nThis defaults to the default PodSecurityContext.\",\n     \"properties\": {\n      \"appArmorProfile\": {\n       \"description\": \"appArmorProfile is the AppArmor options to use by the containers in this pod.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"properties\": {\n        \"localhostProfile\": {\n         \"description\": \"localhostProfile indicates a profile loaded on the node that should be used.\\nThe profile must be preconfigured on the node to work.\\nMust match the loaded name of the profile.\\nMust be set if and only if type is \\\"Localhost\\\".\",\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"description\": \"type indicates which kind of AppArmor profile will be applied.\\nValid options are:\\n  Localhost - a profile pre-loaded on the node.\\n  RuntimeDefault - the container runtime's default profile.\\n  Unconfined - no AppArmor enforcement.\",\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"type\"\n       ],\n       \"type\": \"object\"\n      },\n      \"fsGroup\": {\n       \"description\": \"A special supplemental group that applies to all containers in a pod.\\nSome volume types allow the Kubelet to change the ownership of that volume\\nto be owned by the pod:\\n\\n1. The owning GID will be the FSGroup\\n2. The setgid bit is set (new files created in the volume will be owned by FSGroup)\\n3. The permission bits are OR'd with rw-rw----\\n\\nIf unset, the Kubelet will not modify the ownership and permissions of any volume.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"fsGroupChangePolicy\": {\n       \"description\": \"fsGroupChangePolicy defines behavior of changing ownership and permission of the volume\\nbefore being exposed inside Pod. This field will only apply to\\nvolume types which support fsGroup based ownership(and permissions).\\nIt will have no effect on ephemeral volume types such as: secret, configmaps\\nand emptydir.\\nValid values are \\\"OnRootMismatch\\\" and \\\"Always\\\". If not specified, \\\"Always\\\" is used.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"type\": \"string\"\n      },\n      \"runAsGroup\": {\n       \"description\": \"The GID to run the entrypoint of the container process.\\nUses runtime default if unset.\\nMay also be set in SecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence\\nfor that container.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"runAsNonRoot\": {\n       \"description\": \"Indicates that the container must run as a non-root user.\\nIf true, the Kubelet will validate the image at runtime to ensure that it\\ndoes not run as UID 0 (root) and fail to start the container if it does.\\nIf unset or false, no such validation will be performed.\\nMay also be set in SecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n       \"type\": \"boolean\"\n      },\n      \"runAsUser\": {\n       \"description\": \"The UID to run the entrypoint of the container process.\\nDefaults to user specified in image metadata if unspecified.\\nMay also be set in SecurityContext.  If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence\\nfor that container.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"format\": \"int64\",\n       \"type\": \"integer\"\n      },\n      \"seLinuxChangePolicy\": {\n       \"description\": \"seLinuxChangePolicy defines how the container's SELinux label is applied to all volumes used by the Pod.\\nIt has no effect on nodes that do not support SELinux or to volumes does not support SELinux.\\nValid values are \\\"MountOption\\\" and \\\"Recursive\\\".\\n\\n\\\"Recursive\\\" means relabeling of all files on all Pod volumes by the container runtime.\\nThis may be slow for large volumes, but allows mixing privileged and unprivileged Pods sharing the same volume on the same node.\\n\\n\\\"MountOption\\\" mounts all eligible Pod volumes with `-o context` mount option.\\nThis requires all Pods that share the same volume to use the same SELinux label.\\nIt is not possible to share the same volume among privileged and unprivileged Pods.\\nEligible volumes are in-tree FibreChannel and iSCSI volumes, and all CSI volumes\\nwhose CSI driver announces SELinux support by setting spec.seLinuxMount: true in their\\nCSIDriver instance. Other volumes are always re-labelled recursively.\\n\\\"MountOption\\\" value is allowed only when SELinuxMount feature gate is enabled.\\n\\nIf not specified and SELinuxMount feature gate is enabled, \\\"MountOption\\\" is used.\\nIf not specified and SELinuxMount feature gate is disabled, \\\"MountOption\\\" is used for ReadWriteOncePod volumes\\nand \\\"Recursive\\\" for all other volumes.\\n\\nThis field affects only Pods that have SELinux label set, either in PodSecurityContext or in SecurityContext of all containers.\\n\\nAll Pods that use the same volume should use the same seLinuxChangePolicy, otherwise some pods can get stuck in ContainerCreating state.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"type\": \"string\"\n      },\n      \"seLinuxOptions\": {\n       \"description\": \"The SELinux context to be applied to all containers.\\nIf unspecified, the container runtime will allocate a random SELinux context for each\\ncontainer.  May also be set in SecurityContext.  If set in\\nboth SecurityContext and PodSecurityContext, the value specified in SecurityContext\\ntakes precedence for that container.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"properties\": {\n        \"level\": {\n         \"description\": \"Level is SELinux level label that applies to the container.\",\n         \"type\": \"string\"\n        },\n        \"role\": {\n         \"description\": \"Role is a SELinux role label that applies to the container.\",\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"description\": \"Type is a SELinux type label that applies to the container.\",\n         \"type\": \"string\"\n        },\n        \"user\": {\n         \"description\": \"User is a SELinux user label that applies to the container.\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"seccompProfile\": {\n       \"description\": \"The seccomp options to use by the containers in this pod.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"properties\": {\n        \"localhostProfile\": {\n         \"description\": \"localhostProfile indicates a profile defined in a file on the node should be used.\\nThe profile must be preconfigured on the node to work.\\nMust be a descending path, relative to the kubelet's configured seccomp profile location.\\nMust be set if type is \\\"Localhost\\\". Must NOT be set for any other type.\",\n         \"type\": \"string\"\n        },\n        \"type\": {\n         \"description\": \"type indicates which kind of seccomp profile will be applied.\\nValid options are:\\n\\nLocalhost - a profile defined in a file on the node should be used.\\nRuntimeDefault - the container runtime default profile should be used.\\nUnconfined - no profile should be applied.\",\n         \"type\": \"string\"\n        }\n       },\n       \"required\": [\n        \"type\"\n       ],\n       \"type\": \"object\"\n      },\n      \"supplementalGroups\": {\n       \"description\": \"A list of groups applied to the first process run in each container, in\\naddition to the container's primary GID and fsGroup (if specified).  If\\nthe SupplementalGroupsPolicy feature is enabled, the\\nsupplementalGroupsPolicy field determines whether these are in addition\\nto or instead of any group memberships defined in the container image.\\nIf unspecified, no additional groups are added, though group memberships\\ndefined in the container image may still be used, depending on the\\nsupplementalGroupsPolicy field.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"items\": {\n        \"format\": \"int64\",\n        \"type\": \"integer\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-type\": \"atomic\"\n      },\n      \"supplementalGroupsPolicy\": {\n       \"description\": \"Defines how supplemental groups of the first container processes are calculated.\\nValid values are \\\"Merge\\\" and \\\"Strict\\\". If not specified, \\\"Merge\\\" is used.\\n(Alpha) Using the field requires the SupplementalGroupsPolicy feature gate to be enabled\\nand the container runtime must implement support for this feature.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"type\": \"string\"\n      },\n      \"sysctls\": {\n       \"description\": \"Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported\\nsysctls (by the container runtime) might fail to launch.\\nNote that this field cannot be set when spec.os.name is windows.\",\n       \"items\": {\n        \"description\": \"Sysctl defines a kernel parameter to be set\",\n        \"properties\": {\n         \"name\": {\n          \"description\": \"Name of a property to set\",\n          \"type\": \"string\"\n         },\n         \"value\": {\n          \"description\": \"Value of a property to set\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"name\",\n         \"value\"\n        ],\n        \"type\": \"object\"\n       },\n       \"type\": \"array\",\n       \"x-kubernetes-list-type\": \"atomic\"\n      },\n      \"windowsOptions\": {\n       \"description\": \"The Windows specific settings applied to all containers.\\nIf unspecified, the options within a container's SecurityContext will be used.\\nIf set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.\\nNote that this field cannot be set when spec.os.name is linux.\",\n       \"properties\": {\n        \"gmsaCredentialSpec\": {\n         \"description\": \"GMSACredentialSpec is where the GMSA admission webhook\\n(https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the\\nGMSA credential spec named by the GMSACredentialSpecName field.\",\n         \"type\": \"string\"\n        },\n        \"gmsaCredentialSpecName\": {\n         \"description\": \"GMSACredentialSpecName is the name of the GMSA credential spec to use.\",\n         \"type\": \"string\"\n        },\n        \"hostProcess\": {\n         \"description\": \"HostProcess determines if a container should be run as a 'Host Process' container.\\nAll of a Pod's containers must have the same effective HostProcess value\\n(it is not allowed to have a mix of HostProcess containers and non-HostProcess containers).\\nIn addition, if HostProcess is true then HostNetwork must also be set to true.\",\n         \"type\": \"boolean\"\n        },\n        \"runAsUserName\": {\n         \"description\": \"The UserName in Windows to run the entrypoint of the container process.\\nDefaults to the user specified in image metadata if unspecified.\\nMay also be set in PodSecurityContext. If set in both SecurityContext and\\nPodSecurityContext, the value specified in SecurityContext takes precedence.\",\n         \"type\": \"string\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"serviceAccountName\": {\n     \"description\": \"ServiceAccountName is the name of the ServiceAccount to use to run the\\nPrometheus Pods.\",\n     \"type\": \"string\"\n    },\n    \"serviceName\": {\n     \"description\": \"The name of the service name used by the underlying StatefulSet(s) as the governing service.\\nIf defined, the Service  must be created before the Alertmanager resource in the same namespace and it must define a selector that matches the pod labels.\\nIf empty, the operator will create and manage a headless service named `alertmanager-operated` for Alermanager resources.\\nWhen deploying multiple Alertmanager resources in the same namespace, it is recommended to specify a different value for each.\\nSee https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#stable-network-id for more details.\",\n     \"minLength\": 1,\n     \"type\": \"string\"\n    },\n    \"sha\": {\n     \"description\": \"SHA of Alertmanager container image to be deployed. Defaults to the value of `version`.\\nSimilar to a tag, but the SHA explicitly deploys an immutable container image.\\nVersion and Tag are ignored if SHA is set.\\nDeprecated: use 'image' instead. The image digest can be specified as part of the image URL.\",\n     \"type\": \"string\"\n    },\n    \"storage\": {\n     \"description\": \"Storage is the definition of how storage will be used by the Alertmanager\\ninstances.\",\n     \"properties\": {\n      \"disableMountSubPath\": {\n       \"description\": \"Deprecated: subPath usage will be removed in a future release.\",\n       \"type\": \"boolean\"\n      },\n      \"emptyDir\": {\n       \"description\": \"EmptyDirVolumeSource to be used by the StatefulSet.\\nIf specified, it takes precedence over `ephemeral` and `volumeClaimTemplate`.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes/#emptydir\",\n       \"properties\": {\n        \"medium\": {\n         \"description\": \"medium represents what type of storage medium should back this directory.\\nThe default is \\\"\\\" which means to use the node's default medium.\\nMust be an empty string (default) or Memory.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n         \"type\": \"string\"\n        },\n        \"sizeLimit\": {\n         \"anyOf\": [\n          {\n           \"type\": \"integer\"\n          },\n          {\n           \"type\": \"string\"\n          }\n         ],\n         \"description\": \"sizeLimit is the total amount of local storage required for this EmptyDir volume.\\nThe size limit is also applicable for memory medium.\\nThe maximum usage on memory medium EmptyDir would be the minimum value between\\nthe SizeLimit specified here and the sum of memory limits of all containers in a pod.\\nThe default is nil which means that the limit is undefined.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n         \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n         \"x-kubernetes-int-or-string\": true\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"ephemeral\": {\n       \"description\": \"EphemeralVolumeSource to be used by the StatefulSet.\\nThis is a beta field in k8s 1.21 and GA in 1.15.\\nFor lower versions, starting with k8s 1.19, it requires enabling the GenericEphemeralVolume feature gate.\\nMore info: https://kubernetes.io/docs/concepts/storage/ephemeral-volumes/#generic-ephemeral-volumes\",\n       \"properties\": {\n        \"volumeClaimTemplate\": {\n         \"description\": \"Will be used to create a stand-alone PVC to provision the volume.\\nThe pod in which this EphemeralVolumeSource is embedded will be the\\nowner of the PVC, i.e. the PVC will be deleted together with the\\npod.  The name of the PVC will be `\\u003cpod name\\u003e-\\u003cvolume name\\u003e` where\\n`\\u003cvolume name\\u003e` is the name from the `PodSpec.Volumes` array\\nentry. Pod validation will reject the pod if the concatenated name\\nis not valid for a PVC (for example, too long).\\n\\nAn existing PVC with that name that is not owned by the pod\\nwill *not* be used for the pod to avoid using an unrelated\\nvolume by mistake. Starting the pod is then blocked until\\nthe unrelated PVC is removed. If such a pre-created PVC is\\nmeant to be used by the pod, the PVC has to updated with an\\nowner reference to the pod once the pod exists. Normally\\nthis should not be necessary, but it may be useful when\\nmanually reconstructing a broken cluster.\\n\\nThis field is read-only and no changes will be made by Kubernetes\\nto the PVC after it has been created.\\n\\nRequired, must not be nil.\",\n         \"properties\": {\n          \"metadata\": {\n           \"description\": \"May contain labels and annotations that will be copied into the PVC\\nwhen creating it. No other fields are allowed and will be rejected during\\nvalidation.\",\n           \"type\": \"object\"\n          },\n          \"spec\": {\n           \"description\": \"The specification for the PersistentVolumeClaim. The entire content is\\ncopied unchanged into the PVC that gets created from this\\ntemplate. The same fields as in a PersistentVolumeClaim\\nare also valid here.\",\n           \"properties\": {\n            \"accessModes\": {\n             \"description\": \"accessModes contains the desired access modes the volume should have.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"dataSource\": {\n             \"description\": \"dataSource field can be used to specify either:\\n* An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)\\n* An existing PVC (PersistentVolumeClaim)\\nIf the provisioner or an external controller can support the specified data source,\\nit will create a new volume based on the contents of the specified data source.\\nWhen the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef,\\nand dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified.\\nIf the namespace is specified, then dataSourceRef will not be copied to dataSource.\",\n             \"properties\": {\n              \"apiGroup\": {\n               \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n               \"type\": \"string\"\n              },\n              \"kind\": {\n               \"description\": \"Kind is the type of resource being referenced\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of resource being referenced\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"kind\",\n              \"name\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"dataSourceRef\": {\n             \"description\": \"dataSourceRef specifies the object from which to populate the volume with data, if a non-empty\\nvolume is desired. This may be any object from a non-empty API group (non\\ncore object) or a PersistentVolumeClaim object.\\nWhen this field is specified, volume binding will only succeed if the type of\\nthe specified object matches some installed volume populator or dynamic\\nprovisioner.\\nThis field will replace the functionality of the dataSource field and as such\\nif both fields are non-empty, they must have the same value. For backwards\\ncompatibility, when namespace isn't specified in dataSourceRef,\\nboth fields (dataSource and dataSourceRef) will be set to the same\\nvalue automatically if one of them is empty and the other is non-empty.\\nWhen namespace is specified in dataSourceRef,\\ndataSource isn't set to the same value and must be empty.\\nThere are three important differences between dataSource and dataSourceRef:\\n* While dataSource only allows two specific types of objects, dataSourceRef\\n  allows any non-core object, as well as PersistentVolumeClaim objects.\\n* While dataSource ignores disallowed values (dropping them), dataSourceRef\\n  preserves all values, and generates an error if a disallowed value is\\n  specified.\\n* While dataSource only allows local objects, dataSourceRef allows objects\\n  in any namespaces.\\n(Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\\n(Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\n             \"properties\": {\n              \"apiGroup\": {\n               \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n               \"type\": \"string\"\n              },\n              \"kind\": {\n               \"description\": \"Kind is the type of resource being referenced\",\n               \"type\": \"string\"\n              },\n              \"name\": {\n               \"description\": \"Name is the name of resource being referenced\",\n               \"type\": \"string\"\n              },\n              \"namespace\": {\n               \"description\": \"Namespace is the namespace of resource being referenced\\nNote that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.\\n(Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"kind\",\n              \"name\"\n             ],\n             \"type\": \"object\"\n            },\n            \"resources\": {\n             \"description\": \"resources represents the minimum resources the volume should have.\\nIf RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements\\nthat are lower than previous value but must still be higher than capacity recorded in the\\nstatus field of the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n             \"properties\": {\n              \"limits\": {\n               \"additionalProperties\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n               \"type\": \"object\"\n              },\n              \"requests\": {\n               \"additionalProperties\": {\n                \"anyOf\": [\n                 {\n                  \"type\": \"integer\"\n                 },\n                 {\n                  \"type\": \"string\"\n                 }\n                ],\n                \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                \"x-kubernetes-int-or-string\": true\n               },\n               \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value. Requests cannot exceed Limits.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"selector\": {\n             \"description\": \"selector is a label query over volumes to consider for binding.\",\n             \"properties\": {\n              \"matchExpressions\": {\n               \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n               \"items\": {\n                \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"key is the label key that the selector applies to.\",\n                  \"type\": \"string\"\n                 },\n                 \"operator\": {\n                  \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                  \"type\": \"string\"\n                 },\n                 \"values\": {\n                  \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                  \"items\": {\n                   \"type\": \"string\"\n                  },\n                  \"type\": \"array\",\n                  \"x-kubernetes-list-type\": \"atomic\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"operator\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"matchLabels\": {\n               \"additionalProperties\": {\n                \"type\": \"string\"\n               },\n               \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n               \"type\": \"object\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"storageClassName\": {\n             \"description\": \"storageClassName is the name of the StorageClass required by the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n             \"type\": \"string\"\n            },\n            \"volumeAttributesClassName\": {\n             \"description\": \"volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim.\\nIf specified, the CSI driver will create or update the volume with the attributes defined\\nin the corresponding VolumeAttributesClass. This has a different purpose than storageClassName,\\nit can be changed after the claim is created. An empty string value means that no VolumeAttributesClass\\nwill be applied to the claim but it's not allowed to reset this field to empty string once it is set.\\nIf unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass\\nwill be set by the persistentvolume controller if it exists.\\nIf the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be\\nset to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource\\nexists.\\nMore info: https://kubernetes.io/docs/concepts/storage/volume-attributes-classes/\\n(Beta) Using this field requires the VolumeAttributesClass feature gate to be enabled (off by default).\",\n             \"type\": \"string\"\n            },\n            \"volumeMode\": {\n             \"description\": \"volumeMode defines what type of volume is required by the claim.\\nValue of Filesystem is implied when not included in claim spec.\",\n             \"type\": \"string\"\n            },\n            \"volumeName\": {\n             \"description\": \"volumeName is the binding reference to the PersistentVolume backing this claim.\",\n             \"type\": \"string\"\n            }\n           },\n           \"type\": \"object\"\n          }\n         },\n         \"required\": [\n          \"spec\"\n         ],\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"volumeClaimTemplate\": {\n       \"description\": \"Defines the PVC spec to be used by the Prometheus StatefulSets.\\nThe easiest way to use a volume that cannot be automatically provisioned\\nis to use a label selector alongside manually created PersistentVolumes.\",\n       \"properties\": {\n        \"apiVersion\": {\n         \"description\": \"APIVersion defines the versioned schema of this representation of an object.\\nServers should convert recognized schemas to the latest internal value, and\\nmay reject unrecognized values.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources\",\n         \"type\": \"string\"\n        },\n        \"kind\": {\n         \"description\": \"Kind is a string value representing the REST resource this object represents.\\nServers may infer this from the endpoint the client submits requests to.\\nCannot be updated.\\nIn CamelCase.\\nMore info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds\",\n         \"type\": \"string\"\n        },\n        \"metadata\": {\n         \"description\": \"EmbeddedMetadata contains metadata relevant to an EmbeddedResource.\",\n         \"properties\": {\n          \"annotations\": {\n           \"additionalProperties\": {\n            \"type\": \"string\"\n           },\n           \"description\": \"Annotations is an unstructured key value map stored with a resource that may be\\nset by external tools to store and retrieve arbitrary metadata. They are not\\nqueryable and should be preserved when modifying objects.\\nMore info: http://kubernetes.io/docs/user-guide/annotations\",\n           \"type\": \"object\"\n          },\n          \"labels\": {\n           \"additionalProperties\": {\n            \"type\": \"string\"\n           },\n           \"description\": \"Map of string keys and values that can be used to organize and categorize\\n(scope and select) objects. May match selectors of replication controllers\\nand services.\\nMore info: http://kubernetes.io/docs/user-guide/labels\",\n           \"type\": \"object\"\n          },\n          \"name\": {\n           \"description\": \"Name must be unique within a namespace. Is required when creating resources, although\\nsome resources may allow a client to request the generation of an appropriate name\\nautomatically. Name is primarily intended for creation idempotence and configuration\\ndefinition.\\nCannot be updated.\\nMore info: http://kubernetes.io/docs/user-guide/identifiers#names\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"spec\": {\n         \"description\": \"Defines the desired characteristics of a volume requested by a pod author.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n         \"properties\": {\n          \"accessModes\": {\n           \"description\": \"accessModes contains the desired access modes the volume should have.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"dataSource\": {\n           \"description\": \"dataSource field can be used to specify either:\\n* An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)\\n* An existing PVC (PersistentVolumeClaim)\\nIf the provisioner or an external controller can support the specified data source,\\nit will create a new volume based on the contents of the specified data source.\\nWhen the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef,\\nand dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified.\\nIf the namespace is specified, then dataSourceRef will not be copied to dataSource.\",\n           \"properties\": {\n            \"apiGroup\": {\n             \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind is the type of resource being referenced\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name is the name of resource being referenced\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"kind\",\n            \"name\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"dataSourceRef\": {\n           \"description\": \"dataSourceRef specifies the object from which to populate the volume with data, if a non-empty\\nvolume is desired. This may be any object from a non-empty API group (non\\ncore object) or a PersistentVolumeClaim object.\\nWhen this field is specified, volume binding will only succeed if the type of\\nthe specified object matches some installed volume populator or dynamic\\nprovisioner.\\nThis field will replace the functionality of the dataSource field and as such\\nif both fields are non-empty, they must have the same value. For backwards\\ncompatibility, when namespace isn't specified in dataSourceRef,\\nboth fields (dataSource and dataSourceRef) will be set to the same\\nvalue automatically if one of them is empty and the other is non-empty.\\nWhen namespace is specified in dataSourceRef,\\ndataSource isn't set to the same value and must be empty.\\nThere are three important differences between dataSource and dataSourceRef:\\n* While dataSource only allows two specific types of objects, dataSourceRef\\n  allows any non-core object, as well as PersistentVolumeClaim objects.\\n* While dataSource ignores disallowed values (dropping them), dataSourceRef\\n  preserves all values, and generates an error if a disallowed value is\\n  specified.\\n* While dataSource only allows local objects, dataSourceRef allows objects\\n  in any namespaces.\\n(Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\\n(Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\n           \"properties\": {\n            \"apiGroup\": {\n             \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n             \"type\": \"string\"\n            },\n            \"kind\": {\n             \"description\": \"Kind is the type of resource being referenced\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"description\": \"Name is the name of resource being referenced\",\n             \"type\": \"string\"\n            },\n            \"namespace\": {\n             \"description\": \"Namespace is the namespace of resource being referenced\\nNote that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.\\n(Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"kind\",\n            \"name\"\n           ],\n           \"type\": \"object\"\n          },\n          \"resources\": {\n           \"description\": \"resources represents the minimum resources the volume should have.\\nIf RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements\\nthat are lower than previous value but must still be higher than capacity recorded in the\\nstatus field of the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n           \"properties\": {\n            \"limits\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n             \"type\": \"object\"\n            },\n            \"requests\": {\n             \"additionalProperties\": {\n              \"anyOf\": [\n               {\n                \"type\": \"integer\"\n               },\n               {\n                \"type\": \"string\"\n               }\n              ],\n              \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n              \"x-kubernetes-int-or-string\": true\n             },\n             \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value. Requests cannot exceed Limits.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"selector\": {\n           \"description\": \"selector is a label query over volumes to consider for binding.\",\n           \"properties\": {\n            \"matchExpressions\": {\n             \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n             \"items\": {\n              \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n              \"properties\": {\n               \"key\": {\n                \"description\": \"key is the label key that the selector applies to.\",\n                \"type\": \"string\"\n               },\n               \"operator\": {\n                \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                \"type\": \"string\"\n               },\n               \"values\": {\n                \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                \"items\": {\n                 \"type\": \"string\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               }\n              },\n              \"required\": [\n               \"key\",\n               \"operator\"\n              ],\n              \"type\": \"object\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            },\n            \"matchLabels\": {\n             \"additionalProperties\": {\n              \"type\": \"string\"\n             },\n             \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"storageClassName\": {\n           \"description\": \"storageClassName is the name of the StorageClass required by the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n           \"type\": \"string\"\n          },\n          \"volumeAttributesClassName\": {\n           \"description\": \"volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim.\\nIf specified, the CSI driver will create or update the volume with the attributes defined\\nin the corresponding VolumeAttributesClass. This has a different purpose than storageClassName,\\nit can be changed after the claim is created. An empty string value means that no VolumeAttributesClass\\nwill be applied to the claim but it's not allowed to reset this field to empty string once it is set.\\nIf unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass\\nwill be set by the persistentvolume controller if it exists.\\nIf the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be\\nset to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource\\nexists.\\nMore info: https://kubernetes.io/docs/concepts/storage/volume-attributes-classes/\\n(Beta) Using this field requires the VolumeAttributesClass feature gate to be enabled (off by default).\",\n           \"type\": \"string\"\n          },\n          \"volumeMode\": {\n           \"description\": \"volumeMode defines what type of volume is required by the claim.\\nValue of Filesystem is implied when not included in claim spec.\",\n           \"type\": \"string\"\n          },\n          \"volumeName\": {\n           \"description\": \"volumeName is the binding reference to the PersistentVolume backing this claim.\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"status\": {\n         \"description\": \"Deprecated: this field is never set.\",\n         \"properties\": {\n          \"accessModes\": {\n           \"description\": \"accessModes contains the actual access modes the volume backing the PVC has.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n           \"items\": {\n            \"type\": \"string\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-type\": \"atomic\"\n          },\n          \"allocatedResourceStatuses\": {\n           \"additionalProperties\": {\n            \"description\": \"When a controller receives persistentvolume claim update with ClaimResourceStatus for a resource\\nthat it does not recognizes, then it should ignore that update and let other controllers\\nhandle it.\",\n            \"type\": \"string\"\n           },\n           \"description\": \"allocatedResourceStatuses stores status of resource being resized for the given PVC.\\nKey names follow standard Kubernetes label syntax. Valid values are either:\\n\\t* Un-prefixed keys:\\n\\t\\t- storage - the capacity of the volume.\\n\\t* Custom resources must use implementation-defined prefixed names such as \\\"example.com/my-custom-resource\\\"\\nApart from above values - keys that are unprefixed or have kubernetes.io prefix are considered\\nreserved and hence may not be used.\\n\\nClaimResourceStatus can be in any of following states:\\n\\t- ControllerResizeInProgress:\\n\\t\\tState set when resize controller starts resizing the volume in control-plane.\\n\\t- ControllerResizeFailed:\\n\\t\\tState set when resize has failed in resize controller with a terminal error.\\n\\t- NodeResizePending:\\n\\t\\tState set when resize controller has finished resizing the volume but further resizing of\\n\\t\\tvolume is needed on the node.\\n\\t- NodeResizeInProgress:\\n\\t\\tState set when kubelet starts resizing the volume.\\n\\t- NodeResizeFailed:\\n\\t\\tState set when resizing has failed in kubelet with a terminal error. Transient errors don't set\\n\\t\\tNodeResizeFailed.\\nFor example: if expanding a PVC for more capacity - this field can be one of the following states:\\n\\t- pvc.status.allocatedResourceStatus['storage'] = \\\"ControllerResizeInProgress\\\"\\n     - pvc.status.allocatedResourceStatus['storage'] = \\\"ControllerResizeFailed\\\"\\n     - pvc.status.allocatedResourceStatus['storage'] = \\\"NodeResizePending\\\"\\n     - pvc.status.allocatedResourceStatus['storage'] = \\\"NodeResizeInProgress\\\"\\n     - pvc.status.allocatedResourceStatus['storage'] = \\\"NodeResizeFailed\\\"\\nWhen this field is not set, it means that no resize operation is in progress for the given PVC.\\n\\nA controller that receives PVC update with previously unknown resourceName or ClaimResourceStatus\\nshould ignore the update for the purpose it was designed. For example - a controller that\\nonly is responsible for resizing capacity of the volume, should ignore PVC updates that change other valid\\nresources associated with PVC.\\n\\nThis is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.\",\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"granular\"\n          },\n          \"allocatedResources\": {\n           \"additionalProperties\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"description\": \"allocatedResources tracks the resources allocated to a PVC including its capacity.\\nKey names follow standard Kubernetes label syntax. Valid values are either:\\n\\t* Un-prefixed keys:\\n\\t\\t- storage - the capacity of the volume.\\n\\t* Custom resources must use implementation-defined prefixed names such as \\\"example.com/my-custom-resource\\\"\\nApart from above values - keys that are unprefixed or have kubernetes.io prefix are considered\\nreserved and hence may not be used.\\n\\nCapacity reported here may be larger than the actual capacity when a volume expansion operation\\nis requested.\\nFor storage quota, the larger value from allocatedResources and PVC.spec.resources is used.\\nIf allocatedResources is not set, PVC.spec.resources alone is used for quota calculation.\\nIf a volume expansion capacity request is lowered, allocatedResources is only\\nlowered if there are no expansion operations in progress and if the actual volume capacity\\nis equal or lower than the requested capacity.\\n\\nA controller that receives PVC update with previously unknown resourceName\\nshould ignore the update for the purpose it was designed. For example - a controller that\\nonly is responsible for resizing capacity of the volume, should ignore PVC updates that change other valid\\nresources associated with PVC.\\n\\nThis is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.\",\n           \"type\": \"object\"\n          },\n          \"capacity\": {\n           \"additionalProperties\": {\n            \"anyOf\": [\n             {\n              \"type\": \"integer\"\n             },\n             {\n              \"type\": \"string\"\n             }\n            ],\n            \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n            \"x-kubernetes-int-or-string\": true\n           },\n           \"description\": \"capacity represents the actual resources of the underlying volume.\",\n           \"type\": \"object\"\n          },\n          \"conditions\": {\n           \"description\": \"conditions is the current Condition of persistent volume claim. If underlying persistent volume is being\\nresized then the Condition will be set to 'Resizing'.\",\n           \"items\": {\n            \"description\": \"PersistentVolumeClaimCondition contains details about state of pvc\",\n            \"properties\": {\n             \"lastProbeTime\": {\n              \"description\": \"lastProbeTime is the time we probed the condition.\",\n              \"format\": \"date-time\",\n              \"type\": \"string\"\n             },\n             \"lastTransitionTime\": {\n              \"description\": \"lastTransitionTime is the time the condition transitioned from one status to another.\",\n              \"format\": \"date-time\",\n              \"type\": \"string\"\n             },\n             \"message\": {\n              \"description\": \"message is the human-readable message indicating details about last transition.\",\n              \"type\": \"string\"\n             },\n             \"reason\": {\n              \"description\": \"reason is a unique, this should be a short, machine understandable string that gives the reason\\nfor condition's last transition. If it reports \\\"Resizing\\\" that means the underlying\\npersistent volume is being resized.\",\n              \"type\": \"string\"\n             },\n             \"status\": {\n              \"description\": \"Status is the status of the condition.\\nCan be True, False, Unknown.\\nMore info: https://kubernetes.io/docs/reference/kubernetes-api/config-and-storage-resources/persistent-volume-claim-v1/#:~:text=state%20of%20pvc-,conditions.status,-(string)%2C%20required\",\n              \"type\": \"string\"\n             },\n             \"type\": {\n              \"description\": \"Type is the type of the condition.\\nMore info: https://kubernetes.io/docs/reference/kubernetes-api/config-and-storage-resources/persistent-volume-claim-v1/#:~:text=set%20to%20%27ResizeStarted%27.-,PersistentVolumeClaimCondition,-contains%20details%20about\",\n              \"type\": \"string\"\n             }\n            },\n            \"required\": [\n             \"status\",\n             \"type\"\n            ],\n            \"type\": \"object\"\n           },\n           \"type\": \"array\",\n           \"x-kubernetes-list-map-keys\": [\n            \"type\"\n           ],\n           \"x-kubernetes-list-type\": \"map\"\n          },\n          \"currentVolumeAttributesClassName\": {\n           \"description\": \"currentVolumeAttributesClassName is the current name of the VolumeAttributesClass the PVC is using.\\nWhen unset, there is no VolumeAttributeClass applied to this PersistentVolumeClaim\\nThis is a beta field and requires enabling VolumeAttributesClass feature (off by default).\",\n           \"type\": \"string\"\n          },\n          \"modifyVolumeStatus\": {\n           \"description\": \"ModifyVolumeStatus represents the status object of ControllerModifyVolume operation.\\nWhen this is unset, there is no ModifyVolume operation being attempted.\\nThis is a beta field and requires enabling VolumeAttributesClass feature (off by default).\",\n           \"properties\": {\n            \"status\": {\n             \"description\": \"status is the status of the ControllerModifyVolume operation. It can be in any of following states:\\n - Pending\\n   Pending indicates that the PersistentVolumeClaim cannot be modified due to unmet requirements, such as\\n   the specified VolumeAttributesClass not existing.\\n - InProgress\\n   InProgress indicates that the volume is being modified.\\n - Infeasible\\n  Infeasible indicates that the request has been rejected as invalid by the CSI driver. To\\n\\t  resolve the error, a valid VolumeAttributesClass needs to be specified.\\nNote: New statuses can be added in the future. Consumers should check for unknown statuses and fail appropriately.\",\n             \"type\": \"string\"\n            },\n            \"targetVolumeAttributesClassName\": {\n             \"description\": \"targetVolumeAttributesClassName is the name of the VolumeAttributesClass the PVC currently being reconciled\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"status\"\n           ],\n           \"type\": \"object\"\n          },\n          \"phase\": {\n           \"description\": \"phase represents the current phase of PersistentVolumeClaim.\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    },\n    \"tag\": {\n     \"description\": \"Tag of Alertmanager container image to be deployed. Defaults to the value of `version`.\\nVersion is ignored if Tag is set.\\nDeprecated: use 'image' instead. The image tag can be specified as part of the image URL.\",\n     \"type\": \"string\"\n    },\n    \"terminationGracePeriodSeconds\": {\n     \"description\": \"Optional duration in seconds the pod needs to terminate gracefully.\\nValue must be non-negative integer. The value zero indicates stop immediately via\\nthe kill signal (no opportunity to shut down) which may lead to data corruption.\\n\\nDefaults to 120 seconds.\",\n     \"format\": \"int64\",\n     \"minimum\": 0,\n     \"type\": \"integer\"\n    },\n    \"tolerations\": {\n     \"description\": \"If specified, the pod's tolerations.\",\n     \"items\": {\n      \"description\": \"The pod this Toleration is attached to tolerates any taint that matches\\nthe triple \\u003ckey,value,effect\\u003e using the matching operator \\u003coperator\\u003e.\",\n      \"properties\": {\n       \"effect\": {\n        \"description\": \"Effect indicates the taint effect to match. Empty means match all taint effects.\\nWhen specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.\",\n        \"type\": \"string\"\n       },\n       \"key\": {\n        \"description\": \"Key is the taint key that the toleration applies to. Empty means match all taint keys.\\nIf the key is empty, operator must be Exists; this combination means to match all values and all keys.\",\n        \"type\": \"string\"\n       },\n       \"operator\": {\n        \"description\": \"Operator represents a key's relationship to the value.\\nValid operators are Exists and Equal. Defaults to Equal.\\nExists is equivalent to wildcard for value, so that a pod can\\ntolerate all taints of a particular category.\",\n        \"type\": \"string\"\n       },\n       \"tolerationSeconds\": {\n        \"description\": \"TolerationSeconds represents the period of time the toleration (which must be\\nof effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,\\nit is not set, which means tolerate the taint forever (do not evict). Zero and\\nnegative values will be treated as 0 (evict immediately) by the system.\",\n        \"format\": \"int64\",\n        \"type\": \"integer\"\n       },\n       \"value\": {\n        \"description\": \"Value is the taint value the toleration matches to.\\nIf the operator is Exists, the value should be empty, otherwise just a regular string.\",\n        \"type\": \"string\"\n       }\n      },\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"topologySpreadConstraints\": {\n     \"description\": \"If specified, the pod's topology spread constraints.\",\n     \"items\": {\n      \"description\": \"TopologySpreadConstraint specifies how to spread matching pods among the given topology.\",\n      \"properties\": {\n       \"labelSelector\": {\n        \"description\": \"LabelSelector is used to find matching pods.\\nPods that match this label selector are counted to determine the number of pods\\nin their corresponding topology domain.\",\n        \"properties\": {\n         \"matchExpressions\": {\n          \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n          \"items\": {\n           \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"key is the label key that the selector applies to.\",\n             \"type\": \"string\"\n            },\n            \"operator\": {\n             \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n             \"type\": \"string\"\n            },\n            \"values\": {\n             \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n             \"items\": {\n              \"type\": \"string\"\n             },\n             \"type\": \"array\",\n             \"x-kubernetes-list-type\": \"atomic\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"operator\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"matchLabels\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\",\n        \"x-kubernetes-map-type\": \"atomic\"\n       },\n       \"matchLabelKeys\": {\n        \"description\": \"MatchLabelKeys is a set of pod label keys to select the pods over which\\nspreading will be calculated. The keys are used to lookup values from the\\nincoming pod labels, those key-value labels are ANDed with labelSelector\\nto select the group of existing pods over which spreading will be calculated\\nfor the incoming pod. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector.\\nMatchLabelKeys cannot be set when LabelSelector isn't set.\\nKeys that don't exist in the incoming pod labels will\\nbe ignored. A null or empty list means only match against labelSelector.\\n\\nThis is a beta field and requires the MatchLabelKeysInPodTopologySpread feature gate to be enabled (enabled by default).\",\n        \"items\": {\n         \"type\": \"string\"\n        },\n        \"type\": \"array\",\n        \"x-kubernetes-list-type\": \"atomic\"\n       },\n       \"maxSkew\": {\n        \"description\": \"MaxSkew describes the degree to which pods may be unevenly distributed.\\nWhen `whenUnsatisfiable=DoNotSchedule`, it is the maximum permitted difference\\nbetween the number of matching pods in the target topology and the global minimum.\\nThe global minimum is the minimum number of matching pods in an eligible domain\\nor zero if the number of eligible domains is less than MinDomains.\\nFor example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same\\nlabelSelector spread as 2/2/1:\\nIn this case, the global minimum is 1.\\n| zone1 | zone2 | zone3 |\\n|  P P  |  P P  |   P   |\\n- if MaxSkew is 1, incoming pod can only be scheduled to zone3 to become 2/2/2;\\nscheduling it onto zone1(zone2) would make the ActualSkew(3-1) on zone1(zone2)\\nviolate MaxSkew(1).\\n- if MaxSkew is 2, incoming pod can be scheduled onto any zone.\\nWhen `whenUnsatisfiable=ScheduleAnyway`, it is used to give higher precedence\\nto topologies that satisfy it.\\nIt's a required field. Default value is 1 and 0 is not allowed.\",\n        \"format\": \"int32\",\n        \"type\": \"integer\"\n       },\n       \"minDomains\": {\n        \"description\": \"MinDomains indicates a minimum number of eligible domains.\\nWhen the number of eligible domains with matching topology keys is less than minDomains,\\nPod Topology Spread treats \\\"global minimum\\\" as 0, and then the calculation of Skew is performed.\\nAnd when the number of eligible domains with matching topology keys equals or greater than minDomains,\\nthis value has no effect on scheduling.\\nAs a result, when the number of eligible domains is less than minDomains,\\nscheduler won't schedule more than maxSkew Pods to those domains.\\nIf value is nil, the constraint behaves as if MinDomains is equal to 1.\\nValid values are integers greater than 0.\\nWhen value is not nil, WhenUnsatisfiable must be DoNotSchedule.\\n\\nFor example, in a 3-zone cluster, MaxSkew is set to 2, MinDomains is set to 5 and pods with the same\\nlabelSelector spread as 2/2/2:\\n| zone1 | zone2 | zone3 |\\n|  P P  |  P P  |  P P  |\\nThe number of domains is less than 5(MinDomains), so \\\"global minimum\\\" is treated as 0.\\nIn this situation, new pod with the same labelSelector cannot be scheduled,\\nbecause computed skew will be 3(3 - 0) if new Pod is scheduled to any of the three zones,\\nit will violate MaxSkew.\",\n        \"format\": \"int32\",\n        \"type\": \"integer\"\n       },\n       \"nodeAffinityPolicy\": {\n        \"description\": \"NodeAffinityPolicy indicates how we will treat Pod's nodeAffinity/nodeSelector\\nwhen calculating pod topology spread skew. Options are:\\n- Honor: only nodes matching nodeAffinity/nodeSelector are included in the calculations.\\n- Ignore: nodeAffinity/nodeSelector are ignored. All nodes are included in the calculations.\\n\\nIf this value is nil, the behavior is equivalent to the Honor policy.\",\n        \"type\": \"string\"\n       },\n       \"nodeTaintsPolicy\": {\n        \"description\": \"NodeTaintsPolicy indicates how we will treat node taints when calculating\\npod topology spread skew. Options are:\\n- Honor: nodes without taints, along with tainted nodes for which the incoming pod\\nhas a toleration, are included.\\n- Ignore: node taints are ignored. All nodes are included.\\n\\nIf this value is nil, the behavior is equivalent to the Ignore policy.\",\n        \"type\": \"string\"\n       },\n       \"topologyKey\": {\n        \"description\": \"TopologyKey is the key of node labels. Nodes that have a label with this key\\nand identical values are considered to be in the same topology.\\nWe consider each \\u003ckey, value\\u003e as a \\\"bucket\\\", and try to put balanced number\\nof pods into each bucket.\\nWe define a domain as a particular instance of a topology.\\nAlso, we define an eligible domain as a domain whose nodes meet the requirements of\\nnodeAffinityPolicy and nodeTaintsPolicy.\\ne.g. If TopologyKey is \\\"kubernetes.io/hostname\\\", each Node is a domain of that topology.\\nAnd, if TopologyKey is \\\"topology.kubernetes.io/zone\\\", each zone is a domain of that topology.\\nIt's a required field.\",\n        \"type\": \"string\"\n       },\n       \"whenUnsatisfiable\": {\n        \"description\": \"WhenUnsatisfiable indicates how to deal with a pod if it doesn't satisfy\\nthe spread constraint.\\n- DoNotSchedule (default) tells the scheduler not to schedule it.\\n- ScheduleAnyway tells the scheduler to schedule the pod in any location,\\n  but giving higher precedence to topologies that would help reduce the\\n  skew.\\nA constraint is considered \\\"Unsatisfiable\\\" for an incoming pod\\nif and only if every possible node assignment for that pod would violate\\n\\\"MaxSkew\\\" on some topology.\\nFor example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same\\nlabelSelector spread as 3/1/1:\\n| zone1 | zone2 | zone3 |\\n| P P P |   P   |   P   |\\nIf WhenUnsatisfiable is set to DoNotSchedule, incoming pod can only be scheduled\\nto zone2(zone3) to become 3/2/1(3/1/2) as ActualSkew(2-1) on zone2(zone3) satisfies\\nMaxSkew(1). In other words, the cluster can still be imbalanced, but scheduler\\nwon't make it *more* imbalanced.\\nIt's a required field.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"maxSkew\",\n       \"topologyKey\",\n       \"whenUnsatisfiable\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"version\": {\n     \"description\": \"Version the cluster should be on.\",\n     \"type\": \"string\"\n    },\n    \"volumeMounts\": {\n     \"description\": \"VolumeMounts allows configuration of additional VolumeMounts on the output StatefulSet definition.\\nVolumeMounts specified will be appended to other VolumeMounts in the alertmanager container,\\nthat are generated as a result of StorageSpec objects.\",\n     \"items\": {\n      \"description\": \"VolumeMount describes a mounting of a Volume within a container.\",\n      \"properties\": {\n       \"mountPath\": {\n        \"description\": \"Path within the container at which the volume should be mounted.  Must\\nnot contain ':'.\",\n        \"type\": \"string\"\n       },\n       \"mountPropagation\": {\n        \"description\": \"mountPropagation determines how mounts are propagated from the host\\nto container and the other way around.\\nWhen not set, MountPropagationNone is used.\\nThis field is beta in 1.10.\\nWhen RecursiveReadOnly is set to IfPossible or to Enabled, MountPropagation must be None or unspecified\\n(which defaults to None).\",\n        \"type\": \"string\"\n       },\n       \"name\": {\n        \"description\": \"This must match the Name of a Volume.\",\n        \"type\": \"string\"\n       },\n       \"readOnly\": {\n        \"description\": \"Mounted read-only if true, read-write otherwise (false or unspecified).\\nDefaults to false.\",\n        \"type\": \"boolean\"\n       },\n       \"recursiveReadOnly\": {\n        \"description\": \"RecursiveReadOnly specifies whether read-only mounts should be handled\\nrecursively.\\n\\nIf ReadOnly is false, this field has no meaning and must be unspecified.\\n\\nIf ReadOnly is true, and this field is set to Disabled, the mount is not made\\nrecursively read-only.  If this field is set to IfPossible, the mount is made\\nrecursively read-only, if it is supported by the container runtime.  If this\\nfield is set to Enabled, the mount is made recursively read-only if it is\\nsupported by the container runtime, otherwise the pod will not be started and\\nan error will be generated to indicate the reason.\\n\\nIf this field is set to IfPossible or Enabled, MountPropagation must be set to\\nNone (or be unspecified, which defaults to None).\\n\\nIf this field is not specified, it is treated as an equivalent of Disabled.\",\n        \"type\": \"string\"\n       },\n       \"subPath\": {\n        \"description\": \"Path within the volume from which the container's volume should be mounted.\\nDefaults to \\\"\\\" (volume's root).\",\n        \"type\": \"string\"\n       },\n       \"subPathExpr\": {\n        \"description\": \"Expanded path within the volume from which the container's volume should be mounted.\\nBehaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment.\\nDefaults to \\\"\\\" (volume's root).\\nSubPathExpr and SubPath are mutually exclusive.\",\n        \"type\": \"string\"\n       }\n      },\n      \"required\": [\n       \"mountPath\",\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"volumes\": {\n     \"description\": \"Volumes allows configuration of additional volumes on the output StatefulSet definition.\\nVolumes specified will be appended to other volumes that are generated as a result of\\nStorageSpec objects.\",\n     \"items\": {\n      \"description\": \"Volume represents a named volume in a pod that may be accessed by any container in the pod.\",\n      \"properties\": {\n       \"awsElasticBlockStore\": {\n        \"description\": \"awsElasticBlockStore represents an AWS Disk resource that is attached to a\\nkubelet's host machine and then exposed to the pod.\\nDeprecated: AWSElasticBlockStore is deprecated. All operations for the in-tree\\nawsElasticBlockStore type are redirected to the ebs.csi.aws.com CSI driver.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"fsType is the filesystem type of the volume that you want to mount.\\nTip: Ensure that the filesystem type is supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n          \"type\": \"string\"\n         },\n         \"partition\": {\n          \"description\": \"partition is the partition in the volume that you want to mount.\\nIf omitted, the default is to mount by volume name.\\nExamples: For volume /dev/sda1, you specify the partition as \\\"1\\\".\\nSimilarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty).\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly value true will force the readOnly setting in VolumeMounts.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n          \"type\": \"boolean\"\n         },\n         \"volumeID\": {\n          \"description\": \"volumeID is unique ID of the persistent disk resource in AWS (Amazon EBS volume).\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"azureDisk\": {\n        \"description\": \"azureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.\\nDeprecated: AzureDisk is deprecated. All operations for the in-tree azureDisk type\\nare redirected to the disk.csi.azure.com CSI driver.\",\n        \"properties\": {\n         \"cachingMode\": {\n          \"description\": \"cachingMode is the Host Caching mode: None, Read Only, Read Write.\",\n          \"type\": \"string\"\n         },\n         \"diskName\": {\n          \"description\": \"diskName is the Name of the data disk in the blob storage\",\n          \"type\": \"string\"\n         },\n         \"diskURI\": {\n          \"description\": \"diskURI is the URI of data disk in the blob storage\",\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"default\": \"ext4\",\n          \"description\": \"fsType is Filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"kind\": {\n          \"description\": \"kind expected values are Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"default\": false,\n          \"description\": \"readOnly Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"diskName\",\n         \"diskURI\"\n        ],\n        \"type\": \"object\"\n       },\n       \"azureFile\": {\n        \"description\": \"azureFile represents an Azure File Service mount on the host and bind mount to the pod.\\nDeprecated: AzureFile is deprecated. All operations for the in-tree azureFile type\\nare redirected to the file.csi.azure.com CSI driver.\",\n        \"properties\": {\n         \"readOnly\": {\n          \"description\": \"readOnly defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretName\": {\n          \"description\": \"secretName is the  name of secret that contains Azure Storage Account Name and Key\",\n          \"type\": \"string\"\n         },\n         \"shareName\": {\n          \"description\": \"shareName is the azure share Name\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"secretName\",\n         \"shareName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"cephfs\": {\n        \"description\": \"cephFS represents a Ceph FS mount on the host that shares a pod's lifetime.\\nDeprecated: CephFS is deprecated and the in-tree cephfs type is no longer supported.\",\n        \"properties\": {\n         \"monitors\": {\n          \"description\": \"monitors is Required: Monitors is a collection of Ceph monitors\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"path\": {\n          \"description\": \"path is Optional: Used as the mounted root, rather than the full Ceph tree, default is /\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly is Optional: Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"type\": \"boolean\"\n         },\n         \"secretFile\": {\n          \"description\": \"secretFile is Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"secretRef\": {\n          \"description\": \"secretRef is Optional: SecretRef is reference to the authentication secret for User, default is empty.\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"user\": {\n          \"description\": \"user is optional: User is the rados user name, default is admin\\nMore info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"monitors\"\n        ],\n        \"type\": \"object\"\n       },\n       \"cinder\": {\n        \"description\": \"cinder represents a cinder volume attached and mounted on kubelets host machine.\\nDeprecated: Cinder is deprecated. All operations for the in-tree cinder type\\nare redirected to the cinder.csi.openstack.org CSI driver.\\nMore info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"fsType is the filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\\nMore info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"secretRef is optional: points to a secret object containing parameters used to connect\\nto OpenStack.\",\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"volumeID\": {\n          \"description\": \"volumeID used to identify the volume in cinder.\\nMore info: https://examples.k8s.io/mysql-cinder-pd/README.md\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"configMap\": {\n        \"description\": \"configMap represents a configMap that should populate this volume\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"defaultMode is optional: mode bits used to set permissions on created files by default.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nDefaults to 0644.\\nDirectories within the path are not affected by this setting.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"description\": \"items if unspecified, each key-value pair in the Data field of the referenced\\nConfigMap will be projected into the volume as a file whose name is the\\nkey and content is the value. If specified, the listed keys will be\\nprojected into the specified paths, and unlisted keys will not be\\npresent. If a key is specified which is not present in the ConfigMap,\\nthe volume setup will error unless it is marked optional. Paths must be\\nrelative and may not contain the '..' path or start with '..'.\",\n          \"items\": {\n           \"description\": \"Maps a string key to a path within a volume.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"key is the key to project.\",\n             \"type\": \"string\"\n            },\n            \"mode\": {\n             \"description\": \"mode is Optional: mode bits used to set permissions on this file.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"description\": \"path is the relative path of the file to map the key to.\\nMay not be an absolute path.\\nMay not contain the path element '..'.\\nMay not start with the string '..'.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"name\": {\n          \"default\": \"\",\n          \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n          \"type\": \"string\"\n         },\n         \"optional\": {\n          \"description\": \"optional specify whether the ConfigMap or its keys must be defined\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"type\": \"object\",\n        \"x-kubernetes-map-type\": \"atomic\"\n       },\n       \"csi\": {\n        \"description\": \"csi (Container Storage Interface) represents ephemeral storage that is handled by certain external CSI drivers.\",\n        \"properties\": {\n         \"driver\": {\n          \"description\": \"driver is the name of the CSI driver that handles this volume.\\nConsult with your admin for the correct name as registered in the cluster.\",\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"description\": \"fsType to mount. Ex. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\".\\nIf not provided, the empty value is passed to the associated CSI driver\\nwhich will determine the default filesystem to apply.\",\n          \"type\": \"string\"\n         },\n         \"nodePublishSecretRef\": {\n          \"description\": \"nodePublishSecretRef is a reference to the secret object containing\\nsensitive information to pass to the CSI driver to complete the CSI\\nNodePublishVolume and NodeUnpublishVolume calls.\\nThis field is optional, and  may be empty if no secret is required. If the\\nsecret object contains more than one secret, all secret references are passed.\",\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly specifies a read-only configuration for the volume.\\nDefaults to false (read/write).\",\n          \"type\": \"boolean\"\n         },\n         \"volumeAttributes\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"volumeAttributes stores driver-specific properties that are passed to the CSI\\ndriver. Consult your driver's documentation for supported values.\",\n          \"type\": \"object\"\n         }\n        },\n        \"required\": [\n         \"driver\"\n        ],\n        \"type\": \"object\"\n       },\n       \"downwardAPI\": {\n        \"description\": \"downwardAPI represents downward API about the pod that should populate this volume\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"Optional: mode bits to use on created files by default. Must be a\\nOptional: mode bits used to set permissions on created files by default.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nDefaults to 0644.\\nDirectories within the path are not affected by this setting.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"description\": \"Items is a list of downward API volume file\",\n          \"items\": {\n           \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n           \"properties\": {\n            \"fieldRef\": {\n             \"description\": \"Required: Selects a field of the pod: only annotations, labels, name, namespace and uid are supported.\",\n             \"properties\": {\n              \"apiVersion\": {\n               \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n               \"type\": \"string\"\n              },\n              \"fieldPath\": {\n               \"description\": \"Path of the field to select in the specified API version.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"fieldPath\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"mode\": {\n             \"description\": \"Optional: mode bits used to set permissions on this file, must be an octal value\\nbetween 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n             \"type\": \"string\"\n            },\n            \"resourceFieldRef\": {\n             \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n             \"properties\": {\n              \"containerName\": {\n               \"description\": \"Container name: required for volumes, optional for env vars\",\n               \"type\": \"string\"\n              },\n              \"divisor\": {\n               \"anyOf\": [\n                {\n                 \"type\": \"integer\"\n                },\n                {\n                 \"type\": \"string\"\n                }\n               ],\n               \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n               \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n               \"x-kubernetes-int-or-string\": true\n              },\n              \"resource\": {\n               \"description\": \"Required: resource to select\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"resource\"\n             ],\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            }\n           },\n           \"required\": [\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"emptyDir\": {\n        \"description\": \"emptyDir represents a temporary directory that shares a pod's lifetime.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n        \"properties\": {\n         \"medium\": {\n          \"description\": \"medium represents what type of storage medium should back this directory.\\nThe default is \\\"\\\" which means to use the node's default medium.\\nMust be an empty string (default) or Memory.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n          \"type\": \"string\"\n         },\n         \"sizeLimit\": {\n          \"anyOf\": [\n           {\n            \"type\": \"integer\"\n           },\n           {\n            \"type\": \"string\"\n           }\n          ],\n          \"description\": \"sizeLimit is the total amount of local storage required for this EmptyDir volume.\\nThe size limit is also applicable for memory medium.\\nThe maximum usage on memory medium EmptyDir would be the minimum value between\\nthe SizeLimit specified here and the sum of memory limits of all containers in a pod.\\nThe default is nil which means that the limit is undefined.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir\",\n          \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n          \"x-kubernetes-int-or-string\": true\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"ephemeral\": {\n        \"description\": \"ephemeral represents a volume that is handled by a cluster storage driver.\\nThe volume's lifecycle is tied to the pod that defines it - it will be created before the pod starts,\\nand deleted when the pod is removed.\\n\\nUse this if:\\na) the volume is only needed while the pod runs,\\nb) features of normal volumes like restoring from snapshot or capacity\\n   tracking are needed,\\nc) the storage driver is specified through a storage class, and\\nd) the storage driver supports dynamic volume provisioning through\\n   a PersistentVolumeClaim (see EphemeralVolumeSource for more\\n   information on the connection between this volume type\\n   and PersistentVolumeClaim).\\n\\nUse PersistentVolumeClaim or one of the vendor-specific\\nAPIs for volumes that persist for longer than the lifecycle\\nof an individual pod.\\n\\nUse CSI for light-weight local ephemeral volumes if the CSI driver is meant to\\nbe used that way - see the documentation of the driver for\\nmore information.\\n\\nA pod can use both types of ephemeral volumes and\\npersistent volumes at the same time.\",\n        \"properties\": {\n         \"volumeClaimTemplate\": {\n          \"description\": \"Will be used to create a stand-alone PVC to provision the volume.\\nThe pod in which this EphemeralVolumeSource is embedded will be the\\nowner of the PVC, i.e. the PVC will be deleted together with the\\npod.  The name of the PVC will be `\\u003cpod name\\u003e-\\u003cvolume name\\u003e` where\\n`\\u003cvolume name\\u003e` is the name from the `PodSpec.Volumes` array\\nentry. Pod validation will reject the pod if the concatenated name\\nis not valid for a PVC (for example, too long).\\n\\nAn existing PVC with that name that is not owned by the pod\\nwill *not* be used for the pod to avoid using an unrelated\\nvolume by mistake. Starting the pod is then blocked until\\nthe unrelated PVC is removed. If such a pre-created PVC is\\nmeant to be used by the pod, the PVC has to updated with an\\nowner reference to the pod once the pod exists. Normally\\nthis should not be necessary, but it may be useful when\\nmanually reconstructing a broken cluster.\\n\\nThis field is read-only and no changes will be made by Kubernetes\\nto the PVC after it has been created.\\n\\nRequired, must not be nil.\",\n          \"properties\": {\n           \"metadata\": {\n            \"description\": \"May contain labels and annotations that will be copied into the PVC\\nwhen creating it. No other fields are allowed and will be rejected during\\nvalidation.\",\n            \"type\": \"object\"\n           },\n           \"spec\": {\n            \"description\": \"The specification for the PersistentVolumeClaim. The entire content is\\ncopied unchanged into the PVC that gets created from this\\ntemplate. The same fields as in a PersistentVolumeClaim\\nare also valid here.\",\n            \"properties\": {\n             \"accessModes\": {\n              \"description\": \"accessModes contains the desired access modes the volume should have.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1\",\n              \"items\": {\n               \"type\": \"string\"\n              },\n              \"type\": \"array\",\n              \"x-kubernetes-list-type\": \"atomic\"\n             },\n             \"dataSource\": {\n              \"description\": \"dataSource field can be used to specify either:\\n* An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)\\n* An existing PVC (PersistentVolumeClaim)\\nIf the provisioner or an external controller can support the specified data source,\\nit will create a new volume based on the contents of the specified data source.\\nWhen the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef,\\nand dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified.\\nIf the namespace is specified, then dataSourceRef will not be copied to dataSource.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind is the type of resource being referenced\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name is the name of resource being referenced\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"dataSourceRef\": {\n              \"description\": \"dataSourceRef specifies the object from which to populate the volume with data, if a non-empty\\nvolume is desired. This may be any object from a non-empty API group (non\\ncore object) or a PersistentVolumeClaim object.\\nWhen this field is specified, volume binding will only succeed if the type of\\nthe specified object matches some installed volume populator or dynamic\\nprovisioner.\\nThis field will replace the functionality of the dataSource field and as such\\nif both fields are non-empty, they must have the same value. For backwards\\ncompatibility, when namespace isn't specified in dataSourceRef,\\nboth fields (dataSource and dataSourceRef) will be set to the same\\nvalue automatically if one of them is empty and the other is non-empty.\\nWhen namespace is specified in dataSourceRef,\\ndataSource isn't set to the same value and must be empty.\\nThere are three important differences between dataSource and dataSourceRef:\\n* While dataSource only allows two specific types of objects, dataSourceRef\\n  allows any non-core object, as well as PersistentVolumeClaim objects.\\n* While dataSource ignores disallowed values (dropping them), dataSourceRef\\n  preserves all values, and generates an error if a disallowed value is\\n  specified.\\n* While dataSource only allows local objects, dataSourceRef allows objects\\n  in any namespaces.\\n(Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.\\n(Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\n              \"properties\": {\n               \"apiGroup\": {\n                \"description\": \"APIGroup is the group for the resource being referenced.\\nIf APIGroup is not specified, the specified Kind must be in the core API group.\\nFor any other third-party types, APIGroup is required.\",\n                \"type\": \"string\"\n               },\n               \"kind\": {\n                \"description\": \"Kind is the type of resource being referenced\",\n                \"type\": \"string\"\n               },\n               \"name\": {\n                \"description\": \"Name is the name of resource being referenced\",\n                \"type\": \"string\"\n               },\n               \"namespace\": {\n                \"description\": \"Namespace is the namespace of resource being referenced\\nNote that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.\\n(Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.\",\n                \"type\": \"string\"\n               }\n              },\n              \"required\": [\n               \"kind\",\n               \"name\"\n              ],\n              \"type\": \"object\"\n             },\n             \"resources\": {\n              \"description\": \"resources represents the minimum resources the volume should have.\\nIf RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements\\nthat are lower than previous value but must still be higher than capacity recorded in the\\nstatus field of the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources\",\n              \"properties\": {\n               \"limits\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"description\": \"Limits describes the maximum amount of compute resources allowed.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                \"type\": \"object\"\n               },\n               \"requests\": {\n                \"additionalProperties\": {\n                 \"anyOf\": [\n                  {\n                   \"type\": \"integer\"\n                  },\n                  {\n                   \"type\": \"string\"\n                  }\n                 ],\n                 \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                 \"x-kubernetes-int-or-string\": true\n                },\n                \"description\": \"Requests describes the minimum amount of compute resources required.\\nIf Requests is omitted for a container, it defaults to Limits if that is explicitly specified,\\notherwise to an implementation-defined value. Requests cannot exceed Limits.\\nMore info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\"\n             },\n             \"selector\": {\n              \"description\": \"selector is a label query over volumes to consider for binding.\",\n              \"properties\": {\n               \"matchExpressions\": {\n                \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                \"items\": {\n                 \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                 \"properties\": {\n                  \"key\": {\n                   \"description\": \"key is the label key that the selector applies to.\",\n                   \"type\": \"string\"\n                  },\n                  \"operator\": {\n                   \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                   \"type\": \"string\"\n                  },\n                  \"values\": {\n                   \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                   \"items\": {\n                    \"type\": \"string\"\n                   },\n                   \"type\": \"array\",\n                   \"x-kubernetes-list-type\": \"atomic\"\n                  }\n                 },\n                 \"required\": [\n                  \"key\",\n                  \"operator\"\n                 ],\n                 \"type\": \"object\"\n                },\n                \"type\": \"array\",\n                \"x-kubernetes-list-type\": \"atomic\"\n               },\n               \"matchLabels\": {\n                \"additionalProperties\": {\n                 \"type\": \"string\"\n                },\n                \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                \"type\": \"object\"\n               }\n              },\n              \"type\": \"object\",\n              \"x-kubernetes-map-type\": \"atomic\"\n             },\n             \"storageClassName\": {\n              \"description\": \"storageClassName is the name of the StorageClass required by the claim.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1\",\n              \"type\": \"string\"\n             },\n             \"volumeAttributesClassName\": {\n              \"description\": \"volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim.\\nIf specified, the CSI driver will create or update the volume with the attributes defined\\nin the corresponding VolumeAttributesClass. This has a different purpose than storageClassName,\\nit can be changed after the claim is created. An empty string value means that no VolumeAttributesClass\\nwill be applied to the claim but it's not allowed to reset this field to empty string once it is set.\\nIf unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass\\nwill be set by the persistentvolume controller if it exists.\\nIf the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be\\nset to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource\\nexists.\\nMore info: https://kubernetes.io/docs/concepts/storage/volume-attributes-classes/\\n(Beta) Using this field requires the VolumeAttributesClass feature gate to be enabled (off by default).\",\n              \"type\": \"string\"\n             },\n             \"volumeMode\": {\n              \"description\": \"volumeMode defines what type of volume is required by the claim.\\nValue of Filesystem is implied when not included in claim spec.\",\n              \"type\": \"string\"\n             },\n             \"volumeName\": {\n              \"description\": \"volumeName is the binding reference to the PersistentVolume backing this claim.\",\n              \"type\": \"string\"\n             }\n            },\n            \"type\": \"object\"\n           }\n          },\n          \"required\": [\n           \"spec\"\n          ],\n          \"type\": \"object\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"fc\": {\n        \"description\": \"fc represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"fsType is the filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"lun\": {\n          \"description\": \"lun is Optional: FC target lun number\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly is Optional: Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"targetWWNs\": {\n          \"description\": \"targetWWNs is Optional: FC target worldwide names (WWNs)\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"wwids\": {\n          \"description\": \"wwids Optional: FC volume world wide identifiers (wwids)\\nEither wwids or combination of targetWWNs and lun must be set, but not both simultaneously.\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"flexVolume\": {\n        \"description\": \"flexVolume represents a generic volume resource that is\\nprovisioned/attached using an exec based plugin.\\nDeprecated: FlexVolume is deprecated. Consider using a CSIDriver instead.\",\n        \"properties\": {\n         \"driver\": {\n          \"description\": \"driver is the name of the driver to use for this volume.\",\n          \"type\": \"string\"\n         },\n         \"fsType\": {\n          \"description\": \"fsType is the filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". The default filesystem depends on FlexVolume script.\",\n          \"type\": \"string\"\n         },\n         \"options\": {\n          \"additionalProperties\": {\n           \"type\": \"string\"\n          },\n          \"description\": \"options is Optional: this field holds extra command options if any.\",\n          \"type\": \"object\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly is Optional: defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"secretRef is Optional: secretRef is reference to the secret object containing\\nsensitive information to pass to the plugin scripts. This may be\\nempty if no secret object is specified. If the secret object\\ncontains more than one secret, all secrets are passed to the plugin\\nscripts.\",\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         }\n        },\n        \"required\": [\n         \"driver\"\n        ],\n        \"type\": \"object\"\n       },\n       \"flocker\": {\n        \"description\": \"flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running.\\nDeprecated: Flocker is deprecated and the in-tree flocker type is no longer supported.\",\n        \"properties\": {\n         \"datasetName\": {\n          \"description\": \"datasetName is Name of the dataset stored as metadata -\\u003e name on the dataset for Flocker\\nshould be considered as deprecated\",\n          \"type\": \"string\"\n         },\n         \"datasetUUID\": {\n          \"description\": \"datasetUUID is the UUID of the dataset. This is unique identifier of a Flocker dataset\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"gcePersistentDisk\": {\n        \"description\": \"gcePersistentDisk represents a GCE Disk resource that is attached to a\\nkubelet's host machine and then exposed to the pod.\\nDeprecated: GCEPersistentDisk is deprecated. All operations for the in-tree\\ngcePersistentDisk type are redirected to the pd.csi.storage.gke.io CSI driver.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"fsType is filesystem type of the volume that you want to mount.\\nTip: Ensure that the filesystem type is supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"type\": \"string\"\n         },\n         \"partition\": {\n          \"description\": \"partition is the partition in the volume that you want to mount.\\nIf omitted, the default is to mount by volume name.\\nExamples: For volume /dev/sda1, you specify the partition as \\\"1\\\".\\nSimilarly, the volume partition for /dev/sda is \\\"0\\\" (or you can leave the property empty).\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"pdName\": {\n          \"description\": \"pdName is unique name of the PD resource in GCE. Used to identify the disk in GCE.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly here will force the ReadOnly setting in VolumeMounts.\\nDefaults to false.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"pdName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"gitRepo\": {\n        \"description\": \"gitRepo represents a git repository at a particular revision.\\nDeprecated: GitRepo is deprecated. To provision a container with a git repo, mount an\\nEmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir\\ninto the Pod's container.\",\n        \"properties\": {\n         \"directory\": {\n          \"description\": \"directory is the target directory name.\\nMust not contain or start with '..'.  If '.' is supplied, the volume directory will be the\\ngit repository.  Otherwise, if specified, the volume will contain the git repository in\\nthe subdirectory with the given name.\",\n          \"type\": \"string\"\n         },\n         \"repository\": {\n          \"description\": \"repository is the URL\",\n          \"type\": \"string\"\n         },\n         \"revision\": {\n          \"description\": \"revision is the commit hash for the specified revision.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"repository\"\n        ],\n        \"type\": \"object\"\n       },\n       \"glusterfs\": {\n        \"description\": \"glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime.\\nDeprecated: Glusterfs is deprecated and the in-tree glusterfs type is no longer supported.\\nMore info: https://examples.k8s.io/volumes/glusterfs/README.md\",\n        \"properties\": {\n         \"endpoints\": {\n          \"description\": \"endpoints is the endpoint name that details Glusterfs topology.\\nMore info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n          \"type\": \"string\"\n         },\n         \"path\": {\n          \"description\": \"path is the Glusterfs volume path.\\nMore info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly here will force the Glusterfs volume to be mounted with read-only permissions.\\nDefaults to false.\\nMore info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"endpoints\",\n         \"path\"\n        ],\n        \"type\": \"object\"\n       },\n       \"hostPath\": {\n        \"description\": \"hostPath represents a pre-existing file or directory on the host\\nmachine that is directly exposed to the container. This is generally\\nused for system agents or other privileged things that are allowed\\nto see the host machine. Most containers will NOT need this.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n        \"properties\": {\n         \"path\": {\n          \"description\": \"path of the directory on the host.\\nIf the path is a symlink, it will follow the link to the real path.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n          \"type\": \"string\"\n         },\n         \"type\": {\n          \"description\": \"type for HostPath Volume\\nDefaults to \\\"\\\"\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"path\"\n        ],\n        \"type\": \"object\"\n       },\n       \"image\": {\n        \"description\": \"image represents an OCI object (a container image or artifact) pulled and mounted on the kubelet's host machine.\\nThe volume is resolved at pod startup depending on which PullPolicy value is provided:\\n\\n- Always: the kubelet always attempts to pull the reference. Container creation will fail If the pull fails.\\n- Never: the kubelet never pulls the reference and only uses a local image or artifact. Container creation will fail if the reference isn't present.\\n- IfNotPresent: the kubelet pulls if the reference isn't already present on disk. Container creation will fail if the reference isn't present and the pull fails.\\n\\nThe volume gets re-resolved if the pod gets deleted and recreated, which means that new remote content will become available on pod recreation.\\nA failure to resolve or pull the image during pod startup will block containers from starting and may add significant latency. Failures will be retried using normal volume backoff and will be reported on the pod reason and message.\\nThe types of objects that may be mounted by this volume are defined by the container runtime implementation on a host machine and at minimum must include all valid types supported by the container image field.\\nThe OCI object gets mounted in a single directory (spec.containers[*].volumeMounts.mountPath) by merging the manifest layers in the same way as for container images.\\nThe volume will be mounted read-only (ro) and non-executable files (noexec).\\nSub path mounts for containers are not supported (spec.containers[*].volumeMounts.subpath) before 1.33.\\nThe field spec.securityContext.fsGroupChangePolicy has no effect on this volume type.\",\n        \"properties\": {\n         \"pullPolicy\": {\n          \"description\": \"Policy for pulling OCI objects. Possible values are:\\nAlways: the kubelet always attempts to pull the reference. Container creation will fail If the pull fails.\\nNever: the kubelet never pulls the reference and only uses a local image or artifact. Container creation will fail if the reference isn't present.\\nIfNotPresent: the kubelet pulls if the reference isn't already present on disk. Container creation will fail if the reference isn't present and the pull fails.\\nDefaults to Always if :latest tag is specified, or IfNotPresent otherwise.\",\n          \"type\": \"string\"\n         },\n         \"reference\": {\n          \"description\": \"Required: Image or artifact reference to be used.\\nBehaves in the same way as pod.spec.containers[*].image.\\nPull secrets will be assembled in the same way as for the container image by looking up node credentials, SA image pull secrets, and pod spec image pull secrets.\\nMore info: https://kubernetes.io/docs/concepts/containers/images\\nThis field is optional to allow higher level config management to default or override\\ncontainer images in workload controllers like Deployments and StatefulSets.\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"iscsi\": {\n        \"description\": \"iscsi represents an ISCSI Disk resource that is attached to a\\nkubelet's host machine and then exposed to the pod.\\nMore info: https://examples.k8s.io/volumes/iscsi/README.md\",\n        \"properties\": {\n         \"chapAuthDiscovery\": {\n          \"description\": \"chapAuthDiscovery defines whether support iSCSI Discovery CHAP authentication\",\n          \"type\": \"boolean\"\n         },\n         \"chapAuthSession\": {\n          \"description\": \"chapAuthSession defines whether support iSCSI Session CHAP authentication\",\n          \"type\": \"boolean\"\n         },\n         \"fsType\": {\n          \"description\": \"fsType is the filesystem type of the volume that you want to mount.\\nTip: Ensure that the filesystem type is supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi\",\n          \"type\": \"string\"\n         },\n         \"initiatorName\": {\n          \"description\": \"initiatorName is the custom iSCSI Initiator Name.\\nIf initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface\\n\\u003ctarget portal\\u003e:\\u003cvolume name\\u003e will be created for the connection.\",\n          \"type\": \"string\"\n         },\n         \"iqn\": {\n          \"description\": \"iqn is the target iSCSI Qualified Name.\",\n          \"type\": \"string\"\n         },\n         \"iscsiInterface\": {\n          \"default\": \"default\",\n          \"description\": \"iscsiInterface is the interface Name that uses an iSCSI transport.\\nDefaults to 'default' (tcp).\",\n          \"type\": \"string\"\n         },\n         \"lun\": {\n          \"description\": \"lun represents iSCSI Target Lun number.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"portals\": {\n          \"description\": \"portals is the iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port\\nis other than default (typically TCP ports 860 and 3260).\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly here will force the ReadOnly setting in VolumeMounts.\\nDefaults to false.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"secretRef is the CHAP Secret for iSCSI target and initiator authentication\",\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"targetPortal\": {\n          \"description\": \"targetPortal is iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port\\nis other than default (typically TCP ports 860 and 3260).\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"iqn\",\n         \"lun\",\n         \"targetPortal\"\n        ],\n        \"type\": \"object\"\n       },\n       \"name\": {\n        \"description\": \"name of the volume.\\nMust be a DNS_LABEL and unique within the pod.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n        \"type\": \"string\"\n       },\n       \"nfs\": {\n        \"description\": \"nfs represents an NFS mount on the host that shares a pod's lifetime\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n        \"properties\": {\n         \"path\": {\n          \"description\": \"path that is exported by the NFS server.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly here will force the NFS export to be mounted with read-only permissions.\\nDefaults to false.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n          \"type\": \"boolean\"\n         },\n         \"server\": {\n          \"description\": \"server is the hostname or IP address of the NFS server.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#nfs\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"path\",\n         \"server\"\n        ],\n        \"type\": \"object\"\n       },\n       \"persistentVolumeClaim\": {\n        \"description\": \"persistentVolumeClaimVolumeSource represents a reference to a\\nPersistentVolumeClaim in the same namespace.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n        \"properties\": {\n         \"claimName\": {\n          \"description\": \"claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.\\nMore info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly Will force the ReadOnly setting in VolumeMounts.\\nDefault false.\",\n          \"type\": \"boolean\"\n         }\n        },\n        \"required\": [\n         \"claimName\"\n        ],\n        \"type\": \"object\"\n       },\n       \"photonPersistentDisk\": {\n        \"description\": \"photonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine.\\nDeprecated: PhotonPersistentDisk is deprecated and the in-tree photonPersistentDisk type is no longer supported.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"fsType is the filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"pdID\": {\n          \"description\": \"pdID is the ID that identifies Photon Controller persistent disk\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"pdID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"portworxVolume\": {\n        \"description\": \"portworxVolume represents a portworx volume attached and mounted on kubelets host machine.\\nDeprecated: PortworxVolume is deprecated. All operations for the in-tree portworxVolume type\\nare redirected to the pxd.portworx.com CSI driver when the CSIMigrationPortworx feature-gate\\nis on.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"fSType represents the filesystem type to mount\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"volumeID\": {\n          \"description\": \"volumeID uniquely identifies a Portworx volume\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumeID\"\n        ],\n        \"type\": \"object\"\n       },\n       \"projected\": {\n        \"description\": \"projected items for all in one resources secrets, configmaps, and downward API\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"defaultMode are the mode bits used to set permissions on created files by default.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nDirectories within the path are not affected by this setting.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"sources\": {\n          \"description\": \"sources is the list of volume projections. Each entry in this list\\nhandles one source.\",\n          \"items\": {\n           \"description\": \"Projection that may be projected along with other supported volume types.\\nExactly one of these fields must be set.\",\n           \"properties\": {\n            \"clusterTrustBundle\": {\n             \"description\": \"ClusterTrustBundle allows a pod to access the `.spec.trustBundle` field\\nof ClusterTrustBundle objects in an auto-updating file.\\n\\nAlpha, gated by the ClusterTrustBundleProjection feature gate.\\n\\nClusterTrustBundle objects can either be selected by name, or by the\\ncombination of signer name and a label selector.\\n\\nKubelet performs aggressive normalization of the PEM contents written\\ninto the pod filesystem.  Esoteric PEM features such as inter-block\\ncomments and block headers are stripped.  Certificates are deduplicated.\\nThe ordering of certificates within the file is arbitrary, and Kubelet\\nmay change the order over time.\",\n             \"properties\": {\n              \"labelSelector\": {\n               \"description\": \"Select all ClusterTrustBundles that match this label selector.  Only has\\neffect if signerName is set.  Mutually-exclusive with name.  If unset,\\ninterpreted as \\\"match nothing\\\".  If set but empty, interpreted as \\\"match\\neverything\\\".\",\n               \"properties\": {\n                \"matchExpressions\": {\n                 \"description\": \"matchExpressions is a list of label selector requirements. The requirements are ANDed.\",\n                 \"items\": {\n                  \"description\": \"A label selector requirement is a selector that contains values, a key, and an operator that\\nrelates the key and values.\",\n                  \"properties\": {\n                   \"key\": {\n                    \"description\": \"key is the label key that the selector applies to.\",\n                    \"type\": \"string\"\n                   },\n                   \"operator\": {\n                    \"description\": \"operator represents a key's relationship to a set of values.\\nValid operators are In, NotIn, Exists and DoesNotExist.\",\n                    \"type\": \"string\"\n                   },\n                   \"values\": {\n                    \"description\": \"values is an array of string values. If the operator is In or NotIn,\\nthe values array must be non-empty. If the operator is Exists or DoesNotExist,\\nthe values array must be empty. This array is replaced during a strategic\\nmerge patch.\",\n                    \"items\": {\n                     \"type\": \"string\"\n                    },\n                    \"type\": \"array\",\n                    \"x-kubernetes-list-type\": \"atomic\"\n                   }\n                  },\n                  \"required\": [\n                   \"key\",\n                   \"operator\"\n                  ],\n                  \"type\": \"object\"\n                 },\n                 \"type\": \"array\",\n                 \"x-kubernetes-list-type\": \"atomic\"\n                },\n                \"matchLabels\": {\n                 \"additionalProperties\": {\n                  \"type\": \"string\"\n                 },\n                 \"description\": \"matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels\\nmap is equivalent to an element of matchExpressions, whose key field is \\\"key\\\", the\\noperator is \\\"In\\\", and the values array contains only \\\"value\\\". The requirements are ANDed.\",\n                 \"type\": \"object\"\n                }\n               },\n               \"type\": \"object\",\n               \"x-kubernetes-map-type\": \"atomic\"\n              },\n              \"name\": {\n               \"description\": \"Select a single ClusterTrustBundle by object name.  Mutually-exclusive\\nwith signerName and labelSelector.\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"If true, don't block pod startup if the referenced ClusterTrustBundle(s)\\naren't available.  If using name, then the named ClusterTrustBundle is\\nallowed not to exist.  If using signerName, then the combination of\\nsignerName and labelSelector is allowed to match zero\\nClusterTrustBundles.\",\n               \"type\": \"boolean\"\n              },\n              \"path\": {\n               \"description\": \"Relative path from the volume root to write the bundle.\",\n               \"type\": \"string\"\n              },\n              \"signerName\": {\n               \"description\": \"Select all ClusterTrustBundles that match this signer name.\\nMutually-exclusive with name.  The contents of all selected\\nClusterTrustBundles will be unified and deduplicated.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            },\n            \"configMap\": {\n             \"description\": \"configMap information about the configMap data to project\",\n             \"properties\": {\n              \"items\": {\n               \"description\": \"items if unspecified, each key-value pair in the Data field of the referenced\\nConfigMap will be projected into the volume as a file whose name is the\\nkey and content is the value. If specified, the listed keys will be\\nprojected into the specified paths, and unlisted keys will not be\\npresent. If a key is specified which is not present in the ConfigMap,\\nthe volume setup will error unless it is marked optional. Paths must be\\nrelative and may not contain the '..' path or start with '..'.\",\n               \"items\": {\n                \"description\": \"Maps a string key to a path within a volume.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"key is the key to project.\",\n                  \"type\": \"string\"\n                 },\n                 \"mode\": {\n                  \"description\": \"mode is Optional: mode bits used to set permissions on this file.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"description\": \"path is the relative path of the file to map the key to.\\nMay not be an absolute path.\\nMay not contain the path element '..'.\\nMay not start with the string '..'.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"optional specify whether the ConfigMap or its keys must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"downwardAPI\": {\n             \"description\": \"downwardAPI information about the downwardAPI data to project\",\n             \"properties\": {\n              \"items\": {\n               \"description\": \"Items is a list of DownwardAPIVolume file\",\n               \"items\": {\n                \"description\": \"DownwardAPIVolumeFile represents information to create the file containing the pod field\",\n                \"properties\": {\n                 \"fieldRef\": {\n                  \"description\": \"Required: Selects a field of the pod: only annotations, labels, name, namespace and uid are supported.\",\n                  \"properties\": {\n                   \"apiVersion\": {\n                    \"description\": \"Version of the schema the FieldPath is written in terms of, defaults to \\\"v1\\\".\",\n                    \"type\": \"string\"\n                   },\n                   \"fieldPath\": {\n                    \"description\": \"Path of the field to select in the specified API version.\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"fieldPath\"\n                  ],\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 },\n                 \"mode\": {\n                  \"description\": \"Optional: mode bits used to set permissions on this file, must be an octal value\\nbetween 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"description\": \"Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'\",\n                  \"type\": \"string\"\n                 },\n                 \"resourceFieldRef\": {\n                  \"description\": \"Selects a resource of the container: only resources limits and requests\\n(limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.\",\n                  \"properties\": {\n                   \"containerName\": {\n                    \"description\": \"Container name: required for volumes, optional for env vars\",\n                    \"type\": \"string\"\n                   },\n                   \"divisor\": {\n                    \"anyOf\": [\n                     {\n                      \"type\": \"integer\"\n                     },\n                     {\n                      \"type\": \"string\"\n                     }\n                    ],\n                    \"description\": \"Specifies the output format of the exposed resources, defaults to \\\"1\\\"\",\n                    \"pattern\": \"^(\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\\\+|-)?(([0-9]+(\\\\.[0-9]*)?)|(\\\\.[0-9]+))))?$\",\n                    \"x-kubernetes-int-or-string\": true\n                   },\n                   \"resource\": {\n                    \"description\": \"Required: resource to select\",\n                    \"type\": \"string\"\n                   }\n                  },\n                  \"required\": [\n                   \"resource\"\n                  ],\n                  \"type\": \"object\",\n                  \"x-kubernetes-map-type\": \"atomic\"\n                 }\n                },\n                \"required\": [\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              }\n             },\n             \"type\": \"object\"\n            },\n            \"secret\": {\n             \"description\": \"secret information about the secret data to project\",\n             \"properties\": {\n              \"items\": {\n               \"description\": \"items if unspecified, each key-value pair in the Data field of the referenced\\nSecret will be projected into the volume as a file whose name is the\\nkey and content is the value. If specified, the listed keys will be\\nprojected into the specified paths, and unlisted keys will not be\\npresent. If a key is specified which is not present in the Secret,\\nthe volume setup will error unless it is marked optional. Paths must be\\nrelative and may not contain the '..' path or start with '..'.\",\n               \"items\": {\n                \"description\": \"Maps a string key to a path within a volume.\",\n                \"properties\": {\n                 \"key\": {\n                  \"description\": \"key is the key to project.\",\n                  \"type\": \"string\"\n                 },\n                 \"mode\": {\n                  \"description\": \"mode is Optional: mode bits used to set permissions on this file.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n                  \"format\": \"int32\",\n                  \"type\": \"integer\"\n                 },\n                 \"path\": {\n                  \"description\": \"path is the relative path of the file to map the key to.\\nMay not be an absolute path.\\nMay not contain the path element '..'.\\nMay not start with the string '..'.\",\n                  \"type\": \"string\"\n                 }\n                },\n                \"required\": [\n                 \"key\",\n                 \"path\"\n                ],\n                \"type\": \"object\"\n               },\n               \"type\": \"array\",\n               \"x-kubernetes-list-type\": \"atomic\"\n              },\n              \"name\": {\n               \"default\": \"\",\n               \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n               \"type\": \"string\"\n              },\n              \"optional\": {\n               \"description\": \"optional field specify whether the Secret or its key must be defined\",\n               \"type\": \"boolean\"\n              }\n             },\n             \"type\": \"object\",\n             \"x-kubernetes-map-type\": \"atomic\"\n            },\n            \"serviceAccountToken\": {\n             \"description\": \"serviceAccountToken is information about the serviceAccountToken data to project\",\n             \"properties\": {\n              \"audience\": {\n               \"description\": \"audience is the intended audience of the token. A recipient of a token\\nmust identify itself with an identifier specified in the audience of the\\ntoken, and otherwise should reject the token. The audience defaults to the\\nidentifier of the apiserver.\",\n               \"type\": \"string\"\n              },\n              \"expirationSeconds\": {\n               \"description\": \"expirationSeconds is the requested duration of validity of the service\\naccount token. As the token approaches expiration, the kubelet volume\\nplugin will proactively rotate the service account token. The kubelet will\\nstart trying to rotate the token if the token is older than 80 percent of\\nits time to live or if the token is older than 24 hours.Defaults to 1 hour\\nand must be at least 10 minutes.\",\n               \"format\": \"int64\",\n               \"type\": \"integer\"\n              },\n              \"path\": {\n               \"description\": \"path is the path relative to the mount point of the file to project the\\ntoken into.\",\n               \"type\": \"string\"\n              }\n             },\n             \"required\": [\n              \"path\"\n             ],\n             \"type\": \"object\"\n            }\n           },\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"quobyte\": {\n        \"description\": \"quobyte represents a Quobyte mount on the host that shares a pod's lifetime.\\nDeprecated: Quobyte is deprecated and the in-tree quobyte type is no longer supported.\",\n        \"properties\": {\n         \"group\": {\n          \"description\": \"group to map volume access to\\nDefault is no group\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly here will force the Quobyte volume to be mounted with read-only permissions.\\nDefaults to false.\",\n          \"type\": \"boolean\"\n         },\n         \"registry\": {\n          \"description\": \"registry represents a single or multiple Quobyte Registry services\\nspecified as a string as host:port pair (multiple entries are separated with commas)\\nwhich acts as the central registry for volumes\",\n          \"type\": \"string\"\n         },\n         \"tenant\": {\n          \"description\": \"tenant owning the given Quobyte volume in the Backend\\nUsed with dynamically provisioned Quobyte volumes, value is set by the plugin\",\n          \"type\": \"string\"\n         },\n         \"user\": {\n          \"description\": \"user to map volume access to\\nDefaults to serivceaccount user\",\n          \"type\": \"string\"\n         },\n         \"volume\": {\n          \"description\": \"volume is a string that references an already created Quobyte volume by name.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"registry\",\n         \"volume\"\n        ],\n        \"type\": \"object\"\n       },\n       \"rbd\": {\n        \"description\": \"rbd represents a Rados Block Device mount on the host that shares a pod's lifetime.\\nDeprecated: RBD is deprecated and the in-tree rbd type is no longer supported.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"fsType is the filesystem type of the volume that you want to mount.\\nTip: Ensure that the filesystem type is supported by the host operating system.\\nExamples: \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#rbd\",\n          \"type\": \"string\"\n         },\n         \"image\": {\n          \"description\": \"image is the rados image name.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"keyring\": {\n          \"default\": \"/etc/ceph/keyring\",\n          \"description\": \"keyring is the path to key ring for RBDUser.\\nDefault is /etc/ceph/keyring.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"monitors\": {\n          \"description\": \"monitors is a collection of Ceph monitors.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"items\": {\n           \"type\": \"string\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"pool\": {\n          \"default\": \"rbd\",\n          \"description\": \"pool is the rados pool name.\\nDefault is rbd.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly here will force the ReadOnly setting in VolumeMounts.\\nDefaults to false.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"secretRef is name of the authentication secret for RBDUser. If provided\\noverrides keyring.\\nDefault is nil.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"user\": {\n          \"default\": \"admin\",\n          \"description\": \"user is the rados user name.\\nDefault is admin.\\nMore info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"image\",\n         \"monitors\"\n        ],\n        \"type\": \"object\"\n       },\n       \"scaleIO\": {\n        \"description\": \"scaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.\\nDeprecated: ScaleIO is deprecated and the in-tree scaleIO type is no longer supported.\",\n        \"properties\": {\n         \"fsType\": {\n          \"default\": \"xfs\",\n          \"description\": \"fsType is the filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\".\\nDefault is \\\"xfs\\\".\",\n          \"type\": \"string\"\n         },\n         \"gateway\": {\n          \"description\": \"gateway is the host address of the ScaleIO API Gateway.\",\n          \"type\": \"string\"\n         },\n         \"protectionDomain\": {\n          \"description\": \"protectionDomain is the name of the ScaleIO Protection Domain for the configured storage.\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly Defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"secretRef references to the secret for ScaleIO user and other\\nsensitive information. If this is not provided, Login operation will fail.\",\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"sslEnabled\": {\n          \"description\": \"sslEnabled Flag enable/disable SSL communication with Gateway, default false\",\n          \"type\": \"boolean\"\n         },\n         \"storageMode\": {\n          \"default\": \"ThinProvisioned\",\n          \"description\": \"storageMode indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned.\\nDefault is ThinProvisioned.\",\n          \"type\": \"string\"\n         },\n         \"storagePool\": {\n          \"description\": \"storagePool is the ScaleIO Storage Pool associated with the protection domain.\",\n          \"type\": \"string\"\n         },\n         \"system\": {\n          \"description\": \"system is the name of the storage system as configured in ScaleIO.\",\n          \"type\": \"string\"\n         },\n         \"volumeName\": {\n          \"description\": \"volumeName is the name of a volume already created in the ScaleIO system\\nthat is associated with this volume source.\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"gateway\",\n         \"secretRef\",\n         \"system\"\n        ],\n        \"type\": \"object\"\n       },\n       \"secret\": {\n        \"description\": \"secret represents a secret that should populate this volume.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n        \"properties\": {\n         \"defaultMode\": {\n          \"description\": \"defaultMode is Optional: mode bits used to set permissions on created files by default.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values\\nfor mode bits. Defaults to 0644.\\nDirectories within the path are not affected by this setting.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n          \"format\": \"int32\",\n          \"type\": \"integer\"\n         },\n         \"items\": {\n          \"description\": \"items If unspecified, each key-value pair in the Data field of the referenced\\nSecret will be projected into the volume as a file whose name is the\\nkey and content is the value. If specified, the listed keys will be\\nprojected into the specified paths, and unlisted keys will not be\\npresent. If a key is specified which is not present in the Secret,\\nthe volume setup will error unless it is marked optional. Paths must be\\nrelative and may not contain the '..' path or start with '..'.\",\n          \"items\": {\n           \"description\": \"Maps a string key to a path within a volume.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"key is the key to project.\",\n             \"type\": \"string\"\n            },\n            \"mode\": {\n             \"description\": \"mode is Optional: mode bits used to set permissions on this file.\\nMust be an octal value between 0000 and 0777 or a decimal value between 0 and 511.\\nYAML accepts both octal and decimal values, JSON requires decimal values for mode bits.\\nIf not specified, the volume defaultMode will be used.\\nThis might be in conflict with other options that affect the file\\nmode, like fsGroup, and the result can be other mode bits set.\",\n             \"format\": \"int32\",\n             \"type\": \"integer\"\n            },\n            \"path\": {\n             \"description\": \"path is the relative path of the file to map the key to.\\nMay not be an absolute path.\\nMay not contain the path element '..'.\\nMay not start with the string '..'.\",\n             \"type\": \"string\"\n            }\n           },\n           \"required\": [\n            \"key\",\n            \"path\"\n           ],\n           \"type\": \"object\"\n          },\n          \"type\": \"array\",\n          \"x-kubernetes-list-type\": \"atomic\"\n         },\n         \"optional\": {\n          \"description\": \"optional field specify whether the Secret or its keys must be defined\",\n          \"type\": \"boolean\"\n         },\n         \"secretName\": {\n          \"description\": \"secretName is the name of the secret in the pod's namespace to use.\\nMore info: https://kubernetes.io/docs/concepts/storage/volumes#secret\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"storageos\": {\n        \"description\": \"storageOS represents a StorageOS volume attached and mounted on Kubernetes nodes.\\nDeprecated: StorageOS is deprecated and the in-tree storageos type is no longer supported.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"fsType is the filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"readOnly\": {\n          \"description\": \"readOnly defaults to false (read/write). ReadOnly here will force\\nthe ReadOnly setting in VolumeMounts.\",\n          \"type\": \"boolean\"\n         },\n         \"secretRef\": {\n          \"description\": \"secretRef specifies the secret to use for obtaining the StorageOS API\\ncredentials.  If not specified, default values will be attempted.\",\n          \"properties\": {\n           \"name\": {\n            \"default\": \"\",\n            \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n            \"type\": \"string\"\n           }\n          },\n          \"type\": \"object\",\n          \"x-kubernetes-map-type\": \"atomic\"\n         },\n         \"volumeName\": {\n          \"description\": \"volumeName is the human-readable name of the StorageOS volume.  Volume\\nnames are only unique within a namespace.\",\n          \"type\": \"string\"\n         },\n         \"volumeNamespace\": {\n          \"description\": \"volumeNamespace specifies the scope of the volume within StorageOS.  If no\\nnamespace is specified then the Pod's namespace will be used.  This allows the\\nKubernetes name scoping to be mirrored within StorageOS for tighter integration.\\nSet VolumeName to any name to override the default behaviour.\\nSet to \\\"default\\\" if you are not using namespaces within StorageOS.\\nNamespaces that do not pre-exist within StorageOS will be created.\",\n          \"type\": \"string\"\n         }\n        },\n        \"type\": \"object\"\n       },\n       \"vsphereVolume\": {\n        \"description\": \"vsphereVolume represents a vSphere volume attached and mounted on kubelets host machine.\\nDeprecated: VsphereVolume is deprecated. All operations for the in-tree vsphereVolume type\\nare redirected to the csi.vsphere.vmware.com CSI driver.\",\n        \"properties\": {\n         \"fsType\": {\n          \"description\": \"fsType is filesystem type to mount.\\nMust be a filesystem type supported by the host operating system.\\nEx. \\\"ext4\\\", \\\"xfs\\\", \\\"ntfs\\\". Implicitly inferred to be \\\"ext4\\\" if unspecified.\",\n          \"type\": \"string\"\n         },\n         \"storagePolicyID\": {\n          \"description\": \"storagePolicyID is the storage Policy Based Management (SPBM) profile ID associated with the StoragePolicyName.\",\n          \"type\": \"string\"\n         },\n         \"storagePolicyName\": {\n          \"description\": \"storagePolicyName is the storage Policy Based Management (SPBM) profile name.\",\n          \"type\": \"string\"\n         },\n         \"volumePath\": {\n          \"description\": \"volumePath is the path that identifies vSphere volume vmdk\",\n          \"type\": \"string\"\n         }\n        },\n        \"required\": [\n         \"volumePath\"\n        ],\n        \"type\": \"object\"\n       }\n      },\n      \"required\": [\n       \"name\"\n      ],\n      \"type\": \"object\"\n     },\n     \"type\": \"array\"\n    },\n    \"web\": {\n     \"description\": \"Defines the web command line flags when starting Alertmanager.\",\n     \"properties\": {\n      \"getConcurrency\": {\n       \"description\": \"Maximum number of GET requests processed concurrently. This corresponds to the\\nAlertmanager's `--web.get-concurrency` flag.\",\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"httpConfig\": {\n       \"description\": \"Defines HTTP parameters for web server.\",\n       \"properties\": {\n        \"headers\": {\n         \"description\": \"List of headers that can be added to HTTP responses.\",\n         \"properties\": {\n          \"contentSecurityPolicy\": {\n           \"description\": \"Set the Content-Security-Policy header to HTTP responses.\\nUnset if blank.\",\n           \"type\": \"string\"\n          },\n          \"strictTransportSecurity\": {\n           \"description\": \"Set the Strict-Transport-Security header to HTTP responses.\\nUnset if blank.\\nPlease make sure that you use this with care as this header might force\\nbrowsers to load Prometheus and the other applications hosted on the same\\ndomain and subdomains over HTTPS.\\nhttps://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Strict-Transport-Security\",\n           \"type\": \"string\"\n          },\n          \"xContentTypeOptions\": {\n           \"description\": \"Set the X-Content-Type-Options header to HTTP responses.\\nUnset if blank. Accepted value is nosniff.\\nhttps://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Content-Type-Options\",\n           \"enum\": [\n            \"\",\n            \"NoSniff\"\n           ],\n           \"type\": \"string\"\n          },\n          \"xFrameOptions\": {\n           \"description\": \"Set the X-Frame-Options header to HTTP responses.\\nUnset if blank. Accepted values are deny and sameorigin.\\nhttps://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options\",\n           \"enum\": [\n            \"\",\n            \"Deny\",\n            \"SameOrigin\"\n           ],\n           \"type\": \"string\"\n          },\n          \"xXSSProtection\": {\n           \"description\": \"Set the X-XSS-Protection header to all responses.\\nUnset if blank.\\nhttps://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-XSS-Protection\",\n           \"type\": \"string\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"http2\": {\n         \"description\": \"Enable HTTP/2 support. Note that HTTP/2 is only supported with TLS.\\nWhen TLSConfig is not configured, HTTP/2 will be disabled.\\nWhenever the value of the field changes, a rolling update will be triggered.\",\n         \"type\": \"boolean\"\n        }\n       },\n       \"type\": \"object\"\n      },\n      \"timeout\": {\n       \"description\": \"Timeout for HTTP requests. This corresponds to the Alertmanager's\\n`--web.timeout` flag.\",\n       \"format\": \"int32\",\n       \"type\": \"integer\"\n      },\n      \"tlsConfig\": {\n       \"description\": \"Defines the TLS parameters for HTTPS.\",\n       \"properties\": {\n        \"cert\": {\n         \"description\": \"Secret or ConfigMap containing the TLS certificate for the web server.\\n\\nEither `keySecret` or `keyFile` must be defined.\\n\\nIt is mutually exclusive with `certFile`.\",\n         \"properties\": {\n          \"configMap\": {\n           \"description\": \"ConfigMap containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to select.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"secret\": {\n           \"description\": \"Secret containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"certFile\": {\n         \"description\": \"Path to the TLS certificate file in the container for the web server.\\n\\nEither `keySecret` or `keyFile` must be defined.\\n\\nIt is mutually exclusive with `cert`.\",\n         \"type\": \"string\"\n        },\n        \"cipherSuites\": {\n         \"description\": \"List of supported cipher suites for TLS versions up to TLS 1.2.\\n\\nIf not defined, the Go default cipher suites are used.\\nAvailable cipher suites are documented in the Go documentation:\\nhttps://golang.org/pkg/crypto/tls/#pkg-constants\",\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"clientAuthType\": {\n         \"description\": \"The server policy for client TLS authentication.\\n\\nFor more detail on clientAuth options:\\nhttps://golang.org/pkg/crypto/tls/#ClientAuthType\",\n         \"type\": \"string\"\n        },\n        \"clientCAFile\": {\n         \"description\": \"Path to the CA certificate file for client certificate authentication to\\nthe server.\\n\\nIt is mutually exclusive with `client_ca`.\",\n         \"type\": \"string\"\n        },\n        \"client_ca\": {\n         \"description\": \"Secret or ConfigMap containing the CA certificate for client certificate\\nauthentication to the server.\\n\\nIt is mutually exclusive with `clientCAFile`.\",\n         \"properties\": {\n          \"configMap\": {\n           \"description\": \"ConfigMap containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key to select.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the ConfigMap or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          },\n          \"secret\": {\n           \"description\": \"Secret containing data to use for the targets.\",\n           \"properties\": {\n            \"key\": {\n             \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n             \"type\": \"string\"\n            },\n            \"name\": {\n             \"default\": \"\",\n             \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n             \"type\": \"string\"\n            },\n            \"optional\": {\n             \"description\": \"Specify whether the Secret or its key must be defined\",\n             \"type\": \"boolean\"\n            }\n           },\n           \"required\": [\n            \"key\"\n           ],\n           \"type\": \"object\",\n           \"x-kubernetes-map-type\": \"atomic\"\n          }\n         },\n         \"type\": \"object\"\n        },\n        \"curvePreferences\": {\n         \"description\": \"Elliptic curves that will be used in an ECDHE handshake, in preference\\norder.\\n\\nAvailable curves are documented in the Go documentation:\\nhttps://golang.org/pkg/crypto/tls/#CurveID\",\n         \"items\": {\n          \"type\": \"string\"\n         },\n         \"type\": \"array\"\n        },\n        \"keyFile\": {\n         \"description\": \"Path to the TLS private key file in the container for the web server.\\n\\nIf defined, either `cert` or `certFile` must be defined.\\n\\nIt is mutually exclusive with `keySecret`.\",\n         \"type\": \"string\"\n        },\n        \"keySecret\": {\n         \"description\": \"Secret containing the TLS private key for the web server.\\n\\nEither `cert` or `certFile` must be defined.\\n\\nIt is mutually exclusive with `keyFile`.\",\n         \"properties\": {\n          \"key\": {\n           \"description\": \"The key of the secret to select from.  Must be a valid secret key.\",\n           \"type\": \"string\"\n          },\n          \"name\": {\n           \"default\": \"\",\n           \"description\": \"Name of the referent.\\nThis field is effectively required, but due to backwards compatibility is\\nallowed to be empty. Instances of this type with an empty value here are\\nalmost certainly wrong.\\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names\",\n           \"type\": \"string\"\n          },\n          \"optional\": {\n           \"description\": \"Specify whether the Secret or its key must be defined\",\n           \"type\": \"boolean\"\n          }\n         },\n         \"required\": [\n          \"key\"\n         ],\n         \"type\": \"object\",\n         \"x-kubernetes-map-type\": \"atomic\"\n        },\n        \"maxVersion\": {\n         \"description\": \"Maximum TLS version that is acceptable.\",\n         \"type\": \"string\"\n        },\n        \"minVersion\": {\n         \"description\": \"Minimum TLS version that is acceptable.\",\n         \"type\": \"string\"\n        },\n        \"preferServerCipherSuites\": {\n         \"description\": \"Controls whether the server selects the client's most preferred cipher\\nsuite, or the server's most preferred cipher suite.\\n\\nIf true then the server's preference, as expressed in\\nthe order of elements in cipherSuites, is used.\",\n         \"type\": \"boolean\"\n        }\n       },\n       \"type\": \"object\"\n      }\n     },\n     \"type\": \"object\"\n    }\n   },\n   \"type\": \"object\"\n  }\n },\n \"required\": [\n  \"spec\"\n ],\n \"title\": \"Alertmanager\",\n \"type\": \"object\"\n}"}}